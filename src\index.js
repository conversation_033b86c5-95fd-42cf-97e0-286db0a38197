import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.jsx';
import './styles.css';

// --- Sentry Error Monitoring ---
import { initSentry } from './utils/sentry';

// Initialize Sentry as early as possible
initSentry();

// --- Plausible Analytics ---
(function () {
  const s = document.createElement('script');
  s.setAttribute('defer', 'defer');
  s.setAttribute('data-domain', 'yourdomain.com'); // <-- Replace with your domain
  s.src = 'https://plausible.io/js/plausible.js';
  document.head.appendChild(s);
})();

// This file is the entry point for Vite
console.log('Vite + Tailwind CSS integration is working!');

// Add dark mode toggle functionality
document.addEventListener('DOMContentLoaded', () => {
  const darkModeToggle = document.getElementById('darkModeToggle');

  if (darkModeToggle) {
    // Check for saved theme preference or use system preference
    const isDarkMode = localStorage.getItem('darkMode') === 'true' ||
      (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches);

    // Set initial theme
    if (isDarkMode) {
      document.documentElement.setAttribute('data-theme', 'dark');
      darkModeToggle.checked = true;
    }

    // Toggle theme when button is clicked
    darkModeToggle.addEventListener('change', () => {
      if (darkModeToggle.checked) {
        document.documentElement.setAttribute('data-theme', 'dark');
        localStorage.setItem('darkMode', 'true');
      } else {
        document.documentElement.removeAttribute('data-theme');
        localStorage.setItem('darkMode', 'false');
      }
    });
  }
});

const root = createRoot(document.getElementById('root'));
root.render(<App />);
