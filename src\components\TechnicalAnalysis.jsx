import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

const TechnicalAnalysis = ({ symbol }) => {
  const [analysisData, setAnalysisData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchAnalysis = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/v1/technical/analysis/${symbol}`);
        setAnalysisData(response.data.data);
        setError(null);
      } catch (err) {
        setError(err.response?.data?.message || 'Error fetching analysis data');
      } finally {
        setLoading(false);
      }
    };

    if (symbol) {
      fetchAnalysis();
    }
  }, [symbol]);

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        {error}
      </div>
    );
  }

  if (!analysisData) {
    return null;
  }

  const { indicators } = analysisData;

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-2xl font-bold mb-4">Technical Analysis - {symbol}</h2>
      
      {/* RSI Chart */}
      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-2">RSI</h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={indicators.rsi}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis domain={[0, 100]} />
            <Tooltip />
            <Legend />
            <Line
              type="monotone"
              dataKey="RSI"
              stroke="#8884d8"
              dot={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* MACD Chart */}
      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-2">MACD</h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={indicators.macd}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line
              type="monotone"
              dataKey="MACD"
              stroke="#8884d8"
              dot={false}
            />
            <Line
              type="monotone"
              dataKey="MACD_Signal"
              stroke="#82ca9d"
              dot={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Bollinger Bands */}
      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-2">Bollinger Bands</h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={indicators.bbands}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line
              type="monotone"
              dataKey="Real Upper Band"
              stroke="#82ca9d"
              dot={false}
            />
            <Line
              type="monotone"
              dataKey="Real Middle Band"
              stroke="#8884d8"
              dot={false}
            />
            <Line
              type="monotone"
              dataKey="Real Lower Band"
              stroke="#ff7300"
              dot={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Trading Signals */}
      <div className="mt-8">
        <h3 className="text-xl font-semibold mb-4">Trading Signals</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-700">Trend</h4>
            <p className={`text-lg font-bold ${
              analysisData.signals?.trend === 'bullish' ? 'text-green-600' :
              analysisData.signals?.trend === 'bearish' ? 'text-red-600' :
              'text-gray-600'
            }`}>
              {analysisData.signals?.trend?.toUpperCase()}
            </p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-700">Signal Strength</h4>
            <p className="text-lg font-bold">
              {analysisData.signals?.strength}%
            </p>
          </div>
        </div>
        <div className="mt-4">
          <h4 className="font-medium text-gray-700 mb-2">Recommendations</h4>
          <ul className="list-disc list-inside">
            {analysisData.signals?.recommendations.map((rec, index) => (
              <li key={index} className="text-gray-600">{rec}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default TechnicalAnalysis; 