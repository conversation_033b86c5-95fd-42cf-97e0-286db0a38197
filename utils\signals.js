const logger = require('../logging');
const { calculateSMA, calculateEMA, calculateRSI, calculateMACD, calculateBollingerBands } = require('./indicators');

/**
 * Generate trading signals based on price data
 * @param {Array} data - Price data, most recent first
 * @returns {Object} - Object containing generated signals and analysis
 */
function generateSignals(data) {
  if (!data || data.length < 50) {
    logger.warn('Insufficient data for signal generation');
    return {
      signals: [],
      analysis: {
        trend: 'unknown',
        strength: 0,
        recommendation: 'neutral'
      }
    };
  }
  
  try {
    // Calculate indicators
    const sma20 = calculateSMA(data, 20, 'close');
    const sma50 = calculateSMA(data, 50, 'close');
    const sma200 = calculateSMA(data, 200, 'close');
    const ema12 = calculateEMA(data, 12, 'close');
    const ema26 = calculateEMA(data, 26, 'close');
    const rsi14 = calculateRSI(data, 14, 'close');
    const macd = calculateMACD(data, 12, 26, 9, 'close');
    const bollingerBands = calculateBollingerBands(data, 20, 2, 'close');
    
    // Most recent values for signal generation
    const currentClose = parseFloat(data[0].close);
    const previousClose = parseFloat(data[1].close);
    const currentRSI = rsi14[0];
    const previousRSI = rsi14[1];
    const currentMACD = macd.macd[0];
    const currentSignal = macd.signal[0];
    const previousMACD = macd.macd[1];
    const previousSignal = macd.signal[1];
    const upperBand = bollingerBands.upper[0];
    const lowerBand = bollingerBands.lower[0];
    
    // Generate signals
    const signals = [];
    
    // MA crossover signals
    if (ema12[0] > ema26[0] && ema12[1] <= ema26[1]) {
      signals.push({
        type: 'buy',
        source: 'ema-crossover',
        strength: 0.7,
        message: 'EMA 12 crossed above EMA 26'
      });
    } else if (ema12[0] < ema26[0] && ema12[1] >= ema26[1]) {
      signals.push({
        type: 'sell',
        source: 'ema-crossover',
        strength: 0.7,
        message: 'EMA 12 crossed below EMA 26'
      });
    }
    
    // MACD signals
    if (currentMACD > currentSignal && previousMACD <= previousSignal) {
      signals.push({
        type: 'buy',
        source: 'macd',
        strength: 0.8,
        message: 'MACD line crossed above signal line'
      });
    } else if (currentMACD < currentSignal && previousMACD >= previousSignal) {
      signals.push({
        type: 'sell',
        source: 'macd',
        strength: 0.8,
        message: 'MACD line crossed below signal line'
      });
    }
    
    // RSI signals
    if (currentRSI < 30 && previousRSI >= 30) {
      signals.push({
        type: 'buy',
        source: 'rsi',
        strength: 0.65,
        message: 'RSI entered oversold territory'
      });
    } else if (currentRSI > 70 && previousRSI <= 70) {
      signals.push({
        type: 'sell',
        source: 'rsi',
        strength: 0.65,
        message: 'RSI entered overbought territory'
      });
    }
    
    // Bollinger Band signals
    if (currentClose <= lowerBand) {
      signals.push({
        type: 'buy',
        source: 'bollinger',
        strength: 0.6,
        message: 'Price touched lower Bollinger Band'
      });
    } else if (currentClose >= upperBand) {
      signals.push({
        type: 'sell',
        source: 'bollinger',
        strength: 0.6,
        message: 'Price touched upper Bollinger Band'
      });
    }
    
    // Trend following signals
    if (currentClose > sma20[0] && currentClose > sma50[0] && currentClose > sma200[0]) {
      signals.push({
        type: 'buy',
        source: 'trend',
        strength: 0.5,
        message: 'Price above all major moving averages, bullish trend'
      });
    } else if (currentClose < sma20[0] && currentClose < sma50[0] && currentClose < sma200[0]) {
      signals.push({
        type: 'sell',
        source: 'trend',
        strength: 0.5,
        message: 'Price below all major moving averages, bearish trend'
      });
    }
    
    // Determine overall trend
    let trend = 'neutral';
    if (sma20[0] > sma50[0] && sma50[0] > sma200[0]) {
      trend = 'bullish';
    } else if (sma20[0] < sma50[0] && sma50[0] < sma200[0]) {
      trend = 'bearish';
    }
    
    // Generate overall recommendation
    const buySignals = signals.filter(s => s.type === 'buy');
    const sellSignals = signals.filter(s => s.type === 'sell');
    
    let buyStrength = 0;
    let sellStrength = 0;
    
    buySignals.forEach(signal => {
      buyStrength += signal.strength;
    });
    
    sellSignals.forEach(signal => {
      sellStrength += signal.strength;
    });
    
    // Normalize strengths
    if (buySignals.length > 0) {
      buyStrength = buyStrength / buySignals.length;
    }
    
    if (sellSignals.length > 0) {
      sellStrength = sellStrength / sellSignals.length;
    }
    
    // Determine recommendation
    let recommendation = 'neutral';
    let recommendationStrength = 0;
    
    if (buyStrength > sellStrength) {
      recommendation = 'buy';
      recommendationStrength = buyStrength;
    } else if (sellStrength > buyStrength) {
      recommendation = 'sell';
      recommendationStrength = sellStrength;
    }
    
    // Include market conditions
    let marketConditions = [];
    
    if (currentRSI > 70) {
      marketConditions.push('Overbought');
    } else if (currentRSI < 30) {
      marketConditions.push('Oversold');
    }
    
    if (bollingerBands.upper[0] - bollingerBands.lower[0] > (bollingerBands.middle[0] * 0.05)) {
      marketConditions.push('High volatility');
    } else {
      marketConditions.push('Low volatility');
    }
    
    return {
      signals,
      analysis: {
        trend,
        recommendation,
        strength: recommendationStrength,
        latestPrice: currentClose,
        priceChange: currentClose - previousClose,
        priceChangePercent: ((currentClose - previousClose) / previousClose) * 100,
        rsi: currentRSI,
        macd: {
          line: currentMACD,
          signal: currentSignal,
          histogram: macd.histogram[0]
        },
        movingAverages: {
          sma20: sma20[0],
          sma50: sma50[0],
          sma200: sma200[0]
        },
        bollingerBands: {
          upper: upperBand,
          middle: bollingerBands.middle[0],
          lower: lowerBand
        },
        marketConditions
      }
    };
  } catch (error) {
    logger.error('Error generating signals:', error);
    return {
      signals: [],
      analysis: {
        trend: 'unknown',
        strength: 0,
        recommendation: 'neutral',
        error: error.message
      }
    };
  }
}

module.exports = {
  generateSignals
}; 