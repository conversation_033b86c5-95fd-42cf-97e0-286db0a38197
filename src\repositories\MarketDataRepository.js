/**
 * Market Data Repository for MongoDB
 * 
 * This repository handles all market data-related database operations.
 */

const BaseRepository = require('./BaseRepository');
const logger = require('../utils/logger');

class MarketDataRepository extends BaseRepository {
  /**
   * Create a new MarketDataRepository instance
   * @param {Object} db - MongoDB database instance
   */
  constructor(db) {
    super(db, 'marketData');
    logger.debug('MarketDataRepository initialized');
  }

  /**
   * Find market data by symbol and timeframe
   * @param {string} symbol - Trading symbol (e.g., 'EURUSD')
   * @param {string} timeframe - Timeframe (e.g., 'M5', 'H1', 'D1')
   * @param {Object} options - Query options (sort, limit, skip)
   * @returns {Promise<Array>} Array of market data points
   */
  async findBySymbolAndTimeframe(symbol, timeframe, options = {}) {
    try {
      return await this.find({ symbol, timeframe }, {
        ...options,
        sort: options.sort || { timestamp: -1 }
      });
    } catch (error) {
      logger.error('Error finding market data by symbol and timeframe:', error);
      throw error;
    }
  }

  /**
   * Find latest market data for a symbol
   * @param {string} symbol - Trading symbol (e.g., 'EURUSD')
   * @param {string} timeframe - Timeframe (e.g., 'M5', 'H1', 'D1')
   * @returns {Promise<Object|null>} Latest market data or null if not found
   */
  async findLatest(symbol, timeframe) {
    try {
      const data = await this.find(
        { symbol, timeframe },
        { sort: { timestamp: -1 }, limit: 1 }
      );
      return data.length > 0 ? data[0] : null;
    } catch (error) {
      logger.error('Error finding latest market data:', error);
      throw error;
    }
  }

  /**
   * Save market data
   * @param {Object} marketData - Market data object
   * @returns {Promise<Object>} Saved market data
   */
  async saveMarketData(marketData) {
    try {
      // Validate required fields
      if (!marketData.symbol || !marketData.timeframe || !marketData.timestamp) {
        throw new Error('Symbol, timeframe, and timestamp are required');
      }

      // Check if data already exists
      const existingData = await this.findOne({
        symbol: marketData.symbol,
        timeframe: marketData.timeframe,
        timestamp: marketData.timestamp
      });

      if (existingData) {
        // Update existing data
        await this.updateById(existingData._id, marketData);
        return { ...existingData, ...marketData };
      } else {
        // Insert new data
        return await this.insertOne(marketData);
      }
    } catch (error) {
      logger.error('Error saving market data:', error);
      throw error;
    }
  }

  /**
   * Save multiple market data points
   * @param {Array} dataPoints - Array of market data points
   * @returns {Promise<Object>} InsertMany result
   */
  async saveMultiple(dataPoints) {
    try {
      if (!Array.isArray(dataPoints) || dataPoints.length === 0) {
        throw new Error('Data points must be a non-empty array');
      }

      // Validate all data points
      dataPoints.forEach(point => {
        if (!point.symbol || !point.timeframe || !point.timestamp) {
          throw new Error('Symbol, timeframe, and timestamp are required for all data points');
        }
      });

      // Insert data points
      return await this.insertMany(dataPoints);
    } catch (error) {
      logger.error('Error saving multiple market data points:', error);
      throw error;
    }
  }

  /**
   * Delete old market data
   * @param {string} symbol - Trading symbol (e.g., 'EURUSD')
   * @param {string} timeframe - Timeframe (e.g., 'M5', 'H1', 'D1')
   * @param {Date} olderThan - Delete data older than this date
   * @returns {Promise<Object>} DeleteMany result
   */
  async deleteOldData(symbol, timeframe, olderThan) {
    try {
      return await this.deleteMany({
        symbol,
        timeframe,
        timestamp: { $lt: olderThan }
      });
    } catch (error) {
      logger.error('Error deleting old market data:', error);
      throw error;
    }
  }

  /**
   * Get OHLC data for a symbol and timeframe
   * @param {string} symbol - Trading symbol (e.g., 'EURUSD')
   * @param {string} timeframe - Timeframe (e.g., 'M5', 'H1', 'D1')
   * @param {number} limit - Number of data points to return
   * @returns {Promise<Array>} Array of OHLC data
   */
  async getOHLC(symbol, timeframe, limit = 100) {
    try {
      const data = await this.find(
        { symbol, timeframe },
        { sort: { timestamp: -1 }, limit }
      );

      // Format data for OHLC chart
      return data.map(item => ({
        timestamp: item.timestamp,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume
      })).reverse(); // Reverse to get chronological order
    } catch (error) {
      logger.error('Error getting OHLC data:', error);
      throw error;
    }
  }
}

module.exports = MarketDataRepository;
