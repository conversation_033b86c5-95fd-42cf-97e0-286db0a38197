/**
 * Swagger API Documentation Setup
 *
 * This file configures Swagger UI for API documentation.
 */

const swaggerJsDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const path = require('path');
const { version } = require('../../package.json');

/**
 * Set up Swagger documentation for the API
 * @param {Express} app - Express application
 */
function setupSwagger(app) {
  // Swagger configuration
  const swaggerOptions = {
    definition: {
      openapi: '3.0.0',
      info: {
        title: 'Trading Signals API',
        version,
        description: 'API for Trading Signals Application',
        license: {
          name: 'MIT',
          url: 'https://opensource.org/licenses/MIT',
        },
        contact: {
          name: 'API Support',
          email: '<EMAIL>',
        },
      },
      servers: [
        {
          url: '/api',
          description: 'Production server',
        },
        {
          url: 'http://localhost:3000/api',
          description: 'Development server',
        },
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          },
        },
        schemas: {
          Signal: {
            type: 'object',
            required: ['symbol', 'type', 'source', 'entryPrice', 'confidence', 'timeframe', 'message'],
            properties: {
              id: {
                type: 'string',
                description: 'Unique signal identifier',
                example: 'signal_1234567890_abc123'
              },
              _id: {
                type: 'string',
                description: 'MongoDB ObjectId',
                example: '60d21b4667d0d8992e610c85'
              },
              symbol: {
                type: 'string',
                description: 'Trading symbol',
                example: 'EURUSD',
                minLength: 3,
                maxLength: 20
              },
              type: {
                type: 'string',
                enum: ['BUY', 'SELL', 'HOLD'],
                description: 'Signal type',
                example: 'BUY'
              },
              source: {
                type: 'string',
                enum: ['AI', 'TRADITIONAL', 'UNIFIED', 'MANUAL'],
                description: 'Signal source',
                example: 'AI'
              },
              subSource: {
                type: 'string',
                description: 'Specific source identifier',
                example: 'openai'
              },
              entryPrice: {
                type: 'number',
                description: 'Entry price for the signal',
                example: 1.0850,
                minimum: 0
              },
              stopLoss: {
                type: 'number',
                description: 'Stop loss price',
                example: 1.0800,
                minimum: 0
              },
              takeProfit: {
                type: 'number',
                description: 'Take profit price',
                example: 1.0950,
                minimum: 0
              },
              confidence: {
                type: 'integer',
                description: 'Signal confidence percentage (0-100)',
                example: 85,
                minimum: 0,
                maximum: 100
              },
              strength: {
                type: 'number',
                description: 'Legacy signal strength (0-1)',
                example: 0.85,
                minimum: 0,
                maximum: 1
              },
              qualityScore: {
                type: 'integer',
                description: 'Overall signal quality score (0-100)',
                example: 90,
                minimum: 0,
                maximum: 100
              },
              timeframe: {
                type: 'string',
                enum: ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN'],
                description: 'Chart timeframe',
                example: 'H1'
              },
              timestamp: {
                type: 'string',
                format: 'date-time',
                description: 'Signal generation timestamp',
                example: '2024-01-15T10:30:00.000Z'
              },
              expiresAt: {
                type: 'string',
                format: 'date-time',
                description: 'Signal expiration timestamp',
                example: '2024-01-15T18:30:00.000Z'
              },
              message: {
                type: 'string',
                description: 'Short signal description',
                example: 'Bullish momentum detected with RSI oversold recovery',
                maxLength: 500
              },
              reasoning: {
                type: 'string',
                description: 'Detailed analysis and reasoning',
                example: 'Technical analysis shows strong bullish momentum...',
                maxLength: 2000
              },
              analysis: {
                type: 'string',
                description: 'Legacy analysis field',
                maxLength: 2000
              },
              riskReward: {
                type: 'number',
                description: 'Risk/reward ratio',
                example: 2.5,
                minimum: 0
              },
              status: {
                type: 'string',
                enum: ['ACTIVE', 'CLOSED', 'EXPIRED', 'CANCELLED'],
                description: 'Signal status',
                example: 'ACTIVE'
              },
              userId: {
                type: 'string',
                description: 'User who owns the signal',
                example: '60d21b4667d0d8992e610c85'
              },
              generatedBy: {
                type: 'string',
                description: 'System or user identifier',
                example: 'system'
              },
              createdAt: {
                type: 'string',
                format: 'date-time',
                description: 'Record creation timestamp',
                example: '2024-01-15T10:30:00.000Z'
              },
              updatedAt: {
                type: 'string',
                format: 'date-time',
                description: 'Record last update timestamp',
                example: '2024-01-15T10:35:00.000Z'
              }
            }
          },
          User: {
            type: 'object',
            required: ['email', 'username', 'password'],
            properties: {
              _id: {
                type: 'string',
                description: 'User ID (MongoDB ObjectId)',
                example: '60d21b4667d0d8992e610c85',
              },
              email: {
                type: 'string',
                format: 'email',
                description: 'User email',
                example: '<EMAIL>',
              },
              username: {
                type: 'string',
                description: 'Username',
                example: 'johndoe',
              },
              password: {
                type: 'string',
                format: 'password',
                description: 'User password (hashed)',
                example: '$2a$10$...',
              },
              role: {
                type: 'string',
                enum: ['user', 'admin'],
                description: 'User role',
                example: 'user',
              },
              createdAt: {
                type: 'string',
                format: 'date-time',
                description: 'User creation date',
                example: '2023-01-01T12:00:00Z',
              },
              updatedAt: {
                type: 'string',
                format: 'date-time',
                description: 'User last update date',
                example: '2023-01-02T12:00:00Z',
              },
            },
          },
          Error: {
            type: 'object',
            properties: {
              status: {
                type: 'string',
                description: 'Error status',
                example: 'error',
              },
              message: {
                type: 'string',
                description: 'Error message',
                example: 'Invalid input data',
              },
              errors: {
                type: 'array',
                description: 'Validation errors',
                items: {
                  type: 'object',
                  properties: {
                    field: {
                      type: 'string',
                      description: 'Field with error',
                      example: 'email',
                    },
                    message: {
                      type: 'string',
                      description: 'Error message for field',
                      example: 'Email is required',
                    },
                  },
                },
                example: [
                  {
                    field: 'email',
                    message: 'Email is required',
                  },
                ],
              },
            },
          },
        },
      },
    },
    apis: [
      path.join(__dirname, '../server/routes/*.js'),
      path.join(__dirname, '../controllers/*.js'),
    ],
  };

  // Generate Swagger specification
  const swaggerSpec = swaggerJsDoc(swaggerOptions);

  // Serve Swagger documentation
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Trading Signals API Documentation',
  }));

  // Serve Swagger JSON
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerSpec);
  });

  console.log('Swagger API documentation available at /api-docs');
}

module.exports = setupSwagger;
