/**
 * Enhanced API Service
 *
 * Comprehensive API service with advanced error handling, retry logic,
 * offline detection, and integration with our error management system.
 *
 * Features:
 * - Automatic retry with exponential backoff
 * - Network status detection
 * - Request queuing for offline scenarios
 * - Authentication token management
 * - Request/response interceptors
 * - Error categorization and reporting
 * - Performance monitoring
 * - Request cancellation support
 *
 * @version 1.0.0
 */

import React from 'react';
import { useError, useApiError, useNetworkError } from '../context/ErrorContext';
import { formatTimestamp } from '../types/common';
import { APIResponse, APIErrorResponse } from '../types/api';

// ============================================================================
// INTERFACES
// ============================================================================

export interface ApiConfig {
  baseURL: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  enableOfflineQueue: boolean;
  enableNetworkDetection: boolean;
  enablePerformanceTracking: boolean;
  headers?: Record<string, string>;
}

export interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  timeout?: number;
  retryAttempts?: number;
  skipRetry?: boolean;
  skipQueue?: boolean;
  signal?: AbortSignal;
}

export interface QueuedRequest {
  id: string;
  config: RequestConfig;
  timestamp: string;
  attempts: number;
  resolve: (value: any) => void;
  reject: (error: any) => void;
}

export interface NetworkStatus {
  online: boolean;
  connectionType: string;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
  lastChecked: string;
}

export interface ApiMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  networkErrors: number;
  serverErrors: number;
  retryCount: number;
  queuedRequests: number;
}

// ============================================================================
// ENHANCED API SERVICE CLASS
// ============================================================================

class EnhancedApiService {
  private config: ApiConfig;
  private requestQueue: QueuedRequest[] = [];
  private networkStatus: NetworkStatus;
  private metrics: ApiMetrics;
  private authToken: string | null = null;
  private refreshTokenPromise: Promise<string> | null = null;

  constructor(config: Partial<ApiConfig> = {}) {
    this.config = {
      baseURL: process.env.REACT_APP_API_URL || '/api',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      enableOfflineQueue: true,
      enableNetworkDetection: true,
      enablePerformanceTracking: true,
      headers: {
        'Content-Type': 'application/json'
      },
      ...config
    };

    this.networkStatus = {
      online: navigator.onLine,
      connectionType: this.getConnectionType(),
      lastChecked: formatTimestamp()
    };

    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      networkErrors: 0,
      serverErrors: 0,
      retryCount: 0,
      queuedRequests: 0
    };

    this.setupNetworkListeners();
    this.setupPerformanceTracking();
  }

  // ========================================================================
  // PUBLIC API METHODS
  // ========================================================================

  public async get<T>(url: string, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({ method: 'GET', url, ...config });
  }

  public async post<T>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({ method: 'POST', url, data, ...config });
  }

  public async put<T>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({ method: 'PUT', url, data, ...config });
  }

  public async delete<T>(url: string, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({ method: 'DELETE', url, ...config });
  }

  public async patch<T>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({ method: 'PATCH', url, data, ...config });
  }

  // ========================================================================
  // CORE REQUEST METHOD
  // ========================================================================

  private async request<T>(config: RequestConfig): Promise<T> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.metrics.totalRequests++;

      // Check network status
      if (!this.networkStatus.online && !config.skipQueue && this.config.enableOfflineQueue) {
        return this.queueRequest<T>(config);
      }

      // Prepare request
      const requestConfig = await this.prepareRequest(config);

      // Execute request with retry logic
      const response = await this.executeWithRetry<T>(requestConfig, requestId);

      // Track success metrics
      this.metrics.successfulRequests++;
      this.updateResponseTimeMetrics(Date.now() - startTime);

      return response;

    } catch (error) {
      this.metrics.failedRequests++;
      this.handleRequestError(error as Error, config, requestId);
      throw error;
    }
  }

  // ========================================================================
  // REQUEST PREPARATION AND EXECUTION
  // ========================================================================

  private async prepareRequest(config: RequestConfig): Promise<RequestConfig> {
    const headers = {
      ...this.config.headers,
      ...config.headers
    };

    // Add authentication token
    if (this.authToken) {
      headers.Authorization = `Bearer ${this.authToken}`;
    }

    // Add request ID for tracking
    headers['X-Request-ID'] = this.generateRequestId();

    return {
      ...config,
      headers,
      timeout: config.timeout || this.config.timeout
    };
  }

  private async executeWithRetry<T>(config: RequestConfig, requestId: string): Promise<T> {
    const maxAttempts = config.retryAttempts ?? this.config.retryAttempts;
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await this.executeRequest<T>(config);

        if (attempt > 1) {
          this.metrics.retryCount++;
        }

        return response;

      } catch (error) {
        lastError = error as Error;

        // Don't retry on certain errors
        if (this.shouldNotRetry(error as Error) || config.skipRetry) {
          throw error;
        }

        // Don't retry on last attempt
        if (attempt === maxAttempts) {
          throw error;
        }

        // Wait before retry with exponential backoff
        const delay = this.config.retryDelay * Math.pow(2, attempt - 1);
        await this.delay(delay);

        console.warn(`Request retry ${attempt}/${maxAttempts} for ${config.url}`, {
          requestId,
          error: error.message,
          delay
        });
      }
    }

    throw lastError!;
  }

  private async executeRequest<T>(config: RequestConfig): Promise<T> {
    const url = `${this.config.baseURL}${config.url}`;
    const controller = new AbortController();
    const signal = config.signal || controller.signal;

    // Set timeout
    const timeoutId = setTimeout(() => controller.abort(), config.timeout || this.config.timeout);

    try {
      const fetchConfig: RequestInit = {
        method: config.method,
        headers: config.headers,
        signal,
        credentials: 'include'
      };

      // Add body for non-GET requests
      if (config.data && config.method !== 'GET') {
        fetchConfig.body = JSON.stringify(config.data);
      }

      // Add query parameters for GET requests
      const finalUrl = config.method === 'GET' && config.params
        ? `${url}?${new URLSearchParams(config.params).toString()}`
        : url;

      const response = await fetch(finalUrl, fetchConfig);

      clearTimeout(timeoutId);

      // Handle authentication errors
      if (response.status === 401) {
        await this.handleAuthError();
        throw new Error('Authentication required');
      }

      // Parse response
      const data = await this.parseResponse<T>(response);

      return data;

    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }

      throw error;
    }
  }

  // ========================================================================
  // RESPONSE PARSING AND ERROR HANDLING
  // ========================================================================

  private async parseResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');

    if (!response.ok) {
      let errorData: any;

      try {
        if (contentType?.includes('application/json')) {
          errorData = await response.json();
        } else {
          errorData = { message: await response.text() };
        }
      } catch {
        errorData = { message: `HTTP ${response.status}: ${response.statusText}` };
      }

      const error = new Error(errorData.message || `HTTP ${response.status}`);
      (error as any).status = response.status;
      (error as any).data = errorData;

      throw error;
    }

    if (contentType?.includes('application/json')) {
      const jsonData = await response.json();

      // Handle standardized API response format
      if (jsonData.status === 'success') {
        return jsonData.data;
      } else if (jsonData.status === 'error') {
        throw new Error(jsonData.error?.message || 'API Error');
      }

      return jsonData;
    }

    return response.text() as unknown as T;
  }

  private handleRequestError(error: Error, config: RequestConfig, requestId: string): void {
    const errorMessage = error.message.toLowerCase();

    if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
      this.metrics.networkErrors++;
      this.handleNetworkError(error, config);
    } else if (errorMessage.includes('timeout')) {
      this.handleTimeoutError(error, config);
    } else if (error.message.includes('5')) {
      this.metrics.serverErrors++;
      this.handleServerError(error, config);
    } else {
      this.handleGenericError(error, config);
    }

    console.error('API Request Error', {
      requestId,
      url: config.url,
      method: config.method,
      error: error.message,
      timestamp: formatTimestamp()
    });
  }

  // ========================================================================
  // SPECIFIC ERROR HANDLERS
  // ========================================================================

  private handleNetworkError(error: Error, config: RequestConfig): void {
    // Update network status
    this.updateNetworkStatus(false);

    // Queue request if offline queueing is enabled
    if (this.config.enableOfflineQueue && !config.skipQueue) {
      console.warn('Network error detected, request will be queued for retry when online');
    }
  }

  private handleTimeoutError(error: Error, config: RequestConfig): void {
    console.warn(`Request timeout for ${config.url}`, {
      timeout: config.timeout || this.config.timeout,
      url: config.url
    });
  }

  private handleServerError(error: Error, config: RequestConfig): void {
    console.error(`Server error for ${config.url}`, {
      error: error.message,
      url: config.url
    });
  }

  private handleGenericError(error: Error, config: RequestConfig): void {
    console.error(`API error for ${config.url}`, {
      error: error.message,
      url: config.url
    });
  }

  private async handleAuthError(): Promise<void> {
    if (this.refreshTokenPromise) {
      await this.refreshTokenPromise;
      return;
    }

    this.refreshTokenPromise = this.refreshAuthToken();

    try {
      this.authToken = await this.refreshTokenPromise;
    } catch (error) {
      // Redirect to login or handle auth failure
      window.location.href = '/login';
      throw new Error('Authentication failed');
    } finally {
      this.refreshTokenPromise = null;
    }
  }

  // ========================================================================
  // OFFLINE QUEUE MANAGEMENT
  // ========================================================================

  private async queueRequest<T>(config: RequestConfig): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const queuedRequest: QueuedRequest = {
        id: this.generateRequestId(),
        config,
        timestamp: formatTimestamp(),
        attempts: 0,
        resolve,
        reject
      };

      this.requestQueue.push(queuedRequest);
      this.metrics.queuedRequests++;

      console.info('Request queued for offline processing', {
        requestId: queuedRequest.id,
        url: config.url,
        queueSize: this.requestQueue.length
      });
    });
  }

  private async processQueue(): Promise<void> {
    if (!this.networkStatus.online || this.requestQueue.length === 0) {
      return;
    }

    console.info(`Processing ${this.requestQueue.length} queued requests`);

    const queue = [...this.requestQueue];
    this.requestQueue = [];

    for (const queuedRequest of queue) {
      try {
        const response = await this.request(queuedRequest.config);
        queuedRequest.resolve(response);
        this.metrics.queuedRequests--;
      } catch (error) {
        queuedRequest.attempts++;

        if (queuedRequest.attempts < 3) {
          // Re-queue for retry
          this.requestQueue.push(queuedRequest);
        } else {
          queuedRequest.reject(error);
          this.metrics.queuedRequests--;
        }
      }
    }
  }

  // ========================================================================
  // NETWORK STATUS MANAGEMENT
  // ========================================================================

  private setupNetworkListeners(): void {
    if (!this.config.enableNetworkDetection) return;

    window.addEventListener('online', () => {
      this.updateNetworkStatus(true);
      this.processQueue();
    });

    window.addEventListener('offline', () => {
      this.updateNetworkStatus(false);
    });

    // Check network status periodically
    setInterval(() => {
      this.checkNetworkStatus();
    }, 30000);
  }

  private updateNetworkStatus(online: boolean): void {
    this.networkStatus = {
      ...this.networkStatus,
      online,
      lastChecked: formatTimestamp()
    };

    if (online) {
      console.info('Network connection restored');
    } else {
      console.warn('Network connection lost');
    }
  }

  private async checkNetworkStatus(): Promise<void> {
    try {
      const response = await fetch('/api/health', {
        method: 'HEAD',
        cache: 'no-cache'
      });
      this.updateNetworkStatus(response.ok);
    } catch {
      this.updateNetworkStatus(false);
    }
  }

  // ========================================================================
  // UTILITY METHODS
  // ========================================================================

  private shouldNotRetry(error: Error): boolean {
    const message = error.message.toLowerCase();
    const status = (error as any).status;

    // Don't retry client errors (4xx) except 408, 429
    if (status >= 400 && status < 500 && status !== 408 && status !== 429) {
      return true;
    }

    // Don't retry certain error types
    if (message.includes('abort') || message.includes('cancel')) {
      return true;
    }

    return false;
  }

  private async refreshAuthToken(): Promise<string> {
    // Implement token refresh logic
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error('Token refresh failed');
    }

    const data = await response.json();
    return data.token;
  }

  private getConnectionType(): string {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      return connection.type || 'unknown';
    }
    return 'unknown';
  }

  private generateRequestId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private updateResponseTimeMetrics(responseTime: number): void {
    const totalTime = this.metrics.averageResponseTime * (this.metrics.successfulRequests - 1);
    this.metrics.averageResponseTime = (totalTime + responseTime) / this.metrics.successfulRequests;
  }

  private setupPerformanceTracking(): void {
    if (!this.config.enablePerformanceTracking) return;

    // Log metrics periodically
    setInterval(() => {
      console.info('API Service Metrics', this.metrics);
    }, 60000); // Every minute
  }

  // ========================================================================
  // PUBLIC UTILITY METHODS
  // ========================================================================

  public setAuthToken(token: string): void {
    this.authToken = token;
  }

  public clearAuthToken(): void {
    this.authToken = null;
  }

  public getNetworkStatus(): NetworkStatus {
    return { ...this.networkStatus };
  }

  public getMetrics(): ApiMetrics {
    return { ...this.metrics };
  }

  public clearQueue(): void {
    this.requestQueue.forEach(request => {
      request.reject(new Error('Request queue cleared'));
    });
    this.requestQueue = [];
    this.metrics.queuedRequests = 0;
  }

  public updateConfig(newConfig: Partial<ApiConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

const apiService = new EnhancedApiService();

export default apiService;

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

export const api = {
  get: <T>(url: string, config?: Partial<RequestConfig>) => apiService.get<T>(url, config),
  post: <T>(url: string, data?: any, config?: Partial<RequestConfig>) => apiService.post<T>(url, data, config),
  put: <T>(url: string, data?: any, config?: Partial<RequestConfig>) => apiService.put<T>(url, data, config),
  delete: <T>(url: string, config?: Partial<RequestConfig>) => apiService.delete<T>(url, config),
  patch: <T>(url: string, data?: any, config?: Partial<RequestConfig>) => apiService.patch<T>(url, data, config)
};

export { apiService };

// ============================================================================
// REACT HOOKS FOR API INTEGRATION
// ============================================================================

/**
 * Hook for making API calls with integrated error handling
 */
export function useApiCall<T>() {
  const { handleApiError } = useApiError();
  const { handleNetworkError } = useNetworkError();

  const makeRequest = React.useCallback(async (
    requestFn: () => Promise<T>,
    options: {
      onSuccess?: (data: T) => void;
      onError?: (error: Error) => void;
      context?: string;
    } = {}
  ): Promise<T | null> => {
    try {
      const data = await requestFn();
      options.onSuccess?.(data);
      return data;
    } catch (error) {
      const apiError = error as Error;

      // Categorize and handle error
      if (apiError.message.includes('network') || apiError.message.includes('fetch')) {
        handleNetworkError(apiError);
      } else {
        handleApiError(apiError, { action: options.context });
      }

      options.onError?.(apiError);
      return null;
    }
  }, [handleApiError, handleNetworkError]);

  return { makeRequest };
}
