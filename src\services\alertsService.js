/**
 * Alerts Service
 * 
 * Manages trading alerts including pattern alerts, price alerts, and economic calendar alerts
 */

import logger from '../utils/logger.js';
import enhancedCache from '../utils/cache.js';

// Cache for storing alerts
const alertsCache = enhancedCache.createCache({
  defaultTTL: 0, // No expiration for alerts
  useLocalStorage: true,
  storageKey: 'trading_signals_alerts',
  maxMemoryItems: 1000
});

// Alert types
export const ALERT_TYPES = {
  PRICE: 'price',
  PATTERN: 'pattern',
  INDICATOR: 'indicator',
  ECONOMIC: 'economic',
  NEWS: 'news'
};

// Alert statuses
export const ALERT_STATUSES = {
  ACTIVE: 'active',
  TRIGGERED: 'triggered',
  EXPIRED: 'expired',
  DELETED: 'deleted'
};

// Alert priorities
export const ALERT_PRIORITIES = {
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low'
};

/**
 * Create a new alert
 * 
 * @param {Object} alertData - Alert data
 * @returns {Object} - Created alert
 */
export const createAlert = (alertData) => {
  try {
    // Validate alert data
    if (!alertData.type || !Object.values(ALERT_TYPES).includes(alertData.type)) {
      throw new Error('Invalid alert type');
    }
    
    if (!alertData.symbol) {
      throw new Error('Symbol is required');
    }
    
    // Generate alert ID
    const alertId = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Create alert object
    const alert = {
      id: alertId,
      type: alertData.type,
      symbol: alertData.symbol,
      timeframe: alertData.timeframe || 'D1',
      conditions: alertData.conditions || {},
      message: alertData.message || `Alert for ${alertData.symbol}`,
      status: ALERT_STATUSES.ACTIVE,
      priority: alertData.priority || ALERT_PRIORITIES.MEDIUM,
      createdAt: Date.now(),
      expiresAt: alertData.expiresAt || 0, // 0 means no expiration
      lastChecked: null,
      triggerCount: 0,
      lastTriggered: null,
      notificationMethods: alertData.notificationMethods || ['app'],
      userId: alertData.userId || 'anonymous'
    };
    
    // Save alert to cache
    alertsCache.set(alertId, alert);
    
    logger.info('Alert created:', { alertId, type: alert.type, symbol: alert.symbol });
    
    return alert;
  } catch (error) {
    logger.error('Error creating alert:', error);
    throw error;
  }
};

/**
 * Get all alerts
 * 
 * @param {Object} filters - Optional filters
 * @returns {Array} - Array of alerts
 */
export const getAlerts = (filters = {}) => {
  try {
    // Get all alerts from cache
    const allAlerts = alertsCache.getAll();
    
    // Convert to array
    let alerts = Object.values(allAlerts);
    
    // Apply filters
    if (filters.type) {
      alerts = alerts.filter(alert => alert.type === filters.type);
    }
    
    if (filters.symbol) {
      alerts = alerts.filter(alert => alert.symbol === filters.symbol);
    }
    
    if (filters.status) {
      alerts = alerts.filter(alert => alert.status === filters.status);
    }
    
    if (filters.userId) {
      alerts = alerts.filter(alert => alert.userId === filters.userId);
    }
    
    // Sort by creation date (newest first)
    alerts.sort((a, b) => b.createdAt - a.createdAt);
    
    return alerts;
  } catch (error) {
    logger.error('Error getting alerts:', error);
    return [];
  }
};

/**
 * Get alert by ID
 * 
 * @param {string} alertId - Alert ID
 * @returns {Object|null} - Alert object or null if not found
 */
export const getAlertById = (alertId) => {
  try {
    return alertsCache.get(alertId) || null;
  } catch (error) {
    logger.error('Error getting alert by ID:', error);
    return null;
  }
};

/**
 * Update alert
 * 
 * @param {string} alertId - Alert ID
 * @param {Object} updateData - Data to update
 * @returns {Object|null} - Updated alert or null if not found
 */
export const updateAlert = (alertId, updateData) => {
  try {
    // Get existing alert
    const alert = alertsCache.get(alertId);
    
    if (!alert) {
      logger.warn('Alert not found for update:', alertId);
      return null;
    }
    
    // Create updated alert
    const updatedAlert = {
      ...alert,
      ...updateData,
      updatedAt: Date.now()
    };
    
    // Save updated alert
    alertsCache.set(alertId, updatedAlert);
    
    logger.info('Alert updated:', { alertId });
    
    return updatedAlert;
  } catch (error) {
    logger.error('Error updating alert:', error);
    return null;
  }
};

/**
 * Delete alert
 * 
 * @param {string} alertId - Alert ID
 * @returns {boolean} - Success status
 */
export const deleteAlert = (alertId) => {
  try {
    // Check if alert exists
    const alert = alertsCache.get(alertId);
    
    if (!alert) {
      logger.warn('Alert not found for deletion:', alertId);
      return false;
    }
    
    // Delete alert
    alertsCache.delete(alertId);
    
    logger.info('Alert deleted:', { alertId });
    
    return true;
  } catch (error) {
    logger.error('Error deleting alert:', error);
    return false;
  }
};

/**
 * Check if alert conditions are met
 * 
 * @param {Object} alert - Alert object
 * @param {Object} data - Data to check against alert conditions
 * @returns {boolean} - Whether conditions are met
 */
export const checkAlertConditions = (alert, data) => {
  try {
    // Update last checked timestamp
    updateAlert(alert.id, { lastChecked: Date.now() });
    
    // Check if alert is active
    if (alert.status !== ALERT_STATUSES.ACTIVE) {
      return false;
    }
    
    // Check if alert has expired
    if (alert.expiresAt > 0 && alert.expiresAt < Date.now()) {
      updateAlert(alert.id, { status: ALERT_STATUSES.EXPIRED });
      return false;
    }
    
    // Check conditions based on alert type
    switch (alert.type) {
      case ALERT_TYPES.PRICE:
        return checkPriceAlertConditions(alert, data);
      
      case ALERT_TYPES.PATTERN:
        return checkPatternAlertConditions(alert, data);
      
      case ALERT_TYPES.INDICATOR:
        return checkIndicatorAlertConditions(alert, data);
      
      case ALERT_TYPES.ECONOMIC:
        return checkEconomicAlertConditions(alert, data);
      
      case ALERT_TYPES.NEWS:
        return checkNewsAlertConditions(alert, data);
      
      default:
        return false;
    }
  } catch (error) {
    logger.error('Error checking alert conditions:', error);
    return false;
  }
};

/**
 * Check price alert conditions
 * 
 * @param {Object} alert - Alert object
 * @param {Object} data - Price data
 * @returns {boolean} - Whether conditions are met
 */
const checkPriceAlertConditions = (alert, data) => {
  const { conditions } = alert;
  const { price } = data;
  
  if (!conditions || !price) return false;
  
  // Price above condition
  if (conditions.above && price > conditions.above) {
    return true;
  }
  
  // Price below condition
  if (conditions.below && price < conditions.below) {
    return true;
  }
  
  // Price change condition
  if (conditions.changePercent && data.previousPrice) {
    const changePercent = ((price - data.previousPrice) / data.previousPrice) * 100;
    
    if (conditions.changeDirection === 'up' && changePercent >= conditions.changePercent) {
      return true;
    }
    
    if (conditions.changeDirection === 'down' && changePercent <= -conditions.changePercent) {
      return true;
    }
  }
  
  return false;
};

/**
 * Check pattern alert conditions
 * 
 * @param {Object} alert - Alert object
 * @param {Object} data - Pattern data
 * @returns {boolean} - Whether conditions are met
 */
const checkPatternAlertConditions = (alert, data) => {
  const { conditions } = alert;
  const { patterns } = data;
  
  if (!conditions || !patterns || !patterns.length) return false;
  
  // Check for specific pattern
  if (conditions.pattern) {
    const matchingPattern = patterns.find(p => p.pattern === conditions.pattern);
    
    if (matchingPattern) {
      // Check for minimum significance if specified
      if (conditions.minSignificance && matchingPattern.significance < conditions.minSignificance) {
        return false;
      }
      
      return true;
    }
  }
  
  // Check for pattern type
  if (conditions.patternType) {
    const matchingPattern = patterns.find(p => p.type === conditions.patternType);
    
    if (matchingPattern) {
      // Check for minimum significance if specified
      if (conditions.minSignificance && matchingPattern.significance < conditions.minSignificance) {
        return false;
      }
      
      return true;
    }
  }
  
  return false;
};

/**
 * Check indicator alert conditions
 * 
 * @param {Object} alert - Alert object
 * @param {Object} data - Indicator data
 * @returns {boolean} - Whether conditions are met
 */
const checkIndicatorAlertConditions = (alert, data) => {
  const { conditions } = alert;
  const { indicators } = data;
  
  if (!conditions || !indicators) return false;
  
  // RSI conditions
  if (conditions.indicator === 'rsi') {
    const rsi = indicators.rsi;
    
    if (!rsi || !rsi.value) return false;
    
    if (conditions.above && rsi.value > conditions.above) {
      return true;
    }
    
    if (conditions.below && rsi.value < conditions.below) {
      return true;
    }
  }
  
  // MACD conditions
  if (conditions.indicator === 'macd') {
    const macd = indicators.macd;
    
    if (!macd) return false;
    
    // MACD crossover
    if (conditions.crossover === 'bullish' && 
        macd.previousValue < macd.previousSignal && 
        macd.value > macd.signal) {
      return true;
    }
    
    if (conditions.crossover === 'bearish' && 
        macd.previousValue > macd.previousSignal && 
        macd.value < macd.signal) {
      return true;
    }
  }
  
  // Moving average conditions
  if (conditions.indicator === 'ma') {
    const { fastMA, slowMA } = indicators;
    
    if (!fastMA || !slowMA) return false;
    
    // MA crossover
    if (conditions.crossover === 'bullish' && 
        fastMA.previousValue < slowMA.previousValue && 
        fastMA.value > slowMA.value) {
      return true;
    }
    
    if (conditions.crossover === 'bearish' && 
        fastMA.previousValue > slowMA.previousValue && 
        fastMA.value < slowMA.value) {
      return true;
    }
  }
  
  return false;
};

/**
 * Check economic alert conditions
 * 
 * @param {Object} alert - Alert object
 * @param {Object} data - Economic data
 * @returns {boolean} - Whether conditions are met
 */
const checkEconomicAlertConditions = (alert, data) => {
  const { conditions } = alert;
  const { events } = data;
  
  if (!conditions || !events || !events.length) return false;
  
  // Check for specific event
  if (conditions.eventType) {
    const matchingEvent = events.find(e => e.type === conditions.eventType);
    
    if (matchingEvent) {
      // Check for importance if specified
      if (conditions.importance && matchingEvent.importance !== conditions.importance) {
        return false;
      }
      
      return true;
    }
  }
  
  // Check for country
  if (conditions.country) {
    const matchingEvent = events.find(e => e.country === conditions.country);
    
    if (matchingEvent) {
      // Check for importance if specified
      if (conditions.importance && matchingEvent.importance !== conditions.importance) {
        return false;
      }
      
      return true;
    }
  }
  
  return false;
};

/**
 * Check news alert conditions
 * 
 * @param {Object} alert - Alert object
 * @param {Object} data - News data
 * @returns {boolean} - Whether conditions are met
 */
const checkNewsAlertConditions = (alert, data) => {
  const { conditions } = alert;
  const { articles } = data;
  
  if (!conditions || !articles || !articles.length) return false;
  
  // Check for keywords in news
  if (conditions.keywords && conditions.keywords.length) {
    for (const article of articles) {
      for (const keyword of conditions.keywords) {
        if (article.title.toLowerCase().includes(keyword.toLowerCase()) || 
            article.summary.toLowerCase().includes(keyword.toLowerCase())) {
          return true;
        }
      }
    }
  }
  
  return false;
};

/**
 * Trigger alert
 * 
 * @param {string} alertId - Alert ID
 * @param {Object} triggerData - Data that triggered the alert
 * @returns {Object|null} - Triggered alert or null if not found
 */
export const triggerAlert = (alertId, triggerData = {}) => {
  try {
    // Get alert
    const alert = alertsCache.get(alertId);
    
    if (!alert) {
      logger.warn('Alert not found for triggering:', alertId);
      return null;
    }
    
    // Update alert
    const triggeredAlert = updateAlert(alertId, {
      status: alert.triggerCount > 0 ? ALERT_STATUSES.ACTIVE : ALERT_STATUSES.TRIGGERED,
      triggerCount: alert.triggerCount + 1,
      lastTriggered: Date.now(),
      triggerData
    });
    
    logger.info('Alert triggered:', { alertId, triggerCount: triggeredAlert.triggerCount });
    
    return triggeredAlert;
  } catch (error) {
    logger.error('Error triggering alert:', error);
    return null;
  }
};

export default {
  ALERT_TYPES,
  ALERT_STATUSES,
  ALERT_PRIORITIES,
  createAlert,
  getAlerts,
  getAlertById,
  updateAlert,
  deleteAlert,
  checkAlertConditions,
  triggerAlert
};
