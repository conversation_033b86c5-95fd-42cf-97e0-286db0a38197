import React, { useState } from 'react';
import Modal from './ui/Modal.jsx';
import Button from './ui/Button.jsx';

// Replace with your Google Form endpoint or feedback API
const FEEDBACK_URL = 'https://docs.google.com/forms/d/e/1FAIpQLSfD_fake_form_id/formResponse';

export default function FeedbackWidget() {
  const [open, setOpen] = useState(false);
  const [feedback, setFeedback] = useState('');
  const [sent, setSent] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    // Google Forms expects entries as 'entry.<field_id>'
    const formData = new FormData();
    formData.append('entry.1234567890', feedback); // Replace with your field ID
    await fetch(FEEDBACK_URL, {
      method: 'POST',
      mode: 'no-cors',
      body: formData,
    });
    setLoading(false);
    setSent(true);
    setFeedback('');
    setTimeout(() => setOpen(false), 2000);
  };

  return (
    <>
      <button
        className="fixed bottom-6 right-6 z-50 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg p-4 focus:outline-none"
        onClick={() => setOpen(true)}
        aria-label="Send Feedback"
      >
        <span role="img" aria-label="Feedback" className="text-xl">💬</span>
      </button>
      <Modal open={open} onClose={() => setOpen(false)}>
        <form className="w-full max-w-sm p-4" onSubmit={handleSubmit}>
          <h2 className="text-lg font-bold mb-2">Send Feedback</h2>
          {sent ? (
            <div className="text-green-600 text-center py-8">Thank you for your feedback!</div>
          ) : (
            <>
              <textarea
                className="w-full border rounded p-2 mb-3 min-h-[100px]"
                placeholder="Your feedback..."
                value={feedback}
                onChange={e => setFeedback(e.target.value)}
                required
              />
              <div className="flex justify-end gap-2">
                <Button type="button" variant="secondary" onClick={() => setOpen(false)}>Cancel</Button>
                <Button type="submit" loading={loading} disabled={!feedback.trim()}>
                  Send
                </Button>
              </div>
            </>
          )}
        </form>
      </Modal>
    </>
  );
} 