/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
    './public/**/*.html',
    './index.html'
  ],
  darkMode: ['class', '[data-theme="dark"]'],
  theme: {
    extend: {
      colors: {
        // Navy blue/teal/gold color scheme
        'navy': {
          50: '#E7E9EF',
          100: '#C2C9D6',
          200: '#9AA5BB',
          300: '#7281A0',
          400: '#54648A',
          500: '#364775',
          600: '#2E3D63',
          700: '#25324F',
          800: '#1C273B',
          900: '#131C28',
        },
        'teal': {
          50: '#E6F7F7',
          100: '#C0EBEA',
          200: '#97DEDC',
          300: '#6DD1CE',
          400: '#4DC6C3',
          500: '#2DBBB7',
          600: '#27A5A2',
          700: '#208A87',
          800: '#19706D',
          900: '#115654',
        },
        'gold': {
          50: '#FFF8E6',
          100: '#FEEFC0',
          200: '#FDE496',
          300: '#FCD96C',
          400: '#FBCF4D',
          500: '#FAC42D',
          600: '#F9B828',
          700: '#F8AB22',
          800: '#F79F1C',
          900: '#F58711',
        },
      },
      boxShadow: {
        'trading': '0 4px 12px rgba(0, 0, 0, 0.15)',
        'trading-hover': '0 8px 24px rgba(0, 0, 0, 0.2)',
      },
      animation: {
        'float': 'float 3s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' },
        }
      }
    },
  },
  plugins: [],
}
