#!/usr/bin/env node

/**
 * Create Optimized Database Indexes Script
 * 
 * This script creates compound indexes for high-frequency queries
 * to optimize database performance for the Trading Signals App.
 * 
 * Indexes created:
 * - { symbol: 1, timestamp: -1 } for market data time-series queries
 * - { userId: 1, createdAt: -1 } for user activity tracking
 * - { symbol: 1, analysisType: 1, createdAt: -1 } for AI analysis history
 * - Additional performance-optimized indexes
 * 
 * @version 1.0.0
 */

import mongoose from 'mongoose';
import logger from '../src/utils/logger.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Database index configurations
 */
const indexConfigurations = [
  // Market Data Collection Indexes
  {
    collection: 'marketdata',
    indexes: [
      {
        fields: { symbol: 1, timestamp: -1 },
        options: { 
          name: 'symbol_timestamp_desc',
          background: true,
          comment: 'Optimized for time-series market data queries'
        }
      },
      {
        fields: { symbol: 1, timeframe: 1, timestamp: -1 },
        options: { 
          name: 'symbol_timeframe_timestamp_desc',
          background: true,
          comment: 'Optimized for symbol-timeframe specific queries'
        }
      },
      {
        fields: { timestamp: -1 },
        options: { 
          name: 'timestamp_desc',
          background: true,
          comment: 'Optimized for recent data queries'
        }
      },
      {
        fields: { symbol: 1, volume: -1 },
        options: { 
          name: 'symbol_volume_desc',
          background: true,
          comment: 'Optimized for high-volume trading queries'
        }
      }
    ]
  },

  // Users Collection Indexes
  {
    collection: 'users',
    indexes: [
      {
        fields: { email: 1 },
        options: { 
          unique: true,
          name: 'email_unique',
          background: true,
          comment: 'Unique email constraint for authentication'
        }
      },
      {
        fields: { userId: 1, createdAt: -1 },
        options: { 
          name: 'userId_createdAt_desc',
          background: true,
          comment: 'Optimized for user activity tracking'
        }
      },
      {
        fields: { role: 1, isActive: 1 },
        options: { 
          name: 'role_isActive',
          background: true,
          comment: 'Optimized for role-based access control queries'
        }
      },
      {
        fields: { lastLoginAt: -1 },
        options: { 
          name: 'lastLoginAt_desc',
          background: true,
          comment: 'Optimized for recent activity queries'
        }
      }
    ]
  },

  // Sentiment History Collection Indexes
  {
    collection: 'sentimenthistory',
    indexes: [
      {
        fields: { symbol: 1, createdAt: -1 },
        options: { 
          name: 'symbol_createdAt_desc',
          background: true,
          comment: 'Optimized for symbol sentiment history queries'
        }
      },
      {
        fields: { symbol: 1, overall_sentiment: 1, createdAt: -1 },
        options: { 
          name: 'symbol_sentiment_createdAt_desc',
          background: true,
          comment: 'Optimized for sentiment filtering queries'
        }
      },
      {
        fields: { createdAt: -1 },
        options: { 
          name: 'createdAt_desc',
          background: true,
          comment: 'Optimized for recent sentiment queries'
        }
      }
    ]
  },

  // Trading Signals Collection Indexes
  {
    collection: 'tradingsignals',
    indexes: [
      {
        fields: { symbol: 1, timestamp: -1 },
        options: { 
          name: 'symbol_timestamp_desc',
          background: true,
          comment: 'Optimized for symbol-specific signal queries'
        }
      },
      {
        fields: { signal: 1, confidence: -1, timestamp: -1 },
        options: { 
          name: 'signal_confidence_timestamp_desc',
          background: true,
          comment: 'Optimized for high-confidence signal queries'
        }
      },
      {
        fields: { userId: 1, timestamp: -1 },
        options: { 
          name: 'userId_timestamp_desc',
          background: true,
          comment: 'Optimized for user-specific signal history'
        }
      },
      {
        fields: { symbol: 1, signal: 1, timestamp: -1 },
        options: { 
          name: 'symbol_signal_timestamp_desc',
          background: true,
          comment: 'Optimized for symbol-signal type queries'
        }
      }
    ]
  },

  // AI Response History Collection Indexes
  {
    collection: 'airesponsehistory',
    indexes: [
      {
        fields: { symbol: 1, analysisType: 1, createdAt: -1 },
        options: { 
          name: 'symbol_analysisType_createdAt_desc',
          background: true,
          comment: 'Optimized for AI analysis history queries'
        }
      },
      {
        fields: { model: 1, createdAt: -1 },
        options: { 
          name: 'model_createdAt_desc',
          background: true,
          comment: 'Optimized for model-specific analysis queries'
        }
      },
      {
        fields: { symbol: 1, model: 1, analysisType: 1, createdAt: -1 },
        options: { 
          name: 'symbol_model_analysisType_createdAt_desc',
          background: true,
          comment: 'Optimized for comprehensive AI analysis queries'
        }
      },
      {
        fields: { responseTime: 1, createdAt: -1 },
        options: { 
          name: 'responseTime_createdAt_desc',
          background: true,
          comment: 'Optimized for performance analysis queries'
        }
      }
    ]
  },

  // User Sessions Collection Indexes
  {
    collection: 'usersessions',
    indexes: [
      {
        fields: { sessionId: 1 },
        options: { 
          unique: true,
          name: 'sessionId_unique',
          background: true,
          comment: 'Unique session identifier'
        }
      },
      {
        fields: { userId: 1, createdAt: -1 },
        options: { 
          name: 'userId_createdAt_desc',
          background: true,
          comment: 'Optimized for user session queries'
        }
      },
      {
        fields: { expiresAt: 1 },
        options: { 
          name: 'expiresAt_asc',
          background: true,
          expireAfterSeconds: 0,
          comment: 'TTL index for automatic session cleanup'
        }
      },
      {
        fields: { isActive: 1, lastAccessedAt: -1 },
        options: { 
          name: 'isActive_lastAccessedAt_desc',
          background: true,
          comment: 'Optimized for active session queries'
        }
      }
    ]
  }
];

/**
 * Connect to MongoDB
 */
async function connectToDatabase() {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/trading-signals';
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    logger.info('Connected to MongoDB for index creation');
  } catch (error) {
    logger.error('Error connecting to MongoDB:', error);
    throw error;
  }
}

/**
 * Create indexes for a specific collection
 * 
 * @param {string} collectionName - Name of the collection
 * @param {Array} indexes - Array of index configurations
 */
async function createCollectionIndexes(collectionName, indexes) {
  try {
    const db = mongoose.connection.db;
    const collection = db.collection(collectionName);
    
    logger.info(`Creating indexes for collection: ${collectionName}`);
    
    for (const indexConfig of indexes) {
      const { fields, options } = indexConfig;
      
      try {
        // Check if index already exists
        const existingIndexes = await collection.indexes();
        const indexExists = existingIndexes.some(idx => idx.name === options.name);
        
        if (indexExists) {
          logger.info(`Index ${options.name} already exists on ${collectionName}, skipping`);
          continue;
        }
        
        // Create the index
        const startTime = Date.now();
        await collection.createIndex(fields, options);
        const duration = Date.now() - startTime;
        
        logger.info(`✓ Created index ${options.name} on ${collectionName} (${duration}ms)`);
      } catch (error) {
        logger.error(`✗ Failed to create index ${options.name} on ${collectionName}:`, error.message);
      }
    }
  } catch (error) {
    logger.error(`Error creating indexes for collection ${collectionName}:`, error);
    throw error;
  }
}

/**
 * Analyze existing indexes and provide recommendations
 */
async function analyzeExistingIndexes() {
  try {
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();
    
    logger.info('Analyzing existing indexes...');
    
    for (const collectionInfo of collections) {
      const collectionName = collectionInfo.name;
      const collection = db.collection(collectionName);
      
      try {
        const indexes = await collection.indexes();
        const stats = await collection.stats();
        
        logger.info(`\nCollection: ${collectionName}`);
        logger.info(`  Documents: ${stats.count}`);
        logger.info(`  Size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
        logger.info(`  Indexes: ${indexes.length}`);
        
        indexes.forEach(index => {
          logger.info(`    - ${index.name}: ${JSON.stringify(index.key)}`);
        });
      } catch (error) {
        logger.debug(`Could not analyze collection ${collectionName}:`, error.message);
      }
    }
  } catch (error) {
    logger.error('Error analyzing existing indexes:', error);
  }
}

/**
 * Main function to create all optimized indexes
 */
async function createOptimizedIndexes() {
  try {
    logger.info('Starting optimized index creation process...');
    
    // Connect to database
    await connectToDatabase();
    
    // Analyze existing indexes
    await analyzeExistingIndexes();
    
    // Create indexes for each collection
    let totalIndexesCreated = 0;
    const startTime = Date.now();
    
    for (const config of indexConfigurations) {
      await createCollectionIndexes(config.collection, config.indexes);
      totalIndexesCreated += config.indexes.length;
    }
    
    const totalDuration = Date.now() - startTime;
    
    logger.info(`\n✓ Index creation process completed!`);
    logger.info(`  Total indexes processed: ${totalIndexesCreated}`);
    logger.info(`  Total time: ${totalDuration}ms`);
    
    // Verify index creation
    await verifyIndexes();
    
  } catch (error) {
    logger.error('Error in index creation process:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  }
}

/**
 * Verify that all indexes were created successfully
 */
async function verifyIndexes() {
  try {
    logger.info('\nVerifying index creation...');
    
    const db = mongoose.connection.db;
    let totalVerified = 0;
    
    for (const config of indexConfigurations) {
      const collection = db.collection(config.collection);
      const indexes = await collection.indexes();
      
      for (const indexConfig of config.indexes) {
        const indexExists = indexes.some(idx => idx.name === indexConfig.options.name);
        if (indexExists) {
          totalVerified++;
          logger.info(`✓ Verified index: ${indexConfig.options.name} on ${config.collection}`);
        } else {
          logger.error(`✗ Missing index: ${indexConfig.options.name} on ${config.collection}`);
        }
      }
    }
    
    logger.info(`\nVerification complete: ${totalVerified} indexes verified`);
  } catch (error) {
    logger.error('Error verifying indexes:', error);
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  createOptimizedIndexes()
    .then(() => {
      logger.info('Index creation script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Index creation script failed:', error);
      process.exit(1);
    });
}

export { createOptimizedIndexes, analyzeExistingIndexes };
export default createOptimizedIndexes;
