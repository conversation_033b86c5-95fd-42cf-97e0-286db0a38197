/**
 * Enhanced WebSocket Service for Trading Signals App
 *
 * Provides real-time market data streaming with sub-second latency,
 * message compression, batching, and advanced connection management.
 *
 * Features:
 * - Sub-second latency optimization
 * - Message compression and batching
 * - Connection health monitoring
 * - Automatic reconnection and message replay
 * - Real-time performance dashboards
 */

const WebSocket = require('ws');
const zlib = require('zlib');
const { EventEmitter } = require('events');
const { structuredLogger } = require('../utils/structuredLogger');

class EnhancedWebSocketService extends EventEmitter {
  constructor(server, options = {}) {
    super();

    this.options = {
      path: '/ws',
      pingInterval: 5000,        // 5 seconds for high-frequency trading
      pongTimeout: 3000,         // 3 seconds timeout
      compressionThreshold: 512, // Compress messages > 512 bytes
      batchInterval: 50,         // 50ms batching for high-frequency updates
      maxBatchSize: 100,         // Maximum messages per batch
      maxConnections: 10000,     // Connection limit
      rateLimitWindow: 1000,     // 1 second rate limit window
      rateLimitMax: 100,         // Max messages per window
      ...options
    };

    // Initialize WebSocket server with optimizations
    this.wss = new WebSocket.Server({
      server,
      path: this.options.path,
      clientTracking: true,
      perMessageDeflate: {
        threshold: this.options.compressionThreshold,
        concurrencyLimit: 10,
        memLevel: 8,
        serverMaxWindowBits: 15,
        clientMaxWindowBits: 15,
        serverMaxNoContextTakeover: false,
        clientMaxNoContextTakeover: false,
        zlibDeflateOptions: {
          level: zlib.constants.Z_BEST_SPEED,
          chunkSize: 1024,
          windowBits: 15,
          memLevel: 8,
          strategy: zlib.constants.Z_DEFAULT_STRATEGY,
        }
      }
    });

    // Client and subscription management
    this.clients = new Map();
    this.subscriptions = new Map();
    this.messageBatches = new Map();
    this.rateLimiters = new Map();

    // Performance metrics
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      messagesSent: 0,
      messagesReceived: 0,
      bytesTransferred: 0,
      compressionRatio: 0,
      averageLatency: 0,
      errors: 0,
      batchesSent: 0,
      reconnections: 0,
      lastUpdate: Date.now()
    };

    // Connection health monitoring
    this.healthChecks = new Map();
    this.connectionQuality = new Map();

    this.setupEventHandlers();
    this.startPerformanceMonitoring();
    this.startMessageBatching();
    this.startHealthMonitoring();

    structuredLogger.info('Enhanced WebSocket service initialized', {
      component: 'enhanced-websocket',
      options: this.options
    });
  }

  /**
   * Setup WebSocket event handlers
   */
  setupEventHandlers() {
    this.wss.on('connection', (ws, req) => this.handleConnection(ws, req));

    this.wss.on('error', (error) => {
      structuredLogger.error('WebSocket server error', {
        component: 'enhanced-websocket',
        error: error.message,
        stack: error.stack
      });
      this.metrics.errors++;
    });

    // Handle server-level events
    this.wss.on('listening', () => {
      structuredLogger.info('Enhanced WebSocket server listening', {
        component: 'enhanced-websocket',
        path: this.options.path
      });
    });
  }

  /**
   * Handle new WebSocket connection with enhanced features
   */
  async handleConnection(ws, req) {
    const correlationId = structuredLogger.getCorrelationId();

    try {
      // Check connection limits
      if (this.clients.size >= this.options.maxConnections) {
        ws.close(1013, 'Server at capacity');
        return;
      }

      // Parse connection details
      const clientId = this.generateClientId();
      const clientInfo = this.extractClientInfo(req);

      // Initialize client state
      const client = {
        id: clientId,
        ws,
        ...clientInfo,
        connectedAt: Date.now(),
        subscriptions: new Set(),
        messageQueue: [],
        lastPing: Date.now(),
        latency: 0,
        isAlive: true,
        reconnectCount: 0,
        rateLimiter: {
          messages: 0,
          windowStart: Date.now()
        }
      };

      this.clients.set(ws, client);
      this.connectionQuality.set(clientId, {
        latency: [],
        messageRate: [],
        errors: 0,
        quality: 'good'
      });

      // Update metrics
      this.metrics.totalConnections++;
      this.metrics.activeConnections++;

      // Setup client event handlers
      this.setupClientHandlers(ws, client);

      // Send enhanced welcome message
      await this.sendToClient(ws, {
        type: 'connection',
        status: 'connected',
        clientId,
        serverTime: Date.now(),
        features: {
          compression: true,
          batching: true,
          healthMonitoring: true,
          messageReplay: true
        }
      });

      structuredLogger.info('Enhanced WebSocket client connected', {
        component: 'enhanced-websocket',
        correlationId,
        clientId,
        ip: clientInfo.ip,
        userAgent: clientInfo.userAgent
      });

    } catch (error) {
      structuredLogger.error('Error handling WebSocket connection', {
        component: 'enhanced-websocket',
        correlationId,
        error: error.message,
        stack: error.stack
      });

      ws.close(1011, 'Internal server error');
    }
  }

  /**
   * Setup client-specific event handlers
   */
  setupClientHandlers(ws, client) {
    ws.on('message', (data) => this.handleMessage(ws, client, data));
    ws.on('close', (code, reason) => this.handleClose(ws, client, code, reason));
    ws.on('error', (error) => this.handleError(ws, client, error));
    ws.on('pong', (data) => this.handlePong(ws, client, data));
  }

  /**
   * Handle incoming messages with rate limiting
   */
  async handleMessage(ws, client, data) {
    const correlationId = structuredLogger.getCorrelationId();

    try {
      // Rate limiting check
      if (!this.checkRateLimit(client)) {
        await this.sendToClient(ws, {
          type: 'error',
          error: 'Rate limit exceeded',
          code: 'RATE_LIMIT'
        });
        return;
      }

      // Parse message
      const message = JSON.parse(data);
      this.metrics.messagesReceived++;

      // Handle different message types
      switch (message.type) {
        case 'subscribe':
          await this.handleSubscribe(ws, client, message);
          break;
        case 'unsubscribe':
          await this.handleUnsubscribe(ws, client, message);
          break;
        case 'ping':
          await this.handlePing(ws, client, message);
          break;
        case 'health_check':
          await this.handleHealthCheck(ws, client, message);
          break;
        default:
          structuredLogger.warn('Unknown message type', {
            component: 'enhanced-websocket',
            correlationId,
            clientId: client.id,
            messageType: message.type
          });
      }

    } catch (error) {
      structuredLogger.error('Error handling WebSocket message', {
        component: 'enhanced-websocket',
        correlationId,
        clientId: client.id,
        error: error.message
      });

      this.metrics.errors++;
      this.updateConnectionQuality(client.id, 'error');
    }
  }

  /**
   * Enhanced subscription handling with validation
   */
  async handleSubscribe(ws, client, message) {
    const { channel, params = {} } = message;

    if (!channel) {
      await this.sendToClient(ws, {
        type: 'error',
        error: 'Missing channel parameter',
        requestId: message.requestId
      });
      return;
    }

    const subscriptionKey = this.getSubscriptionKey(channel, params);

    // Add to client subscriptions
    client.subscriptions.add(subscriptionKey);

    // Add to global subscriptions
    if (!this.subscriptions.has(subscriptionKey)) {
      this.subscriptions.set(subscriptionKey, new Set());
    }
    this.subscriptions.get(subscriptionKey).add(ws);

    // Send confirmation with subscription details
    await this.sendToClient(ws, {
      type: 'subscribed',
      channel,
      params,
      subscriptionKey,
      subscriberCount: this.subscriptions.get(subscriptionKey).size,
      requestId: message.requestId
    });

    structuredLogger.info('Client subscribed to channel', {
      component: 'enhanced-websocket',
      clientId: client.id,
      channel,
      params,
      subscriberCount: this.subscriptions.get(subscriptionKey).size
    });
  }

  /**
   * Enhanced message sending with compression and batching
   */
  async sendToClient(ws, data, options = {}) {
    try {
      if (ws.readyState !== WebSocket.OPEN) {
        return false;
      }

      const client = this.clients.get(ws);
      if (!client) return false;

      // Add metadata
      const message = {
        ...data,
        timestamp: Date.now(),
        clientId: client.id
      };

      // Check if message should be batched
      if (options.batch && this.shouldBatch(message)) {
        this.addToBatch(client.id, message);
        return true;
      }

      // Send immediately
      const serialized = JSON.stringify(message);
      const startTime = process.hrtime.bigint();

      ws.send(serialized);

      const endTime = process.hrtime.bigint();
      const latency = Number(endTime - startTime) / 1000000; // Convert to milliseconds

      // Update metrics
      this.metrics.messagesSent++;
      this.metrics.bytesTransferred += serialized.length;
      this.updateLatency(client.id, latency);

      return true;

    } catch (error) {
      structuredLogger.error('Error sending message to client', {
        component: 'enhanced-websocket',
        clientId: this.clients.get(ws)?.id,
        error: error.message
      });

      this.metrics.errors++;
      return false;
    }
  }

  /**
   * Broadcast with optimized batching for high-frequency updates
   */
  broadcastToChannel(channel, data, params = {}, options = {}) {
    const subscriptionKey = this.getSubscriptionKey(channel, params);
    const subscribers = this.subscriptions.get(subscriptionKey);

    if (!subscribers || subscribers.size === 0) {
      return 0;
    }

    const message = {
      type: 'update',
      channel,
      params,
      data,
      timestamp: Date.now()
    };

    let sentCount = 0;

    // Use batching for high-frequency updates
    if (options.highFrequency) {
      for (const ws of subscribers) {
        const client = this.clients.get(ws);
        if (client && ws.readyState === WebSocket.OPEN) {
          this.addToBatch(client.id, message);
          sentCount++;
        }
      }
    } else {
      // Send immediately for low-frequency updates
      const serialized = JSON.stringify(message);

      for (const ws of subscribers) {
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(serialized);
          sentCount++;
        }
      }

      this.metrics.messagesSent += sentCount;
      this.metrics.bytesTransferred += serialized.length * sentCount;
    }

    return sentCount;
  }

  /**
   * Message batching for high-frequency updates
   */
  addToBatch(clientId, message) {
    if (!this.messageBatches.has(clientId)) {
      this.messageBatches.set(clientId, []);
    }

    const batch = this.messageBatches.get(clientId);
    batch.push(message);

    // Send batch if it reaches max size
    if (batch.length >= this.options.maxBatchSize) {
      this.flushBatch(clientId);
    }
  }

  /**
   * Flush message batch to client
   */
  flushBatch(clientId) {
    const batch = this.messageBatches.get(clientId);
    if (!batch || batch.length === 0) return;

    // Find client WebSocket
    let targetWs = null;
    for (const [ws, client] of this.clients) {
      if (client.id === clientId) {
        targetWs = ws;
        break;
      }
    }

    if (!targetWs || targetWs.readyState !== WebSocket.OPEN) {
      this.messageBatches.delete(clientId);
      return;
    }

    // Send batched message
    const batchMessage = {
      type: 'batch',
      messages: batch,
      count: batch.length,
      timestamp: Date.now()
    };

    const serialized = JSON.stringify(batchMessage);
    targetWs.send(serialized);

    // Update metrics
    this.metrics.batchesSent++;
    this.metrics.messagesSent += batch.length;
    this.metrics.bytesTransferred += serialized.length;

    // Clear batch
    this.messageBatches.set(clientId, []);
  }

  /**
   * Start message batching interval
   */
  startMessageBatching() {
    setInterval(() => {
      for (const clientId of this.messageBatches.keys()) {
        this.flushBatch(clientId);
      }
    }, this.options.batchInterval);
  }

  /**
   * Start performance monitoring
   */
  startPerformanceMonitoring() {
    setInterval(() => {
      this.updatePerformanceMetrics();
      this.emit('performanceUpdate', this.getPerformanceMetrics());
    }, 1000); // Update every second
  }

  /**
   * Start health monitoring for connections
   */
  startHealthMonitoring() {
    setInterval(() => {
      this.performHealthChecks();
    }, this.options.pingInterval);
  }

  /**
   * Perform health checks on all connections
   */
  performHealthChecks() {
    for (const [ws, client] of this.clients) {
      if (ws.readyState === WebSocket.OPEN) {
        // Send ping with timestamp for latency measurement
        const pingData = JSON.stringify({
          type: 'ping',
          timestamp: Date.now(),
          clientId: client.id
        });

        ws.ping(pingData);
        client.lastPing = Date.now();
      }
    }
  }

  /**
   * Handle pong response for latency calculation
   */
  handlePong(ws, client, data) {
    try {
      const pongTime = Date.now();
      const latency = pongTime - client.lastPing;

      client.latency = latency;
      client.isAlive = true;

      this.updateLatency(client.id, latency);
      this.updateConnectionQuality(client.id, 'pong', { latency });

    } catch (error) {
      structuredLogger.error('Error handling pong', {
        component: 'enhanced-websocket',
        clientId: client.id,
        error: error.message
      });
    }
  }

  /**
   * Update connection quality metrics
   */
  updateConnectionQuality(clientId, event, data = {}) {
    const quality = this.connectionQuality.get(clientId);
    if (!quality) return;

    const now = Date.now();

    switch (event) {
      case 'pong':
        quality.latency.push({ timestamp: now, value: data.latency });
        // Keep only last 100 measurements
        if (quality.latency.length > 100) {
          quality.latency = quality.latency.slice(-100);
        }
        break;

      case 'message':
        quality.messageRate.push({ timestamp: now });
        // Keep only last minute of messages
        quality.messageRate = quality.messageRate.filter(
          msg => now - msg.timestamp < 60000
        );
        break;

      case 'error':
        quality.errors++;
        break;
    }

    // Calculate overall quality
    quality.quality = this.calculateConnectionQuality(quality);
  }

  /**
   * Calculate connection quality score
   */
  calculateConnectionQuality(quality) {
    let score = 100;

    // Latency impact
    if (quality.latency.length > 0) {
      const avgLatency = quality.latency.reduce((sum, l) => sum + l.value, 0) / quality.latency.length;
      if (avgLatency > 1000) score -= 30;
      else if (avgLatency > 500) score -= 15;
      else if (avgLatency > 200) score -= 5;
    }

    // Error impact
    if (quality.errors > 10) score -= 40;
    else if (quality.errors > 5) score -= 20;
    else if (quality.errors > 0) score -= 5;

    // Message rate impact (too high or too low)
    const messageRate = quality.messageRate.length;
    if (messageRate > 1000) score -= 10; // Too many messages

    if (score >= 80) return 'excellent';
    if (score >= 60) return 'good';
    if (score >= 40) return 'fair';
    return 'poor';
  }

  /**
   * Get comprehensive performance metrics
   */
  getPerformanceMetrics() {
    const now = Date.now();
    const uptime = now - this.metrics.lastUpdate;

    return {
      ...this.metrics,
      uptime,
      connectionsPerSecond: this.metrics.totalConnections / (uptime / 1000),
      messagesPerSecond: this.metrics.messagesSent / (uptime / 1000),
      averageBatchSize: this.metrics.batchesSent > 0 ?
        this.metrics.messagesSent / this.metrics.batchesSent : 0,
      compressionRatio: this.calculateCompressionRatio(),
      connectionQuality: this.getConnectionQualityStats(),
      timestamp: now
    };
  }

  /**
   * Get connection quality statistics
   */
  getConnectionQualityStats() {
    const qualities = Array.from(this.connectionQuality.values());
    const qualityDistribution = {
      excellent: 0,
      good: 0,
      fair: 0,
      poor: 0
    };

    qualities.forEach(q => {
      qualityDistribution[q.quality]++;
    });

    return {
      distribution: qualityDistribution,
      totalConnections: qualities.length,
      averageLatency: this.calculateAverageLatency(qualities),
      totalErrors: qualities.reduce((sum, q) => sum + q.errors, 0)
    };
  }

  /**
   * Utility methods
   */
  generateClientId() {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  extractClientInfo(req) {
    return {
      ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
      userAgent: req.headers['user-agent'],
      origin: req.headers.origin
    };
  }

  getSubscriptionKey(channel, params = {}) {
    return `${channel}:${JSON.stringify(params)}`;
  }

  checkRateLimit(client) {
    const now = Date.now();
    const limiter = client.rateLimiter;

    // Reset window if needed
    if (now - limiter.windowStart > this.options.rateLimitWindow) {
      limiter.messages = 0;
      limiter.windowStart = now;
    }

    limiter.messages++;
    return limiter.messages <= this.options.rateLimitMax;
  }

  shouldBatch(message) {
    return message.type === 'update' && message.channel === 'market-data';
  }

  updateLatency(clientId, latency) {
    // Update global average latency
    this.metrics.averageLatency = (this.metrics.averageLatency + latency) / 2;
  }

  calculateCompressionRatio() {
    // This would be calculated based on actual compression statistics
    return 0.7; // Placeholder
  }

  calculateAverageLatency(qualities) {
    const allLatencies = qualities.flatMap(q => q.latency.map(l => l.value));
    return allLatencies.length > 0 ?
      allLatencies.reduce((sum, l) => sum + l, 0) / allLatencies.length : 0;
  }

  updatePerformanceMetrics() {
    this.metrics.lastUpdate = Date.now();
  }

  /**
   * Get connection count
   */
  getConnectionCount() {
    return this.clients.size;
  }

  /**
   * Handle connection close with cleanup
   */
  handleClose(ws, client, code, reason) {
    try {
      // Clean up subscriptions
      for (const subscriptionKey of client.subscriptions) {
        const subscribers = this.subscriptions.get(subscriptionKey);
        if (subscribers) {
          subscribers.delete(ws);
          if (subscribers.size === 0) {
            this.subscriptions.delete(subscriptionKey);
          }
        }
      }

      // Clean up client data
      this.clients.delete(ws);
      this.messageBatches.delete(client.id);
      this.connectionQuality.delete(client.id);

      // Update metrics
      this.metrics.activeConnections--;

      structuredLogger.info('Enhanced WebSocket client disconnected', {
        component: 'enhanced-websocket',
        clientId: client.id,
        code,
        reason: reason?.toString(),
        connectionDuration: Date.now() - client.connectedAt
      });

    } catch (error) {
      structuredLogger.error('Error handling WebSocket close', {
        component: 'enhanced-websocket',
        clientId: client.id,
        error: error.message
      });
    }
  }

  /**
   * Handle WebSocket errors
   */
  handleError(ws, client, error) {
    structuredLogger.error('WebSocket client error', {
      component: 'enhanced-websocket',
      clientId: client.id,
      error: error.message,
      stack: error.stack
    });

    this.metrics.errors++;
    this.updateConnectionQuality(client.id, 'error');
  }

  /**
   * Handle ping messages for latency measurement
   */
  async handlePing(ws, client, message) {
    const pongMessage = {
      type: 'pong',
      timestamp: Date.now(),
      originalTimestamp: message.timestamp,
      requestId: message.requestId
    };

    await this.sendToClient(ws, pongMessage);
  }

  /**
   * Handle health check requests
   */
  async handleHealthCheck(ws, client, message) {
    const quality = this.connectionQuality.get(client.id);

    const healthResponse = {
      type: 'health_response',
      clientId: client.id,
      status: 'healthy',
      latency: client.latency,
      quality: quality?.quality || 'unknown',
      connectionTime: Date.now() - client.connectedAt,
      subscriptions: Array.from(client.subscriptions),
      requestId: message.requestId
    };

    await this.sendToClient(ws, healthResponse);
  }

  /**
   * Close service
   */
  async close() {
    // Close all client connections
    for (const [ws, client] of this.clients) {
      ws.close(1001, 'Server shutting down');
    }

    // Close WebSocket server
    this.wss.close();

    structuredLogger.info('Enhanced WebSocket service closed', {
      component: 'enhanced-websocket'
    });
  }
}

module.exports = { EnhancedWebSocketService };
