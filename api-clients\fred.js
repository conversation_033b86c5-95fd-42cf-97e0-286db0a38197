const axios = require('axios');

module.exports = async function fred(query) {
  const apiKey = process.env.FRED_API_KEY;
  let url = 'https://api.stlouisfed.org/fred/series/observations';
  let params = {};
  if (query.type === 'economics') {
    params = {
      series_id: query.symbol,
      api_key: apiKey,
      file_type: 'json'
    };
  } else if (query.type === 'commodities' && query.symbol === 'GOLD') {
    params = {
      series_id: 'GOLDAMGBD228NLBM',
      api_key: apiKey,
      file_type: 'json'
    };
  } else {
    throw new Error('FRED only supports some commodities and economics data.');
  }
  const resp = await axios.get(url, { params });
  return resp.data;
}; 