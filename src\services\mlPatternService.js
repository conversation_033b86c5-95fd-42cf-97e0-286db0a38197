/**
 * Machine Learning Pattern Recognition Service
 *
 * Provides functions to detect patterns using machine learning techniques
 */

import * as tf from '@tensorflow/tfjs';
import logger from '../utils/logger.js';

// Cache for loaded models
const modelCache = {};

/**
 * Detect patterns using machine learning
 *
 * @param {Array} data - Array of OHLC data objects
 * @param {Object} options - Configuration options
 * @returns {Array} - Array of detected patterns with their locations
 */
export const detectPatternsWithML = async (data, options = {}) => {
  if (!data || !Array.isArray(data) || data.length < 30) {
    logger.warn('Insufficient data for ML pattern recognition');
    return [];
  }

  const { modelType = 'cnn', confidence = 0.7 } = options;

  try {
    // Load the appropriate model
    const model = await loadModel(modelType);
    
    // Prepare data for the model
    const preparedData = prepareDataForModel(data, modelType);
    
    // Run prediction
    const predictions = await runPrediction(model, preparedData, modelType);
    
    // Process predictions into pattern objects
    return processPredictions(predictions, data, confidence);
  } catch (error) {
    logger.error('Error in ML pattern detection:', error);
    return [];
  }
};

/**
 * Load a TensorFlow.js model
 *
 * @param {string} modelType - Type of model to load ('cnn', 'lstm', etc.)
 * @returns {tf.LayersModel} - Loaded TensorFlow.js model
 */
const loadModel = async (modelType) => {
  // Check if model is already cached
  if (modelCache[modelType]) {
    return modelCache[modelType];
  }
  
  try {
    // In a real implementation, this would load from a server or local storage
    // For demo purposes, we'll create a simple model
    const model = await createDemoModel(modelType);
    
    // Cache the model
    modelCache[modelType] = model;
    
    return model;
  } catch (error) {
    logger.error(`Error loading ${modelType} model:`, error);
    throw new Error(`Failed to load ${modelType} model`);
  }
};

/**
 * Create a demo model for pattern recognition
 *
 * @param {string} modelType - Type of model to create
 * @returns {tf.LayersModel} - TensorFlow.js model
 */
const createDemoModel = async (modelType) => {
  switch (modelType) {
    case 'cnn':
      return createCNNModel();
    case 'lstm':
      return createLSTMModel();
    default:
      return createCNNModel();
  }
};

/**
 * Create a simple CNN model for pattern recognition
 *
 * @returns {tf.LayersModel} - TensorFlow.js CNN model
 */
const createCNNModel = () => {
  // Input shape: [window_size, features]
  // Features: [open, high, low, close, volume]
  const windowSize = 20;
  const numFeatures = 5;
  
  const model = tf.sequential();
  
  // Reshape input to 2D for CNN
  model.add(tf.layers.reshape({
    targetShape: [windowSize, numFeatures, 1],
    inputShape: [windowSize, numFeatures]
  }));
  
  // Add convolutional layers
  model.add(tf.layers.conv2d({
    filters: 16,
    kernelSize: [3, 3],
    activation: 'relu',
    padding: 'same'
  }));
  
  model.add(tf.layers.maxPooling2d({
    poolSize: [2, 1],
    strides: [2, 1]
  }));
  
  model.add(tf.layers.conv2d({
    filters: 32,
    kernelSize: [3, 3],
    activation: 'relu',
    padding: 'same'
  }));
  
  model.add(tf.layers.maxPooling2d({
    poolSize: [2, 1],
    strides: [2, 1]
  }));
  
  // Flatten and add dense layers
  model.add(tf.layers.flatten());
  
  model.add(tf.layers.dense({
    units: 64,
    activation: 'relu'
  }));
  
  model.add(tf.layers.dropout({ rate: 0.5 }));
  
  // Output layer - multiple pattern types
  model.add(tf.layers.dense({
    units: 10, // Number of pattern types
    activation: 'softmax'
  }));
  
  // Compile the model
  model.compile({
    optimizer: 'adam',
    loss: 'categoricalCrossentropy',
    metrics: ['accuracy']
  });
  
  return model;
};

/**
 * Create a simple LSTM model for pattern recognition
 *
 * @returns {tf.LayersModel} - TensorFlow.js LSTM model
 */
const createLSTMModel = () => {
  // Input shape: [window_size, features]
  // Features: [open, high, low, close, volume]
  const windowSize = 20;
  const numFeatures = 5;
  
  const model = tf.sequential();
  
  // Add LSTM layers
  model.add(tf.layers.lstm({
    units: 50,
    returnSequences: true,
    inputShape: [windowSize, numFeatures]
  }));
  
  model.add(tf.layers.dropout({ rate: 0.2 }));
  
  model.add(tf.layers.lstm({
    units: 50,
    returnSequences: false
  }));
  
  model.add(tf.layers.dropout({ rate: 0.2 }));
  
  // Add dense layers
  model.add(tf.layers.dense({
    units: 25,
    activation: 'relu'
  }));
  
  // Output layer - multiple pattern types
  model.add(tf.layers.dense({
    units: 10, // Number of pattern types
    activation: 'softmax'
  }));
  
  // Compile the model
  model.compile({
    optimizer: 'adam',
    loss: 'categoricalCrossentropy',
    metrics: ['accuracy']
  });
  
  return model;
};

/**
 * Prepare data for model input
 *
 * @param {Array} data - OHLC data
 * @param {string} modelType - Type of model
 * @returns {Array} - Prepared data for model input
 */
const prepareDataForModel = (data, modelType) => {
  const windowSize = 20; // Look at 20 candles at a time
  const windows = [];
  
  // Normalize data
  const normalizedData = normalizeData(data);
  
  // Create sliding windows
  for (let i = 0; i <= normalizedData.length - windowSize; i++) {
    const window = normalizedData.slice(i, i + windowSize);
    windows.push({
      data: window,
      index: i + windowSize - 1 // Index of the last candle in the window
    });
  }
  
  return windows;
};

/**
 * Normalize OHLC data
 *
 * @param {Array} data - OHLC data
 * @returns {Array} - Normalized data
 */
const normalizeData = (data) => {
  // Find min and max values for each feature
  const features = ['open', 'high', 'low', 'close', 'volume'];
  const minMax = {};
  
  features.forEach(feature => {
    const values = data.map(d => d[feature] || 0);
    minMax[feature] = {
      min: Math.min(...values),
      max: Math.max(...values)
    };
  });
  
  // Normalize data to [0, 1] range
  return data.map(candle => {
    const normalized = {};
    
    features.forEach(feature => {
      const value = candle[feature] || 0;
      const { min, max } = minMax[feature];
      normalized[feature] = max > min ? (value - min) / (max - min) : 0;
    });
    
    return normalized;
  });
};

/**
 * Run prediction on prepared data
 *
 * @param {tf.LayersModel} model - TensorFlow.js model
 * @param {Array} preparedData - Prepared data for model input
 * @param {string} modelType - Type of model
 * @returns {Array} - Prediction results
 */
const runPrediction = async (model, preparedData, modelType) => {
  // In a real implementation, this would run the model on the prepared data
  // For demo purposes, we'll generate random predictions
  
  // Pattern classes
  const patternClasses = [
    'head_and_shoulders',
    'double_top',
    'double_bottom',
    'triangle',
    'flag',
    'pennant',
    'cup_and_handle',
    'channel',
    'wedge',
    'rectangle'
  ];
  
  // Generate predictions for each window
  return preparedData.map(window => {
    // Generate random confidences for each pattern class
    const confidences = patternClasses.map(() => Math.random());
    
    // Normalize confidences to sum to 1 (softmax-like)
    const sum = confidences.reduce((a, b) => a + b, 0);
    const normalizedConfidences = confidences.map(c => c / sum);
    
    // Find the pattern with highest confidence
    let maxIndex = 0;
    let maxConfidence = normalizedConfidences[0];
    
    for (let i = 1; i < normalizedConfidences.length; i++) {
      if (normalizedConfidences[i] > maxConfidence) {
        maxConfidence = normalizedConfidences[i];
        maxIndex = i;
      }
    }
    
    return {
      index: window.index,
      pattern: patternClasses[maxIndex],
      confidence: maxConfidence,
      allConfidences: normalizedConfidences.map((conf, i) => ({
        pattern: patternClasses[i],
        confidence: conf
      }))
    };
  });
};

/**
 * Process predictions into pattern objects
 *
 * @param {Array} predictions - Prediction results
 * @param {Array} data - Original OHLC data
 * @param {number} confidenceThreshold - Minimum confidence threshold
 * @returns {Array} - Array of detected patterns
 */
const processPredictions = (predictions, data, confidenceThreshold) => {
  // Filter predictions by confidence threshold
  const filteredPredictions = predictions.filter(p => p.confidence >= confidenceThreshold);
  
  // Group nearby predictions of the same pattern
  const groupedPredictions = groupPredictions(filteredPredictions);
  
  // Convert to pattern objects
  return groupedPredictions.map(group => {
    const pattern = group.pattern;
    const index = group.indices[Math.floor(group.indices.length / 2)]; // Middle index
    const confidence = group.confidence;
    
    // Determine pattern type
    const patternType = getPatternType(pattern);
    
    return {
      pattern,
      type: patternType,
      significance: Math.round(confidence * 10), // Scale 0-1 to 0-10
      description: `${pattern.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')} pattern detected by ML with ${(confidence * 100).toFixed(1)}% confidence`,
      index,
      mlDetected: true,
      confidence
    };
  });
};

/**
 * Group nearby predictions of the same pattern
 *
 * @param {Array} predictions - Prediction results
 * @returns {Array} - Grouped predictions
 */
const groupPredictions = (predictions) => {
  if (predictions.length === 0) return [];
  
  // Sort predictions by pattern and index
  const sorted = [...predictions].sort((a, b) => {
    if (a.pattern !== b.pattern) return a.pattern.localeCompare(b.pattern);
    return a.index - b.index;
  });
  
  const groups = [];
  let currentGroup = {
    pattern: sorted[0].pattern,
    indices: [sorted[0].index],
    confidences: [sorted[0].confidence]
  };
  
  for (let i = 1; i < sorted.length; i++) {
    const current = sorted[i];
    const prev = sorted[i - 1];
    
    // If same pattern and close enough, add to current group
    if (current.pattern === prev.pattern && current.index - prev.index <= 5) {
      currentGroup.indices.push(current.index);
      currentGroup.confidences.push(current.confidence);
    } else {
      // Finalize current group
      currentGroup.confidence = Math.max(...currentGroup.confidences);
      groups.push(currentGroup);
      
      // Start new group
      currentGroup = {
        pattern: current.pattern,
        indices: [current.index],
        confidences: [current.confidence]
      };
    }
  }
  
  // Add the last group
  currentGroup.confidence = Math.max(...currentGroup.confidences);
  groups.push(currentGroup);
  
  return groups;
};

/**
 * Get pattern type based on pattern name
 *
 * @param {string} pattern - Pattern name
 * @returns {string} - Pattern type
 */
const getPatternType = (pattern) => {
  const bullishPatterns = ['double_bottom', 'cup_and_handle', 'bullish_flag', 'bullish_pennant'];
  const bearishPatterns = ['head_and_shoulders', 'double_top', 'bearish_flag', 'bearish_pennant'];
  const continuationPatterns = ['flag', 'pennant', 'rectangle', 'triangle'];
  
  if (pattern.includes('bullish') || bullishPatterns.includes(pattern)) {
    return 'bullish';
  } else if (pattern.includes('bearish') || bearishPatterns.includes(pattern)) {
    return 'bearish';
  } else if (continuationPatterns.includes(pattern)) {
    return 'continuation';
  } else {
    return 'neutral';
  }
};

export default {
  detectPatternsWithML
};
