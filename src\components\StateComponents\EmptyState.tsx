/**
 * Empty State Components
 * 
 * Standardized empty state displays for when there's no data to show.
 * Provides contextual guidance and actions to help users understand
 * what to do next.
 * 
 * Features:
 * - Context-aware empty states
 * - Action buttons for next steps
 * - Customizable illustrations
 * - Responsive design
 * - Accessibility support
 * - Integration with application features
 * 
 * @version 1.0.0
 */

import React from 'react';

// ============================================================================
// INTERFACES
// ============================================================================

export interface EmptyStateProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  illustration?: 'signals' | 'charts' | 'search' | 'data' | 'settings' | 'custom';
  actions?: EmptyStateAction[];
  variant?: 'default' | 'compact' | 'minimal';
  className?: string;
}

export interface EmptyStateAction {
  label: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  icon?: React.ReactNode;
}

// ============================================================================
// ILLUSTRATION COMPONENTS
// ============================================================================

const SignalsIllustration: React.FC<{ className?: string }> = ({ className = "w-24 h-24" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} 
          d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
  </svg>
);

const ChartsIllustration: React.FC<{ className?: string }> = ({ className = "w-24 h-24" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} 
          d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

const SearchIllustration: React.FC<{ className?: string }> = ({ className = "w-24 h-24" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} 
          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const DataIllustration: React.FC<{ className?: string }> = ({ className = "w-24 h-24" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} 
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
  </svg>
);

const SettingsIllustration: React.FC<{ className?: string }> = ({ className = "w-24 h-24" }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} 
          d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
);

// ============================================================================
// ACTION BUTTON COMPONENT
// ============================================================================

const ActionButton: React.FC<EmptyStateAction> = ({ label, onClick, variant = 'primary', icon }) => {
  const getButtonClasses = () => {
    const baseClasses = 'inline-flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    switch (variant) {
      case 'primary':
        return `${baseClasses} bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500`;
      case 'secondary':
        return `${baseClasses} bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500`;
      case 'outline':
        return `${baseClasses} border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500`;
      default:
        return `${baseClasses} bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500`;
    }
  };

  return (
    <button onClick={onClick} className={getButtonClasses()}>
      {icon && <span className="mr-2">{icon}</span>}
      {label}
    </button>
  );
};

// ============================================================================
// MAIN EMPTY STATE COMPONENT
// ============================================================================

const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  icon,
  illustration = 'data',
  actions = [],
  variant = 'default',
  className = ''
}) => {
  const getIllustration = () => {
    if (icon) return icon;
    
    const illustrationClass = variant === 'compact' ? 'w-16 h-16' : 'w-24 h-24';
    
    switch (illustration) {
      case 'signals':
        return <SignalsIllustration className={`${illustrationClass} text-gray-400`} />;
      case 'charts':
        return <ChartsIllustration className={`${illustrationClass} text-gray-400`} />;
      case 'search':
        return <SearchIllustration className={`${illustrationClass} text-gray-400`} />;
      case 'settings':
        return <SettingsIllustration className={`${illustrationClass} text-gray-400`} />;
      default:
        return <DataIllustration className={`${illustrationClass} text-gray-400`} />;
    }
  };

  const getContainerClasses = () => {
    const baseClasses = 'text-center';
    
    switch (variant) {
      case 'compact':
        return `${baseClasses} py-8 px-4`;
      case 'minimal':
        return `${baseClasses} py-4 px-4`;
      default:
        return `${baseClasses} py-12 px-4`;
    }
  };

  const getTitleClasses = () => {
    switch (variant) {
      case 'compact':
        return 'text-lg font-medium text-gray-900 mb-2';
      case 'minimal':
        return 'text-base font-medium text-gray-900 mb-1';
      default:
        return 'text-xl font-semibold text-gray-900 mb-3';
    }
  };

  const getDescriptionClasses = () => {
    switch (variant) {
      case 'compact':
        return 'text-sm text-gray-600 mb-4 max-w-sm mx-auto';
      case 'minimal':
        return 'text-sm text-gray-600 mb-2 max-w-xs mx-auto';
      default:
        return 'text-gray-600 mb-6 max-w-md mx-auto';
    }
  };

  return (
    <div className={`${getContainerClasses()} ${className}`}>
      {variant !== 'minimal' && (
        <div className="flex justify-center mb-4">
          {getIllustration()}
        </div>
      )}
      
      <h3 className={getTitleClasses()}>
        {title}
      </h3>
      
      <p className={getDescriptionClasses()}>
        {description}
      </p>
      
      {actions.length > 0 && (
        <div className={`flex justify-center ${variant === 'minimal' ? 'space-x-2' : 'space-x-3'}`}>
          {actions.map((action, index) => (
            <ActionButton key={index} {...action} />
          ))}
        </div>
      )}
    </div>
  );
};

// ============================================================================
// SPECIALIZED EMPTY STATE COMPONENTS
// ============================================================================

export const NoSignalsEmpty: React.FC<{ onCreateSignal?: () => void; onRefresh?: () => void }> = ({ 
  onCreateSignal, 
  onRefresh 
}) => {
  const actions: EmptyStateAction[] = [];
  
  if (onCreateSignal) {
    actions.push({
      label: 'Create Signal',
      onClick: onCreateSignal,
      variant: 'primary',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
        </svg>
      )
    });
  }
  
  if (onRefresh) {
    actions.push({
      label: 'Refresh',
      onClick: onRefresh,
      variant: 'outline',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      )
    });
  }

  return (
    <EmptyState
      title="No Signals Available"
      description="There are no trading signals to display at the moment. Signals will appear here as they are generated or when market conditions change."
      illustration="signals"
      actions={actions}
    />
  );
};

export const NoSearchResultsEmpty: React.FC<{ 
  searchTerm: string; 
  onClearSearch?: () => void;
  onTryDifferentSearch?: () => void;
}> = ({ searchTerm, onClearSearch, onTryDifferentSearch }) => {
  const actions: EmptyStateAction[] = [];
  
  if (onClearSearch) {
    actions.push({
      label: 'Clear Search',
      onClick: onClearSearch,
      variant: 'primary'
    });
  }
  
  if (onTryDifferentSearch) {
    actions.push({
      label: 'Try Different Search',
      onClick: onTryDifferentSearch,
      variant: 'outline'
    });
  }

  return (
    <EmptyState
      title="No Results Found"
      description={`We couldn't find any results for "${searchTerm}". Try adjusting your search terms or filters.`}
      illustration="search"
      actions={actions}
    />
  );
};

export const NoDataEmpty: React.FC<{ 
  dataType: string;
  onRefresh?: () => void;
  onGoBack?: () => void;
}> = ({ dataType, onRefresh, onGoBack }) => {
  const actions: EmptyStateAction[] = [];
  
  if (onRefresh) {
    actions.push({
      label: 'Refresh Data',
      onClick: onRefresh,
      variant: 'primary'
    });
  }
  
  if (onGoBack) {
    actions.push({
      label: 'Go Back',
      onClick: onGoBack,
      variant: 'outline'
    });
  }

  return (
    <EmptyState
      title={`No ${dataType} Available`}
      description={`There is no ${dataType.toLowerCase()} data to display. This could be due to filters applied or data not being available for the selected time period.`}
      illustration="data"
      actions={actions}
    />
  );
};

export const NoChartsEmpty: React.FC<{ onCreateChart?: () => void; onBrowseTemplates?: () => void }> = ({ 
  onCreateChart, 
  onBrowseTemplates 
}) => {
  const actions: EmptyStateAction[] = [];
  
  if (onCreateChart) {
    actions.push({
      label: 'Create Chart',
      onClick: onCreateChart,
      variant: 'primary'
    });
  }
  
  if (onBrowseTemplates) {
    actions.push({
      label: 'Browse Templates',
      onClick: onBrowseTemplates,
      variant: 'outline'
    });
  }

  return (
    <EmptyState
      title="No Charts Created"
      description="Start analyzing market data by creating your first chart. You can customize indicators, timeframes, and save your favorite configurations."
      illustration="charts"
      actions={actions}
    />
  );
};

export const FirstTimeUserEmpty: React.FC<{ onGetStarted?: () => void; onWatchTutorial?: () => void }> = ({ 
  onGetStarted, 
  onWatchTutorial 
}) => {
  const actions: EmptyStateAction[] = [];
  
  if (onGetStarted) {
    actions.push({
      label: 'Get Started',
      onClick: onGetStarted,
      variant: 'primary'
    });
  }
  
  if (onWatchTutorial) {
    actions.push({
      label: 'Watch Tutorial',
      onClick: onWatchTutorial,
      variant: 'outline',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V8a2 2 0 012-2h2a2 2 0 012 2v2" />
        </svg>
      )
    });
  }

  return (
    <EmptyState
      title="Welcome to Trading Signals!"
      description="You're all set up! Start by exploring our features, creating your first signal, or watching our tutorial to learn how to make the most of the platform."
      illustration="settings"
      actions={actions}
    />
  );
};

export const MaintenanceEmpty: React.FC<{ estimatedTime?: string; onCheckStatus?: () => void }> = ({ 
  estimatedTime, 
  onCheckStatus 
}) => {
  const actions: EmptyStateAction[] = [];
  
  if (onCheckStatus) {
    actions.push({
      label: 'Check Status',
      onClick: onCheckStatus,
      variant: 'primary'
    });
  }

  return (
    <EmptyState
      title="Temporarily Unavailable"
      description={`This feature is currently under maintenance. ${estimatedTime ? `Expected to be back ${estimatedTime}.` : 'Please check back later.'}`}
      illustration="settings"
      actions={actions}
      variant="compact"
    />
  );
};

export const OfflineEmpty: React.FC<{ onRetry?: () => void }> = ({ onRetry }) => {
  const actions: EmptyStateAction[] = [];
  
  if (onRetry) {
    actions.push({
      label: 'Try Again',
      onClick: onRetry,
      variant: 'primary'
    });
  }

  return (
    <EmptyState
      title="You're Offline"
      description="Please check your internet connection and try again. Some features may not be available while offline."
      illustration="data"
      actions={actions}
      variant="compact"
    />
  );
};

export default EmptyState;
