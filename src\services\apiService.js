import axios from 'axios';
import config from '../config/config.js';
import logger from '../utils/logger.js';
import { APIError } from '../middleware/errorHandler.js';
import enhancedCache, { EnhancedCache } from '../utils/cache.js';
import Bottleneck from 'bottleneck';

const IS_TEST_MODE = process.env.API_TEST_MODE === 'true';
const mockResponses = {};

class APIService {
  constructor(baseURL, apiKey, cacheTTL = 300) {
    // Store API info for debugging
    this.baseURL = baseURL;
    this.apiKeyPrefix = apiKey ? apiKey.substring(0, 4) + '...' : 'none';

    // Create a dedicated cache instance for this API service
    this.cache = new EnhancedCache({
      defaultTTL: cacheTTL,
      maxMemoryItems: 200,
      useCompression: true,
      compressionThreshold: 5000
    });

    // Determine if this is an API that uses query parameter for API key
    const isQueryParamAuth = baseURL.includes('alphavantage.co') ||
                            baseURL.includes('stlouisfed.org') ||
                            baseURL.includes('polygon.io') ||
                            baseURL.includes('finnhub.io') ||
                            baseURL.includes('twelvedata.com') ||
                            baseURL.includes('financialmodelingprep.com');

    // Create axios instance with appropriate auth method
    this.instance = axios.create({
      baseURL,
      timeout: 15000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Trading Signals App/1.0',
        ...(!isQueryParamAuth && apiKey ? { 'Authorization': `Bearer ${apiKey}` } : {})
      },
    });

    // Store auth method for later use
    this.isQueryParamAuth = isQueryParamAuth;
    this.apiKey = apiKey;
    this.apiKeyParamName = this.getApiKeyParamName(baseURL);

    // Add response interceptor for error handling
    this.instance.interceptors.response.use(
      (response) => response,
      (error) => this.handleError(error)
    );

    // Track API usage statistics
    this.stats = {
      requests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      errors: 0,
      lastRequest: null,
      rateLimitRemaining: null
    };

    // Throttling with more conservative limits
    this.limiter = new Bottleneck({
      maxConcurrent: 3,
      minTime: 1500,
      reservoir: 45,
      reservoirRefreshAmount: 45,
      reservoirRefreshInterval: 60 * 1000
    });

    // Register callbacks
    APIService.alertCallback = null;
    APIService.fallbackCallback = null;
  }

  // Generate cache key from request parameters
  generateCacheKey(endpoint, params) {
    return `${endpoint}:${JSON.stringify(params)}`;
  }

  // Get the appropriate API key parameter name based on the API
  getApiKeyParamName(baseURL) {
    if (baseURL.includes('alphavantage.co')) return 'apikey';
    if (baseURL.includes('stlouisfed.org')) return 'api_key';
    if (baseURL.includes('polygon.io')) return 'apiKey';
    if (baseURL.includes('finnhub.io')) return 'token';
    if (baseURL.includes('twelvedata.com')) return 'apikey';
    if (baseURL.includes('financialmodelingprep.com')) return 'apikey';
    return 'api_key';
  }

  // Handle API errors with improved diagnostics
  handleError(error) {
    const errorDetails = {
      message: error.message,
      code: error.code,
      api: this.baseURL,
      apiKeyPrefix: this.apiKeyPrefix,
      timestamp: new Date().toISOString(),
      requestId: `api-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`
    };

    if (error.response) {
      errorDetails.status = error.response.status;
      errorDetails.statusText = error.response.statusText;
      errorDetails.responseData = error.response.data;
      errorDetails.headers = error.response.headers;
    }

    if (error.config) {
      errorDetails.url = error.config.url;
      errorDetails.method = error.config.method;
      errorDetails.timeout = error.config.timeout;

      if (error.config.params) {
        const safeParams = { ...error.config.params };
        if (this.apiKeyParamName in safeParams) {
          safeParams[this.apiKeyParamName] = 'HIDDEN';
        }
        errorDetails.params = safeParams;
      }
    }

    logger.error('API Error:', errorDetails);

    let errorMessage = 'An error occurred while communicating with the external API';
    let statusCode = 500;

    if (error.response) {
      statusCode = error.response.status;

      if (error.response.status === 401 || error.response.status === 403) {
        errorMessage = 'API authentication failed. The API key may be invalid or expired.';
      } else if (error.response.status === 404) {
        errorMessage = 'The requested resource was not found on the external API server.';
      } else if (error.response.status === 429) {
        errorMessage = 'API rate limit exceeded. Please try again later.';
      } else if (error.response.status >= 500) {
        errorMessage = 'The external API server encountered an error. Please try again later.';
      } else {
        if (error.response.data) {
          if (typeof error.response.data === 'string') {
            errorMessage = error.response.data;
          } else if (error.response.data.message) {
            errorMessage = error.response.data.message;
          } else if (error.response.data.error) {
            errorMessage = typeof error.response.data.error === 'string'
              ? error.response.data.error
              : JSON.stringify(error.response.data.error);
          } else if (error.response.data.Note && error.response.data.Note.includes('API call frequency')) {
            errorMessage = 'API rate limit exceeded. Please try again later.';
            statusCode = 429;
          }
        }
      }
    } else if (error.code === 'ECONNABORTED') {
      errorMessage = 'The request to the external API timed out. Please try again later.';
      statusCode = 504;
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      errorMessage = 'Could not connect to the external API server. Please check your internet connection.';
      statusCode = 503;
    } else if (error.request) {
      errorMessage = 'No response received from the external API server.';
      statusCode = 503;
    }

    throw new APIError(
      statusCode,
      errorMessage,
      true,
      errorDetails.requestId
    );
  }

  // Enhanced request method with retry, backoff, and logging
  async requestWithRetry(method, endpoint, { params = {}, data = {}, options = {} } = {}, attempt = 1) {
    if (IS_TEST_MODE && mockResponses[endpoint]) {
      logger.info(`[TEST MODE] Returning mock data for ${endpoint}`);
      return mockResponses[endpoint];
    }

    const maxRetries = options.retries ?? 3;
    const backoffBase = 1000;
    const backoff = Math.pow(2, attempt - 1) * backoffBase + Math.random() * 200;
    const start = Date.now();

    try {
      return await this.limiter.schedule(async () => {
        let response;
        if (method === 'get') {
          response = await this.instance.get(endpoint, { params });
        } else if (method === 'post') {
          response = await this.instance.post(endpoint, data, { params });
        } else if (method === 'put') {
          response = await this.instance.put(endpoint, data, { params });
        } else if (method === 'delete') {
          response = await this.instance.delete(endpoint, { params });
        } else {
          throw new Error(`Unsupported method: ${method}`);
        }

        logger.info('API Call', {
          method,
          endpoint,
          status: response.status,
          responseTime: Date.now() - start,
          params,
        });

        const rl = response.headers && (response.headers['x-ratelimit-remaining'] || response.headers['ratelimit-remaining']);
        if (rl) {
          this.stats.rateLimitRemaining = Number(rl);
          if (Number(rl) < 5) {
            APIService.triggerAlert('rate_limit_low', { endpoint, remaining: rl });
          }
        }

        return response.data;
      });
    } catch (error) {
      const status = error.response?.status;
      let errorType = 'unknown';
      
      if (status === 401 || status === 403) errorType = 'auth';
      else if (status === 429) errorType = 'rate_limit';
      else if (status >= 500 && status < 600) errorType = 'server';
      else if (error.code === 'ECONNABORTED' || error.code === 'ENOTFOUND') errorType = 'network';
      else if (status === 404) errorType = 'not_found';
      else if (error.request && !error.response) errorType = 'no_response';

      if (
        attempt < maxRetries &&
        (status === 429 || (status >= 500 && status < 600) || error.code === 'ECONNABORTED' || error.code === 'ENOTFOUND')
      ) {
        logger.warn(`Retrying API call to ${endpoint} (attempt ${attempt + 1}) after ${backoff}ms`);
        await new Promise(res => setTimeout(res, backoff));
        return this.requestWithRetry(method, endpoint, { params, data, options }, attempt + 1);
      }

      logger.error('API Call Failed', {
        method,
        endpoint,
        status: status || 'N/A',
        responseTime: Date.now() - start,
        params,
        error: error.message,
        errorType
      });

      APIService.triggerAlert('api_error', { endpoint, error, errorType });
      APIService.triggerFallback(errorType, { endpoint, error });
      throw error;
    }
  }

  async get(endpoint, params = {}, options = {}) {
    const {
      useCache = true,
      cacheTTL = null,
      forceRefresh = false,
      priority = 'normal',
      retries = 2
    } = options;

    this.stats.requests++;
    this.stats.lastRequest = new Date();

    const cacheKey = this.generateCacheKey(endpoint, params);

    if (useCache && !forceRefresh) {
      const cachedData = this.cache.get(cacheKey);
      if (cachedData) {
        logger.debug('Returning cached data for:', { endpoint, params });
        this.stats.cacheHits++;
        return cachedData;
      }
    }

    this.stats.cacheMisses++;

    const requestParams = { ...params };
    if (this.isQueryParamAuth && this.apiKey) {
      requestParams[this.apiKeyParamName] = this.apiKey;
    }

    const timeoutMap = {
      high: 8000,
      normal: 15000,
      low: 30000
    };
    const timeout = timeoutMap[priority] || 15000;

    const data = await this.requestWithRetry('get', endpoint, { params: requestParams, options: { ...options, timeout } });

    if (useCache) {
      this.cache.set(cacheKey, data, cacheTTL);
    }

    return data;
  }

  async post(endpoint, data = {}, options = {}) {
    return this.requestWithRetry('post', endpoint, { data, options });
  }

  async put(endpoint, data = {}, options = {}) {
    return this.requestWithRetry('put', endpoint, { data, options });
  }

  async delete(endpoint, params = {}, options = {}) {
    return this.requestWithRetry('delete', endpoint, { params, options });
  }

  clearCache() {
    this.cache.clear();
    logger.info('API cache cleared');
  }

  getStats() {
    return {
      ...this.stats,
      cacheStats: this.cache.getStats(),
      hitRate: this.stats.requests > 0
        ? Math.round((this.stats.cacheHits / this.stats.requests) * 100)
        : 0
    };
  }

  async prefetch(endpoint, params = {}, ttl = null) {
    logger.info('Prefetching data for:', { endpoint, params });
    try {
      await this.get(endpoint, params, {
        useCache: false,
        cacheTTL: ttl,
        priority: 'low'
      });
      logger.info('Successfully prefetched data for:', { endpoint, params });
      return true;
    } catch (error) {
      logger.error('Error prefetching data:', error);
      return false;
    }
  }

  static triggerAlert(type, details) {
    if (APIService.alertCallback) {
      APIService.alertCallback(type, details);
    }
  }

  static triggerFallback(type, details) {
    if (APIService.fallbackCallback) {
      APIService.fallbackCallback(type, details);
    }
  }

  static setAlertCallback(callback) {
    APIService.alertCallback = callback;
  }

  static setFallbackCallback(callback) {
    APIService.fallbackCallback = callback;
  }
}

// Create instances for each external API
export const alphaVantageAPI = new APIService(
  config.api.alphaVantage.baseUrl,
  config.api.alphaVantage.apiKey
);

export const polygonAPI = new APIService(
  config.api.polygon.baseUrl,
  config.api.polygon.apiKey
);

export const finnhubAPI = new APIService(
  config.api.finnhub.baseUrl,
  config.api.finnhub.apiKey
);

export const twelveDataAPI = new APIService(
  config.api.twelveData.baseUrl,
  config.api.twelveData.apiKey
);

export const fredAPI = new APIService(
  config.api.fred.baseUrl,
  config.api.fred.apiKey
);

export const openaiAPI = new APIService(
  config.api.openai.baseUrl,
  config.api.openai.apiKey,
  60 // Shorter cache TTL for OpenAI responses
);

export const fmpAPI = new APIService(
  config.api.fmp.baseUrl,
  config.api.fmp.apiKey
);

export default {
  alphaVantageAPI,
  polygonAPI,
  finnhubAPI,
  twelveDataAPI,
  fredAPI,
  openaiAPI,
  fmpAPI
};