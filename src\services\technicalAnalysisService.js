import { alphaVantageAPI, fredAPI, finnhubAPI, twelveDataAPI } from './apiService.js';
import marketDataService from './marketDataService.js';
import logger from '../utils/logger.js';
import { APIError } from '../middleware/errorHandler.js';

class TechnicalAnalysisService {
  constructor() {
    this.supportedIndicators = ['SMA', 'EMA', 'RSI', 'MACD', 'BBANDS'];
  }

  // Get technical indicators from Alpha Vantage
  async getTechnicalIndicator(symbol, indicator, params = {}) {
    try {
      const defaultParams = {
        symbol,
        interval: '1d',
        time_period: 14,
        series_type: 'close',
      };

      const queryParams = {
        function: `TECHNICAL_${indicator}`,
        ...defaultParams,
        ...params,
        apikey: process.env.ALPHA_VANTAGE_API_KEY,
      };

      const data = await alphaVantageAPI.get('', queryParams);
      return this.formatIndicatorData(data, indicator);
    } catch (error) {
      logger.error('Error fetching technical indicator:', error);
      throw new APIError(500, 'Error fetching technical indicator data');
    }
  }

  // Get economic indicators from FRED
  async getEconomicIndicator(seriesId, params = {}) {
    try {
      const queryParams = {
        series_id: seriesId,
        api_key: process.env.FRED_API_KEY,
        file_type: 'json',
        ...params,
      };

      const data = await fredAPI.get('/series/observations', queryParams);
      return this.formatFredData(data);
    } catch (error) {
      logger.error('Error fetching FRED data:', error);
      throw new APIError(500, 'Error fetching economic indicator data');
    }
  }

  // Calculate Simple Moving Average (SMA)
  async getSMA(symbol, period = 14, interval = '1d') {
    return this.getTechnicalIndicator(symbol, 'SMA', {
      time_period: period,
      interval,
    });
  }

  // Calculate Relative Strength Index (RSI)
  async getRSI(symbol, period = 14, interval = '1d') {
    return this.getTechnicalIndicator(symbol, 'RSI', {
      time_period: period,
      interval,
    });
  }

  // Calculate Moving Average Convergence Divergence (MACD)
  async getMACD(symbol, interval = '1d') {
    return this.getTechnicalIndicator(symbol, 'MACD', {
      interval,
      fastperiod: 12,
      slowperiod: 26,
      signalperiod: 9,
    });
  }

  // Calculate Bollinger Bands
  async getBollingerBands(symbol, period = 20, interval = '1d') {
    return this.getTechnicalIndicator(symbol, 'BBANDS', {
      time_period: period,
      interval,
      nbdevup: 2,
      nbdevdn: 2,
    });
  }

  // Get full technical analysis
  async getFullAnalysis(symbol) {
    try {
      const [sma, rsi, macd, bbands] = await Promise.all([
        this.getSMA(symbol),
        this.getRSI(symbol),
        this.getMACD(symbol),
        this.getBollingerBands(symbol),
      ]);

      return {
        symbol,
        timestamp: new Date().toISOString(),
        indicators: {
          sma,
          rsi,
          macd,
          bbands,
        },
      };
    } catch (error) {
      logger.error('Error getting full analysis:', error);
      throw new APIError(500, 'Error performing technical analysis');
    }
  }

  // Format indicator data
  formatIndicatorData(data, indicator) {
    try {
      const technicalData = data[`Technical Analysis: ${indicator}`];
      return Object.entries(technicalData).map(([date, values]) => ({
        date,
        ...values,
      }));
    } catch (error) {
      logger.error('Error formatting indicator data:', error);
      throw new APIError(500, 'Error formatting technical indicator data');
    }
  }

  // Format FRED data
  formatFredData(data) {
    try {
      return data.observations.map(obs => ({
        date: obs.date,
        value: parseFloat(obs.value) || null,
      }));
    } catch (error) {
      logger.error('Error formatting FRED data:', error);
      throw new APIError(500, 'Error formatting economic data');
    }
  }

  // Get trading signals based on technical analysis
  async getTradingSignals(symbol) {
    try {
      const analysis = await this.getFullAnalysis(symbol);
      const signals = this.generateSignals(analysis);
      
      return {
        symbol,
        timestamp: new Date().toISOString(),
        signals,
      };
    } catch (error) {
      logger.error('Error generating trading signals:', error);
      throw new APIError(500, 'Error generating trading signals');
    }
  }

  // Generate trading signals based on technical indicators
  generateSignals(analysis) {
    const signals = {
      trend: 'neutral',
      strength: 0,
      recommendations: [],
    };

    try {
      const { rsi, macd, bbands } = analysis.indicators;

      // RSI signals
      const latestRSI = parseFloat(rsi[0].RSI);
      if (latestRSI > 70) {
        signals.recommendations.push('Overbought - Consider Selling');
        signals.trend = 'bearish';
      } else if (latestRSI < 30) {
        signals.recommendations.push('Oversold - Consider Buying');
        signals.trend = 'bullish';
      }

      // MACD signals
      const latestMACD = parseFloat(macd[0].MACD);
      const latestSignal = parseFloat(macd[0].MACD_Signal);
      if (latestMACD > latestSignal) {
        signals.recommendations.push('MACD Bullish Crossover');
        signals.trend = 'bullish';
      } else if (latestMACD < latestSignal) {
        signals.recommendations.push('MACD Bearish Crossover');
        signals.trend = 'bearish';
      }

      // Calculate signal strength (0-100)
      signals.strength = this.calculateSignalStrength(analysis);

      return signals;
    } catch (error) {
      logger.error('Error in signal generation:', error);
      return signals;
    }
  }

  // Calculate signal strength based on multiple indicators
  calculateSignalStrength(analysis) {
    try {
      const { rsi, macd } = analysis.indicators;
      let strength = 50; // Start at neutral

      // RSI contribution
      const latestRSI = parseFloat(rsi[0].RSI);
      if (latestRSI > 70 || latestRSI < 30) {
        strength += (latestRSI > 70 ? -20 : 20);
      }

      // MACD contribution
      const latestMACD = parseFloat(macd[0].MACD);
      const latestSignal = parseFloat(macd[0].MACD_Signal);
      if (Math.abs(latestMACD - latestSignal) > 0) {
        strength += (latestMACD > latestSignal ? 15 : -15);
      }

      // Ensure strength stays within 0-100
      return Math.min(Math.max(strength, 0), 100);
    } catch (error) {
      logger.error('Error calculating signal strength:', error);
      return 50;
    }
  }

  // Helper: Find swing highs/lows
  findSwings(data, lookback = 2) {
    const swings = [];
    for (let i = lookback; i < data.length - lookback; i++) {
      const isHigh = data.slice(i - lookback, i + lookback + 1)
        .every((c, idx) => idx === lookback || c.high < data[i].high);
      const isLow = data.slice(i - lookback, i + lookback + 1)
        .every((c, idx) => idx === lookback || c.low > data[i].low);
      if (isHigh) swings.push({ type: 'high', index: i, price: data[i].high, time: data[i].datetime || data[i].time });
      if (isLow) swings.push({ type: 'low', index: i, price: data[i].low, time: data[i].datetime || data[i].time });
    }
    return swings;
  }

  // Helper: Detect BOS/CHoCH
  detectMarketStructure(swings) {
    const structure = [];
    for (let i = 1; i < swings.length; i++) {
      if (swings[i].type === 'high' && swings[i].price > swings[i - 1].price) {
        structure.push({ type: 'BOS', price: swings[i].price, timestamp: swings[i].time });
      }
      if (swings[i].type === 'low' && swings[i].price < swings[i - 1].price) {
        structure.push({ type: 'CHoCH', price: swings[i].price, timestamp: swings[i].time });
      }
    }
    return structure;
  }

  // Helper: Detect Order Blocks (last opposite candle before BOS)
  detectOrderBlocks(data, structure) {
    const blocks = [];
    structure.forEach(s => {
      const idx = data.findIndex(c => (c.datetime || c.time) === s.timestamp);
      if (idx > 0) {
        const prev = data[idx - 1];
        blocks.push({
          side: s.type === 'BOS' ? 'buy' : 'sell',
          start: prev.open,
          end: prev.close,
          timestamp: prev.datetime || prev.time
        });
      }
    });
    return blocks;
  }

  // Helper: Detect Fair Value Gaps
  detectFVG(data) {
    const fvg = [];
    for (let i = 1; i < data.length - 1; i++) {
      if (data[i - 1].high < data[i + 1].low) {
        fvg.push({
          start: data[i - 1].high,
          end: data[i + 1].low,
          timestamp: data[i].datetime || data[i].time
        });
      }
    }
    return fvg;
  }

  // Helper: Detect Liquidity Pools (clusters of equal highs/lows)
  detectLiquidityPools(swings, tolerance = 0.0005, minTouches = 2) {
    const buySide = [];
    const sellSide = [];
    // Group swing highs (buy-side liquidity)
    for (let i = 0; i < swings.length; i++) {
      if (swings[i].type === 'high') {
        // Find other highs within tolerance
        const cluster = [swings[i]];
        for (let j = i + 1; j < swings.length; j++) {
          if (swings[j].type === 'high' && Math.abs(swings[j].price - swings[i].price) <= tolerance) {
            cluster.push(swings[j]);
          }
        }
        if (cluster.length >= minTouches) {
          buySide.push({
            price: swings[i].price,
            count: cluster.length,
            timestamps: cluster.map(s => s.time)
          });
        }
      }
    }
    // Group swing lows (sell-side liquidity)
    for (let i = 0; i < swings.length; i++) {
      if (swings[i].type === 'low') {
        const cluster = [swings[i]];
        for (let j = i + 1; j < swings.length; j++) {
          if (swings[j].type === 'low' && Math.abs(swings[j].price - swings[i].price) <= tolerance) {
            cluster.push(swings[j]);
          }
        }
        if (cluster.length >= minTouches) {
          sellSide.push({
            price: swings[i].price,
            count: cluster.length,
            timestamps: cluster.map(s => s.time)
          });
        }
      }
    }
    return { buySide, sellSide };
  }

  // Helper: Detect Mitigation Blocks
  detectMitigationBlocks(data, orderBlocks) {
    const mitigated = [];
    orderBlocks.forEach(block => {
      // Find if price returns to the block after its timestamp
      const idx = data.findIndex(c => (c.datetime || c.time) === block.timestamp);
      if (idx < 0) return;
      for (let i = idx + 1; i < data.length; i++) {
        if (
          (block.side === 'buy' && data[i].low <= block.end && data[i].low >= block.start) ||
          (block.side === 'sell' && data[i].high >= block.end && data[i].high <= block.start)
        ) {
          mitigated.push({ ...block, mitigatedAt: data[i].datetime || data[i].time });
          break;
        }
      }
    });
    return mitigated;
  }

  // Detect SMC/ICT structures (enhanced)
  async detectSMCStructures(symbol, timeframe = '1H') {
    try {
      // 1. Fetch historical OHLCV data
      const priceData = await marketDataService.getHistoricalData(symbol, timeframe, 200);
      const values = priceData.data?.values || priceData.values || [];
      if (!values.length) throw new APIError(404, 'No price data found');

      // 2. Find swings
      const swings = this.findSwings(values);

      // 3. Detect market structure
      const marketStructure = this.detectMarketStructure(swings);

      // 4. Detect order blocks
      const orderBlocks = this.detectOrderBlocks(values, marketStructure);

      // 5. Detect fair value gaps
      const fairValueGaps = this.detectFVG(values);

      // 6. Detect liquidity pools
      const liquidityPools = this.detectLiquidityPools(swings);

      // 7. Detect mitigation blocks
      const mitigationBlocks = this.detectMitigationBlocks(values, orderBlocks);

      return { marketStructure, orderBlocks, fairValueGaps, liquidityPools, mitigationBlocks };
    } catch (error) {
      logger.error('Error in SMC/ICT detection:', error);
      throw new APIError(500, 'Error detecting SMC/ICT structures');
    }
  }
}

export default new TechnicalAnalysisService(); 