/**
 * Mock Data Generator
 * 
 * Provides realistic-looking mock data for testing and development
 * when external APIs are unavailable or rate-limited.
 */

/**
 * Generate random mock market data
 * @param {Object} params - Parameters for data generation
 * @returns {Array} - Array of market data candles
 */
function generateMockData(params) {
  const { symbol, timeframe = 'D1', limit = 100 } = params;
  
  console.log(`Generating mock data for ${symbol} (${timeframe}), ${limit} candles`);
  
  const result = [];
  const now = new Date();
  let currentPrice = getBasePrice(symbol);
  let volatility = getVolatility(symbol);
  
  // Generate data points working backward from current time
  for (let i = 0; i < limit; i++) {
    // Calculate timestamp based on timeframe
    const timestamp = getTimestampForTimeframe(now, timeframe, i);
    
    // Generate random price movement
    const changePercent = (Math.random() - 0.5) * volatility;
    const change = currentPrice * changePercent;
    
    // Calculate OHLC values
    const close = currentPrice;
    const open = currentPrice - change;
    const high = Math.max(open, close) + Math.abs(change) * Math.random();
    const low = Math.min(open, close) - Math.abs(change) * Math.random();
    
    // Generate volume
    const volume = Math.floor(getBaseVolume(symbol) * (0.5 + Math.random()));
    
    // Add the data point
    result.unshift({
      timestamp: timestamp.toISOString(),
      open: parseFloat(open.toFixed(2)),
      high: parseFloat(high.toFixed(2)),
      low: parseFloat(low.toFixed(2)),
      close: parseFloat(close.toFixed(2)),
      volume
    });
    
    // Set current price for next iteration
    currentPrice = open;
  }
  
  return result;
}

/**
 * Get a realistic base price for a symbol
 * @param {string} symbol - The trading symbol
 * @returns {number} - Base price
 */
function getBasePrice(symbol) {
  // Common stock prices
  const stockPrices = {
    AAPL: 170.00,
    MSFT: 350.00,
    GOOGL: 140.00,
    AMZN: 130.00,
    TSLA: 250.00,
    NVDA: 800.00,
    META: 370.00,
    NFLX: 600.00
  };
  
  // Forex rates
  const forexRates = {
    EURUSD: 1.10,
    GBPUSD: 1.30,
    USDJPY: 145.00,
    AUDUSD: 0.67,
    USDCAD: 1.35,
    USDCHF: 0.90
  };
  
  // Crypto prices
  const cryptoPrices = {
    BTCUSD: 60000.00,
    ETHUSD: 3500.00,
    LTCUSD: 80.00,
    XRPUSD: 0.50,
    ADAUSD: 0.40
  };
  
  // Commodity prices
  const commodityPrices = {
    GOLD: 2000.00,
    SILVER: 25.00,
    OIL: 75.00,
    NATGAS: 3.00,
    COPPER: 4.00
  };
  
  // Try to find the symbol in our hardcoded lists
  if (stockPrices[symbol]) return stockPrices[symbol];
  if (forexRates[symbol]) return forexRates[symbol];
  if (cryptoPrices[symbol]) return cryptoPrices[symbol];
  if (commodityPrices[symbol]) return commodityPrices[symbol];
  
  // For unknown symbols, generate a reasonable price
  if (symbol.includes('USD') && symbol.length === 6) {
    // Forex pair
    return 0.5 + Math.random() * 1.5;
  } else if (symbol.includes('USD') && symbol.length > 6) {
    // Probably crypto
    return Math.random() * 1000;
  } else {
    // Probably a stock
    return 10 + Math.random() * 190;
  }
}

/**
 * Get an appropriate volatility for a symbol
 * @param {string} symbol - The trading symbol
 * @returns {number} - Volatility as a decimal (0.01 = 1%)
 */
function getVolatility(symbol) {
  // Crypto is most volatile, then commodities, then stocks, then forex
  if (symbol.includes('BTC') || symbol.includes('ETH')) {
    return 0.05; // 5% daily movement
  } else if (symbol.includes('USD') && symbol.length > 6) {
    return 0.04; // 4% daily movement for other crypto
  } else if (['GOLD', 'SILVER', 'OIL', 'NATGAS', 'COPPER'].includes(symbol)) {
    return 0.02; // 2% daily movement for commodities
  } else if (symbol.includes('USD') && symbol.length === 6) {
    return 0.005; // 0.5% daily movement for forex
  } else {
    return 0.015; // 1.5% daily movement for stocks
  }
}

/**
 * Get a base volume for a symbol
 * @param {string} symbol - The trading symbol
 * @returns {number} - Base volume
 */
function getBaseVolume(symbol) {
  // Crypto typically has higher volume
  if (symbol.includes('BTC') || symbol.includes('ETH')) {
    return 5000000;
  } else if (symbol.includes('USD') && symbol.length > 6) {
    return 1000000;
  } else if (['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA'].includes(symbol)) {
    return 10000000; // High volume stocks
  } else if (symbol.includes('USD') && symbol.length === 6) {
    return 100000000; // Forex has very high volume
  } else {
    return 2000000; // Average stock volume
  }
}

/**
 * Calculate a timestamp based on timeframe and offset
 * @param {Date} now - Current timestamp
 * @param {string} timeframe - Timeframe code
 * @param {number} offset - Number of periods to go back
 * @returns {Date} - Calculated timestamp
 */
function getTimestampForTimeframe(now, timeframe, offset) {
  const date = new Date(now);
  
  switch (timeframe) {
    case 'M1':
      date.setMinutes(date.getMinutes() - offset);
      break;
    case 'M5':
      date.setMinutes(date.getMinutes() - (offset * 5));
      break;
    case 'M15':
      date.setMinutes(date.getMinutes() - (offset * 15));
      break;
    case 'M30':
      date.setMinutes(date.getMinutes() - (offset * 30));
      break;
    case 'H1':
      date.setHours(date.getHours() - offset);
      break;
    case 'H4':
      date.setHours(date.getHours() - (offset * 4));
      break;
    case 'D1':
      date.setDate(date.getDate() - offset);
      break;
    case 'W1':
      date.setDate(date.getDate() - (offset * 7));
      break;
    case 'MN':
      date.setMonth(date.getMonth() - offset);
      break;
    default:
      date.setDate(date.getDate() - offset);
  }
  
  return date;
}

module.exports = {
  generateMockData
};
