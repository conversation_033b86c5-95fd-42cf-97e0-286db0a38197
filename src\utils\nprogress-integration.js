/**
 * NProgress Integration for Trading Signals App
 * 
 * This file integrates NProgress.js for page loading progress indication.
 */

// Create NProgress object if it doesn't exist
if (typeof NProgress === 'undefined') {
  window.NProgress = {
    start: function() {
      console.log('NProgress start called, but NProgress is not loaded');
      // Create a simple progress bar as fallback
      if (!document.getElementById('nprogress-fallback')) {
        const progressBar = document.createElement('div');
        progressBar.id = 'nprogress-fallback';
        progressBar.style.position = 'fixed';
        progressBar.style.top = '0';
        progressBar.style.left = '0';
        progressBar.style.height = '3px';
        progressBar.style.width = '0%';
        progressBar.style.backgroundColor = '#29d';
        progressBar.style.zIndex = '9999';
        progressBar.style.transition = 'width 0.3s ease';
        document.body.appendChild(progressBar);
        
        // Animate the progress bar
        setTimeout(() => {
          progressBar.style.width = '30%';
          setTimeout(() => {
            progressBar.style.width = '60%';
          }, 300);
        }, 100);
      }
    },
    done: function() {
      console.log('NProgress done called, but NProgress is not loaded');
      // Complete the fallback progress bar
      const progressBar = document.getElementById('nprogress-fallback');
      if (progressBar) {
        progressBar.style.width = '100%';
        setTimeout(() => {
          progressBar.style.opacity = '0';
          setTimeout(() => {
            progressBar.remove();
          }, 300);
        }, 500);
      }
    }
  };
}

// Initialize NProgress
document.addEventListener('DOMContentLoaded', function() {
  console.log('Initializing NProgress integration');
  
  // Start NProgress when navigation starts
  window.addEventListener('beforeunload', function() {
    NProgress.start();
  });
  
  // Complete NProgress when page is fully loaded
  window.addEventListener('load', function() {
    NProgress.done();
  });
  
  // Handle AJAX requests
  const originalXHR = window.XMLHttpRequest;
  let activeRequests = 0;
  
  window.XMLHttpRequest = function() {
    const xhr = new originalXHR();
    
    xhr.addEventListener('loadstart', function() {
      if (activeRequests === 0) {
        NProgress.start();
      }
      activeRequests++;
    });
    
    xhr.addEventListener('loadend', function() {
      activeRequests--;
      if (activeRequests === 0) {
        NProgress.done();
      }
    });
    
    return xhr;
  };
  
  // Handle fetch requests
  const originalFetch = window.fetch;
  
  window.fetch = function() {
    if (activeRequests === 0) {
      NProgress.start();
    }
    activeRequests++;
    
    return originalFetch.apply(this, arguments)
      .then(function(response) {
        activeRequests--;
        if (activeRequests === 0) {
          NProgress.done();
        }
        return response;
      })
      .catch(function(error) {
        activeRequests--;
        if (activeRequests === 0) {
          NProgress.done();
        }
        throw error;
      });
  };
  
  // Add NProgress CSS if not already present
  if (!document.getElementById('nprogress-css')) {
    const style = document.createElement('style');
    style.id = 'nprogress-css';
    style.textContent = `
      #nprogress {
        pointer-events: none;
      }
      #nprogress .bar {
        background: #29d;
        position: fixed;
        z-index: 1031;
        top: 0;
        left: 0;
        width: 100%;
        height: 3px;
      }
      #nprogress .peg {
        display: block;
        position: absolute;
        right: 0px;
        width: 100px;
        height: 100%;
        box-shadow: 0 0 10px #29d, 0 0 5px #29d;
        opacity: 1.0;
        transform: rotate(3deg) translate(0px, -4px);
      }
    `;
    document.head.appendChild(style);
  }
  
  // Start NProgress for initial page load
  NProgress.start();
  
  // Complete NProgress when page is fully loaded
  setTimeout(function() {
    NProgress.done();
  }, 500);
});

console.log('NProgress integration loaded');
