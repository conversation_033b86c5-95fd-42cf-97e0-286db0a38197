import React, { useState, useEffect, useRef } from 'react';
import Modal from './ui/Modal.jsx';
import Button from './ui/Button.jsx';

const fetchUser = async () => {
  const res = await fetch('/api/v1/auth/me', { credentials: 'include' });
  const data = await res.json();
  return data.data.user;
};

const updateUser = async (payload) => {
  const res = await fetch('/api/v1/auth/me', {
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    credentials: 'include',
    body: JSON.stringify(payload),
  });
  return res.json();
};

const setup2FA = async () => {
  const res = await fetch('/api/v1/auth/2fa/setup', {
    method: 'POST',
    credentials: 'include',
  });
  return res.json();
};

const verify2FA = async (token) => {
  const res = await fetch('/api/v1/auth/2fa/verify', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    credentials: 'include',
    body: JSON.stringify({ token }),
  });
  return res.json();
};

const disable2FA = async () => {
  const res = await fetch('/api/v1/auth/2fa/disable', {
    method: 'POST',
    credentials: 'include',
  });
  return res.json();
};

const exportUserData = async () => {
  const res = await fetch('/api/v1/auth/export', {
    method: 'GET',
    credentials: 'include',
  });
  const blob = await res.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'user-data.json';
  document.body.appendChild(a);
  a.click();
  a.remove();
  window.URL.revokeObjectURL(url);
};

const deleteAccount = async () => {
  const res = await fetch('/api/v1/auth/me', {
    method: 'DELETE',
    credentials: 'include',
  });
  return res.json();
};

export default function ProfileModal({ open, onClose }) {
  const [user, setUser] = useState(null);
  const [form, setForm] = useState({ name: '', password: '', avatar: '', preferences: { theme: 'light', notifications: true } });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [show2FA, setShow2FA] = useState(false);
  const [qr, setQr] = useState('');
  const [twoFAToken, setTwoFAToken] = useState('');
  const [twoFAStatus, setTwoFAStatus] = useState('');
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [premiumLoading, setPremiumLoading] = useState(false);
  const [premiumError, setPremiumError] = useState('');
  const fileInputRef = useRef();

  useEffect(() => {
    if (open) {
      fetchUser().then(u => {
        setUser(u);
        setForm({
          name: u.name || '',
          password: '',
          avatar: u.avatar || '',
          preferences: { theme: u.preferences?.theme || 'light', notifications: u.preferences?.notifications !== false },
        });
        setShow2FA(false);
        setQr('');
        setTwoFAToken('');
        setTwoFAStatus('');
        setConfirmDelete(false);
      });
    }
  }, [open]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    if (name.startsWith('preferences.')) {
      const key = name.split('.')[1];
      setForm(f => ({ ...f, preferences: { ...f.preferences, [key]: type === 'checkbox' ? checked : value } }));
    } else {
      setForm(f => ({ ...f, [name]: value }));
    }
  };

  const handleAvatarUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = (ev) => {
      setForm(f => ({ ...f, avatar: ev.target.result }));
    };
    reader.readAsDataURL(file);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    const payload = {
      name: form.name,
      avatar: form.avatar,
      preferences: form.preferences,
    };
    if (form.password) payload.password = form.password;
    const res = await updateUser(payload);
    setLoading(false);
    if (res.status === 'success') {
      onClose();
    } else {
      setError(res.message || 'Failed to update profile');
    }
  };

  const handleSetup2FA = async () => {
    setTwoFAStatus('');
    const res = await setup2FA();
    if (res.status === 'success') {
      setQr(res.data.qr);
      setShow2FA(true);
    } else {
      setTwoFAStatus(res.message || 'Failed to setup 2FA');
    }
  };

  const handleVerify2FA = async () => {
    setTwoFAStatus('');
    const res = await verify2FA(twoFAToken);
    if (res.status === 'success') {
      setTwoFAStatus('2FA enabled!');
      setShow2FA(false);
      setQr('');
      setTwoFAToken('');
      setUser({ ...user, twoFactorEnabled: true });
    } else {
      setTwoFAStatus(res.message || 'Invalid code');
    }
  };

  const handleDisable2FA = async () => {
    setTwoFAStatus('');
    const res = await disable2FA();
    if (res.status === 'success') {
      setTwoFAStatus('2FA disabled');
      setUser({ ...user, twoFactorEnabled: false });
    } else {
      setTwoFAStatus(res.message || 'Failed to disable 2FA');
    }
  };

  const handleExport = async () => {
    await exportUserData();
  };

  const handleDelete = async () => {
    setLoading(true);
    setError('');
    const res = await deleteAccount();
    setLoading(false);
    if (res.status === 'success') {
      window.location.href = '/';
    } else {
      setError(res.message || 'Failed to delete account');
    }
  };

  const handleGoPremium = async () => {
    setPremiumLoading(true);
    setPremiumError('');
    try {
      const res = await fetch('/api/v1/billing/create-checkout-session', {
        method: 'POST',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
      });
      const data = await res.json();
      if (data.url) {
        window.location.href = data.url;
      } else {
        setPremiumError(data.message || 'Failed to start upgrade');
      }
    } catch (err) {
      setPremiumError('Failed to start upgrade');
    } finally {
      setPremiumLoading(false);
    }
  };

  if (!open) return null;

  return (
    <Modal open={open} onClose={onClose}>
      <form className="w-full max-w-md p-6" onSubmit={handleSubmit}>
        <h2 className="text-xl font-bold mb-4">Profile & Settings</h2>
        {error && <div className="mb-2 text-red-500 text-sm">{error}</div>}
        <div className="flex flex-col items-center mb-4">
          <img
            src={form.avatar || '/default-avatar.png'}
            alt="Avatar"
            className="w-20 h-20 rounded-full object-cover border mb-2"
          />
          <input
            type="file"
            accept="image/*"
            className="hidden"
            ref={fileInputRef}
            onChange={handleAvatarUpload}
          />
          <Button type="button" size="sm" onClick={() => fileInputRef.current.click()}>
            Upload Avatar
          </Button>
        </div>
        <div className="mb-3">
          <label className="block text-sm font-medium mb-1">Name</label>
          <input
            type="text"
            name="name"
            value={form.name}
            onChange={handleChange}
            className="w-full px-3 py-2 border rounded"
            required
          />
        </div>
        <div className="mb-3">
          <label className="block text-sm font-medium mb-1">Email</label>
          <input
            type="email"
            value={user?.email || ''}
            disabled
            className="w-full px-3 py-2 border rounded bg-gray-100 dark:bg-gray-700 text-gray-500"
          />
        </div>
        <div className="mb-4 flex flex-col items-start gap-2">
          <div className="text-sm">
            <span className="font-semibold">Plan:</span> {user?.plan === 'pro' ? 'Pro' : 'Free'}
          </div>
          {user?.plan !== 'pro' && (
            <Button type="button" onClick={handleGoPremium} loading={premiumLoading}>
              Go Premium
            </Button>
          )}
          {premiumError && <div className="text-xs text-red-500">{premiumError}</div>}
        </div>
        <div className="mb-3">
          <label className="block text-sm font-medium mb-1">New Password</label>
          <input
            type="password"
            name="password"
            value={form.password}
            onChange={handleChange}
            className="w-full px-3 py-2 border rounded"
            placeholder="Leave blank to keep current password"
          />
        </div>
        <div className="mb-3">
          <label className="block text-sm font-medium mb-1">Theme</label>
          <select
            name="preferences.theme"
            value={form.preferences.theme}
            onChange={handleChange}
            className="w-full px-3 py-2 border rounded"
          >
            <option value="light">Light</option>
            <option value="dark">Dark</option>
          </select>
        </div>
        <div className="mb-4 flex items-center">
          <input
            type="checkbox"
            name="preferences.notifications"
            checked={form.preferences.notifications}
            onChange={handleChange}
            className="mr-2"
            id="notifications"
          />
          <label htmlFor="notifications" className="text-sm">Enable notifications</label>
        </div>
        {/* 2FA Section */}
        <div className="mb-4 border-t pt-4">
          <div className="flex items-center justify-between mb-2">
            <span className="font-semibold">Two-Factor Authentication (2FA)</span>
            {user?.twoFactorEnabled ? (
              <Button type="button" size="sm" variant="secondary" onClick={handleDisable2FA}>
                Disable 2FA
              </Button>
            ) : (
              <Button type="button" size="sm" onClick={handleSetup2FA}>
                Setup 2FA
              </Button>
            )}
          </div>
          {user?.twoFactorEnabled && <div className="text-green-600 text-sm mb-2">2FA is enabled on your account.</div>}
          {show2FA && qr && (
            <div className="flex flex-col items-center mb-2">
              <img src={qr} alt="2FA QR Code" className="w-32 h-32 mb-2" />
              <input
                type="text"
                placeholder="Enter code from app"
                value={twoFAToken}
                onChange={e => setTwoFAToken(e.target.value)}
                className="w-full px-3 py-2 border rounded mb-2"
              />
              <Button type="button" size="sm" onClick={handleVerify2FA}>
                Verify & Enable 2FA
              </Button>
            </div>
          )}
          {twoFAStatus && <div className="text-sm text-blue-600 mb-2">{twoFAStatus}</div>}
        </div>
        {/* Data Export & Delete */}
        <div className="mb-4 border-t pt-4 flex flex-col gap-2">
          <Button type="button" variant="secondary" onClick={handleExport}>
            Export My Data
          </Button>
          {!confirmDelete ? (
            <Button type="button" variant="danger" onClick={() => setConfirmDelete(true)}>
              Delete My Account
            </Button>
          ) : (
            <div className="flex flex-col gap-2">
              <div className="text-red-600 text-sm">Are you sure? This cannot be undone.</div>
              <div className="flex gap-2">
                <Button type="button" variant="secondary" onClick={() => setConfirmDelete(false)}>Cancel</Button>
                <Button type="button" variant="danger" onClick={handleDelete} loading={loading}>Yes, Delete</Button>
              </div>
            </div>
          )}
        </div>
        <div className="flex justify-end gap-2">
          <Button type="button" onClick={onClose} variant="secondary">Cancel</Button>
          <Button type="submit" loading={loading}>
            Save
          </Button>
        </div>
        <div className="mt-6 text-center text-xs text-gray-500 dark:text-gray-400">
          <a href="/privacy-policy.html" target="_blank" rel="noopener noreferrer" className="underline mr-4">Privacy Policy</a>
          <a href="/terms.html" target="_blank" rel="noopener noreferrer" className="underline">Terms of Service</a>
        </div>
      </form>
    </Modal>
  );
} 