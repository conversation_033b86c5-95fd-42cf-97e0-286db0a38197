const axios = require('axios');
const logger = console;

/**
 * Financial Modeling Prep (FMP) API client
 * @param {Object} query - Query parameters
 * @param {string} query.type - Market type (stocks, economics, news)
 * @param {string} query.symbol - Market symbol
 * @param {string} query.timeframe - Time frame (D1, W1, MN)
 * @returns {Promise<Object>} - Market data
 */
module.exports = async function fmp(query) {
  // Check if API key is available
  const apiKey = process.env.FMP_API_KEY;
  if (!apiKey) {
    throw new Error('FMP API key is not set');
  }

  let url = '';
  
  try {
    // Format the URL based on query type
    if (query.type === 'stocks') {
      if (query.timeframe === 'D1') {
        url = `https://financialmodelingprep.com/api/v3/historical-price-full/${query.symbol}?apikey=${apiKey}`;
      } else if (query.timeframe === 'W1') {
        url = `https://financialmodelingprep.com/api/v3/historical-price-full/${query.symbol}?apikey=${apiKey}&serietype=line&timeseries=365`;
      } else if (query.timeframe === 'MN') {
        url = `https://financialmodelingprep.com/api/v3/historical-price-full/${query.symbol}?apikey=${apiKey}&serietype=line&timeseries=1000`;
      } else {
        throw new Error(`Unsupported timeframe for FMP stocks: ${query.timeframe}`);
      }
    } else if (query.type === 'economics') {
      url = `https://financialmodelingprep.com/api/v3/economic_calendar?apikey=${apiKey}`;
    } else if (query.type === 'news') {
      url = `https://financialmodelingprep.com/api/v3/stock_news?tickers=${query.symbol}&apikey=${apiKey}&limit=50`;
    } else if (query.type === 'analysis') {
      url = `https://financialmodelingprep.com/api/v3/analyst-stock-recommendations/${query.symbol}?apikey=${apiKey}&limit=100`;
    } else {
      throw new Error(`Unsupported market type for FMP: ${query.type}`);
    }

    logger.info(`Calling FMP API for ${query.type}:${query.symbol}`);
    
    const response = await axios.get(url, {
      timeout: 10000,
      headers: {
        'User-Agent': 'TradingSignalsApp/1.0'
      }
    });

    return formatResponse(response.data, query);
  } catch (error) {
    logger.error('FMP API error:', error.message);
    if (error.response) {
      logger.error('Status:', error.response.status);
      logger.error('Data:', JSON.stringify(error.response.data));
    }
    throw error;
  }
};

/**
 * Format the FMP response to a standard format
 * @param {Object|Array} data - FMP response
 * @param {Object} query - Original query
 * @returns {Object} - Formatted response
 */
function formatResponse(data, query) {
  const result = {
    symbol: query.symbol,
    timeframe: query.timeframe,
    type: query.type,
    source: 'fmp',
    timestamp: new Date().toISOString()
  };

  // Handle different response formats based on query type
  if (query.type === 'stocks') {
    if (!data || !data.historical || !Array.isArray(data.historical)) {
      throw new Error('Invalid or empty stock data from FMP API');
    }
    
    result.data = data.historical.map(bar => ({
      timestamp: bar.date,
      open: bar.open,
      high: bar.high,
      low: bar.low,
      close: bar.close,
      volume: bar.volume
    })).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  } else if (query.type === 'economics') {
    if (!Array.isArray(data)) {
      throw new Error('Invalid economics data from FMP API');
    }
    
    result.observations = data.map(item => ({
      date: item.date,
      indicator: item.event,
      value: item.actual,
      forecast: item.estimate,
      previous: item.previous,
      country: item.country
    }));
  } else if (query.type === 'news') {
    if (!Array.isArray(data)) {
      throw new Error('Invalid news data from FMP API');
    }
    
    result.news = data.map(item => ({
      title: item.title,
      date: item.publishedDate,
      summary: item.text,
      url: item.url,
      source: item.site
    }));
  } else if (query.type === 'analysis') {
    if (!Array.isArray(data)) {
      throw new Error('Invalid analysis data from FMP API');
    }
    
    result.analysis = data.map(item => ({
      date: item.date,
      strongBuy: item.strongBuy,
      buy: item.buy,
      hold: item.hold,
      sell: item.sell,
      strongSell: item.strongSell,
      consensus: item.consensus
    }));
  }

  return result;
} 