/**
 * Candlestick Patterns Utility
 * 
 * Provides information and visualization helpers for candlestick patterns
 */

/**
 * Candlestick pattern definitions with descriptions and images
 */
export const CANDLESTICK_PATTERNS = {
  // Single Candlestick Patterns
  doji: {
    name: '<PERSON><PERSON>',
    type: 'neutral',
    description: 'A Doji represents indecision in the market. The open and close prices are very close, indicating a balance between buyers and sellers.',
    interpretation: 'A Doji after an uptrend or downtrend suggests a potential reversal. Confirmation is needed from the next candle.',
    reliability: 'Medium',
    image: '/images/patterns/doji.png'
  },
  hammer: {
    name: '<PERSON>',
    type: 'bullish',
    description: 'A Hammer is a bullish reversal pattern that forms during a downtrend. It has a small body at the upper end of the trading range with a long lower shadow.',
    interpretation: 'The long lower shadow indicates that sellers drove prices lower during the session, but buyers were able to push the price back up by the close. This suggests a potential bullish reversal.',
    reliability: 'High',
    image: '/images/patterns/hammer.png'
  },
  inverted_hammer: {
    name: 'Inverted Hammer',
    type: 'bullish',
    description: 'An Inverted Hammer is a bullish reversal pattern that forms during a downtrend. It has a small body at the lower end of the trading range with a long upper shadow.',
    interpretation: 'The long upper shadow indicates that buyers drove prices higher during the session, but sellers were able to push the price back down. However, the close remained near the open, suggesting a potential bullish reversal.',
    reliability: 'Medium',
    image: '/images/patterns/inverted_hammer.png'
  },
  shooting_star: {
    name: 'Shooting Star',
    type: 'bearish',
    description: 'A Shooting Star is a bearish reversal pattern that forms during an uptrend. It has a small body at the lower end of the trading range with a long upper shadow.',
    interpretation: 'The long upper shadow indicates that buyers drove prices higher during the session, but sellers were able to push the price back down by the close. This suggests a potential bearish reversal.',
    reliability: 'High',
    image: '/images/patterns/shooting_star.png'
  },
  hanging_man: {
    name: 'Hanging Man',
    type: 'bearish',
    description: 'A Hanging Man is a bearish reversal pattern that forms during an uptrend. It has a small body at the upper end of the trading range with a long lower shadow.',
    interpretation: 'The long lower shadow indicates that sellers drove prices lower during the session, but buyers were able to push the price back up. However, this selling pressure during an uptrend suggests a potential bearish reversal.',
    reliability: 'Medium',
    image: '/images/patterns/hanging_man.png'
  },
  
  // Double Candlestick Patterns
  bullish_engulfing: {
    name: 'Bullish Engulfing',
    type: 'bullish',
    description: 'A Bullish Engulfing pattern forms when a small bearish candle is followed by a large bullish candle that completely engulfs the previous candle.',
    interpretation: 'The bullish engulfing pattern shows that sellers were in control during the first day, but buyers took control on the second day, overwhelming the sellers. This suggests a potential bullish reversal.',
    reliability: 'High',
    image: '/images/patterns/bullish_engulfing.png'
  },
  bearish_engulfing: {
    name: 'Bearish Engulfing',
    type: 'bearish',
    description: 'A Bearish Engulfing pattern forms when a small bullish candle is followed by a large bearish candle that completely engulfs the previous candle.',
    interpretation: 'The bearish engulfing pattern shows that buyers were in control during the first day, but sellers took control on the second day, overwhelming the buyers. This suggests a potential bearish reversal.',
    reliability: 'High',
    image: '/images/patterns/bearish_engulfing.png'
  },
  piercing_line: {
    name: 'Piercing Line',
    type: 'bullish',
    description: 'A Piercing Line is a bullish reversal pattern where a bearish candle is followed by a bullish candle that opens below the previous low and closes above the midpoint of the previous candle.',
    interpretation: 'The piercing line shows that despite opening lower, buyers were able to push the price up significantly, closing above the midpoint of the previous bearish candle. This suggests a potential bullish reversal.',
    reliability: 'Medium',
    image: '/images/patterns/piercing_line.png'
  },
  dark_cloud_cover: {
    name: 'Dark Cloud Cover',
    type: 'bearish',
    description: 'A Dark Cloud Cover is a bearish reversal pattern where a bullish candle is followed by a bearish candle that opens above the previous high and closes below the midpoint of the previous candle.',
    interpretation: 'The dark cloud cover shows that despite opening higher, sellers were able to push the price down significantly, closing below the midpoint of the previous bullish candle. This suggests a potential bearish reversal.',
    reliability: 'Medium',
    image: '/images/patterns/dark_cloud_cover.png'
  },
  bullish_harami: {
    name: 'Bullish Harami',
    type: 'bullish',
    description: 'A Bullish Harami is a reversal pattern where a large bearish candle is followed by a smaller bullish candle that is completely contained within the body of the previous candle.',
    interpretation: 'The bullish harami shows that the downward momentum has weakened, as the second day\'s trading range was much smaller. This suggests a potential bullish reversal.',
    reliability: 'Medium',
    image: '/images/patterns/bullish_harami.png'
  },
  bearish_harami: {
    name: 'Bearish Harami',
    type: 'bearish',
    description: 'A Bearish Harami is a reversal pattern where a large bullish candle is followed by a smaller bearish candle that is completely contained within the body of the previous candle.',
    interpretation: 'The bearish harami shows that the upward momentum has weakened, as the second day\'s trading range was much smaller. This suggests a potential bearish reversal.',
    reliability: 'Medium',
    image: '/images/patterns/bearish_harami.png'
  },
  
  // Triple Candlestick Patterns
  morning_star: {
    name: 'Morning Star',
    type: 'bullish',
    description: 'A Morning Star is a bullish reversal pattern consisting of three candles: a large bearish candle, a small-bodied candle, and a large bullish candle.',
    interpretation: 'The morning star shows a transition from bearish to bullish sentiment. The small middle candle represents indecision, followed by a strong bullish candle confirming the reversal.',
    reliability: 'High',
    image: '/images/patterns/morning_star.png'
  },
  evening_star: {
    name: 'Evening Star',
    type: 'bearish',
    description: 'An Evening Star is a bearish reversal pattern consisting of three candles: a large bullish candle, a small-bodied candle, and a large bearish candle.',
    interpretation: 'The evening star shows a transition from bullish to bearish sentiment. The small middle candle represents indecision, followed by a strong bearish candle confirming the reversal.',
    reliability: 'High',
    image: '/images/patterns/evening_star.png'
  },
  three_white_soldiers: {
    name: 'Three White Soldiers',
    type: 'bullish',
    description: 'Three White Soldiers is a bullish reversal pattern consisting of three consecutive bullish candles, each closing higher than the previous and opening within the previous candle\'s body.',
    interpretation: 'The three white soldiers show a strong bullish momentum, with buyers in control for three consecutive sessions. This suggests a potential bullish reversal or continuation.',
    reliability: 'High',
    image: '/images/patterns/three_white_soldiers.png'
  },
  three_black_crows: {
    name: 'Three Black Crows',
    type: 'bearish',
    description: 'Three Black Crows is a bearish reversal pattern consisting of three consecutive bearish candles, each closing lower than the previous and opening within the previous candle\'s body.',
    interpretation: 'The three black crows show a strong bearish momentum, with sellers in control for three consecutive sessions. This suggests a potential bearish reversal or continuation.',
    reliability: 'High',
    image: '/images/patterns/three_black_crows.png'
  },
  three_inside_up: {
    name: 'Three Inside Up',
    type: 'bullish',
    description: 'Three Inside Up is a bullish reversal pattern consisting of a bearish candle, followed by a bullish harami, and then a third bullish candle that closes above the high of the second candle.',
    interpretation: 'The three inside up pattern starts with a bullish harami (first two candles), which is then confirmed by the third bullish candle. This suggests a potential bullish reversal.',
    reliability: 'Medium',
    image: '/images/patterns/three_inside_up.png'
  },
  three_inside_down: {
    name: 'Three Inside Down',
    type: 'bearish',
    description: 'Three Inside Down is a bearish reversal pattern consisting of a bullish candle, followed by a bearish harami, and then a third bearish candle that closes below the low of the second candle.',
    interpretation: 'The three inside down pattern starts with a bearish harami (first two candles), which is then confirmed by the third bearish candle. This suggests a potential bearish reversal.',
    reliability: 'Medium',
    image: '/images/patterns/three_inside_down.png'
  }
};

/**
 * Chart pattern definitions with descriptions and images
 */
export const CHART_PATTERNS = {
  head_and_shoulders: {
    name: 'Head and Shoulders',
    type: 'bearish',
    description: 'A Head and Shoulders pattern consists of three peaks, with the middle peak (head) being higher than the two surrounding peaks (shoulders).',
    interpretation: 'This pattern indicates a bullish-to-bearish trend reversal. The neckline serves as a support level, and a break below it confirms the pattern.',
    reliability: 'High',
    image: '/images/patterns/head_and_shoulders.png'
  },
  inverse_head_and_shoulders: {
    name: 'Inverse Head and Shoulders',
    type: 'bullish',
    description: 'An Inverse Head and Shoulders pattern consists of three troughs, with the middle trough (head) being lower than the two surrounding troughs (shoulders).',
    interpretation: 'This pattern indicates a bearish-to-bullish trend reversal. The neckline serves as a resistance level, and a break above it confirms the pattern.',
    reliability: 'High',
    image: '/images/patterns/inverse_head_and_shoulders.png'
  },
  double_top: {
    name: 'Double Top',
    type: 'bearish',
    description: 'A Double Top pattern consists of two peaks at approximately the same price level, with a moderate trough in between.',
    interpretation: 'This pattern indicates a bullish-to-bearish trend reversal. The trough between the peaks serves as a support level, and a break below it confirms the pattern.',
    reliability: 'High',
    image: '/images/patterns/double_top.png'
  },
  double_bottom: {
    name: 'Double Bottom',
    type: 'bullish',
    description: 'A Double Bottom pattern consists of two troughs at approximately the same price level, with a moderate peak in between.',
    interpretation: 'This pattern indicates a bearish-to-bullish trend reversal. The peak between the troughs serves as a resistance level, and a break above it confirms the pattern.',
    reliability: 'High',
    image: '/images/patterns/double_bottom.png'
  },
  ascending_triangle: {
    name: 'Ascending Triangle',
    type: 'bullish',
    description: 'An Ascending Triangle is a continuation pattern characterized by a flat upper resistance line and an upward-sloping lower support line.',
    interpretation: 'This pattern indicates a bullish continuation. A break above the upper resistance line confirms the pattern.',
    reliability: 'Medium',
    image: '/images/patterns/ascending_triangle.png'
  },
  descending_triangle: {
    name: 'Descending Triangle',
    type: 'bearish',
    description: 'A Descending Triangle is a continuation pattern characterized by a flat lower support line and a downward-sloping upper resistance line.',
    interpretation: 'This pattern indicates a bearish continuation. A break below the lower support line confirms the pattern.',
    reliability: 'Medium',
    image: '/images/patterns/descending_triangle.png'
  },
  symmetrical_triangle: {
    name: 'Symmetrical Triangle',
    type: 'neutral',
    description: 'A Symmetrical Triangle is a continuation pattern characterized by converging trendlines, with prices making lower highs and higher lows.',
    interpretation: 'This pattern indicates a period of consolidation before the price continues in the direction of the prior trend. A break above or below the triangle confirms the direction.',
    reliability: 'Medium',
    image: '/images/patterns/symmetrical_triangle.png'
  },
  flag: {
    name: 'Flag',
    type: 'continuation',
    description: 'A Flag is a continuation pattern that appears as a small rectangle pattern that slopes against the prevailing trend.',
    interpretation: 'This pattern indicates a brief consolidation before the price continues in the direction of the prior trend. A break in the direction of the trend confirms the pattern.',
    reliability: 'Medium',
    image: '/images/patterns/flag.png'
  },
  pennant: {
    name: 'Pennant',
    type: 'continuation',
    description: 'A Pennant is a continuation pattern that appears as a small symmetrical triangle after a strong price movement.',
    interpretation: 'This pattern indicates a brief consolidation before the price continues in the direction of the prior trend. A break in the direction of the trend confirms the pattern.',
    reliability: 'Medium',
    image: '/images/patterns/pennant.png'
  },
  wedge: {
    name: 'Wedge',
    type: 'reversal',
    description: 'A Wedge is a pattern formed by converging trendlines, similar to a symmetrical triangle but with both lines sloping in the same direction.',
    interpretation: 'A rising wedge in an uptrend is bearish, while a falling wedge in a downtrend is bullish. A break of the trendline confirms the reversal.',
    reliability: 'Medium',
    image: '/images/patterns/wedge.png'
  }
};

/**
 * Get pattern information by pattern ID
 * 
 * @param {string} patternId - The pattern identifier
 * @returns {Object|null} - Pattern information or null if not found
 */
export const getPatternInfo = (patternId) => {
  return CANDLESTICK_PATTERNS[patternId] || CHART_PATTERNS[patternId] || null;
};

/**
 * Get all patterns of a specific type
 * 
 * @param {string} type - Pattern type ('bullish', 'bearish', 'neutral', 'continuation', 'reversal')
 * @returns {Array} - Array of pattern objects matching the type
 */
export const getPatternsByType = (type) => {
  const patterns = [];
  
  // Check candlestick patterns
  Object.entries(CANDLESTICK_PATTERNS).forEach(([id, pattern]) => {
    if (pattern.type === type) {
      patterns.push({ id, ...pattern });
    }
  });
  
  // Check chart patterns
  Object.entries(CHART_PATTERNS).forEach(([id, pattern]) => {
    if (pattern.type === type) {
      patterns.push({ id, ...pattern });
    }
  });
  
  return patterns;
};

/**
 * Get all available pattern types
 * 
 * @returns {Array} - Array of pattern types
 */
export const getPatternTypes = () => {
  const types = new Set();
  
  // Get types from candlestick patterns
  Object.values(CANDLESTICK_PATTERNS).forEach(pattern => {
    types.add(pattern.type);
  });
  
  // Get types from chart patterns
  Object.values(CHART_PATTERNS).forEach(pattern => {
    types.add(pattern.type);
  });
  
  return Array.from(types);
};

export default {
  CANDLESTICK_PATTERNS,
  CHART_PATTERNS,
  getPatternInfo,
  getPatternsByType,
  getPatternTypes
};
