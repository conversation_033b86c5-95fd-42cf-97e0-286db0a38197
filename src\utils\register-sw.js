/**
 * Service Worker Registration for Trading Signals App
 *
 * This file registers the service worker for offline functionality
 * and caching.
 */

// Check online status and redirect to offline page if needed
function checkOnlineStatus() {
    if (!navigator.onLine) {
        console.log('Browser is offline, redirecting to offline page');
        // Only redirect if not already on the offline page
        if (!window.location.pathname.includes('/offline')) {
            window.location.href = '/offline';
        }
    }
}

// Set default language preference to English if not already set
function setDefaultLanguage() {
    if (!localStorage.getItem('language_preference')) {
        localStorage.setItem('language_preference', 'en');
        console.log('Default language set to English');
    }
}

// Check online status and set default language on page load
window.addEventListener('load', () => {
    checkOnlineStatus();
    setDefaultLanguage();
});

// Listen for online/offline events
window.addEventListener('online', () => {
    console.log('Browser is now online');
    // If on offline page, redirect to home
    if (window.location.pathname.includes('/offline')) {
        window.location.href = '/';
    }
});

window.addEventListener('offline', () => {
    console.log('Browser is now offline');
    checkOnlineStatus();
});

// Check if service workers are supported
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/service-worker.js')
            .then(registration => {
                console.log('Service Worker registered with scope:', registration.scope);

                // Check for updates
                registration.update();

                // Set up periodic updates
                setInterval(() => {
                    registration.update();
                    console.log('Service Worker update check');
                }, 60 * 60 * 1000); // Check every hour
            })
            .catch(error => {
                console.error('Service Worker registration failed:', error);
            });
    });

    // Listen for service worker updates
    navigator.serviceWorker.addEventListener('controllerchange', () => {
        console.log('Service Worker updated, reloading page for new version');
        window.location.reload();
    });

    // Listen for messages from service worker
    navigator.serviceWorker.addEventListener('message', event => {
        console.log('Message from Service Worker:', event.data);

        // Handle different message types
        if (event.data.type === 'economic-calendar-updated') {
            // Update economic calendar UI
            if (window.economicCalendar && typeof window.economicCalendar.refreshEconomicCalendar === 'function') {
                window.economicCalendar.refreshEconomicCalendar();
            }
        } else if (event.data.type === 'offline') {
            // Handle offline notification from service worker
            checkOnlineStatus();
        }
    });

    // Request background sync for economic calendar
    function requestEconomicCalendarSync() {
        navigator.serviceWorker.ready
            .then(registration => {
                return registration.sync.register('sync-economic-calendar');
            })
            .then(() => {
                console.log('Economic calendar background sync registered');
            })
            .catch(error => {
                console.error('Background sync registration failed:', error);
            });
    }

    // Request background sync for trading signals
    function requestTradingSignalsSync() {
        navigator.serviceWorker.ready
            .then(registration => {
                return registration.sync.register('sync-trading-signals');
            })
            .then(() => {
                console.log('Trading signals background sync registered');
            })
            .catch(error => {
                console.error('Background sync registration failed:', error);
            });
    }

    // Export functions for use in other modules
    window.serviceWorkerUtils = {
        requestEconomicCalendarSync,
        requestTradingSignalsSync,
        checkOnlineStatus,
        setDefaultLanguage
    };
} else {
    console.warn('Service Workers are not supported in this browser');
    // Still check online status and set default language even if service workers aren't supported
    checkOnlineStatus();
    setDefaultLanguage();
}
