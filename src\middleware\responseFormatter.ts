/**
 * Response Formatter Middleware
 * 
 * Standardizes all API responses to ensure consistency across the application.
 * Implements the APIResponse<T> interface defined in src/types/api.ts
 * 
 * @version 1.0.0
 */

import { Request, Response, NextFunction } from 'express';
import {
  APIResponse,
  PaginatedAPIResponse,
  APIErrorResponse,
  ValidationError,
  ResponseMetadata,
  PaginationMetadata,
  generateRequestId,
  formatTimestamp
} from '../types';
import logger from '../utils/logger';

// ============================================================================
// RESPONSE FORMATTER INTERFACE
// ============================================================================

interface ResponseFormatterOptions {
  includeRequestId?: boolean;
  includeProcessingTime?: boolean;
  includeVersion?: boolean;
  defaultVersion?: string;
}

// ============================================================================
// EXTENDED RESPONSE INTERFACE
// ============================================================================

declare global {
  namespace Express {
    interface Response {
      success<T>(data: T, message?: string, meta?: Partial<ResponseMetadata>): Response;
      paginated<T>(
        data: T[], 
        pagination: PaginationMetadata, 
        message?: string,
        meta?: Partial<ResponseMetadata>
      ): Response;
      error(
        code: string, 
        message: string, 
        statusCode?: number,
        details?: any,
        suggestion?: string
      ): Response;
      validationError(errors: ValidationError[], message?: string): Response;
    }
    
    interface Request {
      requestId: string;
      startTime: number;
    }
  }
}

// ============================================================================
// RESPONSE FORMATTER MIDDLEWARE
// ============================================================================

export function responseFormatter(options: ResponseFormatterOptions = {}) {
  const {
    includeRequestId = true,
    includeProcessingTime = true,
    includeVersion = true,
    defaultVersion = '1.0.0'
  } = options;

  return (req: Request, res: Response, next: NextFunction) => {
    // Add request ID and start time for tracking
    if (includeRequestId && !req.requestId) {
      req.requestId = generateRequestId();
    }
    
    if (includeProcessingTime && !req.startTime) {
      req.startTime = Date.now();
    }

    // ========================================================================
    // SUCCESS RESPONSE METHOD
    // ========================================================================
    
    res.success = function<T>(
      data: T, 
      message?: string, 
      meta: Partial<ResponseMetadata> = {}
    ): Response {
      const processingTime = includeProcessingTime ? Date.now() - req.startTime : undefined;
      
      const response: APIResponse<T> = {
        status: 'success',
        data,
        message,
        meta: {
          timestamp: formatTimestamp(),
          requestId: includeRequestId ? req.requestId : undefined,
          version: includeVersion ? defaultVersion : undefined,
          processingTime,
          ...meta
        } as ResponseMetadata
      };

      // Log successful response
      logger.info('API Success Response', {
        requestId: req.requestId,
        method: req.method,
        path: req.path,
        statusCode: res.statusCode || 200,
        processingTime,
        dataType: typeof data,
        message
      });

      return this.json(response);
    };

    // ========================================================================
    // PAGINATED RESPONSE METHOD
    // ========================================================================
    
    res.paginated = function<T>(
      data: T[], 
      pagination: PaginationMetadata, 
      message?: string,
      meta: Partial<ResponseMetadata> = {}
    ): Response {
      const processingTime = includeProcessingTime ? Date.now() - req.startTime : undefined;
      
      const response: PaginatedAPIResponse<T> = {
        status: 'success',
        data,
        message,
        meta: {
          timestamp: formatTimestamp(),
          requestId: includeRequestId ? req.requestId : undefined,
          version: includeVersion ? defaultVersion : undefined,
          processingTime,
          pagination,
          ...meta
        } as ResponseMetadata & { pagination: PaginationMetadata }
      };

      // Log paginated response
      logger.info('API Paginated Response', {
        requestId: req.requestId,
        method: req.method,
        path: req.path,
        statusCode: res.statusCode || 200,
        processingTime,
        itemCount: data.length,
        pagination,
        message
      });

      return this.json(response);
    };

    // ========================================================================
    // ERROR RESPONSE METHOD
    // ========================================================================
    
    res.error = function(
      code: string, 
      message: string, 
      statusCode: number = 500,
      details?: any,
      suggestion?: string
    ): Response {
      const processingTime = includeProcessingTime ? Date.now() - req.startTime : undefined;
      
      const response: APIErrorResponse = {
        status: 'error',
        error: {
          code,
          message,
          details,
          suggestion
        },
        meta: {
          timestamp: formatTimestamp(),
          requestId: includeRequestId ? req.requestId : undefined,
          version: includeVersion ? defaultVersion : undefined,
          processingTime
        } as ResponseMetadata
      };

      // Log error response
      logger.error('API Error Response', {
        requestId: req.requestId,
        method: req.method,
        path: req.path,
        statusCode,
        processingTime,
        errorCode: code,
        errorMessage: message,
        details,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });

      return this.status(statusCode).json(response);
    };

    // ========================================================================
    // VALIDATION ERROR RESPONSE METHOD
    // ========================================================================
    
    res.validationError = function(
      errors: ValidationError[], 
      message: string = 'Validation failed'
    ): Response {
      const processingTime = includeProcessingTime ? Date.now() - req.startTime : undefined;
      
      const response: APIResponse<never> = {
        status: 'error',
        message,
        errors,
        meta: {
          timestamp: formatTimestamp(),
          requestId: includeRequestId ? req.requestId : undefined,
          version: includeVersion ? defaultVersion : undefined,
          processingTime
        } as ResponseMetadata
      };

      // Log validation error
      logger.warn('API Validation Error', {
        requestId: req.requestId,
        method: req.method,
        path: req.path,
        statusCode: 400,
        processingTime,
        validationErrors: errors,
        message
      });

      return this.status(400).json(response);
    };

    next();
  };
}

// ============================================================================
// PAGINATION HELPER FUNCTIONS
// ============================================================================

export function createPaginationMetadata(
  page: number,
  limit: number,
  total: number
): PaginationMetadata {
  const totalPages = Math.ceil(total / limit);
  
  return {
    page,
    limit,
    total,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  };
}

export function parsePaginationParams(req: Request): { page: number; limit: number } {
  const page = Math.max(1, parseInt(req.query.page as string) || 1);
  const limit = Math.min(100, Math.max(1, parseInt(req.query.limit as string) || 20));
  
  return { page, limit };
}

// ============================================================================
// ERROR CODE MAPPING
// ============================================================================

export const HTTP_STATUS_CODES = {
  // Success
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,
  
  // Client Errors
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  
  // Server Errors
  INTERNAL_SERVER_ERROR: 500,
  NOT_IMPLEMENTED: 501,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504
} as const;

export const ERROR_CODE_TO_HTTP_STATUS: Record<string, number> = {
  // Authentication
  'AUTH_REQUIRED': HTTP_STATUS_CODES.UNAUTHORIZED,
  'AUTH_INVALID_TOKEN': HTTP_STATUS_CODES.UNAUTHORIZED,
  'AUTH_TOKEN_EXPIRED': HTTP_STATUS_CODES.UNAUTHORIZED,
  'AUTH_INVALID_CREDENTIALS': HTTP_STATUS_CODES.UNAUTHORIZED,
  
  // Authorization
  'INSUFFICIENT_PERMISSIONS': HTTP_STATUS_CODES.FORBIDDEN,
  'FEATURE_NOT_AVAILABLE': HTTP_STATUS_CODES.FORBIDDEN,
  'SUBSCRIPTION_REQUIRED': HTTP_STATUS_CODES.FORBIDDEN,
  
  // Validation
  'VALIDATION_ERROR': HTTP_STATUS_CODES.BAD_REQUEST,
  'INVALID_INPUT': HTTP_STATUS_CODES.BAD_REQUEST,
  'REQUIRED_FIELD_MISSING': HTTP_STATUS_CODES.BAD_REQUEST,
  'INVALID_FORMAT': HTTP_STATUS_CODES.BAD_REQUEST,
  
  // Resources
  'RESOURCE_NOT_FOUND': HTTP_STATUS_CODES.NOT_FOUND,
  'RESOURCE_ALREADY_EXISTS': HTTP_STATUS_CODES.CONFLICT,
  'RESOURCE_CONFLICT': HTTP_STATUS_CODES.CONFLICT,
  
  // Rate limiting
  'RATE_LIMIT_EXCEEDED': HTTP_STATUS_CODES.TOO_MANY_REQUESTS,
  'TOO_MANY_REQUESTS': HTTP_STATUS_CODES.TOO_MANY_REQUESTS,
  
  // External services
  'EXTERNAL_SERVICE_ERROR': HTTP_STATUS_CODES.BAD_GATEWAY,
  'AI_SERVICE_UNAVAILABLE': HTTP_STATUS_CODES.SERVICE_UNAVAILABLE,
  'MARKET_DATA_UNAVAILABLE': HTTP_STATUS_CODES.SERVICE_UNAVAILABLE,
  
  // System
  'INTERNAL_SERVER_ERROR': HTTP_STATUS_CODES.INTERNAL_SERVER_ERROR,
  'SERVICE_UNAVAILABLE': HTTP_STATUS_CODES.SERVICE_UNAVAILABLE,
  'MAINTENANCE_MODE': HTTP_STATUS_CODES.SERVICE_UNAVAILABLE
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export function getHttpStatusForErrorCode(errorCode: string): number {
  return ERROR_CODE_TO_HTTP_STATUS[errorCode] || HTTP_STATUS_CODES.INTERNAL_SERVER_ERROR;
}

export function createStandardError(
  code: string,
  message: string,
  details?: any,
  suggestion?: string
): { code: string; message: string; statusCode: number; details?: any; suggestion?: string } {
  return {
    code,
    message,
    statusCode: getHttpStatusForErrorCode(code),
    details,
    suggestion
  };
}

// ============================================================================
// RESPONSE VALIDATION
// ============================================================================

export function validateResponseData<T>(data: T, schema?: any): ValidationError[] {
  const errors: ValidationError[] = [];
  
  // Basic validation
  if (data === null || data === undefined) {
    errors.push({
      field: 'data',
      message: 'Response data cannot be null or undefined',
      code: 'INVALID_DATA'
    });
  }
  
  // Additional schema validation can be added here
  // if (schema) {
  //   const result = schema.safeParse(data);
  //   if (!result.success) {
  //     // Convert Zod errors to ValidationError format
  //   }
  // }
  
  return errors;
}

// ============================================================================
// EXPORT DEFAULT
// ============================================================================

export default responseFormatter;
