/**
 * Enhanced Services Integration
 * Integrates all enhanced services with the frontend application
 */

class EnhancedServicesIntegration {
  constructor() {
    this.apiClient = window.enhancedAPIClient;
    this.isInitialized = false;
    this.services = {
      marketData: null,
      aiAnalysis: null,
      errorHandler: null,
      notifications: null
    };

    this.init();
  }

  /**
   * Initialize all enhanced services
   */
  async init() {
    try {
      console.log('Initializing enhanced services integration...');

      // Initialize error handling
      this.initializeErrorHandling();

      // Initialize AI services
      this.initializeAIServices();

      // Initialize market data services
      this.initializeMarketDataServices();

      // Initialize monitoring
      this.initializeMonitoring();

      // Setup global event listeners
      this.setupGlobalEventListeners();

      this.isInitialized = true;
      console.log('Enhanced services integration initialized successfully');

      // Emit initialization event
      window.dispatchEvent(new CustomEvent('enhanced-services:initialized'));

    } catch (error) {
      console.error('Failed to initialize enhanced services:', error);
      this.showNotification('Failed to initialize enhanced services', 'error');
    }
  }

  /**
   * Initialize error handling
   */
  initializeErrorHandling() {
    // Enhanced error handler
    this.services.errorHandler = {
      handleError: (error, context = {}) => {
        console.error('Enhanced Error Handler:', error);

        // Log error with context
        const errorData = {
          message: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          context
        };

        // Send error to backend if possible
        if (this.apiClient) {
          this.apiClient.post('/errors/log', errorData).catch(logError => {
            console.warn('Failed to log error to backend:', logError);
          });
        }

        // Show user-friendly message
        this.showUserFriendlyError(error, context);

        return errorData;
      },

      showUserFriendlyError: (error, context) => {
        let message = 'An unexpected error occurred';

        if (error.category) {
          switch (error.category) {
            case 'network':
              message = 'Connection problem. Please check your internet connection.';
              break;
            case 'authentication':
              message = 'Please log in again to continue.';
              break;
            case 'authorization':
              message = 'You don\'t have permission to access this resource.';
              break;
            case 'validation':
              message = 'Please check your input and try again.';
              break;
            case 'rate_limit':
              message = 'Too many requests. Please wait a moment and try again.';
              break;
            case 'server_error':
              message = 'Server error. Please try again later.';
              break;
          }
        }

        this.showNotification(message, 'error');
      }
    };

    // Make error handler globally available
    window.enhancedErrorHandler = this.services.errorHandler;
  }

  /**
   * Initialize enhanced AI services with consolidated OpenAI integration
   */
  initializeAIServices() {
    this.services.aiAnalysis = {
      // Market analysis using consolidated AI service
      analyzeMarket: async (symbol, marketData, indicators) => {
        try {
          const response = await this.apiClient.post('/api/ai/market-analysis', {
            marketData: {
              symbol,
              ...marketData,
              indicators
            },
            options: {
              enableFallback: true,
              temperature: 0.3
            }
          });

          return response.data;
        } catch (error) {
          this.services.errorHandler.handleError(error, {
            service: 'ai-analysis',
            operation: 'analyzeMarket',
            symbol
          });
          throw error;
        }
      },

      // Sentiment analysis using consolidated AI service
      analyzeSentiment: async (headlines) => {
        try {
          const response = await this.apiClient.post('/api/ai/sentiment-analysis', {
            headlines,
            options: {
              enableFallback: true,
              temperature: 0.2
            }
          });

          return response.data;
        } catch (error) {
          this.services.errorHandler.handleError(error, {
            service: 'ai-analysis',
            operation: 'analyzeSentiment'
          });
          throw error;
        }
      },

      // Generate trading signals using consolidated AI service
      generateTradingSignals: async (symbol, marketData, indicators) => {
        try {
          const response = await this.apiClient.post('/api/ai/trading-signals', {
            marketData: {
              symbol,
              ...marketData
            },
            indicators,
            options: {
              enableFallback: true,
              temperature: 0.3
            }
          });

          return response.data;
        } catch (error) {
          this.services.errorHandler.handleError(error, {
            service: 'ai-analysis',
            operation: 'generateTradingSignals',
            symbol
          });
          throw error;
        }
      },

      // Risk assessment using consolidated AI service
      assessRisk: async (position, marketConditions) => {
        try {
          const response = await this.apiClient.post('/api/ai/risk-assessment', {
            position,
            marketConditions,
            options: {
              enableFallback: true,
              temperature: 0.2
            }
          });

          return response.data;
        } catch (error) {
          this.services.errorHandler.handleError(error, {
            service: 'ai-analysis',
            operation: 'assessRisk',
            symbol: position.symbol
          });
          throw error;
        }
      },

      // Get AI service metrics
      getMetrics: async (model = null, operation = null) => {
        try {
          const params = new URLSearchParams();
          if (model) params.append('model', model);
          if (operation) params.append('operation', operation);

          const response = await this.apiClient.get(`/api/ai/metrics?${params}`);
          return response.data;
        } catch (error) {
          this.services.errorHandler.handleError(error, {
            service: 'ai-analysis',
            operation: 'getMetrics'
          });
          throw error;
        }
      },

      // Compare different AI models
      compareModels: async (marketData, models, operation = 'market_analysis') => {
        try {
          const response = await this.apiClient.post('/api/ai/compare-models', {
            marketData,
            models,
            operation
          });

          return response.data;
        } catch (error) {
          this.services.errorHandler.handleError(error, {
            service: 'ai-analysis',
            operation: 'compareModels',
            symbol: marketData.symbol
          });
          throw error;
        }
      },

      // Reset circuit breaker (admin function)
      resetCircuitBreaker: async () => {
        try {
          const response = await this.apiClient.post('/api/ai/reset-circuit-breaker');
          return response.data;
        } catch (error) {
          this.services.errorHandler.handleError(error, {
            service: 'ai-analysis',
            operation: 'resetCircuitBreaker'
          });
          throw error;
        }
      }
    };

    // Make AI services globally available
    window.enhancedAIServices = this.services.aiAnalysis;
  }

  /**
   * Initialize enhanced market data services
   */
  initializeMarketDataServices() {
    this.services.marketData = {
      // Get market data with enhanced fallback
      getMarketData: async (symbol, timeframe, options = {}) => {
        try {
          const response = await this.apiClient.get(`/market/${symbol}/${timeframe}`, {
            limit: options.limit || 100,
            ...options.params
          });

          return response.data;
        } catch (error) {
          this.services.errorHandler.handleError(error, {
            service: 'market-data',
            operation: 'getMarketData',
            symbol,
            timeframe
          });
          throw error;
        }
      },

      // Get indicators
      getIndicators: async (symbol, timeframe, indicators) => {
        try {
          const response = await this.apiClient.get(`/indicators/${symbol}/${timeframe}`, {
            indicators: Array.isArray(indicators) ? indicators.join(',') : indicators
          });

          return response.data;
        } catch (error) {
          this.services.errorHandler.handleError(error, {
            service: 'market-data',
            operation: 'getIndicators',
            symbol,
            timeframe
          });
          throw error;
        }
      },

      // Get trading signals
      getTradingSignals: async (symbol, timeframe) => {
        try {
          const response = await this.apiClient.get(`/signals/${symbol}/${timeframe}`);

          return response.data;
        } catch (error) {
          this.services.errorHandler.handleError(error, {
            service: 'market-data',
            operation: 'getTradingSignals',
            symbol,
            timeframe
          });
          throw error;
        }
      }
    };

    // Make market data services globally available
    window.enhancedMarketDataServices = this.services.marketData;
  }

  /**
   * Initialize monitoring services
   */
  initializeMonitoring() {
    // API health monitoring
    setInterval(async () => {
      try {
        const response = await this.apiClient.get('/health/apis');

        // Check for any critical issues
        const healthData = response.data;
        if (healthData.errorMetrics && healthData.errorMetrics.totalErrors > 100) {
          console.warn('High error rate detected:', healthData.errorMetrics);
        }

        // Update UI indicators if available
        this.updateHealthIndicators(healthData);

      } catch (error) {
        console.warn('Health check failed:', error);
      }
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  /**
   * Setup global event listeners
   */
  setupGlobalEventListeners() {
    // Authentication events
    window.addEventListener('auth:logout', () => {
      this.showNotification('Session expired. Please log in again.', 'warning');
      // Redirect to login page
      setTimeout(() => {
        window.location.href = '/login.html';
      }, 2000);
    });

    // Network status events
    window.addEventListener('online', () => {
      this.showNotification('Connection restored', 'success');
    });

    window.addEventListener('offline', () => {
      this.showNotification('You are offline. Some features may be limited.', 'warning');
    });

    // Error events
    window.addEventListener('error', (event) => {
      this.services.errorHandler.handleError(event.error, {
        type: 'global-error',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.services.errorHandler.handleError(event.reason, {
        type: 'unhandled-promise-rejection'
      });
    });
  }

  /**
   * Update health indicators in UI
   */
  updateHealthIndicators(healthData) {
    // Update API status indicators
    const apiStatusElement = document.getElementById('api-status');
    if (apiStatusElement) {
      const hasHealthyAPIs = Object.values(healthData.apiHealth || {}).some(
        status => status.circuitState === 'closed'
      );

      apiStatusElement.className = hasHealthyAPIs ? 'status-healthy' : 'status-degraded';
      apiStatusElement.title = hasHealthyAPIs ? 'APIs are healthy' : 'Some APIs are experiencing issues';
    }

    // Update error rate indicator
    const errorRateElement = document.getElementById('error-rate');
    if (errorRateElement && healthData.errorMetrics) {
      const errorRate = healthData.errorMetrics.totalErrors || 0;
      errorRateElement.textContent = errorRate;

      if (errorRate > 50) {
        errorRateElement.className = 'error-rate high';
      } else if (errorRate > 10) {
        errorRateElement.className = 'error-rate medium';
      } else {
        errorRateElement.className = 'error-rate low';
      }
    }
  }

  /**
   * Show notification helper
   */
  showNotification(message, type = 'info') {
    if (window.showNotification) {
      window.showNotification(message, type);
    } else {
      console.log(`[${type.toUpperCase()}] ${message}`);
    }
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      services: Object.keys(this.services),
      apiClientStatus: this.apiClient ? 'available' : 'unavailable'
    };
  }

  /**
   * Reset all services (for testing/debugging)
   */
  reset() {
    this.isInitialized = false;
    this.services = {
      marketData: null,
      aiAnalysis: null,
      errorHandler: null,
      notifications: null
    };

    // Clear caches
    if (this.apiClient) {
      this.apiClient.clearCache();
    }

    console.log('Enhanced services integration reset');
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Wait for API client to be available
  if (window.enhancedAPIClient) {
    window.enhancedServicesIntegration = new EnhancedServicesIntegration();
  } else {
    // Wait for API client to load
    const checkAPIClient = setInterval(() => {
      if (window.enhancedAPIClient) {
        clearInterval(checkAPIClient);
        window.enhancedServicesIntegration = new EnhancedServicesIntegration();
      }
    }, 100);
  }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedServicesIntegration;
}
