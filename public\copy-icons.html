<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Copy PWA Icons - Trading Signals App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        h1 {
            color: #0d6efd;
            text-align: center;
        }
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        .instructions {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #0d6efd;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .drop-area {
            border: 2px dashed #0d6efd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            width: 100%;
            max-width: 500px;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }
        .drop-area.highlight {
            background-color: #e8f0fe;
            border-color: #0b5ed7;
        }
        button {
            background-color: #0d6efd;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.2s ease;
        }
        button:hover {
            background-color: #0b5ed7;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            display: none;
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            display: none;
        }
        .icon-preview {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .icon-item img {
            max-width: 100px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>Copy PWA Icons for Trading Signals App</h1>
    
    <div class="container">
        <div class="instructions">
            <h2>Instructions</h2>
            <p>This tool helps you copy the PWA icons to the correct location in your project.</p>
            <ol>
                <li>Drag and drop the icon-192.png and icon-512.png files you generated</li>
                <li>The tool will show a preview of the icons</li>
                <li>Click "Save Icons to Public Directory" to save them</li>
            </ol>
            <p><strong>Note:</strong> This tool requires modern browser features and may not work in all browsers.</p>
        </div>
        
        <div id="successMessage" class="success-message">
            Icons saved successfully!
        </div>
        
        <div id="errorMessage" class="error-message">
            There was an error saving the icons. Please try again.
        </div>
        
        <div id="dropArea" class="drop-area">
            <p>Drag and drop icon-192.png and icon-512.png here</p>
            <p>or</p>
            <button id="selectFilesBtn">Select Files</button>
            <input type="file" id="fileInput" style="display: none;" multiple accept="image/png">
        </div>
        
        <div id="iconPreview" class="icon-preview"></div>
        
        <button id="saveIconsBtn" disabled>Save Icons to Public Directory</button>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const dropArea = document.getElementById('dropArea');
            const fileInput = document.getElementById('fileInput');
            const selectFilesBtn = document.getElementById('selectFilesBtn');
            const saveIconsBtn = document.getElementById('saveIconsBtn');
            const iconPreview = document.getElementById('iconPreview');
            const successMessage = document.getElementById('successMessage');
            const errorMessage = document.getElementById('errorMessage');
            
            let icon192File = null;
            let icon512File = null;
            
            // Prevent default drag behaviors
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });
            
            // Highlight drop area when item is dragged over it
            ['dragenter', 'dragover'].forEach(eventName => {
                dropArea.addEventListener(eventName, highlight, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, unhighlight, false);
            });
            
            // Handle dropped files
            dropArea.addEventListener('drop', handleDrop, false);
            
            // Handle selected files
            fileInput.addEventListener('change', handleFiles, false);
            
            // Open file selector when button is clicked
            selectFilesBtn.addEventListener('click', () => {
                fileInput.click();
            });
            
            // Save icons button
            saveIconsBtn.addEventListener('click', saveIcons);
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            function highlight() {
                dropArea.classList.add('highlight');
            }
            
            function unhighlight() {
                dropArea.classList.remove('highlight');
            }
            
            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                handleFiles({ target: { files } });
            }
            
            function handleFiles(e) {
                const files = [...e.target.files];
                
                // Reset
                icon192File = null;
                icon512File = null;
                iconPreview.innerHTML = '';
                
                files.forEach(file => {
                    if (file.type === 'image/png') {
                        if (file.name === 'icon-192.png') {
                            icon192File = file;
                        } else if (file.name === 'icon-512.png') {
                            icon512File = file;
                        }
                    }
                });
                
                updatePreview();
            }
            
            function updatePreview() {
                iconPreview.innerHTML = '';
                
                if (icon192File) {
                    const item = document.createElement('div');
                    item.className = 'icon-item';
                    
                    const img = document.createElement('img');
                    img.src = URL.createObjectURL(icon192File);
                    
                    const p = document.createElement('p');
                    p.textContent = 'icon-192.png';
                    
                    item.appendChild(img);
                    item.appendChild(p);
                    iconPreview.appendChild(item);
                }
                
                if (icon512File) {
                    const item = document.createElement('div');
                    item.className = 'icon-item';
                    
                    const img = document.createElement('img');
                    img.src = URL.createObjectURL(icon512File);
                    
                    const p = document.createElement('p');
                    p.textContent = 'icon-512.png';
                    
                    item.appendChild(img);
                    item.appendChild(p);
                    iconPreview.appendChild(item);
                }
                
                saveIconsBtn.disabled = !(icon192File && icon512File);
            }
            
            function saveIcons() {
                try {
                    // In a real implementation, this would save the files to the server
                    // Since we can't do that directly in the browser, we'll just show instructions
                    
                    showSuccess('Icons ready! Please manually copy these files to your project\'s public directory.');
                    
                    // Create download links for convenience
                    if (icon192File) {
                        const link = document.createElement('a');
                        link.href = URL.createObjectURL(icon192File);
                        link.download = 'icon-192.png';
                        link.click();
                    }
                    
                    if (icon512File) {
                        setTimeout(() => {
                            const link = document.createElement('a');
                            link.href = URL.createObjectURL(icon512File);
                            link.download = 'icon-512.png';
                            link.click();
                        }, 500);
                    }
                } catch (error) {
                    console.error('Error saving icons:', error);
                    showError('Error saving icons: ' + error.message);
                }
            }
            
            function showSuccess(message) {
                successMessage.textContent = message;
                successMessage.style.display = 'block';
                errorMessage.style.display = 'none';
                
                // Hide after 10 seconds
                setTimeout(() => {
                    successMessage.style.display = 'none';
                }, 10000);
            }
            
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
                successMessage.style.display = 'none';
                
                // Hide after 10 seconds
                setTimeout(() => {
                    errorMessage.style.display = 'none';
                }, 10000);
            }
        });
    </script>
</body>
</html>
