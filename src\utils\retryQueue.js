/**
 * Retry Queue for API Requests
 * 
 * This utility provides a queue system for retrying failed API requests
 * with configurable backoff, priorities, and rate limit handling.
 */

class RetryQueue {
  /**
   * Create a new RetryQueue instance
   * @param {Object} options - Configuration options
   * @param {number} options.maxConcurrent - Maximum concurrent requests (default: 3)
   * @param {number} options.maxRetries - Maximum retry attempts (default: 5)
   * @param {number} options.baseDelay - Base delay for exponential backoff in ms (default: 1000)
   * @param {number} options.maxDelay - Maximum delay for exponential backoff in ms (default: 60000)
   * @param {Function} options.logger - Logger function (default: console.log)
   */
  constructor(options = {}) {
    this.options = {
      maxConcurrent: 3,
      maxRetries: 5,
      baseDelay: 1000,
      maxDelay: 60000,
      logger: console.log,
      ...options
    };

    this.queue = [];
    this.processing = new Set();
    this.providerLimits = new Map();
  }

  /**
   * Add a request to the retry queue
   * @param {Object} request - Request object
   * @param {string} request.id - Unique request ID
   * @param {string} request.provider - API provider name
   * @param {Function} request.execute - Function that executes the request
   * @param {Function} request.onSuccess - Callback for successful execution
   * @param {Function} request.onError - Callback for failed execution
   * @param {number} request.priority - Priority level (lower is higher priority, default: 5)
   * @param {number} request.retries - Current retry count (default: 0)
   * @returns {string} Request ID
   */
  add(request) {
    const id = request.id || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.queue.push({
      id,
      provider: request.provider,
      execute: request.execute,
      onSuccess: request.onSuccess || (() => {}),
      onError: request.onError || (() => {}),
      priority: request.priority || 5,
      retries: request.retries || 0,
      added: Date.now()
    });

    // Sort queue by priority and then by added time
    this.queue.sort((a, b) => {
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      return a.added - b.added;
    });

    // Process queue if we're not at max concurrent requests
    this._processQueue();

    return id;
  }

  /**
   * Process the next items in the queue
   * @private
   */
  _processQueue() {
    if (this.processing.size >= this.options.maxConcurrent || this.queue.length === 0) {
      return;
    }

    // Find the next request that isn't rate limited
    const nextIndex = this.queue.findIndex(req => !this._isRateLimited(req.provider));
    
    if (nextIndex === -1) {
      // All providers are rate limited
      this.options.logger('All providers are currently rate limited, waiting...');
      return;
    }

    const request = this.queue.splice(nextIndex, 1)[0];
    this.processing.add(request.id);

    this._executeRequest(request);
  }

  /**
   * Execute a request with retry logic
   * @param {Object} request - Request object
   * @private
   */
  async _executeRequest(request) {
    try {
      this.options.logger(`Executing request ${request.id} (provider: ${request.provider}, attempt: ${request.retries + 1})`);
      
      const result = await request.execute();
      
      this.options.logger(`Request ${request.id} succeeded`);
      this.processing.delete(request.id);
      
      // Call success callback
      request.onSuccess(result);
      
      // Process next items in queue
      this._processQueue();
    } catch (error) {
      this.options.logger(`Request ${request.id} failed: ${error.message}`);
      
      // Check if this is a rate limit error
      if (this._isRateLimitError(error)) {
        this._handleRateLimit(request.provider, error);
      }
      
      // Check if we should retry
      if (request.retries < this.options.maxRetries) {
        const delay = this._calculateBackoff(request.retries);
        
        this.options.logger(`Retrying request ${request.id} in ${delay}ms (attempt ${request.retries + 1}/${this.options.maxRetries})`);
        
        // Add back to queue with incremented retry count after delay
        setTimeout(() => {
          this.processing.delete(request.id);
          this.add({
            ...request,
            retries: request.retries + 1
          });
        }, delay);
      } else {
        this.options.logger(`Request ${request.id} failed permanently after ${request.retries} retries`);
        this.processing.delete(request.id);
        
        // Call error callback
        request.onError(error);
        
        // Process next items in queue
        this._processQueue();
      }
    }
  }

  /**
   * Calculate exponential backoff delay
   * @param {number} retryCount - Current retry count
   * @returns {number} Delay in milliseconds
   * @private
   */
  _calculateBackoff(retryCount) {
    // Exponential backoff with jitter
    const expBackoff = Math.min(
      this.options.maxDelay,
      this.options.baseDelay * Math.pow(2, retryCount)
    );
    
    // Add jitter (±20%)
    const jitter = expBackoff * 0.2 * (Math.random() * 2 - 1);
    
    return Math.floor(expBackoff + jitter);
  }

  /**
   * Check if an error is a rate limit error
   * @param {Error} error - Error object
   * @returns {boolean} True if it's a rate limit error
   * @private
   */
  _isRateLimitError(error) {
    // Check common rate limit patterns in error messages
    const message = error.message.toLowerCase();
    return (
      message.includes('rate limit') ||
      message.includes('too many requests') ||
      message.includes('api call frequency') ||
      message.includes('429') ||
      error.status === 429 ||
      (error.response && error.response.status === 429)
    );
  }

  /**
   * Handle rate limit for a provider
   * @param {string} provider - Provider name
   * @param {Error} error - Error object
   * @private
   */
  _handleRateLimit(provider, error) {
    // Default cooldown period (5 minutes)
    let cooldownPeriod = 5 * 60 * 1000;
    
    // Try to extract cooldown period from error if available
    const message = error.message.toLowerCase();
    if (message.includes('minute')) {
      cooldownPeriod = 60 * 1000;
    } else if (message.includes('hour')) {
      cooldownPeriod = 60 * 60 * 1000;
    }
    
    this.providerLimits.set(provider, {
      until: Date.now() + cooldownPeriod,
      reason: error.message
    });
    
    this.options.logger(`Provider ${provider} rate limited until ${new Date(Date.now() + cooldownPeriod).toISOString()}`);
  }

  /**
   * Check if a provider is currently rate limited
   * @param {string} provider - Provider name
   * @returns {boolean} True if rate limited
   * @private
   */
  _isRateLimited(provider) {
    if (!this.providerLimits.has(provider)) {
      return false;
    }
    
    const limit = this.providerLimits.get(provider);
    
    if (Date.now() > limit.until) {
      // Cooldown period has passed
      this.providerLimits.delete(provider);
      return false;
    }
    
    return true;
  }

  /**
   * Get the current queue status
   * @returns {Object} Queue status
   */
  getStatus() {
    return {
      queueLength: this.queue.length,
      processing: this.processing.size,
      rateLimited: Array.from(this.providerLimits.entries()).map(([provider, limit]) => ({
        provider,
        until: new Date(limit.until).toISOString(),
        reason: limit.reason,
        remainingMs: Math.max(0, limit.until - Date.now())
      }))
    };
  }
}

module.exports = RetryQueue;
