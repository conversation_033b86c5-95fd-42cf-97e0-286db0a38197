import tensorflow as tf
import pandas as pd
import numpy as np

# CONFIGURABLE: Path to your CSV file with historical data
CSV_PATH = 'historical_data.csv'  # Change this to your actual data file

# CONFIGURABLE: Features and label columns
FEATURE_COLUMNS = ['feature1', 'feature2', 'feature3']  # Update with your features
LABEL_COLUMN = 'signal'  # Update with your label column

# Load data
def load_data(csv_path):
    df = pd.read_csv(csv_path)
    X = df[FEATURE_COLUMNS].values
    y = df[LABEL_COLUMN].values
    return X, y

# Build a simple model
def build_model(input_dim, num_classes):
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(64, activation='relu', input_shape=(input_dim,)),
        tf.keras.layers.Dense(num_classes, activation='softmax')
    ])
    model.compile(optimizer='adam', loss='sparse_categorical_crossentropy', metrics=['accuracy'])
    return model

if __name__ == '__main__':
    # Load and preprocess data
    X, y = load_data(CSV_PATH)
    num_classes = len(np.unique(y))

    # Build and train model
    model = build_model(X.shape[1], num_classes)
    model.fit(X, y, epochs=10, batch_size=32)

    # Save the trained model
    model.save('signal_model')
    print('Model trained and saved as signal_model/') 