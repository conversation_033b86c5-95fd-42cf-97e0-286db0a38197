/**
 * Chart Indicators Module for Trading Signals App
 * 
 * This module provides functions to add technical indicators to the chart.
 */

/**
 * Add indicators to chart
 */
function addIndicatorsToChart() {
    const marketData = window.marketData;
    const chart = window.chartIntegration.chart;
    const indicators = window.chartIntegration.indicators;
    const colors = window.chartIntegration.colors;
    const technicalAnalysis = window.technicalAnalysis;
    
    if (!marketData || !chart || !indicators || !colors || !technicalAnalysis) {
        console.warn('Required dependencies not available for adding indicators');
        return;
    }
    
    // Prepare data arrays
    const dates = marketData.dates.map(date => new Date(date));
    const closes = marketData.closes;
    const highs = marketData.highs;
    const lows = marketData.lows;
    const volumes = marketData.volumes;
    
    // Check if we have enough data
    if (!dates.length || !closes.length) {
        console.warn('Not enough data for indicators');
        return;
    }
    
    // Add Volume indicator
    if (indicators.volume.visible && volumes && volumes.length) {
        addVolumeIndicator(chart, dates, volumes, closes, colors);
    }
    
    // Add Moving Averages
    if (indicators.ema50.visible) {
        addEMAIndicator(chart, dates, closes, 50, colors.ema50, 'EMA 50');
    }
    
    if (indicators.ema200.visible) {
        addEMAIndicator(chart, dates, closes, 200, colors.ema200, 'EMA 200');
    }
    
    // Add Bollinger Bands
    if (indicators.bollinger.visible) {
        addBollingerBands(chart, dates, closes, colors);
    }
    
    // Add RSI
    if (indicators.rsi.visible) {
        addRSIIndicator(chart, dates, closes, colors);
    }
    
    // Add MACD
    if (indicators.macd.visible) {
        addMACDIndicator(chart, dates, closes, colors);
    }
    
    // Add Stochastic
    if (indicators.stochastic.visible) {
        addStochasticIndicator(chart, dates, closes, highs, lows, colors);
    }
    
    // Add ADX
    if (indicators.adx.visible) {
        addADXIndicator(chart, dates, closes, highs, lows, colors);
    }
    
    // Add Supertrend
    if (indicators.supertrend.visible) {
        addSupertrendIndicator(chart, dates, closes, highs, lows, colors);
    }
}

/**
 * Add Volume indicator to chart
 * @param {Chart} chart - Chart.js instance
 * @param {Array} dates - Array of dates
 * @param {Array} volumes - Array of volume data
 * @param {Array} closes - Array of closing prices
 * @param {Object} colors - Chart colors
 */
function addVolumeIndicator(chart, dates, volumes, closes, colors) {
    // Create volume colors based on price movement
    const volumeColors = [];
    for (let i = 1; i < closes.length; i++) {
        if (closes[i] >= closes[i - 1]) {
            volumeColors.push(colors.volumeUp); // Green for up
        } else {
            volumeColors.push(colors.volumeDown); // Red for down
        }
    }
    
    // Add first color
    volumeColors.unshift(colors.volume);
    
    // Create volume data
    const volumeData = [];
    for (let i = 0; i < dates.length; i++) {
        volumeData.push({
            x: dates[i],
            y: volumes[i]
        });
    }
    
    // Add volume dataset
    chart.data.datasets.push({
        label: 'Volume',
        data: volumeData,
        type: 'bar',
        backgroundColor: volumeColors,
        yAxisID: 'volume',
        order: 1
    });
    
    // Add volume scale if not exists
    if (!chart.options.scales.volume) {
        chart.options.scales.volume = {
            position: 'left',
            beginAtZero: true,
            grid: {
                drawOnChartArea: false,
                color: colors.grid
            },
            ticks: {
                color: colors.text
            }
        };
    }
}

/**
 * Add EMA indicator to chart
 * @param {Chart} chart - Chart.js instance
 * @param {Array} dates - Array of dates
 * @param {Array} closes - Array of closing prices
 * @param {Number} period - EMA period
 * @param {String} color - Line color
 * @param {String} label - Line label
 */
function addEMAIndicator(chart, dates, closes, period, color, label) {
    // Calculate EMA
    const ema = window.technicalAnalysis.calculateEMA(closes, period);
    
    // Create EMA data
    const emaData = [];
    for (let i = 0; i < dates.length; i++) {
        if (ema[i] !== null) {
            emaData.push({
                x: dates[i],
                y: ema[i]
            });
        }
    }
    
    // Add EMA dataset
    chart.data.datasets.push({
        label: label,
        data: emaData,
        type: 'line',
        borderColor: color,
        borderWidth: 1.5,
        pointRadius: 0,
        fill: false,
        tension: 0.1,
        yAxisID: 'y'
    });
}

/**
 * Add Bollinger Bands to chart
 * @param {Chart} chart - Chart.js instance
 * @param {Array} dates - Array of dates
 * @param {Array} closes - Array of closing prices
 * @param {Object} colors - Chart colors
 */
function addBollingerBands(chart, dates, closes, colors) {
    // Calculate Bollinger Bands
    const bollinger = window.technicalAnalysis.calculateBollingerBands(closes);
    
    // Create upper band data
    const upperData = [];
    for (let i = 0; i < dates.length; i++) {
        if (bollinger.upper[i] !== null) {
            upperData.push({
                x: dates[i],
                y: bollinger.upper[i]
            });
        }
    }
    
    // Create middle band data
    const middleData = [];
    for (let i = 0; i < dates.length; i++) {
        if (bollinger.middle[i] !== null) {
            middleData.push({
                x: dates[i],
                y: bollinger.middle[i]
            });
        }
    }
    
    // Create lower band data
    const lowerData = [];
    for (let i = 0; i < dates.length; i++) {
        if (bollinger.lower[i] !== null) {
            lowerData.push({
                x: dates[i],
                y: bollinger.lower[i]
            });
        }
    }
    
    // Add upper band dataset
    chart.data.datasets.push({
        label: 'Bollinger Upper',
        data: upperData,
        type: 'line',
        borderColor: colors.bollingerUpper,
        borderWidth: 1,
        pointRadius: 0,
        fill: false,
        tension: 0.1,
        yAxisID: 'y'
    });
    
    // Add middle band dataset
    chart.data.datasets.push({
        label: 'Bollinger Middle',
        data: middleData,
        type: 'line',
        borderColor: colors.bollingerMiddle,
        borderWidth: 1,
        pointRadius: 0,
        fill: false,
        tension: 0.1,
        yAxisID: 'y'
    });
    
    // Add lower band dataset
    chart.data.datasets.push({
        label: 'Bollinger Lower',
        data: lowerData,
        type: 'line',
        borderColor: colors.bollingerLower,
        borderWidth: 1,
        pointRadius: 0,
        fill: '+2', // Fill between this dataset and the dataset 2 indices above
        backgroundColor: 'rgba(0, 128, 0, 0.1)',
        tension: 0.1,
        yAxisID: 'y'
    });
}

/**
 * Add RSI indicator to chart
 * @param {Chart} chart - Chart.js instance
 * @param {Array} dates - Array of dates
 * @param {Array} closes - Array of closing prices
 * @param {Object} colors - Chart colors
 */
function addRSIIndicator(chart, dates, closes, colors) {
    // Calculate RSI
    const rsi = window.technicalAnalysis.calculateRSI(closes);
    
    // Create RSI data
    const rsiData = [];
    for (let i = 0; i < dates.length; i++) {
        if (rsi[i] !== null) {
            rsiData.push({
                x: dates[i],
                y: rsi[i]
            });
        }
    }
    
    // Add RSI dataset
    chart.data.datasets.push({
        label: 'RSI',
        data: rsiData,
        type: 'line',
        borderColor: colors.rsi,
        borderWidth: 1.5,
        pointRadius: 0,
        fill: false,
        tension: 0.1,
        yAxisID: 'rsi'
    });
    
    // Add RSI scale if not exists
    if (!chart.options.scales.rsi) {
        chart.options.scales.rsi = {
            position: 'right',
            min: 0,
            max: 100,
            grid: {
                drawOnChartArea: false,
                color: colors.grid
            },
            ticks: {
                color: colors.text
            }
        };
    }
    
    // Add overbought and oversold lines
    chart.data.datasets.push({
        label: 'RSI Overbought',
        data: dates.map(date => ({ x: date, y: 70 })),
        type: 'line',
        borderColor: colors.rsiOverbought,
        borderWidth: 1,
        pointRadius: 0,
        fill: false,
        tension: 0,
        yAxisID: 'rsi'
    });
    
    chart.data.datasets.push({
        label: 'RSI Oversold',
        data: dates.map(date => ({ x: date, y: 30 })),
        type: 'line',
        borderColor: colors.rsiOversold,
        borderWidth: 1,
        pointRadius: 0,
        fill: false,
        tension: 0,
        yAxisID: 'rsi'
    });
}

/**
 * Add MACD indicator to chart
 * @param {Chart} chart - Chart.js instance
 * @param {Array} dates - Array of dates
 * @param {Array} closes - Array of closing prices
 * @param {Object} colors - Chart colors
 */
function addMACDIndicator(chart, dates, closes, colors) {
    // Calculate MACD
    const macd = window.technicalAnalysis.calculateMACD(closes);
    
    // Create MACD line data
    const macdLineData = [];
    for (let i = 0; i < dates.length; i++) {
        if (macd.line[i] !== null) {
            macdLineData.push({
                x: dates[i],
                y: macd.line[i]
            });
        }
    }
    
    // Create MACD signal line data
    const macdSignalData = [];
    for (let i = 0; i < dates.length; i++) {
        if (macd.signal[i] !== null) {
            macdSignalData.push({
                x: dates[i],
                y: macd.signal[i]
            });
        }
    }
    
    // Create MACD histogram data
    const macdHistogramData = [];
    const macdHistogramColors = [];
    for (let i = 0; i < dates.length; i++) {
        if (macd.histogram[i] !== null) {
            macdHistogramData.push({
                x: dates[i],
                y: macd.histogram[i]
            });
            
            // Set color based on histogram value
            if (macd.histogram[i] >= 0) {
                macdHistogramColors.push(colors.macdHistogramUp);
            } else {
                macdHistogramColors.push(colors.macdHistogramDown);
            }
        }
    }
    
    // Add MACD line dataset
    chart.data.datasets.push({
        label: 'MACD Line',
        data: macdLineData,
        type: 'line',
        borderColor: colors.macdLine,
        borderWidth: 1.5,
        pointRadius: 0,
        fill: false,
        tension: 0.1,
        yAxisID: 'macd'
    });
    
    // Add MACD signal line dataset
    chart.data.datasets.push({
        label: 'MACD Signal',
        data: macdSignalData,
        type: 'line',
        borderColor: colors.macdSignal,
        borderWidth: 1.5,
        pointRadius: 0,
        fill: false,
        tension: 0.1,
        yAxisID: 'macd'
    });
    
    // Add MACD histogram dataset
    chart.data.datasets.push({
        label: 'MACD Histogram',
        data: macdHistogramData,
        type: 'bar',
        backgroundColor: macdHistogramColors,
        yAxisID: 'macd'
    });
    
    // Add MACD scale if not exists
    if (!chart.options.scales.macd) {
        chart.options.scales.macd = {
            position: 'right',
            grid: {
                drawOnChartArea: false,
                color: colors.grid
            },
            ticks: {
                color: colors.text
            }
        };
    }
}

// Make the function available globally
window.addIndicatorsToChart = addIndicatorsToChart;
