/**
 * Error State Components
 * 
 * Standardized error display components for consistent error handling
 * across the entire application. Provides various error styles and
 * recovery options based on error type and context.
 * 
 * Features:
 * - Multiple error display styles
 * - Context-aware error messages
 * - Recovery action buttons
 * - Error categorization support
 * - Accessibility compliance
 * - Integration with error context
 * 
 * @version 1.0.0
 */

import React from 'react';
import { useError, AppError, ErrorType, ErrorCategory } from '../../context/ErrorContext';

// ============================================================================
// INTERFACES
// ============================================================================

export interface ErrorStateProps {
  error?: AppError | Error | string;
  title?: string;
  message?: string;
  type?: ErrorType;
  category?: ErrorCategory;
  variant?: 'full' | 'compact' | 'inline' | 'banner';
  showRetry?: boolean;
  showDetails?: boolean;
  onRetry?: () => void | Promise<void>;
  onDismiss?: () => void;
  className?: string;
  children?: React.ReactNode;
}

export interface NetworkErrorProps {
  onRetry?: () => void | Promise<void>;
  variant?: 'full' | 'compact';
}

export interface APIErrorProps {
  error: Error;
  endpoint?: string;
  onRetry?: () => void | Promise<void>;
  variant?: 'full' | 'compact';
}

export interface ValidationErrorProps {
  errors: Record<string, string> | string[];
  title?: string;
  onDismiss?: () => void;
}

// ============================================================================
// ERROR ICON COMPONENT
// ============================================================================

const ErrorIcon: React.FC<{ category: ErrorCategory; size?: 'sm' | 'md' | 'lg' }> = ({ 
  category, 
  size = 'md' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  const getIconAndColor = () => {
    switch (category) {
      case 'critical':
        return {
          color: 'text-red-500',
          icon: (
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          )
        };
      case 'high':
        return {
          color: 'text-orange-500',
          icon: (
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          )
        };
      case 'medium':
        return {
          color: 'text-yellow-500',
          icon: (
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          )
        };
      default:
        return {
          color: 'text-blue-500',
          icon: (
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          )
        };
    }
  };

  const { color, icon } = getIconAndColor();

  return (
    <svg className={`${sizeClasses[size]} ${color}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
      {icon}
    </svg>
  );
};

// ============================================================================
// MAIN ERROR STATE COMPONENT
// ============================================================================

const ErrorState: React.FC<ErrorStateProps> = ({
  error,
  title,
  message,
  type = 'unknown',
  category = 'medium',
  variant = 'full',
  showRetry = true,
  showDetails = false,
  onRetry,
  onDismiss,
  className = '',
  children
}) => {
  const { reportError } = useError();
  const [isRetrying, setIsRetrying] = React.useState(false);

  // Extract error information
  const errorInfo = React.useMemo(() => {
    if (typeof error === 'string') {
      return {
        title: title || 'Error',
        message: message || error,
        type,
        category
      };
    } else if (error instanceof Error) {
      return {
        title: title || `${error.name || 'Error'}`,
        message: message || error.message,
        type,
        category,
        stack: error.stack
      };
    } else if (error && typeof error === 'object' && 'userMessage' in error) {
      const appError = error as AppError;
      return {
        title: title || `${appError.type.charAt(0).toUpperCase() + appError.type.slice(1)} Error`,
        message: message || appError.userMessage,
        type: appError.type,
        category: appError.category,
        details: appError.details
      };
    } else {
      return {
        title: title || 'Unknown Error',
        message: message || 'An unexpected error occurred',
        type,
        category
      };
    }
  }, [error, title, message, type, category]);

  const handleRetry = async () => {
    if (!onRetry || isRetrying) return;

    setIsRetrying(true);
    try {
      await onRetry();
    } catch (retryError) {
      console.error('Retry failed:', retryError);
      reportError(retryError as Error, {
        type: 'component',
        source: 'error-state-retry',
        context: { originalError: errorInfo.message }
      });
    } finally {
      setIsRetrying(false);
    }
  };

  const getContainerClasses = () => {
    const baseClasses = 'rounded-lg';
    
    switch (variant) {
      case 'full':
        return `${baseClasses} p-6 text-center`;
      case 'compact':
        return `${baseClasses} p-4`;
      case 'inline':
        return 'flex items-center space-x-2';
      case 'banner':
        return `${baseClasses} p-4 border-l-4`;
      default:
        return `${baseClasses} p-6 text-center`;
    }
  };

  const getBackgroundClasses = () => {
    if (variant === 'inline') return '';
    
    switch (errorInfo.category) {
      case 'critical':
        return variant === 'banner' 
          ? 'bg-red-50 border-red-400' 
          : 'bg-red-50 border border-red-200';
      case 'high':
        return variant === 'banner' 
          ? 'bg-orange-50 border-orange-400' 
          : 'bg-orange-50 border border-orange-200';
      case 'medium':
        return variant === 'banner' 
          ? 'bg-yellow-50 border-yellow-400' 
          : 'bg-yellow-50 border border-yellow-200';
      default:
        return variant === 'banner' 
          ? 'bg-blue-50 border-blue-400' 
          : 'bg-blue-50 border border-blue-200';
    }
  };

  const renderFullError = () => (
    <div className={`${getContainerClasses()} ${getBackgroundClasses()} ${className}`}>
      <div className="flex justify-center mb-4">
        <ErrorIcon category={errorInfo.category} size="lg" />
      </div>
      
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        {errorInfo.title}
      </h3>
      
      <p className="text-gray-600 mb-4 max-w-md mx-auto">
        {errorInfo.message}
      </p>
      
      {children && (
        <div className="mb-4">
          {children}
        </div>
      )}
      
      <div className="flex justify-center space-x-3">
        {showRetry && onRetry && (
          <button
            onClick={handleRetry}
            disabled={isRetrying}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRetrying ? 'Retrying...' : 'Try Again'}
          </button>
        )}
        
        {onDismiss && (
          <button
            onClick={onDismiss}
            className="text-gray-600 hover:text-gray-800 px-4 py-2 rounded-md border border-gray-300 hover:bg-gray-50"
          >
            Dismiss
          </button>
        )}
      </div>
      
      {showDetails && errorInfo.stack && process.env.NODE_ENV === 'development' && (
        <details className="mt-4 text-left">
          <summary className="cursor-pointer text-sm text-gray-500">Technical Details</summary>
          <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32">
            {errorInfo.stack}
          </pre>
        </details>
      )}
    </div>
  );

  const renderCompactError = () => (
    <div className={`${getContainerClasses()} ${getBackgroundClasses()} ${className}`}>
      <div className="flex items-start">
        <ErrorIcon category={errorInfo.category} size="md" />
        
        <div className="ml-3 flex-1">
          <h4 className="text-sm font-medium text-gray-900">
            {errorInfo.title}
          </h4>
          <p className="mt-1 text-sm text-gray-600">
            {errorInfo.message}
          </p>
          
          {children && (
            <div className="mt-2">
              {children}
            </div>
          )}
        </div>
        
        <div className="ml-4 flex-shrink-0 flex space-x-2">
          {showRetry && onRetry && (
            <button
              onClick={handleRetry}
              disabled={isRetrying}
              className="text-sm bg-white border border-gray-300 rounded px-2 py-1 hover:bg-gray-50 disabled:opacity-50"
            >
              {isRetrying ? 'Retrying...' : 'Retry'}
            </button>
          )}
          
          {onDismiss && (
            <button
              onClick={onDismiss}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>
    </div>
  );

  const renderInlineError = () => (
    <div className={`${getContainerClasses()} ${className}`}>
      <ErrorIcon category={errorInfo.category} size="sm" />
      <span className="text-sm text-gray-600">{errorInfo.message}</span>
      {showRetry && onRetry && (
        <button
          onClick={handleRetry}
          disabled={isRetrying}
          className="text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50"
        >
          {isRetrying ? 'Retrying...' : 'Retry'}
        </button>
      )}
    </div>
  );

  const renderBannerError = () => (
    <div className={`${getContainerClasses()} ${getBackgroundClasses()} ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <ErrorIcon category={errorInfo.category} size="md" />
          <div className="ml-3">
            <h4 className="text-sm font-medium text-gray-900">
              {errorInfo.title}
            </h4>
            <p className="text-sm text-gray-600">
              {errorInfo.message}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {showRetry && onRetry && (
            <button
              onClick={handleRetry}
              disabled={isRetrying}
              className="text-sm bg-white border border-gray-300 rounded px-3 py-1 hover:bg-gray-50 disabled:opacity-50"
            >
              {isRetrying ? 'Retrying...' : 'Try Again'}
            </button>
          )}
          
          {onDismiss && (
            <button
              onClick={onDismiss}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>
      
      {children && (
        <div className="mt-3">
          {children}
        </div>
      )}
    </div>
  );

  switch (variant) {
    case 'compact':
      return renderCompactError();
    case 'inline':
      return renderInlineError();
    case 'banner':
      return renderBannerError();
    default:
      return renderFullError();
  }
};

// ============================================================================
// SPECIALIZED ERROR COMPONENTS
// ============================================================================

export const NetworkError: React.FC<NetworkErrorProps> = ({ onRetry, variant = 'full' }) => (
  <ErrorState
    title="Connection Problem"
    message="Unable to connect to the server. Please check your internet connection and try again."
    type="network"
    category="medium"
    variant={variant}
    onRetry={onRetry}
    showRetry={!!onRetry}
  >
    <div className="text-sm text-gray-500 mt-2">
      <p>• Check your internet connection</p>
      <p>• Try refreshing the page</p>
      <p>• Contact support if the problem persists</p>
    </div>
  </ErrorState>
);

export const APIError: React.FC<APIErrorProps> = ({ error, endpoint, onRetry, variant = 'full' }) => {
  const getAPIErrorMessage = (error: Error) => {
    if (error.message.includes('401')) {
      return 'Your session has expired. Please log in again.';
    } else if (error.message.includes('403')) {
      return 'You don\'t have permission to access this resource.';
    } else if (error.message.includes('404')) {
      return 'The requested resource was not found.';
    } else if (error.message.includes('429')) {
      return 'Too many requests. Please wait a moment and try again.';
    } else if (error.message.includes('500')) {
      return 'Server error. Our team has been notified and is working on a fix.';
    } else {
      return 'We\'re experiencing technical difficulties. Please try again.';
    }
  };

  return (
    <ErrorState
      title="API Error"
      message={getAPIErrorMessage(error)}
      type="api"
      category="high"
      variant={variant}
      onRetry={onRetry}
      showRetry={!!onRetry}
    >
      {endpoint && (
        <p className="text-xs text-gray-500 mt-2">
          Endpoint: {endpoint}
        </p>
      )}
    </ErrorState>
  );
};

export const ValidationError: React.FC<ValidationErrorProps> = ({ 
  errors, 
  title = 'Validation Error',
  onDismiss 
}) => {
  const errorList = Array.isArray(errors) ? errors : Object.values(errors);

  return (
    <ErrorState
      title={title}
      message="Please correct the following errors:"
      type="validation"
      category="low"
      variant="compact"
      showRetry={false}
      onDismiss={onDismiss}
    >
      <ul className="text-sm text-left mt-2 space-y-1">
        {errorList.map((error, index) => (
          <li key={index} className="flex items-start">
            <span className="text-red-500 mr-2">•</span>
            <span>{error}</span>
          </li>
        ))}
      </ul>
    </ErrorState>
  );
};

export const NotFoundError: React.FC<{ resource?: string; onGoBack?: () => void }> = ({ 
  resource = 'page',
  onGoBack 
}) => (
  <ErrorState
    title="Not Found"
    message={`The ${resource} you're looking for doesn't exist or has been moved.`}
    type="data"
    category="medium"
    variant="full"
    showRetry={false}
  >
    <div className="space-y-2">
      {onGoBack && (
        <button
          onClick={onGoBack}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          Go Back
        </button>
      )}
      <button
        onClick={() => window.location.href = '/dashboard'}
        className="block mx-auto text-blue-600 hover:text-blue-800 text-sm"
      >
        Go to Dashboard
      </button>
    </div>
  </ErrorState>
);

export const PermissionError: React.FC<{ feature?: string }> = ({ feature = 'feature' }) => (
  <ErrorState
    title="Access Denied"
    message={`You don't have permission to access this ${feature}. Please contact your administrator or upgrade your plan.`}
    type="authorization"
    category="high"
    variant="full"
    showRetry={false}
  >
    <div className="space-y-2">
      <button
        onClick={() => window.location.href = '/settings/subscription'}
        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
      >
        Upgrade Plan
      </button>
      <button
        onClick={() => window.location.href = '/contact'}
        className="block mx-auto text-blue-600 hover:text-blue-800 text-sm"
      >
        Contact Support
      </button>
    </div>
  </ErrorState>
);

export default ErrorState;
