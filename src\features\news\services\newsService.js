import axios from 'axios';
import { consolidatedOpenAIService } from '../../../services/consolidatedOpenAIService.js';
import SentimentHistory from '../models/SentimentHistory.js';
import COTHistory from '../models/COTHistory.js';
import logger from '../../../utils/logger.js';

/**
 * Enhanced News Service with consolidated AI integration
 *
 * Provides news fetching and sentiment analysis using the consolidated OpenAI service
 * with improved error handling, caching, and performance tracking.
 *
 * @class NewsService
 * @version 2.0.0
 */
class NewsService {
  /**
   * Fetch news articles for a given symbol
   *
   * @param {Object} params - Parameters object
   * @param {string} params.symbol - Stock symbol
   * @param {number} [params.limit=50] - Maximum number of articles
   * @returns {Promise<Array>} Array of news articles
   */
  async getNews({ symbol, limit = 50 }) {
    try {
      const apiKey = process.env.FMP_API_KEY;
      if (!apiKey) {
        throw new Error('FMP_API_KEY is not configured');
      }

      const url = `https://financialmodelingprep.com/api/v3/stock_news?tickers=${symbol}&limit=${limit}&apikey=${apiKey}`;
      const { data } = await axios.get(url);

      logger.info(`Fetched ${data.length} news articles for ${symbol}`);
      return data;
    } catch (error) {
      logger.error(`Error fetching news for ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Get sentiment analysis for a symbol using AI
   *
   * @param {Object} params - Parameters object
   * @param {string} params.symbol - Stock symbol
   * @param {Object} [params.options] - AI analysis options
   * @returns {Promise<Object>} Sentiment analysis result
   */
  async getSentiment({ symbol, options = {} }) {
    try {
      // Fetch news articles
      const news = await this.getNews({ symbol });

      if (!news || news.length === 0) {
        logger.warn(`No news articles found for ${symbol}`);
        return {
          symbol,
          overall_sentiment: 'neutral',
          sentiment_score: 0,
          confidence: 0,
          summary: 'No news articles available for analysis',
          articleCount: 0
        };
      }

      // Extract headlines for sentiment analysis
      const headlines = news
        .map(article => article.title || article.headline)
        .filter(headline => headline && headline.trim().length > 0)
        .slice(0, 20); // Limit to 20 headlines for API efficiency

      if (headlines.length === 0) {
        logger.warn(`No valid headlines found for ${symbol}`);
        return {
          symbol,
          overall_sentiment: 'neutral',
          sentiment_score: 0,
          confidence: 0,
          summary: 'No valid headlines available for analysis',
          articleCount: news.length
        };
      }

      // Use consolidated OpenAI service for sentiment analysis
      const sentiment = await consolidatedOpenAIService.analyzeSentiment(headlines, {
        ...options,
        symbol // Add symbol to options for better tracking
      });

      // Enhance sentiment result with additional metadata
      const enhancedSentiment = {
        symbol,
        ...sentiment,
        articleCount: news.length,
        headlineCount: headlines.length,
        analysisTimestamp: new Date().toISOString(),
        model: sentiment._metadata?.model || 'unknown'
      };

      // Save to history
      try {
        await saveSentimentHistory(enhancedSentiment);
        logger.info(`Saved sentiment history for ${symbol}`);
      } catch (historyError) {
        logger.error(`Error saving sentiment history for ${symbol}:`, historyError);
        // Don't fail the main operation if history saving fails
      }

      return enhancedSentiment;
    } catch (error) {
      logger.error(`Error getting sentiment for ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Get news summary using AI
   *
   * @param {Object} params - Parameters object
   * @param {string} params.symbol - Stock symbol
   * @param {number} [params.articleLimit=10] - Maximum articles to summarize
   * @param {Object} [params.options] - AI analysis options
   * @returns {Promise<Object>} News summary result
   */
  async getNewsSummary({ symbol, articleLimit = 10, options = {} }) {
    try {
      // Fetch news articles
      const news = await this.getNews({ symbol, limit: articleLimit });

      if (!news || news.length === 0) {
        return {
          symbol,
          summary: 'No news articles available',
          key_points: [],
          market_impact: 'low',
          sentiment: 'neutral',
          articleCount: 0
        };
      }

      // Prepare articles for summarization
      const articles = news.map(article => ({
        title: article.title || article.headline || 'Untitled',
        content: article.text || article.summary || article.description || '',
        publishedDate: article.publishedDate || article.date
      }));

      // Use consolidated OpenAI service for news summarization
      const summary = await consolidatedOpenAIService.summarizeNews(articles, {
        ...options,
        symbol
      });

      return {
        symbol,
        ...summary,
        articleCount: news.length,
        analysisTimestamp: new Date().toISOString(),
        model: summary._metadata?.model || 'unknown'
      };
    } catch (error) {
      logger.error(`Error getting news summary for ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Get comprehensive news analysis (sentiment + summary)
   *
   * @param {Object} params - Parameters object
   * @param {string} params.symbol - Stock symbol
   * @param {Object} [params.options] - AI analysis options
   * @returns {Promise<Object>} Comprehensive analysis result
   */
  async getComprehensiveAnalysis({ symbol, options = {} }) {
    try {
      const [sentiment, summary] = await Promise.all([
        this.getSentiment({ symbol, options }),
        this.getNewsSummary({ symbol, options })
      ]);

      return {
        symbol,
        sentiment,
        summary,
        combinedAnalysis: {
          overallSentiment: sentiment.overall_sentiment,
          marketImpact: summary.market_impact,
          confidence: Math.min(sentiment.confidence || 0, 100),
          keyInsights: summary.key_points || [],
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.error(`Error getting comprehensive analysis for ${symbol}:`, error);
      throw error;
    }
  }

  async getCOT({ symbol }) {
    const apiKey = process.env.FMP_API_KEY;
    const url = `https://financialmodelingprep.com/api/v4/commitment_of_traders_report?symbol=${symbol}&apikey=${apiKey}`;
    const { data } = await axios.get(url);
    if (data && data.length > 0) {
      const latest = data[0];
      const cotData = {
        symbol: latest.symbol,
        reportDate: latest.date,
        commercials: { long: latest.commercial_long, short: latest.commercial_short },
        nonCommercials: { long: latest.non_commercial_long, short: latest.non_commercial_short },
        openInterest: latest.open_interest,
      };
      // Save to history
      await saveCOTHistory(cotData);
      return cotData;
    }
    return { symbol, error: 'No COT data found' };
  }
}

// Save sentiment after analysis
export async function saveSentimentHistory({ symbol, sentiment, score, summary }) {
  const date = new Date();
  await SentimentHistory.create({ symbol, date, sentiment, score, summary });
}

// Save COT after fetching
export async function saveCOTHistory({ symbol, reportDate, commercials, nonCommercials, openInterest }) {
  await COTHistory.create({ symbol, reportDate, commercials, nonCommercials, openInterest });
}

// Fetch sentiment history
export async function getSentimentHistory(symbol) {
  return SentimentHistory.find({ symbol }).sort({ date: 1 });
}

// Fetch COT history
export async function getCOTHistory(symbol) {
  return COTHistory.find({ symbol }).sort({ reportDate: 1 });
}

export default new NewsService();