/**
 * Central Type Definitions Export
 * 
 * This file serves as the single source of truth for all TypeScript types
 * used throughout the trading signals application. It ensures consistency
 * between frontend, backend, database, and API layers.
 * 
 * @version 1.0.0
 */

// ============================================================================
// COMMON TYPES
// ============================================================================

export * from './common';

// ============================================================================
// SIGNAL TYPES
// ============================================================================

export * from './signals';

// ============================================================================
// USER TYPES
// ============================================================================

export * from './users';

// ============================================================================
// MARKET DATA TYPES
// ============================================================================

export * from './market';

// ============================================================================
// API TYPES
// ============================================================================

export * from './api';

// ============================================================================
// WEBSOCKET TYPES
// ============================================================================

export * from './websocket';

// ============================================================================
// TYPE UTILITIES AND HELPERS
// ============================================================================

/**
 * Utility type for making all properties optional recursively
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * Utility type for making specific properties required
 */
export type RequireFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * Utility type for omitting specific properties
 */
export type OmitFields<T, K extends keyof T> = Omit<T, K>;

/**
 * Utility type for picking specific properties
 */
export type PickFields<T, K extends keyof T> = Pick<T, K>;

/**
 * Utility type for creating a type with all properties as strings
 */
export type Stringify<T> = {
  [K in keyof T]: string;
};

/**
 * Utility type for database document (with _id)
 */
export type DatabaseDocument<T> = T & {
  _id: string;
  createdAt: string;
  updatedAt: string;
};

/**
 * Utility type for API request with pagination
 */
export type PaginatedRequest<T = {}> = T & {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
};

/**
 * Utility type for API response with pagination
 */
export type PaginatedResponse<T> = {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
};

// ============================================================================
// VALIDATION SCHEMAS (ZOD)
// ============================================================================

import { z } from 'zod';

/**
 * Common validation schemas using Zod
 */
export const ValidationSchemas = {
  // Basic types
  ObjectId: z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format'),
  Email: z.string().email('Invalid email format'),
  Password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain uppercase letter')
    .regex(/[a-z]/, 'Password must contain lowercase letter')
    .regex(/\d/, 'Password must contain number')
    .regex(/[!@#$%^&*(),.?":{}|<>]/, 'Password must contain special character'),
  
  // Enums
  SignalType: z.enum(['BUY', 'SELL', 'HOLD']),
  SignalSource: z.enum(['AI', 'TRADITIONAL', 'UNIFIED', 'MANUAL']),
  SignalStatus: z.enum(['ACTIVE', 'CLOSED', 'EXPIRED', 'CANCELLED']),
  Timeframe: z.enum(['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN']),
  UserRole: z.enum(['user', 'admin', 'premium']),
  MarketType: z.enum(['stocks', 'forex', 'crypto', 'commodities', 'economics']),
  
  // Numbers
  Price: z.number().positive('Price must be positive'),
  Percentage: z.number().min(0).max(100, 'Percentage must be between 0 and 100'),
  Confidence: z.number().min(0).max(100, 'Confidence must be between 0 and 100'),
  
  // Dates
  Timestamp: z.string().datetime('Invalid timestamp format'),
  
  // Complex objects
  UnifiedSignal: z.object({
    id: z.string(),
    symbol: z.string().min(1, 'Symbol is required'),
    type: z.enum(['BUY', 'SELL', 'HOLD']),
    source: z.enum(['AI', 'TRADITIONAL', 'UNIFIED', 'MANUAL']),
    entryPrice: z.number().positive('Entry price must be positive'),
    stopLoss: z.number().positive().optional(),
    takeProfit: z.number().positive().optional(),
    confidence: z.number().min(0).max(100),
    timeframe: z.enum(['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN']),
    status: z.enum(['ACTIVE', 'CLOSED', 'EXPIRED', 'CANCELLED']),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    userId: z.string().optional(),
    metadata: z.object({
      version: z.string(),
      model: z.string().optional(),
      processingTime: z.number().optional(),
      tokens: z.number().optional(),
      tags: z.array(z.string()).optional()
    })
  }),
  
  User: z.object({
    id: z.string(),
    email: z.string().email(),
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    role: z.enum(['user', 'admin', 'premium']),
    emailVerified: z.boolean(),
    active: z.boolean(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
  }),
  
  MarketData: z.object({
    symbol: z.string().min(1, 'Symbol is required'),
    timeframe: z.enum(['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN']),
    timestamp: z.string().datetime(),
    open: z.number().positive(),
    high: z.number().positive(),
    low: z.number().positive(),
    close: z.number().positive(),
    volume: z.number().nonnegative(),
    source: z.string(),
    lastUpdated: z.string().datetime(),
    marketType: z.enum(['stocks', 'forex', 'crypto', 'commodities', 'economics'])
  }),
  
  APIResponse: z.object({
    status: z.enum(['success', 'error']),
    data: z.any().optional(),
    message: z.string().optional(),
    errors: z.array(z.object({
      field: z.string(),
      message: z.string(),
      code: z.string(),
      value: z.any().optional()
    })).optional(),
    meta: z.object({
      timestamp: z.string().datetime(),
      requestId: z.string(),
      version: z.string(),
      processingTime: z.number().optional(),
      source: z.string().optional()
    })
  })
};

// ============================================================================
// TYPE ASSERTION HELPERS
// ============================================================================

/**
 * Type assertion helper for runtime type checking
 */
export function assertType<T>(value: unknown, validator: (value: unknown) => value is T, errorMessage?: string): asserts value is T {
  if (!validator(value)) {
    throw new Error(errorMessage || 'Type assertion failed');
  }
}

/**
 * Safe type casting with validation
 */
export function safeCast<T>(value: unknown, validator: (value: unknown) => value is T): T | null {
  return validator(value) ? value : null;
}

/**
 * Validate and parse using Zod schema
 */
export function validateAndParse<T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; errors: z.ZodError } {
  const result = schema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  } else {
    return { success: false, errors: result.error };
  }
}

// ============================================================================
// CONSTANTS
// ============================================================================

export const CONSTANTS = {
  // API
  API_VERSION: '1.0.0',
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // Timeouts
  DEFAULT_TIMEOUT: 30000,
  WEBSOCKET_TIMEOUT: 60000,
  AI_REQUEST_TIMEOUT: 120000,
  
  // Limits
  MAX_SIGNALS_PER_REQUEST: 50,
  MAX_SYMBOLS_PER_REQUEST: 10,
  MAX_SUBSCRIPTION_CHANNELS: 20,
  
  // Cache TTL (in seconds)
  CACHE_TTL: {
    MARKET_DATA: 60,
    SIGNALS: 300,
    USER_PROFILE: 3600,
    TECHNICAL_INDICATORS: 300
  },
  
  // WebSocket
  WEBSOCKET_HEARTBEAT_INTERVAL: 30000,
  WEBSOCKET_RECONNECT_INTERVAL: 5000,
  MAX_WEBSOCKET_RECONNECT_ATTEMPTS: 10,
  
  // Rate limiting
  RATE_LIMIT: {
    API_REQUESTS_PER_MINUTE: 100,
    WEBSOCKET_MESSAGES_PER_MINUTE: 200,
    AI_REQUESTS_PER_HOUR: 50
  },
  
  // Signal generation
  MIN_SIGNAL_CONFIDENCE: 60,
  DEFAULT_SIGNAL_EXPIRY_HOURS: 24,
  MAX_ACTIVE_SIGNALS_PER_USER: 50,
  
  // File uploads
  MAX_AVATAR_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  
  // Pagination
  DEFAULT_PAGINATION: {
    page: 1,
    limit: 20,
    maxLimit: 100
  }
} as const;

// ============================================================================
// ERROR CODES
// ============================================================================

export const ERROR_CODES = {
  // Authentication
  AUTH_REQUIRED: 'AUTH_REQUIRED',
  AUTH_INVALID_TOKEN: 'AUTH_INVALID_TOKEN',
  AUTH_TOKEN_EXPIRED: 'AUTH_TOKEN_EXPIRED',
  AUTH_INVALID_CREDENTIALS: 'AUTH_INVALID_CREDENTIALS',
  
  // Authorization
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  FEATURE_NOT_AVAILABLE: 'FEATURE_NOT_AVAILABLE',
  SUBSCRIPTION_REQUIRED: 'SUBSCRIPTION_REQUIRED',
  
  // Validation
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  REQUIRED_FIELD_MISSING: 'REQUIRED_FIELD_MISSING',
  INVALID_FORMAT: 'INVALID_FORMAT',
  
  // Resources
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  
  // Rate limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  TOO_MANY_REQUESTS: 'TOO_MANY_REQUESTS',
  
  // External services
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  AI_SERVICE_UNAVAILABLE: 'AI_SERVICE_UNAVAILABLE',
  MARKET_DATA_UNAVAILABLE: 'MARKET_DATA_UNAVAILABLE',
  
  // System
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  MAINTENANCE_MODE: 'MAINTENANCE_MODE',
  
  // WebSocket
  WEBSOCKET_CONNECTION_FAILED: 'WEBSOCKET_CONNECTION_FAILED',
  WEBSOCKET_AUTHENTICATION_FAILED: 'WEBSOCKET_AUTHENTICATION_FAILED',
  WEBSOCKET_SUBSCRIPTION_FAILED: 'WEBSOCKET_SUBSCRIPTION_FAILED'
} as const;

// ============================================================================
// SUCCESS MESSAGES
// ============================================================================

export const SUCCESS_MESSAGES = {
  // Authentication
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  REGISTRATION_SUCCESS: 'Registration successful',
  PASSWORD_CHANGED: 'Password changed successfully',
  
  // Signals
  SIGNAL_CREATED: 'Signal created successfully',
  SIGNAL_UPDATED: 'Signal updated successfully',
  SIGNAL_DELETED: 'Signal deleted successfully',
  
  // User
  PROFILE_UPDATED: 'Profile updated successfully',
  PREFERENCES_UPDATED: 'Preferences updated successfully',
  
  // Subscriptions
  SUBSCRIPTION_CREATED: 'Subscription created successfully',
  SUBSCRIPTION_CANCELLED: 'Subscription cancelled successfully',
  
  // WebSocket
  WEBSOCKET_CONNECTED: 'WebSocket connected successfully',
  SUBSCRIPTION_CONFIRMED: 'Subscription confirmed'
} as const;
