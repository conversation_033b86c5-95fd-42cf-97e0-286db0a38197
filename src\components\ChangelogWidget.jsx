import React, { useState } from 'react';
import Modal from './ui/Modal.jsx';
import Button from './ui/Button.jsx';

const CHANGELOG = [
  {
    date: '2024-06-01',
    title: 'App Store & Play Store Ready',
    details: [
      'Full legal compliance (GDPR, CCPA, disclaimers)',
      'Privacy Policy & Terms linked everywhere',
      'PWA install prompt and push notification opt-in',
    ],
  },
  {
    date: '2024-05-28',
    title: 'Security & Privacy',
    details: [
      '2FA (Two-Factor Authentication) for all users',
      'Data export and account deletion',
    ],
  },
  {
    date: '2024-05-20',
    title: 'Notification Center & Profile',
    details: [
      'Real-time notifications with unread count',
      'Profile modal with avatar, preferences, password',
    ],
  },
];

export default function ChangelogWidget() {
  const [open, setOpen] = useState(false);
  return (
    <>
      <button
        className="fixed bottom-6 left-6 z-50 bg-green-600 hover:bg-green-700 text-white rounded-full shadow-lg p-4 focus:outline-none"
        onClick={() => setOpen(true)}
        aria-label="What's New?"
      >
        <span role="img" aria-label="Changelog" className="text-xl">📝</span>
      </button>
      <Modal open={open} onClose={() => setOpen(false)}>
        <div className="w-full max-w-sm p-4">
          <h2 className="text-lg font-bold mb-2">What's New?</h2>
          <ul className="space-y-4">
            {CHANGELOG.map((entry, i) => (
              <li key={i} className="border-b pb-2 last:border-b-0">
                <div className="text-xs text-gray-500 mb-1">{entry.date}</div>
                <div className="font-semibold mb-1">{entry.title}</div>
                <ul className="list-disc ml-5 text-sm text-gray-700 dark:text-gray-200">
                  {entry.details.map((d, j) => <li key={j}>{d}</li>)}
                </ul>
              </li>
            ))}
          </ul>
          <div className="flex justify-end mt-4">
            <Button type="button" onClick={() => setOpen(false)} variant="secondary">Close</Button>
          </div>
        </div>
      </Modal>
    </>
  );
} 