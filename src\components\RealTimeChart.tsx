/**
 * Real-Time Chart Component
 * 
 * Example component demonstrating real-time market data integration
 * with chart visualization using our WebSocket system.
 * 
 * Features:
 * - Real-time price updates
 * - Live signal overlays
 * - Connection status monitoring
 * - Performance optimized rendering
 * - Responsive design
 * 
 * @version 1.0.0
 */

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { UnifiedSignal } from '../types/signals';
import { Quote, MarketData } from '../types/market';
import { useRealTimeMarketData, useRealTimeSignals, useWebSocketContext } from '../context/WebSocketContext';

// ============================================================================
// COMPONENT INTERFACES
// ============================================================================

interface RealTimeChartProps {
  symbol: string;
  height?: number;
  showSignals?: boolean;
  showVolume?: boolean;
  timeframe?: string;
  onSignalClick?: (signal: UnifiedSignal) => void;
}

interface ChartDataPoint {
  timestamp: string;
  price: number;
  volume?: number;
  signal?: UnifiedSignal;
}

interface PriceTickerProps {
  symbol: string;
  currentPrice?: number;
  change?: number;
  changePercent?: number;
  volume?: number;
  lastUpdate?: string;
}

// ============================================================================
// PRICE TICKER COMPONENT
// ============================================================================

const PriceTicker: React.FC<PriceTickerProps> = ({
  symbol,
  currentPrice,
  change,
  changePercent,
  volume,
  lastUpdate
}) => {
  const [priceAnimation, setPriceAnimation] = useState<'up' | 'down' | null>(null);
  const prevPriceRef = useRef<number | undefined>(currentPrice);

  // Animate price changes
  useEffect(() => {
    if (currentPrice !== undefined && prevPriceRef.current !== undefined) {
      if (currentPrice > prevPriceRef.current) {
        setPriceAnimation('up');
      } else if (currentPrice < prevPriceRef.current) {
        setPriceAnimation('down');
      }
      
      // Clear animation after 1 second
      const timer = setTimeout(() => setPriceAnimation(null), 1000);
      return () => clearTimeout(timer);
    }
    prevPriceRef.current = currentPrice;
  }, [currentPrice]);

  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4
    }).format(price);
  };

  const formatVolume = (vol: number): string => {
    if (vol >= 1000000) {
      return `${(vol / 1000000).toFixed(1)}M`;
    } else if (vol >= 1000) {
      return `${(vol / 1000).toFixed(1)}K`;
    }
    return vol.toString();
  };

  const getChangeColor = (): string => {
    if (change === undefined) return 'text-gray-600';
    return change >= 0 ? 'text-green-600' : 'text-red-600';
  };

  const getPriceAnimationClass = (): string => {
    switch (priceAnimation) {
      case 'up':
        return 'bg-green-100 text-green-800';
      case 'down':
        return 'bg-red-100 text-red-800';
      default:
        return '';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-4 mb-4">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{symbol}</h3>
          {lastUpdate && (
            <p className="text-xs text-gray-500">
              Last update: {new Date(lastUpdate).toLocaleTimeString()}
            </p>
          )}
        </div>
        
        <div className="text-right">
          {currentPrice !== undefined && (
            <div className={`text-2xl font-bold transition-all duration-300 rounded px-2 py-1 ${getPriceAnimationClass()}`}>
              {formatPrice(currentPrice)}
            </div>
          )}
          
          {change !== undefined && changePercent !== undefined && (
            <div className={`text-sm font-medium ${getChangeColor()}`}>
              {change >= 0 ? '+' : ''}{formatPrice(change)} ({change >= 0 ? '+' : ''}{changePercent.toFixed(2)}%)
            </div>
          )}
          
          {volume !== undefined && (
            <div className="text-xs text-gray-500">
              Volume: {formatVolume(volume)}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// SIMPLE CHART COMPONENT
// ============================================================================

const SimpleChart: React.FC<{
  data: ChartDataPoint[];
  height: number;
  showSignals: boolean;
  onSignalClick?: (signal: UnifiedSignal) => void;
}> = ({ data, height, showSignals, onSignalClick }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || data.length === 0) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    // Clear canvas
    ctx.clearRect(0, 0, rect.width, rect.height);

    // Calculate price range
    const prices = data.map(d => d.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;

    // Draw price line
    ctx.strokeStyle = '#3B82F6';
    ctx.lineWidth = 2;
    ctx.beginPath();

    data.forEach((point, index) => {
      const x = (index / (data.length - 1)) * rect.width;
      const y = rect.height - ((point.price - minPrice) / priceRange) * rect.height;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // Draw signals if enabled
    if (showSignals) {
      data.forEach((point, index) => {
        if (point.signal) {
          const x = (index / (data.length - 1)) * rect.width;
          const y = rect.height - ((point.price - minPrice) / priceRange) * rect.height;

          // Draw signal marker
          ctx.fillStyle = point.signal.type === 'BUY' ? '#10B981' : '#EF4444';
          ctx.beginPath();
          ctx.arc(x, y, 6, 0, 2 * Math.PI);
          ctx.fill();

          // Draw signal label
          ctx.fillStyle = '#FFFFFF';
          ctx.font = '10px Arial';
          ctx.textAlign = 'center';
          ctx.fillText(point.signal.type, x, y + 3);
        }
      });
    }

    // Draw grid lines
    ctx.strokeStyle = '#E5E7EB';
    ctx.lineWidth = 1;
    
    // Horizontal grid lines
    for (let i = 0; i <= 4; i++) {
      const y = (i / 4) * rect.height;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(rect.width, y);
      ctx.stroke();
    }

    // Vertical grid lines
    for (let i = 0; i <= 6; i++) {
      const x = (i / 6) * rect.width;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, rect.height);
      ctx.stroke();
    }

  }, [data, height, showSignals]);

  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!showSignals || !onSignalClick) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    
    // Find nearest signal
    const clickIndex = Math.round((x / rect.width) * (data.length - 1));
    const clickedPoint = data[clickIndex];
    
    if (clickedPoint?.signal) {
      onSignalClick(clickedPoint.signal);
    }
  };

  return (
    <div className="relative">
      <canvas
        ref={canvasRef}
        width="100%"
        height={height}
        className="w-full border rounded"
        style={{ height: `${height}px` }}
        onClick={handleCanvasClick}
      />
      {showSignals && (
        <div className="absolute top-2 right-2 text-xs text-gray-500 bg-white px-2 py-1 rounded shadow">
          Click signals for details
        </div>
      )}
    </div>
  );
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

const RealTimeChart: React.FC<RealTimeChartProps> = ({
  symbol,
  height = 400,
  showSignals = true,
  showVolume = false,
  timeframe = 'M5',
  onSignalClick
}) => {
  // WebSocket integration
  const { quotes, marketData } = useRealTimeMarketData([symbol]);
  const signals = useRealTimeSignals([symbol]);
  const { isConnected, connectionStatus } = useWebSocketContext();

  // Local state
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [maxDataPoints] = useState(100);

  // Get current quote
  const currentQuote = quotes.get(symbol);
  const currentMarketData = marketData.get(symbol);

  // Update chart data when new market data arrives
  useEffect(() => {
    if (currentQuote) {
      const newDataPoint: ChartDataPoint = {
        timestamp: currentQuote.timestamp,
        price: currentQuote.price,
        volume: showVolume ? currentQuote.volume : undefined
      };

      setChartData(prev => {
        const updated = [...prev, newDataPoint];
        // Keep only the latest maxDataPoints
        return updated.slice(-maxDataPoints);
      });
    }
  }, [currentQuote, showVolume, maxDataPoints]);

  // Add signals to chart data
  const chartDataWithSignals = useMemo(() => {
    if (!showSignals || signals.length === 0) {
      return chartData;
    }

    return chartData.map(dataPoint => {
      // Find signal that matches this timestamp (within 1 minute)
      const matchingSignal = signals.find(signal => {
        const signalTime = new Date(signal.createdAt).getTime();
        const dataTime = new Date(dataPoint.timestamp).getTime();
        return Math.abs(signalTime - dataTime) < 60000; // 1 minute tolerance
      });

      return {
        ...dataPoint,
        signal: matchingSignal
      };
    });
  }, [chartData, signals, showSignals]);

  // Connection status indicator
  const renderConnectionStatus = () => {
    if (!isConnected) {
      return (
        <div className="absolute top-4 left-4 bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm">
          Disconnected
        </div>
      );
    }

    return (
      <div className="absolute top-4 left-4 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
        Live
      </div>
    );
  };

  // Loading state
  if (chartData.length === 0) {
    return (
      <div className="space-y-4">
        <PriceTicker symbol={symbol} />
        <div 
          className="flex items-center justify-center border rounded bg-gray-50"
          style={{ height: `${height}px` }}
        >
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-500">Loading chart data...</p>
            <p className="text-sm text-gray-400 mt-2">
              Status: {connectionStatus}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Price ticker */}
      <PriceTicker
        symbol={symbol}
        currentPrice={currentQuote?.price}
        change={currentQuote?.change}
        changePercent={currentQuote?.changePercent}
        volume={currentQuote?.volume}
        lastUpdate={currentQuote?.timestamp}
      />

      {/* Chart */}
      <div className="relative">
        {renderConnectionStatus()}
        <SimpleChart
          data={chartDataWithSignals}
          height={height}
          showSignals={showSignals}
          onSignalClick={onSignalClick}
        />
      </div>

      {/* Chart info */}
      <div className="flex justify-between items-center text-sm text-gray-500">
        <span>
          Timeframe: {timeframe} | Data points: {chartData.length}
        </span>
        {showSignals && (
          <span>
            Signals: {signals.length}
          </span>
        )}
      </div>
    </div>
  );
};

export default RealTimeChart;
