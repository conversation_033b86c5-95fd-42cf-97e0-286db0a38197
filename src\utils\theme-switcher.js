/**
 * Theme Switcher for Trading Signals App
 * 
 * This file provides functionality to switch between light and dark themes.
 */

// Theme constants
const THEME_STORAGE_KEY = 'trading_signals_theme';
const DARK_THEME = 'dark';
const LIGHT_THEME = 'light';

// Theme colors
const THEME_COLORS = {
  [DARK_THEME]: {
    background: '#121826',
    text: '#e4e6eb',
    primary: '#3a7bd5',
    secondary: '#00d2d3',
    accent: '#ffd700',
    border: '#2a3a5a',
    card: '#1e293b',
    success: '#00b894',
    warning: '#fdcb6e',
    danger: '#ff7675'
  },
  [LIGHT_THEME]: {
    background: '#f8fafc',
    text: '#334155',
    primary: '#3a7bd5',
    secondary: '#00a8a8',
    accent: '#f59e0b',
    border: '#e2e8f0',
    card: '#ffffff',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444'
  }
};

// Initialize theme
function initTheme() {
  console.log('Initializing theme switcher');
  
  // Get saved theme or use system preference
  const savedTheme = localStorage.getItem(THEME_STORAGE_KEY);
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  const initialTheme = savedTheme || (prefersDark ? DARK_THEME : LIGHT_THEME);
  
  // Apply the theme
  applyTheme(initialTheme);
  
  // Set up theme toggle button
  setupThemeToggle();
  
  // Listen for system theme changes
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
    if (!localStorage.getItem(THEME_STORAGE_KEY)) {
      applyTheme(e.matches ? DARK_THEME : LIGHT_THEME);
    }
  });
}

// Apply theme to document
function applyTheme(theme) {
  console.log(`Applying ${theme} theme`);
  
  // Set data-theme attribute on document
  document.documentElement.setAttribute('data-theme', theme);
  
  // Add/remove dark class on body
  if (theme === DARK_THEME) {
    document.body.classList.add('dark');
  } else {
    document.body.classList.remove('dark');
  }
  
  // Apply CSS variables
  const colors = THEME_COLORS[theme];
  Object.keys(colors).forEach(key => {
    document.documentElement.style.setProperty(`--color-${key}`, colors[key]);
  });
  
  // Update theme toggle button state
  updateToggleButton(theme);
  
  // Save theme preference
  localStorage.setItem(THEME_STORAGE_KEY, theme);
}

// Set up theme toggle button
function setupThemeToggle() {
  // Find all theme toggle buttons
  const toggleButtons = document.querySelectorAll('.theme-toggle');
  
  toggleButtons.forEach(button => {
    button.addEventListener('click', () => {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === DARK_THEME ? LIGHT_THEME : DARK_THEME;
      applyTheme(newTheme);
    });
  });
  
  // Update initial button state
  updateToggleButton(document.documentElement.getAttribute('data-theme'));
}

// Update toggle button appearance
function updateToggleButton(theme) {
  const toggleButtons = document.querySelectorAll('.theme-toggle');
  
  toggleButtons.forEach(button => {
    // Update icon
    const iconElement = button.querySelector('i, .icon');
    if (iconElement) {
      if (theme === DARK_THEME) {
        iconElement.className = iconElement.className.replace('moon', 'sun');
      } else {
        iconElement.className = iconElement.className.replace('sun', 'moon');
      }
    }
    
    // Update text if present
    const textElement = button.querySelector('.text');
    if (textElement) {
      textElement.textContent = theme === DARK_THEME ? 'Light Mode' : 'Dark Mode';
    }
  });
}

// Initialize theme when DOM is loaded
document.addEventListener('DOMContentLoaded', initTheme);

// Export functions for external use
window.ThemeSwitcher = {
  toggle: function() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === DARK_THEME ? LIGHT_THEME : DARK_THEME;
    applyTheme(newTheme);
  },
  setTheme: applyTheme,
  getCurrentTheme: function() {
    return document.documentElement.getAttribute('data-theme');
  }
};

console.log('Theme switcher loaded');
