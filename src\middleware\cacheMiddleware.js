/**
 * Cache Middleware for API Responses
 * 
 * This middleware caches API responses.
 */

const cacheService = require('../services/cacheService');
const logger = require('../utils/logger');

/**
 * Cache API response
 * @param {number} ttl - Cache TTL in seconds
 * @param {Function} keyGenerator - Function to generate cache key (optional)
 * @returns {Function} Express middleware
 */
function cacheResponse(ttl = cacheService.DEFAULT_TTL.MEDIUM, keyGenerator = null) {
  return (req, res, next) => {
    try {
      // Generate cache key
      const cacheKey = keyGenerator 
        ? keyGenerator(req)
        : generateDefaultCacheKey(req);
      
      // Try to get from cache
      const cachedData = cacheService.get(cacheKey);
      
      if (cachedData) {
        logger.debug(`Cache hit for ${req.method} ${req.originalUrl}`);
        return res.status(cachedData.status).json(cachedData.data);
      }
      
      logger.debug(`Cache miss for ${req.method} ${req.originalUrl}`);
      
      // Store original json method
      const originalJson = res.json;
      
      // Override json method to cache the response
      res.json = function(data) {
        // Cache the response
        const responseData = {
          status: res.statusCode,
          data: data
        };
        
        cacheService.set(cacheKey, responseData, ttl);
        
        // Call the original json method
        return originalJson.call(this, data);
      };
      
      next();
    } catch (error) {
      logger.error('Error in cache middleware', error);
      next();
    }
  };
}

/**
 * Generate default cache key from request
 * @param {Object} req - Express request
 * @returns {string} Cache key
 * @private
 */
function generateDefaultCacheKey(req) {
  const method = req.method;
  const path = req.originalUrl || req.url;
  const query = JSON.stringify(req.query || {});
  const user = req.user ? req.user.userId : 'anonymous';
  
  return `api_cache:${method}:${path}:${query}:${user}`;
}

/**
 * Clear cache for specific patterns
 * @param {string|Array} patterns - Pattern or array of patterns to match
 * @returns {Function} Express middleware
 */
function clearCache(patterns) {
  return (req, res, next) => {
    try {
      const patternsArray = Array.isArray(patterns) ? patterns : [patterns];
      
      // Store original end method
      const originalEnd = res.end;
      
      // Override end method to clear cache after successful response
      res.end = function(chunk, encoding) {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          patternsArray.forEach(pattern => {
            const count = cacheService.invalidateByPattern(pattern);
            logger.debug(`Cleared ${count} cache entries matching pattern: ${pattern}`);
          });
        }
        
        // Call the original end method
        return originalEnd.call(this, chunk, encoding);
      };
      
      next();
    } catch (error) {
      logger.error('Error in clear cache middleware', error);
      next();
    }
  };
}

module.exports = {
  cacheResponse,
  clearCache
};
