/**
 * Market News Routes for Trading Signals App
 */

const logger = require('../utils/logger');

module.exports = function(app, apiCache, API_CONFIG) {
  // Get market news
  app.get('/api/market-news', (req, res) => {
    // Create cache key
    const cacheKey = 'market_news';
    
    // Check cache first
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      return res.json(cachedData);
    }
    
    // Mock market news data
    const news = [
      {
        title: 'Fed Signals Potential Rate Cut in September',
        summary: 'Federal Reserve officials indicated they may consider cutting interest rates in September if inflation continues to moderate.',
        source: 'Financial Times',
        url: 'https://www.ft.com',
        date: '2023-07-05T14:30:00Z',
        impact: 'high'
      },
      {
        title: 'ECB Maintains Current Interest Rate Levels',
        summary: 'The European Central Bank decided to maintain current interest rate levels, citing ongoing inflation concerns.',
        source: 'Bloomberg',
        url: 'https://www.bloomberg.com',
        date: '2023-07-05T12:00:00Z',
        impact: 'medium'
      },
      {
        title: 'Oil Prices Drop on Increased US Inventory',
        summary: 'Crude oil prices fell after US inventory data showed a larger-than-expected increase in stockpiles.',
        source: 'Reuters',
        url: 'https://www.reuters.com',
        date: '2023-07-05T10:15:00Z',
        impact: 'medium'
      }
    ];
    
    // Cache the response
    apiCache.set(cacheKey, { news }, 300); // 5 minutes TTL
    
    return res.json({ news });
  });
  
  // Get market news for a specific symbol
  app.get('/api/market-news/:symbol', (req, res) => {
    const symbol = req.params.symbol;
    
    // Create cache key
    const cacheKey = `market_news_${symbol}`;
    
    // Check cache first
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      return res.json(cachedData);
    }
    
    // Generate symbol-specific news
    let news = [];
    
    switch (symbol) {
      case 'EURUSD':
        news = [
          {
            title: 'ECB Hints at Potential Rate Hike in Next Meeting',
            summary: 'European Central Bank officials suggested they may consider raising interest rates at their next policy meeting.',
            source: 'Financial Times',
            url: 'https://www.ft.com',
            date: '2023-07-05T14:30:00Z',
            impact: 'high'
          },
          {
            title: 'Euro Zone Inflation Drops to 2.9%',
            summary: 'Euro zone inflation fell to 2.9% in June, approaching the ECB\'s 2% target.',
            source: 'Bloomberg',
            url: 'https://www.bloomberg.com',
            date: '2023-07-05T12:00:00Z',
            impact: 'medium'
          }
        ];
        break;
      case 'XAUUSD':
        news = [
          {
            title: 'Gold Prices Surge on Geopolitical Tensions',
            summary: 'Gold prices reached a new high as investors seek safe-haven assets amid rising geopolitical tensions.',
            source: 'Reuters',
            url: 'https://www.reuters.com',
            date: '2023-07-05T14:30:00Z',
            impact: 'high'
          },
          {
            title: 'Central Banks Increase Gold Reserves',
            summary: 'Several central banks reported increasing their gold reserves in the second quarter of 2023.',
            source: 'Bloomberg',
            url: 'https://www.bloomberg.com',
            date: '2023-07-05T12:00:00Z',
            impact: 'medium'
          }
        ];
        break;
      case 'BTCUSD':
        news = [
          {
            title: 'Bitcoin ETF Approval Expected Soon',
            summary: 'Analysts predict that the SEC may approve a spot Bitcoin ETF in the coming months.',
            source: 'CoinDesk',
            url: 'https://www.coindesk.com',
            date: '2023-07-05T14:30:00Z',
            impact: 'high'
          },
          {
            title: 'Major Bank Launches Crypto Custody Service',
            summary: 'A major financial institution announced the launch of a cryptocurrency custody service for institutional clients.',
            source: 'Bloomberg',
            url: 'https://www.bloomberg.com',
            date: '2023-07-05T12:00:00Z',
            impact: 'medium'
          }
        ];
        break;
      default:
        news = [
          {
            title: 'Markets React to Fed Minutes',
            summary: 'Financial markets showed mixed reactions to the latest Federal Reserve meeting minutes.',
            source: 'Financial Times',
            url: 'https://www.ft.com',
            date: '2023-07-05T14:30:00Z',
            impact: 'medium'
          },
          {
            title: 'Global Economic Outlook Improves',
            summary: 'The IMF revised its global economic growth forecast upward for the remainder of 2023.',
            source: 'Reuters',
            url: 'https://www.reuters.com',
            date: '2023-07-05T12:00:00Z',
            impact: 'medium'
          }
        ];
    }
    
    // Cache the response
    apiCache.set(cacheKey, { news }, 300); // 5 minutes TTL
    
    return res.json({ news });
  });
};
