/**
 * Sentry configuration for server-side error tracking and monitoring
 * 
 * This file initializes Sentry for Node.js server-side error tracking.
 * It should be imported as early as possible in the application lifecycle.
 */

const Sentry = require('@sentry/node');

/**
 * Initialize Sentry with the provided configuration
 */
const initSentry = (app) => {
  // Only initialize in production to avoid noise during development
  if (process.env.NODE_ENV === 'production') {
    // Initialize Sentry
    Sentry.init({
      dsn: process.env.SENTRY_DSN || "https://<EMAIL>/4509316906811472",
      integrations: [
        // Enable HTTP calls tracing
        new Sentry.Integrations.Http({ tracing: true }),
        // Enable Express.js middleware tracing
        new Sentry.Integrations.Express({ app }),
      ],
      // Set tracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring
      tracesSampleRate: 1.0,
    });

    // RequestHandler creates a separate execution context using domains, so that every
    // transaction/span/breadcrumb is attached to its own Hub instance
    app.use(Sentry.Handlers.requestHandler());
    
    // TracingHandler creates a trace for every incoming request
    app.use(Sentry.Handlers.tracingHandler());
    
    console.log('Sentry initialized for production environment');
  } else {
    console.log('Sentry not initialized in development environment');
  }
};

/**
 * Add Sentry error handler to Express app
 * This should be added after all controllers but before any other error middleware
 */
const addSentryErrorHandler = (app) => {
  if (process.env.NODE_ENV === 'production') {
    app.use(Sentry.Handlers.errorHandler());
  }
};

/**
 * Capture an exception with Sentry
 * @param {Error} error - The error to capture
 * @param {Object} context - Additional context information
 */
const captureException = (error, context = {}) => {
  if (process.env.NODE_ENV === 'production') {
    Sentry.captureException(error, { 
      extra: context 
    });
  } else {
    console.error('Error captured (not sent to Sentry in dev):', error, context);
  }
};

/**
 * Set user information for Sentry
 * @param {Object} user - User information
 */
const setUser = (user) => {
  if (process.env.NODE_ENV === 'production') {
    Sentry.setUser(user);
  }
};

/**
 * Clear user information from Sentry
 */
const clearUser = () => {
  if (process.env.NODE_ENV === 'production') {
    Sentry.setUser(null);
  }
};

module.exports = {
  initSentry,
  addSentryErrorHandler,
  captureException,
  setUser,
  clearUser,
  Sentry
};
