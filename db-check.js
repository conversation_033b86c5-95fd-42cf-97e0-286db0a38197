require('dotenv').config();
const { connect, close } = require('./db/mongo');

async function checkDatabase() {
  console.log('Checking MongoDB connection...');
  try {
    const db = await connect();
    console.log('✅ MongoDB connection successful!');
    console.log(`Connected to database: ${db.databaseName}`);
    
    // List collections
    const collections = await db.listCollections().toArray();
    console.log('\nAvailable collections:');
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
    await close();
    console.log('\nConnection closed successfully.');
  } catch (error) {
    console.error('❌ MongoDB connection failed!');
    console.error(error);
  }
}

checkDatabase(); 