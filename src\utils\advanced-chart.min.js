/**
 * Advanced Chart Module for Trading Signals App (Minified)
 */
!function(){const t={candlestick:{upColor:"#26a69a",downColor:"#ef5350",borderVisible:!1,wickUpColor:"#26a69a",wickDownColor:"#ef5350"},volume:{upColor:"rgba(38, 166, 154, 0.5)",downColor:"rgba(239, 83, 80, 0.5)"},sma:{color:"rgba(4, 111, 232, 1)",lineWidth:2},ema:{color:"rgba(255, 152, 0, 1)",lineWidth:2},rsi:{color:"rgba(76, 175, 80, 1)",lineWidth:1.5,overbought:70,oversold:30},macd:{fastPeriod:12,slowPeriod:26,signalPeriod:9,macdColor:"rgba(76, 175, 80, 1)",signalColor:"rgba(255, 82, 82, 1)",histColor:"rgba(187, 134, 252, 0.5)"},bollingerBands:{period:20,stdDev:2,upperColor:"rgba(76, 175, 80, 0.5)",middleColor:"rgba(255, 255, 255, 0.5)",lowerColor:"rgba(255, 82, 82, 0.5)"}},e={light:{backgroundColor:"#ffffff",textColor:"#333333",gridColor:"rgba(0, 0, 0, 0.1)",crosshairColor:"#758696"},dark:{backgroundColor:"#131722",textColor:"#d1d4dc",gridColor:"rgba(255, 255, 255, 0.1)",crosshairColor:"#758696"}};let a={},o={};function r(t,e={}){if(!window.LightweightCharts)return console.error("LightweightCharts library not loaded"),null;const r=document.getElementById(t);if(!r)return console.error(`Container element with ID "${t}" not found`),null;r.innerHTML="";const i=window.LightweightCharts.createChart(r,{width:e.width||r.clientWidth,height:e.height||400,layout:{backgroundColor:e.backgroundColor||"#131722",textColor:e.textColor||"#d1d4dc"},grid:{vertLines:{color:e.gridColor||"#2B2B43"},horzLines:{color:e.gridColor||"#363C4E"}},crosshair:{mode:window.LightweightCharts.CrosshairMode.Normal,vertLine:{width:1,color:e.crosshairColor||"#758696",style:1},horzLine:{width:1,color:e.crosshairColor||"#758696",style:1}},timeScale:{timeVisible:!0,secondsVisible:!1,borderColor:e.gridColor||"#2B2B43"},rightPriceScale:{borderColor:e.gridColor||"#2B2B43"}});return a[t]=i,o[t]={},i}function i(e,a,r={}){if(!e||!a)return null;const i=Object.assign({},t.candlestick,r);return e.addCandlestickSeries(i)}function n(e,a,r={}){if(!e||!a)return null;const i=Object.assign({},t.volume,r),n=e.addHistogramSeries({color:i.upColor,priceFormat:{type:"volume"},priceScaleId:"volume",scaleMargins:{top:.8,bottom:0}});return n.applyOptions({color:t=>a.closes[t]>=a.opens[t]?i.upColor:i.downColor}),n}function l(e,a,r,i={}){if(!e||!a||!r)return null;const n=Object.assign({},t.sma,i),l=e.addLineSeries({color:n.color,lineWidth:n.lineWidth,priceLineVisible:!1,lastValueVisible:!0,title:`SMA (${r})`});return o[a].sma=l,l}function s(e,a,r,i={}){if(!e||!a||!r)return null;const n=Object.assign({},t.ema,i),l=e.addLineSeries({color:n.color,lineWidth:n.lineWidth,priceLineVisible:!1,lastValueVisible:!0,title:`EMA (${r})`});return o[a].ema=l,l}function c(e,a,r={}){if(!e||!a)return null;const i=Object.assign({},t.rsi,r),n=e.addLineSeries({color:i.color,lineWidth:i.lineWidth,priceLineVisible:!1,lastValueVisible:!0,title:"RSI (14)",priceScaleId:"rsi",priceFormat:{type:"price",precision:2,minMove:.01},autoscaleInfoProvider:()=>({priceRange:{minValue:0,maxValue:100},margins:{above:5,below:5}}),scaleMargins:{top:.1,bottom:.1}});return n.createPriceLine({price:i.overbought,color:i.color,lineWidth:1,lineStyle:2,axisLabelVisible:!0,title:"Overbought"}),n.createPriceLine({price:i.oversold,color:i.color,lineWidth:1,lineStyle:2,axisLabelVisible:!0,title:"Oversold"}),o[a].rsi=n,n}function d(e,a,r={}){if(!e||!a)return null;const i=Object.assign({},t.macd,r),n=e.addLineSeries({color:i.macdColor,lineWidth:2,priceLineVisible:!1,lastValueVisible:!0,title:"MACD",priceScaleId:"macd",priceFormat:{type:"price",precision:2,minMove:.01},scaleMargins:{top:.7,bottom:0}}),l=e.addLineSeries({color:i.signalColor,lineWidth:1,priceLineVisible:!1,lastValueVisible:!0,title:"Signal",priceScaleId:"macd",priceFormat:{type:"price",precision:2,minMove:.01},scaleMargins:{top:.7,bottom:0}}),s=e.addHistogramSeries({color:i.histColor,priceLineVisible:!1,lastValueVisible:!1,title:"Histogram",priceScaleId:"macd",priceFormat:{type:"price",precision:2,minMove:.01},scaleMargins:{top:.7,bottom:0}});return o[a].macd={macd:n,signal:l,histogram:s},{macd:n,signal:l,histogram:s}}function h(e,a,r={}){if(!e||!a)return null;const i=Object.assign({},t.bollingerBands,r),n=e.addLineSeries({color:i.upperColor,lineWidth:1,priceLineVisible:!1,lastValueVisible:!0,title:`Upper Band (${i.period}, ${i.stdDev})`}),l=e.addLineSeries({color:i.middleColor,lineWidth:1,priceLineVisible:!1,lastValueVisible:!0,title:`Middle Band (${i.period})`}),s=e.addLineSeries({color:i.lowerColor,lineWidth:1,priceLineVisible:!1,lastValueVisible:!0,title:`Lower Band (${i.period}, ${i.stdDev})`});return o[a].bollingerBands={upper:n,middle:l,lower:s},{upper:n,middle:l,lower:s}}function p(t,e){if(!t||!e||!e.dates||!e.opens||!e.highs||!e.lows||!e.closes)return null;const a=e.dates.map((t,a)=>({time:new Date(t).getTime()/1e3,open:e.opens[a],high:e.highs[a],low:e.lows[a],close:e.closes[a]}));return t.setData(a),t}function g(t,e){if(!t||!e||!e.dates||!e.volumes)return null;const a=e.dates.map((t,a)=>({time:new Date(t).getTime()/1e3,value:e.volumes[a],color:e.closes[a]>=e.opens[a]?"rgba(38, 166, 154, 0.5)":"rgba(239, 83, 80, 0.5)"}));return t.setData(a),t}function m(t,e){const a=document.getElementById(t);if(!a)return;const o=a.getAttribute("data-theme")||"dark",r=e[o];if(!r)return;const i=window.advancedChart.getChartInstance(t);i&&i.applyOptions({layout:{backgroundColor:r.backgroundColor,textColor:r.textColor},grid:{vertLines:{color:r.gridColor},horzLines:{color:r.gridColor}},crosshair:{vertLine:{color:r.crosshairColor},horzLine:{color:r.crosshairColor}}})}window.addEventListener("themechange",t=>{const a=t.detail.theme,o=a&&"dark"===a?e.dark:e.light;Object.keys(window.advancedChart.getAllChartInstances()).forEach(t=>{const e=window.advancedChart.getChartInstance(t);e&&e.applyOptions({layout:{backgroundColor:o.backgroundColor,textColor:o.textColor},grid:{vertLines:{color:o.gridColor},horzLines:{color:o.gridColor}},crosshair:{vertLine:{color:o.crosshairColor},horzLine:{color:o.crosshairColor}}})})}),window.advancedChart={createChart:r,addCandlestickSeries:i,addVolumeSeries:n,addSMAIndicator:l,addEMAIndicator:s,addRSIIndicator:c,addMACDIndicator:d,addBollingerBandsIndicator:h,updateCandlestickSeries:p,updateVolumeSeries:g,applyTheme:m,getChartInstance:t=>a[t]||null,getAllChartInstances:()=>a,getSeriesInstance:(t,e)=>o[t]&&o[t][e]?o[t][e]:null,resizeChart:(t,e,o)=>{const r=a[t];r&&r.resize(e||r.clientWidth,o||400)},destroyChart:t=>{a[t]&&(a[t].remove(),delete a[t],delete o[t])}}}();
