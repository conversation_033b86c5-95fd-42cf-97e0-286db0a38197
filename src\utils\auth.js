/**
 * Authentication module for the Trading Signals App
 *
 * This file contains functions for user authentication, registration,
 * and password reset functionality.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Configure NProgress
    if (typeof NProgress !== 'undefined') {
        NProgress.configure({
            showSpinner: false,
            minimum: 0.1,
            easing: 'ease',
            speed: 500
        });
    }

    // Get form elements
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');
    const forgotPasswordForm = document.getElementById('forgot-password-form');

    // Get container elements
    const loginContainer = document.querySelector('.row:not(.d-none)');
    const registerContainer = document.getElementById('register-container');
    const forgotPasswordContainer = document.getElementById('forgot-password-container');

    // Get link elements
    const registerLink = document.getElementById('register-link');
    const loginLink = document.getElementById('login-link');
    const forgotPasswordLink = document.getElementById('forgot-password-link');
    const backToLoginLink = document.getElementById('back-to-login');

    // Get error message elements
    const loginError = document.getElementById('login-error');
    const registerError = document.getElementById('register-error');
    const forgotError = document.getElementById('forgot-error');
    const forgotSuccess = document.getElementById('forgot-success');

    // Add event listeners for form submission
    loginForm.addEventListener('submit', handleLogin);
    registerForm.addEventListener('submit', handleRegister);
    forgotPasswordForm.addEventListener('submit', handleForgotPassword);

    // Add event listeners for form switching
    registerLink.addEventListener('click', showRegisterForm);
    loginLink.addEventListener('click', showLoginForm);
    forgotPasswordLink.addEventListener('click', showForgotPasswordForm);
    backToLoginLink.addEventListener('click', showLoginForm);

    // Add event listeners for password toggle
    const togglePasswordElements = document.querySelectorAll('.toggle-password');
    togglePasswordElements.forEach(element => {
        element.addEventListener('click', togglePasswordVisibility);
        // Add keyboard support for accessibility
        element.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                togglePasswordVisibility.call(this);
            }
        });
    });

    // Function to handle login form submission
    function handleLogin(event) {
        event.preventDefault();

        // Get form values
        const email = document.getElementById('login-email').value;
        const password = document.getElementById('login-password').value;
        const submitButton = loginForm.querySelector('button[type="submit"]');

        // Hide any previous error messages
        loginError.classList.add('d-none');

        // Validate form
        if (!email || !password) {
            loginError.textContent = 'Please enter both email and password.';
            loginError.classList.remove('d-none');
            return;
        }

        // Disable submit button and show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Signing in...';

        // Start progress bar
        if (typeof NProgress !== 'undefined') {
            NProgress.start();
        }

        // Simulate API call for authentication
        simulateAuthentication(email, password)
            .then(response => {
                // Store user data in localStorage
                localStorage.setItem('user', JSON.stringify(response.user));
                localStorage.setItem('token', response.token);

                if (typeof NProgress !== 'undefined') {
                    NProgress.set(0.7);

                    setTimeout(() => {
                        NProgress.done();
                        // Redirect to dashboard
                        window.location.href = 'index.html';
                    }, 200);
                } else {
                    // Redirect to dashboard if NProgress is not available
                    window.location.href = 'index.html';
                }
            })
            .catch(error => {
                // Display error message
                loginError.textContent = error.message;
                loginError.classList.remove('d-none');

                // Re-enable submit button
                submitButton.disabled = false;
                submitButton.innerHTML = 'Sign In';

                if (typeof NProgress !== 'undefined') {
                    NProgress.done();
                }
            });
    }

    // Function to handle register form submission
    function handleRegister(event) {
        event.preventDefault();

        // Get form values
        const name = document.getElementById('register-name').value;
        const email = document.getElementById('register-email').value;
        const password = document.getElementById('register-password').value;
        const confirmPassword = document.getElementById('register-confirm-password').value;
        const submitButton = registerForm.querySelector('button[type="submit"]');

        // Hide any previous error messages
        registerError.classList.add('d-none');

        // Validate form
        if (!name || !email || !password || !confirmPassword) {
            registerError.textContent = 'Please fill in all fields.';
            registerError.classList.remove('d-none');
            return;
        }

        if (password !== confirmPassword) {
            registerError.textContent = 'Passwords do not match.';
            registerError.classList.remove('d-none');
            return;
        }

        if (password.length < 8) {
            registerError.textContent = 'Password must be at least 8 characters long.';
            registerError.classList.remove('d-none');
            return;
        }

        // Disable submit button and show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Creating account...';

        // Start progress bar
        if (typeof NProgress !== 'undefined') {
            NProgress.start();
        }

        // Simulate API call for registration
        simulateRegistration(name, email, password)
            .then(response => {
                // Store user data in localStorage
                localStorage.setItem('user', JSON.stringify(response.user));
                localStorage.setItem('token', response.token);

                if (typeof NProgress !== 'undefined') {
                    NProgress.set(0.7);

                    setTimeout(() => {
                        NProgress.done();
                        // Redirect to dashboard
                        window.location.href = 'index.html';
                    }, 200);
                } else {
                    // Redirect to dashboard if NProgress is not available
                    window.location.href = 'index.html';
                }
            })
            .catch(error => {
                // Display error message
                registerError.textContent = error.message;
                registerError.classList.remove('d-none');

                // Re-enable submit button
                submitButton.disabled = false;
                submitButton.innerHTML = 'Sign Up';

                if (typeof NProgress !== 'undefined') {
                    NProgress.done();
                }
            });
    }

    // Function to handle forgot password form submission
    function handleForgotPassword(event) {
        event.preventDefault();

        // Get form values
        const email = document.getElementById('forgot-email').value;
        const submitButton = forgotPasswordForm.querySelector('button[type="submit"]');

        // Hide any previous messages
        forgotError.classList.add('d-none');
        forgotSuccess.classList.add('d-none');

        // Validate form
        if (!email) {
            forgotError.textContent = 'Please enter your email address.';
            forgotError.classList.remove('d-none');
            return;
        }

        // Disable submit button and show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...';

        // Start progress bar
        if (typeof NProgress !== 'undefined') {
            NProgress.start();
        }

        // Simulate API call for password reset
        simulatePasswordReset(email)
            .then(result => {
                // Display success message
                forgotSuccess.textContent = result.message;
                forgotSuccess.classList.remove('d-none');

                // Clear form
                document.getElementById('forgot-email').value = '';

                if (typeof NProgress !== 'undefined') {
                    NProgress.set(0.7);
                }

                // For demo account, show password
                if (email === '<EMAIL>') {
                    forgotSuccess.innerHTML += '<br><br><strong>Demo Account:</strong> For demonstration purposes, the password is "password123"';
                }

                // Automatically redirect to login after 5 seconds
                setTimeout(() => {
                    if (typeof NProgress !== 'undefined') {
                        NProgress.done();
                    }
                    showLoginForm();
                }, 5000);
            })
            .catch(error => {
                // Display error message
                forgotError.textContent = error.message;
                forgotError.classList.remove('d-none');

                // Re-enable submit button
                submitButton.disabled = false;
                submitButton.innerHTML = 'Reset Password';

                if (typeof NProgress !== 'undefined') {
                    NProgress.done();
                }
            });
    }

    // Function to show register form
    function showRegisterForm(event) {
        event.preventDefault();

        // Start progress bar
        if (typeof NProgress !== 'undefined') {
            NProgress.start();
            NProgress.set(0.3);
        }

        // Hide login form and show register form
        loginContainer.classList.add('d-none');
        forgotPasswordContainer.classList.add('d-none');
        registerContainer.classList.remove('d-none');

        // Clear any error messages
        loginError.classList.add('d-none');
        forgotError.classList.add('d-none');
        forgotSuccess.classList.add('d-none');
        registerError.classList.add('d-none');

        // Clear login form
        loginForm.reset();

        // Complete progress bar
        if (typeof NProgress !== 'undefined') {
            setTimeout(() => {
                NProgress.done();
            }, 300);
        }
    }

    // Function to show login form
    function showLoginForm(event) {
        if (event) {
            event.preventDefault();
        }

        // Start progress bar
        if (typeof NProgress !== 'undefined') {
            NProgress.start();
            NProgress.set(0.3);
        }

        // Hide register form and forgot password form, show login form
        registerContainer.classList.add('d-none');
        forgotPasswordContainer.classList.add('d-none');
        loginContainer.classList.remove('d-none');

        // Clear any error messages
        loginError.classList.add('d-none');
        forgotError.classList.add('d-none');
        forgotSuccess.classList.add('d-none');
        registerError.classList.add('d-none');

        // Clear register form and forgot password form
        registerForm.reset();
        forgotPasswordForm.reset();

        // Complete progress bar
        if (typeof NProgress !== 'undefined') {
            setTimeout(() => {
                NProgress.done();
            }, 300);
        }
    }

    // Function to show forgot password form
    function showForgotPasswordForm(event) {
        event.preventDefault();

        // Start progress bar
        if (typeof NProgress !== 'undefined') {
            NProgress.start();
            NProgress.set(0.3);
        }

        // Hide login form and register form, show forgot password form
        loginContainer.classList.add('d-none');
        registerContainer.classList.add('d-none');
        forgotPasswordContainer.classList.remove('d-none');

        // Clear any error messages
        loginError.classList.add('d-none');
        forgotError.classList.add('d-none');
        forgotSuccess.classList.add('d-none');
        registerError.classList.add('d-none');

        // Clear login form
        loginForm.reset();

        // Complete progress bar
        if (typeof NProgress !== 'undefined') {
            setTimeout(() => {
                NProgress.done();
            }, 300);
        }
    }

    // Function to toggle password visibility
    function togglePasswordVisibility() {
        const passwordField = document.querySelector(this.getAttribute('toggle'));
        const isVisible = passwordField.type === 'text';

        // Toggle password visibility
        passwordField.type = isVisible ? 'password' : 'text';

        // Update icon
        if (isVisible) {
            this.classList.remove('fa-eye-slash');
            this.classList.add('fa-eye');
            this.setAttribute('aria-label', 'Show password');
        } else {
            this.classList.remove('fa-eye');
            this.classList.add('fa-eye-slash');
            this.setAttribute('aria-label', 'Hide password');
        }
    }

    // Function to simulate authentication API call
    function simulateAuthentication(email, password) {
        return new Promise((resolve, reject) => {
            // Simulate API delay
            setTimeout(() => {
                // Check if there's a saved user with this email
                const savedUsers = JSON.parse(localStorage.getItem('savedUsers') || '[]');
                const user = savedUsers.find(u => u.email === email);

                if (!user) {
                    // User doesn't exist - require registration first
                    reject({ message: 'No account found with this email. Please register first.' });
                    return;
                }

                // Verify password using hash comparison
                if (verifyPassword(password, user.passwordHash, user.salt)) {
                    // User found with matching password
                    resolve({
                        user: {
                            id: user.id,
                            name: user.name,
                            email: user.email,
                            createdAt: user.createdAt
                        },
                        token: generateToken()
                    });
                } else if (email === '<EMAIL>' && password === 'password123') {
                    // Demo account for easy testing
                    resolve({
                        user: {
                            id: 'demo_user',
                            name: 'Demo User',
                            email: '<EMAIL>',
                            createdAt: new Date().toISOString()
                        },
                        token: generateToken()
                    });
                } else {
                    // Password is incorrect
                    reject({ message: 'Incorrect password. Please try again.' });
                }
            }, 1000);
        });
    }

    // Function to simulate registration API call
    function simulateRegistration(name, email, password) {
        return new Promise((resolve, reject) => {
            // Simulate API delay
            setTimeout(() => {
                // For demo purposes, accept any valid email that's not already in use
                if (isValidEmail(email)) {
                    // Get saved users from localStorage
                    const savedUsers = JSON.parse(localStorage.getItem('savedUsers') || '[]');

                    // Check if email is already in use
                    if (savedUsers.some(user => user.email === email)) {
                        // For demo purposes, if the email is already in use but it's the demo account,
                        // we'll allow login anyway
                        if (email === '<EMAIL>') {
                            resolve({
                                user: {
                                    id: 'demo_user',
                                    name: 'Demo User',
                                    email: '<EMAIL>',
                                    createdAt: new Date().toISOString()
                                },
                                token: generateToken()
                            });
                            return;
                        }

                        reject({ message: 'Email is already in use. Please use a different email.' });
                    } else {
                        // Generate salt and hash password
                        const salt = generateSalt();
                        const passwordHash = hashPassword(password, salt);

                        // Create new user with hashed password
                        const newUser = {
                            id: generateUserId(),
                            name: name,
                            email: email,
                            passwordHash: passwordHash, // Store hashed password
                            salt: salt, // Store salt for verification
                            createdAt: new Date().toISOString()
                        };

                        // Add user to saved users
                        savedUsers.push(newUser);

                        // Save updated users list
                        localStorage.setItem('savedUsers', JSON.stringify(savedUsers));

                        // Return user data without password
                        const userResponse = {
                            id: newUser.id,
                            name: newUser.name,
                            email: newUser.email,
                            createdAt: newUser.createdAt
                        };

                        resolve({
                            user: userResponse,
                            token: generateToken()
                        });
                    }
                } else {
                    reject({ message: 'Invalid email format. Please enter a valid email.' });
                }
            }, 1000);
        });
    }

    // Function to simulate password reset API call
    function simulatePasswordReset(email) {
        return new Promise((resolve, reject) => {
            // Simulate API delay
            setTimeout(() => {
                // Get saved users from localStorage
                const savedUsers = JSON.parse(localStorage.getItem('savedUsers') || '[]');

                // Check if email exists in saved users
                const userExists = savedUsers.some(user => user.email === email);

                // For demo purposes, accept any valid email
                if (isValidEmail(email)) {
                    if (userExists) {
                        resolve({ message: 'Password reset link has been sent to your email.' });
                    } else {
                        // For demo purposes, also accept emails not in the system
                        resolve({ message: 'If an account with this email exists, a password reset link has been sent.' });
                    }
                } else {
                    reject({ message: 'Invalid email format. Please enter a valid email.' });
                }
            }, 1000);
        });
    }

    // Helper function to validate email format
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Helper function to generate a random user ID
    function generateUserId() {
        return 'user_' + Math.random().toString(36).substring(2, 11);
    }

    // Helper function to generate a random token
    function generateToken() {
        return 'token_' + Math.random().toString(36).substring(2, 18);
    }

    // Helper function to generate a random salt
    function generateSalt() {
        return Math.random().toString(36).substring(2, 15) +
               Math.random().toString(36).substring(2, 15);
    }

    // Helper function to hash a password with a salt
    function hashPassword(password, salt) {
        // In a real application, you would use a proper hashing library like bcrypt
        // This is a simplified version for demonstration purposes only
        const hashInput = password + salt;
        let hash = 0;

        // Simple string hash function
        for (let i = 0; i < hashInput.length; i++) {
            const char = hashInput.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }

        // Convert to hex string
        return hash.toString(16);
    }

    // Helper function to verify a password against a hash
    function verifyPassword(password, hash, salt) {
        const calculatedHash = hashPassword(password, salt);
        return calculatedHash === hash;
    }

    // Check if user is already logged in
    function checkLoggedInUser() {
        const token = localStorage.getItem('token');
        const user = localStorage.getItem('user');

        if (token && user) {
            // Redirect to dashboard
            window.location.href = 'index.html';
        }
    }

    // Check logged in user on page load
    checkLoggedInUser();
});
