/**
 * Unified API Service for Trading Signals App
 *
 * This service provides a centralized interface for all API interactions,
 * with intelligent fallback, caching, and error handling.
 *
 * Features:
 * - Multiple API provider support with automatic fallback
 * - Intelligent caching with TTL
 * - Request throttling and rate limiting
 * - Offline support with background sync
 * - Detailed error handling and logging
 * - Request prioritization
 */

// API Configuration
const API_CONFIG = {
  // Alpha Vantage API
  ALPHA_VANTAGE: {
    BASE_URL: 'https://www.alphavantage.co/query',
    API_KEYS: [
      process.env.ALPHA_VANTAGE_API_KEY // Use environment variable
    ],
    ENDPOINTS: {
      INTRADAY: 'TIME_SERIES_INTRADAY',
      DAILY: 'TIME_SERIES_DAILY',
      QUOTE: 'GLOBAL_QUOTE',
      FOREX: 'FX_INTRADAY',
      CRYPTO: 'CRYPTO_INTRADAY'
    },
    RATE_LIMIT: {
      REQUESTS_PER_MINUTE: 5,
      REQUESTS_PER_DAY: 500
    }
  },

  // FRED API (Federal Reserve Economic Data)
  FRED: {
    BASE_URL: 'https://api.stlouisfed.org/fred',
    API_KEYS: [
      process.env.FRED_API_KEY // Use environment variable
    ],
    ENDPOINTS: {
      SERIES: 'series',
      RELEASES: 'releases',
      CATEGORIES: 'categories'
    },
    RATE_LIMIT: {
      REQUESTS_PER_MINUTE: 120,
      REQUESTS_PER_DAY: 1000
    }
  },

  // Polygon.io API
  POLYGON: {
    BASE_URL: 'https://api.polygon.io',
    API_KEYS: [
      process.env.POLYGON_API_KEY // Use environment variable
    ],
    ENDPOINTS: {
      FOREX: '/v2/aggs/ticker',
      STOCKS: '/v2/aggs/ticker',
      CRYPTO: '/v2/aggs/ticker'
    },
    RATE_LIMIT: {
      REQUESTS_PER_MINUTE: 30,
      REQUESTS_PER_DAY: 5000
    }
  },

  // Local API
  LOCAL_API: {
    BASE_URL: '/api',
    ENDPOINTS: {
      MARKET_DATA: '/market-data',
      TECHNICAL_INDICATORS: '/technical-indicators',
      ECONOMIC_CALENDAR: '/economic-calendar',
      TRADING_SIGNALS: '/trading-signals',
      NEWS: '/market-news',
      STATUS: '/status'
    }
  }
};

// Cache configuration
const CACHE_CONFIG = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes
  MARKET_DATA_TTL: 60 * 1000, // 1 minute
  ECONOMIC_CALENDAR_TTL: 60 * 60 * 1000, // 1 hour
  NEWS_TTL: 30 * 60 * 1000, // 30 minutes
  TECHNICAL_INDICATORS_TTL: 5 * 60 * 1000, // 5 minutes
  ENABLE_PERSISTENT_CACHE: true,
  MAX_CACHE_SIZE: 100 // Maximum number of items to cache
};

// In-memory cache
const apiCache = new Map();

/**
 * Unified API Service Class
 */
class UnifiedAPIService {
  constructor() {
    this.initializeKeyManagers();
    this.loadCacheFromStorage();
    this.stats = {
      requests: 0,
      cacheHits: 0,
      errors: 0,
      lastRequest: null
    };

    // Set up event listeners for online/offline events
    if (typeof window !== 'undefined') {
      window.addEventListener('online', this.handleOnline.bind(this));
      window.addEventListener('offline', this.handleOffline.bind(this));
    }

    console.log('Unified API Service initialized');
  }

  /**
   * Initialize API key managers for all providers
   */
  initializeKeyManagers() {
    this.keyManagers = {};
    
    // Simple key rotation since we removed ApiKeyManager dependency
    this.currentKeyIndex = {
      ALPHA_VANTAGE: 0,
      FRED: 0,
      POLYGON: 0
    };
  }

  /**
   * Get next API key for a provider using simple rotation
   */
  getNextApiKey(provider) {
    const keys = API_CONFIG[provider].API_KEYS;
    const currentIndex = this.currentKeyIndex[provider];
    
    // Rotate to next key
    this.currentKeyIndex[provider] = (currentIndex + 1) % keys.length;
    
    return keys[currentIndex];
  }

  /**
   * Handle online event
   */
  handleOnline() {
    console.log('API Service: Browser is online. Processing queued requests...');
    this.processQueuedRequests();
  }

  /**
   * Handle offline event
   */
  handleOffline() {
    console.log('API Service: Browser is offline. Requests will be queued.');
  }

  /**
   * Process queued requests when coming back online
   */
  async processQueuedRequests() {
    // Implementation would go here in a full version
    console.log('Processing queued requests...');
  }

  /**
   * Load cache from localStorage
   */
  loadCacheFromStorage() {
    if (CACHE_CONFIG.ENABLE_PERSISTENT_CACHE && typeof localStorage !== 'undefined') {
      try {
        const savedCache = localStorage.getItem('api_cache');
        if (savedCache) {
          const parsedCache = JSON.parse(savedCache);

          // Check if items are still valid
          const now = Date.now();
          Object.entries(parsedCache).forEach(([key, item]) => {
            if (item.expires > now) {
              apiCache.set(key, item);
            }
          });

          console.log(`Loaded ${apiCache.size} items from persistent cache`);
        }
      } catch (error) {
        console.error('Error loading cache from storage:', error);
      }
    }
  }

  /**
   * Save cache to localStorage
   */
  saveCacheToStorage() {
    if (CACHE_CONFIG.ENABLE_PERSISTENT_CACHE && typeof localStorage !== 'undefined') {
      try {
        const cacheObject = {};
        apiCache.forEach((value, key) => {
          cacheObject[key] = value;
        });

        localStorage.setItem('api_cache', JSON.stringify(cacheObject));
        console.log('Cache saved to persistent storage');
      } catch (error) {
        console.error('Error saving cache to storage:', error);
      }
    }
  }

  /**
   * Generate cache key from endpoint and params
   */
  generateCacheKey(endpoint, params) {
    return JSON.stringify({ endpoint, params });
  }

  /**
   * Get cached data if available and not expired
   */
  getCachedData(cacheKey) {
    const cachedItem = apiCache.get(cacheKey);

    if (cachedItem) {
      // Check if expired
      if (cachedItem.expires > Date.now()) {
        this.stats.cacheHits++;
        return cachedItem.data;
      } else {
        // Remove expired item
        apiCache.delete(cacheKey);
      }
    }

    return null;
  }

  /**
   * Cache data with TTL
   */
  cacheData(cacheKey, data, ttl = CACHE_CONFIG.DEFAULT_TTL) {
    // Ensure cache doesn't grow too large
    if (apiCache.size >= CACHE_CONFIG.MAX_CACHE_SIZE) {
      // Remove oldest item
      const oldestKey = apiCache.keys().next().value;
      apiCache.delete(oldestKey);
    }

    // Add to cache
    apiCache.set(cacheKey, {
      data,
      expires: Date.now() + ttl,
      timestamp: Date.now()
    });

    // Save to persistent storage
    this.saveCacheToStorage();
  }

  /**
   * Main method to fetch data from any API with fallback
   */
  async fetchData(options) {
    const {
      provider = 'LOCAL_API',
      endpoint,
      params = {},
      useCache = true,
      cacheTTL = CACHE_CONFIG.DEFAULT_TTL,
      priority = 'normal',
      retries = 2
    } = options;

    // Update stats
    this.stats.requests++;
    this.stats.lastRequest = new Date();

    // Generate cache key
    const cacheKey = this.generateCacheKey(endpoint, params);

    // Check cache if enabled
    if (useCache) {
      const cachedData = this.getCachedData(cacheKey);
      if (cachedData) {
        console.log(`Using cached data for ${endpoint}`);
        return cachedData;
      }
    }

    // Check if online
    if (typeof navigator !== 'undefined' && !navigator.onLine) {
      console.warn('Browser is offline. Cannot fetch new data.');
      throw new Error('Offline. Cannot fetch new data.');
    }

    try {
      // Fetch data based on provider
      let data;

      switch (provider) {
        case 'ALPHA_VANTAGE':
          data = await this.fetchFromAlphaVantage(endpoint, params);
          break;
        case 'FRED':
          data = await this.fetchFromFRED(endpoint, params);
          break;
        case 'POLYGON':
          data = await this.fetchFromPolygon(endpoint, params);
          break;
        case 'LOCAL_API':
        default:
          data = await this.fetchFromLocalAPI(endpoint, params);
          break;
      }

      // Cache the result if we got valid data
      if (data) {
        this.cacheData(cacheKey, data, cacheTTL);
      }

      return data;
    } catch (error) {
      this.stats.errors++;
      console.error(`Error fetching data from ${provider}:`, error);

      // Try fallback if available and not already on last retry
      if (retries > 0) {
        console.log(`Trying fallback for ${endpoint}...`);

        // Determine fallback provider
        let fallbackProvider;
        if (provider === 'ALPHA_VANTAGE') {
          fallbackProvider = 'POLYGON';
        } else if (provider === 'POLYGON') {
          fallbackProvider = 'LOCAL_API';
        } else {
          fallbackProvider = 'ALPHA_VANTAGE';
        }

        // Try with fallback provider
        return this.fetchData({
          ...options,
          provider: fallbackProvider,
          retries: retries - 1
        });
      }

      throw error;
    }
  }

  async fetchFromAlphaVantage(endpoint, params) {
    console.log('Fetching from Alpha Vantage:', endpoint);

    try {
      const apiKey = this.getNextApiKey('ALPHA_VANTAGE');
      if (!apiKey) {
        throw new Error('No Alpha Vantage API key available');
      }

      // Construct URL
      const url = new URL(API_CONFIG.ALPHA_VANTAGE.BASE_URL);
      url.searchParams.append('function', endpoint);
      url.searchParams.append('apikey', apiKey);

      // Add all other params
      Object.keys(params).forEach(key => {
        url.searchParams.append(key, params[key]);
      });

      // Log the URL (hide API key)
      console.log('Alpha Vantage URL:', url.toString().replace(/apikey=[^&]+/, 'apikey=HIDDEN'));

      const response = await fetch(url.toString());
      if (!response.ok) {
        throw new Error(`Alpha Vantage API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data['Error Message']) {
        throw new Error(`Alpha Vantage API error: ${data['Error Message']}`);
      }

      if (data['Note'] && data['Note'].includes('API call frequency')) {
        throw new Error('API rate limit reached');
      }

      return { source: 'alpha_vantage', data };
    } catch (error) {
      console.error('Error fetching from Alpha Vantage:', error);
      throw error;
    }
  }

  async fetchFromFRED(endpoint, params) {
    console.log('Fetching from FRED:', endpoint);

    try {
      const apiKey = this.getNextApiKey('FRED');
      if (!apiKey) {
        throw new Error('No FRED API key available');
      }

      // Construct URL
      const url = new URL(`${API_CONFIG.FRED.BASE_URL}/${endpoint}`);
      url.searchParams.append('api_key', apiKey);
      url.searchParams.append('file_type', 'json');

      // Add all other params
      Object.keys(params).forEach(key => {
        url.searchParams.append(key, params[key]);
      });

      // Log the URL (hide API key)
      console.log('FRED URL:', url.toString().replace(/api_key=[^&]+/, 'api_key=HIDDEN'));

      const response = await fetch(url.toString());
      if (!response.ok) {
        throw new Error(`FRED API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error_code) {
        throw new Error(`FRED API error: ${data.error_message || data.error_code}`);
      }

      return { source: 'fred', data };
    } catch (error) {
      console.error('Error fetching from FRED:', error);
      throw error;
    }
  }

  async fetchFromPolygon(endpoint, params) {
    console.log('Fetching from Polygon:', endpoint);

    try {
      const apiKey = this.getNextApiKey('POLYGON');
      if (!apiKey) {
        throw new Error('No Polygon API key available');
      }

      // Construct URL
      const url = new URL(`${API_CONFIG.POLYGON.BASE_URL}${endpoint}`);

      // Add all params
      Object.keys(params).forEach(key => {
        url.searchParams.append(key, params[key]);
      });

      // Log the URL
      console.log('Polygon URL:', url.toString());

      const response = await fetch(url.toString(), {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      });

      if (!response.ok) {
        throw new Error(`Polygon API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.status === 'ERROR') {
        throw new Error(`Polygon API error: ${data.error || 'Unknown error'}`);
      }

      return { source: 'polygon', data };
    } catch (error) {
      console.error('Error fetching from Polygon:', error);
      throw error;
    }
  }

  async fetchFromLocalAPI(endpoint, params) {
    console.log('Fetching from Local API:', endpoint);

    try {
      // Construct URL
      let url = API_CONFIG.LOCAL_API.BASE_URL + endpoint;

      // Add query parameters if any
      if (Object.keys(params).length > 0) {
        const queryParams = new URLSearchParams();
        Object.keys(params).forEach(key => {
          queryParams.append(key, params[key]);
        });
        url += '?' + queryParams.toString();
      }

      console.log('Local API URL:', url);

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Local API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return { source: 'local_api', data };
    } catch (error) {
      console.error('Error fetching from Local API:', error);
      throw error;
    }
  }
}

// Create singleton instance
const unifiedAPIService = new UnifiedAPIService();

// Export for use in other modules
export default unifiedAPIService;
