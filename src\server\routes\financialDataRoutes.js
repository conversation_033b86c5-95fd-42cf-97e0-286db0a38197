/**
 * Financial Data Routes for Trading Signals App
 * 
 * This file provides routes for accessing financial data from multiple sources:
 * - Alpha Vantage
 * - FRED
 * - Polygon
 * - Finnhub
 * - Twelve Data
 * - FMP
 */

const express = require('express');
const integratedApiService = require('../../services/integrated-api-service');
const logger = require('../utils/logger');

// Create router
const router = express.Router();

/**
 * Set up financial data routes
 * @param {Object} app - Express app
 */
module.exports = function(app) {
  /**
   * Get API status
   * 
   * @route GET /api/financial/status
   * @group Financial Data - Financial data endpoints
   * @returns {Object} 200 - API status response
   * @returns {Error} 500 - Server error
   */
  router.get('/status', async (req, res) => {
    try {
      const status = integratedApiService.getStatus();
      res.json({
        status: 'success',
        data: status
      });
    } catch (error) {
      logger.error('Error getting API status:', error);
      res.status(500).json({
        status: 'error',
        message: error.message
      });
    }
  });

  /**
   * Fetch data from all sources
   * 
   * @route GET /api/financial/all
   * @group Financial Data - Financial data endpoints
   * @param {string} symbol.query - Trading symbol (default: AAPL)
   * @param {string} interval.query - Time interval (default: 5min)
   * @returns {Object} 200 - Data from all sources
   * @returns {Error} 500 - Server error
   */
  router.get('/all', async (req, res) => {
    try {
      logger.info('Fetching data from all sources...');
      
      const params = {
        symbol: req.query.symbol || 'AAPL',
        interval: req.query.interval || '5min'
      };
      
      const results = await integratedApiService.fetchAllData(params);
      
      res.json({
        status: 'success',
        timestamp: new Date().toISOString(),
        params,
        results
      });
    } catch (error) {
      logger.error('Error fetching all data:', error);
      res.status(500).json({
        status: 'error',
        message: error.message
      });
    }
  });

  /**
   * Fetch data from a specific source
   * 
   * @route GET /api/financial/:source
   * @group Financial Data - Financial data endpoints
   * @param {string} source.path.required - API source (alpha, fred, polygon, etc.)
   * @param {string} symbol.query - Trading symbol (default: AAPL)
   * @param {string} interval.query - Time interval (default: 5min)
   * @returns {Object} 200 - Data from the specified source
   * @returns {Error} 400 - Bad request
   * @returns {Error} 500 - Server error
   */
  router.get('/:source', async (req, res) => {
    try {
      const { source } = req.params;
      const validSources = ['alpha', 'fred', 'polygon', 'twelve', 'finnhub', 'fmp'];
      
      if (!validSources.includes(source)) {
        return res.status(400).json({
          status: 'error',
          message: `Invalid source: ${source}. Valid sources are: ${validSources.join(', ')}`
        });
      }
      
      logger.info(`Fetching data from ${source}...`);
      
      const params = {
        ...req.query,
        symbol: req.query.symbol || 'AAPL',
        interval: req.query.interval || '5min'
      };
      
      // Special handling for FRED
      if (source === 'fred') {
        params.series_id = params.series_id || 'CPIAUCSL';
        delete params.symbol;
        delete params.interval;
      }
      
      const data = await integratedApiService.fetchData(source, params);
      
      res.json({
        status: 'success',
        timestamp: new Date().toISOString(),
        source,
        params,
        data
      });
    } catch (error) {
      logger.error(`Error fetching data from ${req.params.source}:`, error);
      res.status(500).json({
        status: 'error',
        message: error.message
      });
    }
  });

  /**
   * Clear API cache
   * 
   * @route POST /api/financial/clear-cache
   * @group Financial Data - Financial data endpoints
   * @returns {Object} 200 - Cache cleared response
   * @returns {Error} 500 - Server error
   */
  router.post('/clear-cache', (req, res) => {
    try {
      integratedApiService.clearCache();
      res.json({
        status: 'success',
        message: 'Cache cleared',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Error clearing cache:', error);
      res.status(500).json({
        status: 'error',
        message: error.message
      });
    }
  });

  /**
   * Get market data for a symbol
   * 
   * @route GET /api/financial/market/:symbol
   * @group Financial Data - Financial data endpoints
   * @param {string} symbol.path.required - Trading symbol
   * @param {string} interval.query - Time interval (default: 5min)
   * @returns {Object} 200 - Market data response
   * @returns {Error} 400 - Bad request
   * @returns {Error} 500 - Server error
   */
  router.get('/market/:symbol', async (req, res) => {
    try {
      const { symbol } = req.params;
      const interval = req.query.interval || '5min';
      
      if (!symbol) {
        return res.status(400).json({
          status: 'error',
          message: 'Symbol is required'
        });
      }
      
      logger.info(`Fetching market data for ${symbol} (${interval})...`);
      
      // Try Alpha Vantage first
      try {
        const data = await integratedApiService.fetchData('alpha', {
          function: 'TIME_SERIES_INTRADAY',
          symbol,
          interval
        });
        
        res.json({
          status: 'success',
          timestamp: new Date().toISOString(),
          source: 'alpha',
          symbol,
          interval,
          data
        });
      } catch (alphaError) {
        logger.warn(`Alpha Vantage error: ${alphaError.message}, trying Polygon...`);
        
        // Try Polygon as fallback
        try {
          const data = await integratedApiService.fetchData('polygon', {
            ticker: symbol
          });
          
          res.json({
            status: 'success',
            timestamp: new Date().toISOString(),
            source: 'polygon',
            symbol,
            interval,
            data
          });
        } catch (polygonError) {
          throw new Error(`All data sources failed: ${alphaError.message}, ${polygonError.message}`);
        }
      }
    } catch (error) {
      logger.error(`Error fetching market data for ${req.params.symbol}:`, error);
      res.status(500).json({
        status: 'error',
        message: error.message
      });
    }
  });

  /**
   * Get economic data
   * 
   * @route GET /api/financial/economic/:series
   * @group Financial Data - Financial data endpoints
   * @param {string} series.path.required - FRED series ID
   * @returns {Object} 200 - Economic data response
   * @returns {Error} 400 - Bad request
   * @returns {Error} 500 - Server error
   */
  router.get('/economic/:series', async (req, res) => {
    try {
      const { series } = req.params;
      
      if (!series) {
        return res.status(400).json({
          status: 'error',
          message: 'Series ID is required'
        });
      }
      
      logger.info(`Fetching economic data for series ${series}...`);
      
      const data = await integratedApiService.fetchData('fred', {
        series_id: series
      });
      
      res.json({
        status: 'success',
        timestamp: new Date().toISOString(),
        source: 'fred',
        series,
        data
      });
    } catch (error) {
      logger.error(`Error fetching economic data for ${req.params.series}:`, error);
      res.status(500).json({
        status: 'error',
        message: error.message
      });
    }
  });

  // Mount the router
  app.use('/api/financial', router);
  
  logger.info('Financial data routes initialized');
  
  return router;
};

module.exports.router = router;
