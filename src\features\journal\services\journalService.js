import Trade from '../models/Trade.js';

class JournalService {
  async createTrade(data) {
    const trade = new Trade(data);
    await trade.save();
    return trade;
  }

  async getTradesByUser(userId) {
    return Trade.find({ user: userId }).sort({ openedAt: -1 });
  }

  async getTradeById(id, userId) {
    return Trade.findOne({ _id: id, user: userId });
  }

  async deleteTrade(id, userId) {
    return Trade.findOneAndDelete({ _id: id, user: userId });
  }

  async getTradeAnalytics(userId) {
    const trades = await Trade.find({ user: userId });
    if (!trades.length) return { count: 0 };
    const wins = trades.filter(t => t.result > 0).length;
    const losses = trades.filter(t => t.result < 0).length;
    const total = trades.length;
    const winRate = total ? (wins / total) * 100 : 0;
    const totalPL = trades.reduce((sum, t) => sum + (t.result || 0), 0);
    const bestTrade = trades.reduce((best, t) => (!best || (t.result || 0) > (best.result || 0)) ? t : best, null);
    const worstTrade = trades.reduce((worst, t) => (!worst || (t.result || 0) < (worst.result || 0)) ? t : worst, null);
    // Equity curve
    let equity = 0;
    const equityCurve = trades
      .sort((a, b) => a.closedAt - b.closedAt)
      .map(t => {
        equity += t.result || 0;
        return { date: t.closedAt, equity };
      });
    return {
      count: total,
      winRate,
      totalPL,
      bestTrade,
      worstTrade,
      equityCurve,
    };
  }
}

export default new JournalService(); 