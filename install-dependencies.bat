@echo off
SETLOCAL enabledelayedexpansion

echo ===================================================
echo     Trading Signals App - Dependency Installer
echo ===================================================
echo.

:: Check if npm is installed
where npm >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ERROR: npm is not installed or not in your PATH.
    echo Please install Node.js from https://nodejs.org/
    exit /b 1
)

echo Checking Node.js version...
for /f "tokens=*" %%i in ('node -v') do set NODE_VERSION=%%i
echo Detected Node.js !NODE_VERSION!
echo.

echo Installing core dependencies...
call npm install express cors compression helmet express-rate-limit morgan || (
    echo ERROR: Failed to install core dependencies.
    exit /b 1
)
echo Core dependencies installed successfully.
echo.

echo Installing database dependencies...
call npm install mongoose || (
    echo ERROR: Failed to install database dependencies.
    exit /b 1
)
echo Database dependencies installed successfully.
echo.

echo Installing logging dependencies...
call npm install winston winston-daily-rotate-file || (
    echo ERROR: Failed to install logging dependencies.
    exit /b 1
)
echo Logging dependencies installed successfully.
echo.

echo Installing caching dependencies...
call npm install ioredis node-cache || (
    echo ERROR: Failed to install caching dependencies.
    exit /b 1
)
echo Caching dependencies installed successfully.
echo.

echo Installing development dependencies...
call npm install --save-dev nodemon || (
    echo ERROR: Failed to install development dependencies.
    exit /b 1
)
echo Development dependencies installed successfully.
echo.

echo ===================================================
echo All dependencies have been installed successfully!
echo.
echo To start the development server:
echo   npm run dev
echo.
echo To start the production server:
echo   npm start
echo ===================================================

ENDLOCAL 