/**
 * Webhook Service for Trading Signals App
 * 
 * This service provides webhook functionality for:
 * - Sending trading signals to external systems
 * - Receiving real-time data from external providers
 * - Integrating with third-party platforms
 */

const crypto = require('crypto');
const axios = require('axios');
const logger = require('../utils/logger');
const config = require('../config/config');
const { EventEmitter } = require('events');

class WebhookService extends EventEmitter {
  /**
   * Create a new WebhookService
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      secretKey: config.webhooks?.secretKey || crypto.randomBytes(32).toString('hex'),
      maxRetries: 3,
      retryDelay: 5000,
      timeout: 10000,
      ...options
    };
    
    // Initialize webhooks configuration
    this.webhooks = new Map();
    
    // Initialize stats
    this.stats = {
      sent: 0,
      received: 0,
      failed: 0,
      retried: 0
    };
    
    logger.info('Webhook service initialized');
  }
  
  /**
   * Register a webhook endpoint
   * @param {Object} webhook - Webhook configuration
   * @param {string} webhook.id - Unique webhook ID
   * @param {string} webhook.url - Webhook URL
   * @param {string} webhook.event - Event type to trigger this webhook
   * @param {string} webhook.description - Webhook description
   * @param {Object} webhook.headers - Custom headers to include
   * @param {boolean} webhook.active - Whether the webhook is active
   * @returns {string} Webhook ID
   */
  registerWebhook(webhook) {
    // Generate ID if not provided
    const id = webhook.id || `wh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Store webhook
    this.webhooks.set(id, {
      id,
      url: webhook.url,
      event: webhook.event,
      description: webhook.description || '',
      headers: webhook.headers || {},
      active: webhook.active !== false,
      createdAt: new Date(),
      lastTriggered: null,
      lastResponse: null,
      failCount: 0
    });
    
    logger.info(`Registered webhook ${id} for event ${webhook.event}`);
    
    return id;
  }
  
  /**
   * Unregister a webhook
   * @param {string} id - Webhook ID
   * @returns {boolean} Success
   */
  unregisterWebhook(id) {
    const result = this.webhooks.delete(id);
    
    if (result) {
      logger.info(`Unregistered webhook ${id}`);
    } else {
      logger.warn(`Webhook ${id} not found`);
    }
    
    return result;
  }
  
  /**
   * Get all registered webhooks
   * @param {string} event - Optional event filter
   * @returns {Array} Webhooks
   */
  getWebhooks(event = null) {
    const webhooks = Array.from(this.webhooks.values());
    
    if (event) {
      return webhooks.filter(webhook => webhook.event === event);
    }
    
    return webhooks;
  }
  
  /**
   * Trigger webhooks for an event
   * @param {string} event - Event type
   * @param {Object} payload - Event payload
   * @returns {Promise<Array>} Results
   */
  async triggerEvent(event, payload) {
    // Find all active webhooks for this event
    const webhooks = Array.from(this.webhooks.values())
      .filter(webhook => webhook.active && webhook.event === event);
    
    if (webhooks.length === 0) {
      logger.debug(`No active webhooks found for event ${event}`);
      return [];
    }
    
    logger.info(`Triggering ${webhooks.length} webhooks for event ${event}`);
    
    // Prepare payload
    const webhookPayload = {
      event,
      timestamp: new Date().toISOString(),
      data: payload
    };
    
    // Add signature
    const signature = this.generateSignature(webhookPayload);
    
    // Send to all webhooks
    const results = await Promise.all(
      webhooks.map(webhook => this.sendWebhook(webhook, webhookPayload, signature))
    );
    
    // Emit event
    this.emit('triggered', {
      event,
      webhookCount: webhooks.length,
      successCount: results.filter(r => r.success).length,
      failCount: results.filter(r => !r.success).length
    });
    
    return results;
  }
  
  /**
   * Send webhook to a specific endpoint
   * @param {Object} webhook - Webhook configuration
   * @param {Object} payload - Webhook payload
   * @param {string} signature - Payload signature
   * @returns {Promise<Object>} Result
   */
  async sendWebhook(webhook, payload, signature) {
    let retries = 0;
    let lastError = null;
    
    // Update webhook
    webhook.lastTriggered = new Date();
    
    // Try to send webhook with retries
    while (retries <= this.options.maxRetries) {
      try {
        // Send webhook
        const response = await axios({
          method: 'POST',
          url: webhook.url,
          data: payload,
          headers: {
            'Content-Type': 'application/json',
            'X-Webhook-Signature': signature,
            'X-Webhook-ID': webhook.id,
            'X-Webhook-Event': payload.event,
            ...webhook.headers
          },
          timeout: this.options.timeout
        });
        
        // Update stats
        this.stats.sent++;
        
        // Update webhook
        webhook.lastResponse = {
          status: response.status,
          timestamp: new Date(),
          success: true
        };
        webhook.failCount = 0;
        
        logger.info(`Successfully sent webhook ${webhook.id} for event ${payload.event}`);
        
        return {
          webhookId: webhook.id,
          success: true,
          status: response.status,
          retries
        };
      } catch (error) {
        lastError = error;
        
        // Update stats
        if (retries === 0) {
          this.stats.failed++;
        } else {
          this.stats.retried++;
        }
        
        // Log error
        logger.error(`Error sending webhook ${webhook.id} (attempt ${retries + 1}/${this.options.maxRetries + 1}):`, error.message);
        
        // Increment retry counter
        retries++;
        
        // If we have more retries, wait before trying again
        if (retries <= this.options.maxRetries) {
          await new Promise(resolve => setTimeout(resolve, this.options.retryDelay));
        }
      }
    }
    
    // Update webhook
    webhook.lastResponse = {
      status: lastError.response?.status || 0,
      timestamp: new Date(),
      success: false,
      error: lastError.message
    };
    webhook.failCount++;
    
    // If webhook has failed too many times, deactivate it
    if (webhook.failCount >= 5) {
      logger.warn(`Deactivating webhook ${webhook.id} after ${webhook.failCount} consecutive failures`);
      webhook.active = false;
    }
    
    return {
      webhookId: webhook.id,
      success: false,
      error: lastError.message,
      retries
    };
  }
  
  /**
   * Generate signature for payload
   * @param {Object} payload - Payload to sign
   * @returns {string} Signature
   */
  generateSignature(payload) {
    const hmac = crypto.createHmac('sha256', this.options.secretKey);
    hmac.update(JSON.stringify(payload));
    return hmac.digest('hex');
  }
  
  /**
   * Verify webhook signature
   * @param {string} signature - Signature to verify
   * @param {Object|string} payload - Payload that was signed
   * @returns {boolean} Whether signature is valid
   */
  verifySignature(signature, payload) {
    const expectedSignature = this.generateSignature(
      typeof payload === 'string' ? payload : JSON.stringify(payload)
    );
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  }
  
  /**
   * Process incoming webhook
   * @param {Object} req - Express request
   * @returns {Promise<Object>} Processing result
   */
  async processIncomingWebhook(req) {
    try {
      // Update stats
      this.stats.received++;
      
      // Get signature
      const signature = req.headers['x-webhook-signature'];
      
      // Verify signature if provided
      if (signature) {
        const isValid = this.verifySignature(signature, req.body);
        
        if (!isValid) {
          logger.warn('Invalid webhook signature');
          return {
            success: false,
            error: 'Invalid signature'
          };
        }
      }
      
      // Extract event type
      const event = req.headers['x-webhook-event'] || req.body.event;
      
      if (!event) {
        logger.warn('Missing event type in webhook');
        return {
          success: false,
          error: 'Missing event type'
        };
      }
      
      // Emit event
      this.emit(event, req.body);
      this.emit('webhook', { event, data: req.body });
      
      logger.info(`Processed incoming webhook for event ${event}`);
      
      return {
        success: true,
        event
      };
    } catch (error) {
      logger.error('Error processing incoming webhook:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Get service statistics
   * @returns {Object} Service statistics
   */
  getStats() {
    return {
      ...this.stats,
      webhookCount: this.webhooks.size,
      activeWebhookCount: Array.from(this.webhooks.values()).filter(w => w.active).length,
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = WebhookService;
