/**
 * Enhanced Trading Signals Component
 * 
 * Example component demonstrating the integration of all our enhanced
 * error handling, state management, and user experience improvements.
 * 
 * Features:
 * - Standardized loading, error, and empty states
 * - Integrated error handling with recovery options
 * - Form validation with user-friendly messages
 * - Real-time data updates with WebSocket integration
 * - Responsive design with accessibility support
 * - Performance optimization with caching
 * 
 * @version 1.0.0
 */

import React, { useState, useMemo } from 'react';
import { useSignalsData } from '../hooks/useComponentState';
import { useError } from '../context/ErrorContext';
import FeatureErrorBoundary from './ErrorBoundary/FeatureErrorBoundary';
import LoadingState, { ComponentLoading, InlineLoading } from './StateComponents/LoadingState';
import ErrorState, { NetworkError, APIError } from './StateComponents/ErrorState';
import EmptyState, { NoSignalsEmpty } from './StateComponents/EmptyState';
import { UnifiedSignal } from '../types/signals';

// ============================================================================
// INTERFACES
// ============================================================================

interface EnhancedTradingSignalsProps {
  symbol?: string;
  timeframe?: string;
  maxSignals?: number;
  enableRealTime?: boolean;
  showFilters?: boolean;
  onSignalClick?: (signal: UnifiedSignal) => void;
}

interface SignalFilters {
  symbol: string;
  type: 'all' | 'BUY' | 'SELL';
  confidence: number;
  timeframe: string;
  source: 'all' | 'ai' | 'traditional';
}

// ============================================================================
// FILTER COMPONENT
// ============================================================================

const SignalFilters: React.FC<{
  filters: SignalFilters;
  onFiltersChange: (filters: SignalFilters) => void;
  isLoading?: boolean;
}> = ({ filters, onFiltersChange, isLoading = false }) => {
  const handleFilterChange = (key: keyof SignalFilters, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Filters</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {/* Symbol Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Symbol
          </label>
          <input
            type="text"
            value={filters.symbol}
            onChange={(e) => handleFilterChange('symbol', e.target.value.toUpperCase())}
            placeholder="e.g., AAPL"
            disabled={isLoading}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          />
        </div>

        {/* Type Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Signal Type
          </label>
          <select
            value={filters.type}
            onChange={(e) => handleFilterChange('type', e.target.value)}
            disabled={isLoading}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <option value="all">All Types</option>
            <option value="BUY">Buy Signals</option>
            <option value="SELL">Sell Signals</option>
          </select>
        </div>

        {/* Confidence Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Min Confidence: {filters.confidence}%
          </label>
          <input
            type="range"
            min="0"
            max="100"
            value={filters.confidence}
            onChange={(e) => handleFilterChange('confidence', parseInt(e.target.value))}
            disabled={isLoading}
            className="w-full disabled:opacity-50"
          />
        </div>

        {/* Timeframe Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Timeframe
          </label>
          <select
            value={filters.timeframe}
            onChange={(e) => handleFilterChange('timeframe', e.target.value)}
            disabled={isLoading}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <option value="1m">1 Minute</option>
            <option value="5m">5 Minutes</option>
            <option value="15m">15 Minutes</option>
            <option value="1h">1 Hour</option>
            <option value="4h">4 Hours</option>
            <option value="1d">1 Day</option>
          </select>
        </div>

        {/* Source Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Source
          </label>
          <select
            value={filters.source}
            onChange={(e) => handleFilterChange('source', e.target.value)}
            disabled={isLoading}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <option value="all">All Sources</option>
            <option value="ai">AI Generated</option>
            <option value="traditional">Traditional</option>
          </select>
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// SIGNAL CARD COMPONENT
// ============================================================================

const SignalCard: React.FC<{
  signal: UnifiedSignal;
  onClick?: (signal: UnifiedSignal) => void;
}> = ({ signal, onClick }) => {
  const getSignalTypeColor = (type: string) => {
    return type === 'BUY' ? 'text-green-600 bg-green-50' : 'text-red-600 bg-red-50';
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600';
    if (confidence >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div
      className="bg-white rounded-lg shadow-sm border p-4 hover:shadow-md transition-shadow cursor-pointer"
      onClick={() => onClick?.(signal)}
    >
      <div className="flex justify-between items-start mb-3">
        <div>
          <h4 className="text-lg font-semibold text-gray-900">{signal.symbol}</h4>
          <p className="text-sm text-gray-600">{signal.source}</p>
        </div>
        <div className="text-right">
          <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getSignalTypeColor(signal.type)}`}>
            {signal.type}
          </span>
          <p className="text-sm text-gray-600 mt-1">
            {new Date(signal.createdAt).toLocaleTimeString()}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <p className="text-sm text-gray-600">Entry Price</p>
          <p className="text-lg font-semibold text-gray-900">${signal.entryPrice}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Confidence</p>
          <p className={`text-lg font-semibold ${getConfidenceColor(signal.confidence)}`}>
            {signal.confidence}%
          </p>
        </div>
      </div>

      {signal.reasoning && (
        <div className="mb-3">
          <p className="text-sm text-gray-600 mb-1">Analysis</p>
          <p className="text-sm text-gray-800 line-clamp-2">{signal.reasoning}</p>
        </div>
      )}

      <div className="flex justify-between items-center text-xs text-gray-500">
        <span>Timeframe: {signal.timeframe}</span>
        <span>Status: {signal.status}</span>
      </div>
    </div>
  );
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

const EnhancedTradingSignals: React.FC<EnhancedTradingSignalsProps> = ({
  symbol: initialSymbol = '',
  timeframe = '1h',
  maxSignals = 20,
  enableRealTime = true,
  showFilters = true,
  onSignalClick
}) => {
  // State
  const [filters, setFilters] = useState<SignalFilters>({
    symbol: initialSymbol,
    type: 'all',
    confidence: 50,
    timeframe,
    source: 'all'
  });

  // Data fetching with our enhanced hook
  const {
    data: signals,
    loading,
    error,
    isEmpty,
    refetch,
    retry,
    canRetry
  } = useSignalsData(filters.symbol || undefined);

  // Filter signals based on current filters
  const filteredSignals = useMemo(() => {
    if (!signals) return [];

    return signals
      .filter(signal => {
        if (filters.type !== 'all' && signal.type !== filters.type) return false;
        if (signal.confidence < filters.confidence) return false;
        if (filters.timeframe !== 'all' && signal.timeframe !== filters.timeframe) return false;
        if (filters.source !== 'all' && signal.source !== filters.source) return false;
        return true;
      })
      .slice(0, maxSignals);
  }, [signals, filters, maxSignals]);

  // Error handling
  const handleRetry = async () => {
    try {
      await retry();
    } catch (error) {
      console.error('Retry failed:', error);
    }
  };

  const handleRefresh = async () => {
    try {
      await refetch();
    } catch (error) {
      console.error('Refresh failed:', error);
    }
  };

  // ========================================================================
  // RENDER METHODS
  // ========================================================================

  const renderContent = () => {
    // Loading state
    if (loading && !signals) {
      return (
        <ComponentLoading 
          message="Loading trading signals..." 
          overlay={false}
        />
      );
    }

    // Error state
    if (error && !signals) {
      if (error.message.includes('network') || error.message.includes('fetch')) {
        return (
          <NetworkError
            onRetry={canRetry ? handleRetry : undefined}
            variant="full"
          />
        );
      }

      return (
        <APIError
          error={error}
          endpoint="/signals"
          onRetry={canRetry ? handleRetry : undefined}
          variant="full"
        />
      );
    }

    // Empty state
    if (isEmpty || filteredSignals.length === 0) {
      if (signals && signals.length > 0 && filteredSignals.length === 0) {
        // Filtered out all results
        return (
          <EmptyState
            title="No Signals Match Your Filters"
            description="Try adjusting your filter criteria to see more signals."
            illustration="search"
            actions={[
              {
                label: 'Clear Filters',
                onClick: () => setFilters({
                  symbol: '',
                  type: 'all',
                  confidence: 0,
                  timeframe: 'all',
                  source: 'all'
                }),
                variant: 'primary'
              }
            ]}
          />
        );
      }

      return (
        <NoSignalsEmpty
          onRefresh={handleRefresh}
          onCreateSignal={() => console.log('Create signal clicked')}
        />
      );
    }

    // Success state with data
    return (
      <div className="space-y-4">
        {/* Loading indicator for refresh */}
        {loading && (
          <div className="flex justify-center">
            <InlineLoading message="Refreshing signals..." />
          </div>
        )}

        {/* Signals grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredSignals.map((signal) => (
            <SignalCard
              key={signal.id}
              signal={signal}
              onClick={onSignalClick}
            />
          ))}
        </div>

        {/* Results summary */}
        <div className="text-center text-sm text-gray-600">
          Showing {filteredSignals.length} of {signals?.length || 0} signals
          {filters.symbol && ` for ${filters.symbol}`}
        </div>
      </div>
    );
  };

  return (
    <FeatureErrorBoundary
      featureName="trading-signals"
      level="important"
      enableRetry={true}
      maxRetries={2}
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Trading Signals</h2>
            <p className="text-gray-600">
              Real-time trading signals with AI-powered analysis
            </p>
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <SignalFilters
            filters={filters}
            onFiltersChange={setFilters}
            isLoading={loading}
          />
        )}

        {/* Content */}
        {renderContent()}
      </div>
    </FeatureErrorBoundary>
  );
};

export default EnhancedTradingSignals;
