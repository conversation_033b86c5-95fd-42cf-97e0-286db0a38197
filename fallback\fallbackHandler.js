/**
 * Enhanced API Fallback Handler
 *
 * This module implements a sophisticated cascading fallback mechanism with:
 * - Circuit breaker pattern for failed APIs
 * - Health monitoring and automatic recovery
 * - Performance-based API ranking
 * - Intelligent retry strategies
 */

const logger = require('../logging');
const config = require('../config');
const mockData = require('./mockData');

// Import all API clients
let apiClients = {};

// Dynamically load API clients if possible
try {
  apiClients = {
    alphaVantage: require('../api-clients/alphaVantage'),
    twelveData: require('../api-clients/twelveData'),
    finnhub: require('../api-clients/finnhub'),
    fred: require('../api-clients/fred'),
    polygon: require('../api-clients/polygon'),
    fmp: require('../api-clients/fmp')
  };
} catch (error) {
  logger.warn('Some API clients could not be loaded:', error.message);
}

/**
 * Circuit Breaker states
 */
const CircuitState = {
  CLOSED: 'closed',     // Normal operation
  OPEN: 'open',         // Circuit is open, requests fail fast
  HALF_OPEN: 'half_open' // Testing if service has recovered
};

/**
 * API Health Monitor
 */
class APIHealthMonitor {
  constructor() {
    this.apiHealth = new Map();
    this.circuitBreakers = new Map();
    this.performanceMetrics = new Map();
    this.healthCheckInterval = 5 * 60 * 1000; // 5 minutes
    this.circuitBreakerConfig = {
      failureThreshold: 5,
      recoveryTimeout: 60 * 1000, // 1 minute
      halfOpenMaxCalls: 3
    };

    this.startHealthMonitoring();
  }

  /**
   * Initialize circuit breaker for an API
   */
  initCircuitBreaker(apiName) {
    if (!this.circuitBreakers.has(apiName)) {
      this.circuitBreakers.set(apiName, {
        state: CircuitState.CLOSED,
        failureCount: 0,
        lastFailureTime: null,
        halfOpenCalls: 0
      });
    }
  }

  /**
   * Record API call result
   */
  recordApiCall(apiName, success, responseTime, error = null) {
    this.initCircuitBreaker(apiName);

    const breaker = this.circuitBreakers.get(apiName);
    const metrics = this.performanceMetrics.get(apiName) || {
      totalCalls: 0,
      successfulCalls: 0,
      averageResponseTime: 0,
      lastCallTime: null
    };

    // Update performance metrics
    metrics.totalCalls++;
    metrics.lastCallTime = Date.now();

    if (success) {
      metrics.successfulCalls++;
      metrics.averageResponseTime = (metrics.averageResponseTime + responseTime) / 2;

      // Reset circuit breaker on success
      if (breaker.state === CircuitState.HALF_OPEN) {
        breaker.halfOpenCalls++;
        if (breaker.halfOpenCalls >= this.circuitBreakerConfig.halfOpenMaxCalls) {
          breaker.state = CircuitState.CLOSED;
          breaker.failureCount = 0;
          breaker.halfOpenCalls = 0;
          logger.info(`Circuit breaker for ${apiName} closed - service recovered`);
        }
      } else if (breaker.state === CircuitState.CLOSED) {
        breaker.failureCount = Math.max(0, breaker.failureCount - 1);
      }
    } else {
      // Handle failure
      breaker.failureCount++;
      breaker.lastFailureTime = Date.now();

      if (breaker.state === CircuitState.CLOSED &&
          breaker.failureCount >= this.circuitBreakerConfig.failureThreshold) {
        breaker.state = CircuitState.OPEN;
        logger.warn(`Circuit breaker for ${apiName} opened - too many failures`);
      } else if (breaker.state === CircuitState.HALF_OPEN) {
        breaker.state = CircuitState.OPEN;
        breaker.halfOpenCalls = 0;
        logger.warn(`Circuit breaker for ${apiName} reopened - recovery failed`);
      }
    }

    this.performanceMetrics.set(apiName, metrics);
    this.circuitBreakers.set(apiName, breaker);
  }

  /**
   * Check if API call should be allowed
   */
  shouldAllowCall(apiName) {
    this.initCircuitBreaker(apiName);
    const breaker = this.circuitBreakers.get(apiName);

    switch (breaker.state) {
      case CircuitState.CLOSED:
        return true;

      case CircuitState.OPEN:
        // Check if recovery timeout has passed
        if (Date.now() - breaker.lastFailureTime > this.circuitBreakerConfig.recoveryTimeout) {
          breaker.state = CircuitState.HALF_OPEN;
          breaker.halfOpenCalls = 0;
          logger.info(`Circuit breaker for ${apiName} half-opened - testing recovery`);
          return true;
        }
        return false;

      case CircuitState.HALF_OPEN:
        return breaker.halfOpenCalls < this.circuitBreakerConfig.halfOpenMaxCalls;

      default:
        return true;
    }
  }

  /**
   * Get API ranking based on performance and health
   */
  getApiRanking(apiNames) {
    return apiNames
      .map(apiName => {
        const metrics = this.performanceMetrics.get(apiName) || {};
        const breaker = this.circuitBreakers.get(apiName) || {};

        const successRate = metrics.totalCalls > 0 ?
          (metrics.successfulCalls / metrics.totalCalls) : 0;
        const responseTime = metrics.averageResponseTime || 10000;
        const isHealthy = breaker.state !== CircuitState.OPEN;

        // Calculate score (higher is better)
        let score = successRate * 100;
        score -= Math.min(responseTime / 100, 50); // Penalize slow responses
        score += isHealthy ? 20 : -50; // Bonus for healthy APIs

        return { apiName, score, successRate, responseTime, isHealthy };
      })
      .sort((a, b) => b.score - a.score)
      .map(item => item.apiName);
  }

  /**
   * Start health monitoring
   */
  startHealthMonitoring() {
    setInterval(() => {
      this.performHealthChecks();
    }, this.healthCheckInterval);
  }

  /**
   * Perform health checks on all APIs
   */
  async performHealthChecks() {
    for (const [apiName, client] of Object.entries(apiClients)) {
      if (typeof client.healthCheck === 'function') {
        try {
          const startTime = Date.now();
          await client.healthCheck();
          const responseTime = Date.now() - startTime;
          this.recordApiCall(apiName, true, responseTime);
        } catch (error) {
          this.recordApiCall(apiName, false, 0, error);
        }
      }
    }
  }

  /**
   * Get health status for all APIs
   */
  getHealthStatus() {
    const status = {};

    for (const [apiName] of Object.entries(apiClients)) {
      const breaker = this.circuitBreakers.get(apiName) || {};
      const metrics = this.performanceMetrics.get(apiName) || {};

      status[apiName] = {
        circuitState: breaker.state || CircuitState.CLOSED,
        failureCount: breaker.failureCount || 0,
        successRate: metrics.totalCalls > 0 ?
          Math.round((metrics.successfulCalls / metrics.totalCalls) * 100) : 0,
        averageResponseTime: Math.round(metrics.averageResponseTime || 0),
        totalCalls: metrics.totalCalls || 0,
        lastCallTime: metrics.lastCallTime
      };
    }

    return status;
  }
}

// Create global health monitor instance
const healthMonitor = new APIHealthMonitor();

/**
 * Enhanced fallback API execution with circuit breaker and intelligent routing
 * @param {Array} apiOrder - Priority order of APIs to try
 * @param {Object} params - Parameters for the API request
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} - API response data
 */
async function enhancedFallbackAPIs(apiOrder, params, options = {}) {
  if (!apiOrder || !Array.isArray(apiOrder) || apiOrder.length === 0) {
    logger.error('Invalid API order provided');
    throw new Error('Invalid API configuration');
  }

  const {
    useIntelligentRouting = true,
    maxRetries = 3,
    retryDelay = 1000,
    enableCircuitBreaker = true
  } = options;

  const errors = [];
  let attemptedAPIs = [];

  // Use intelligent routing to reorder APIs based on performance
  let orderedAPIs = apiOrder;
  if (useIntelligentRouting) {
    orderedAPIs = healthMonitor.getApiRanking(apiOrder);
    logger.info(`Intelligent routing order: ${orderedAPIs.join(' -> ')}`);
  }

  // Try each API in order until one succeeds
  for (const apiName of orderedAPIs) {
    // Skip if circuit breaker is open
    if (enableCircuitBreaker && !healthMonitor.shouldAllowCall(apiName)) {
      logger.warn(`Skipping ${apiName} - circuit breaker is open`);
      errors.push(`${apiName}: Circuit breaker open`);
      continue;
    }

    // Check if API client exists
    if (!apiClients[apiName]) {
      logger.warn(`API client ${apiName} not found, skipping`);
      errors.push(`${apiName}: API client not found`);
      continue;
    }

    attemptedAPIs.push(apiName);
    let lastError = null;

    // Retry logic for each API
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const startTime = Date.now();

      try {
        logger.info(`Attempting to fetch data from ${apiName} for ${params.symbol} (attempt ${attempt}/${maxRetries})`);

        // Call the API
        const result = await apiClients[apiName].getData(params);
        const responseTime = Date.now() - startTime;

        // Validate result
        if (!result || (result.data && Array.isArray(result.data) && result.data.length === 0)) {
          throw new Error('Empty data returned');
        }

        // Record successful call
        healthMonitor.recordApiCall(apiName, true, responseTime);

        logger.info(`Successfully fetched data from ${apiName} for ${params.symbol} in ${responseTime}ms`);

        // Add metadata to the result
        return {
          ...result,
          source: apiName,
          timestamp: new Date().toISOString(),
          responseTime,
          attempt,
          attemptedAPIs,
          healthStatus: healthMonitor.getHealthStatus()[apiName]
        };

      } catch (error) {
        const responseTime = Date.now() - startTime;
        lastError = error;

        // Record failed call
        healthMonitor.recordApiCall(apiName, false, responseTime, error);

        logger.error(`Error fetching data from ${apiName} (attempt ${attempt}/${maxRetries}):`, error.message);

        // Don't retry on certain errors
        if (error.response?.status === 401 || error.response?.status === 403) {
          logger.warn(`Authentication error for ${apiName}, skipping retries`);
          break;
        }

        // Wait before retry (except on last attempt)
        if (attempt < maxRetries) {
          const delay = retryDelay * Math.pow(2, attempt - 1); // Exponential backoff
          logger.info(`Waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // Add the final error for this API
    if (lastError) {
      errors.push(`${apiName}: ${lastError.message}`);
    }
  }

  // If all APIs failed, try to use mock data if enabled
  if (process.env.ENABLE_MOCK_DATA === 'true') {
    logger.warn(`All APIs failed for ${params.symbol}, using mock data`);

    return {
      data: mockData.generateMockData(params),
      source: 'mock',
      timestamp: new Date().toISOString(),
      mockData: true,
      attemptedAPIs,
      errors: errors.slice(0, 3) // Limit error details
    };
  }

  // If mock data is disabled, throw an error with all the failures
  logger.error(`All APIs failed for ${params.symbol}. Attempted: ${attemptedAPIs.join(', ')}`);

  const error = new Error(`Failed to fetch data from all available sources: ${errors.slice(0, 3).join('; ')}`);
  error.attemptedAPIs = attemptedAPIs;
  error.allErrors = errors;
  error.healthStatus = healthMonitor.getHealthStatus();

  throw error;
}

/**
 * Get API health status
 */
function getAPIHealthStatus() {
  return healthMonitor.getHealthStatus();
}

/**
 * Force health check for all APIs
 */
async function performHealthCheck() {
  return await healthMonitor.performHealthChecks();
}

/**
 * Reset circuit breakers (for testing/admin purposes)
 */
function resetCircuitBreakers() {
  healthMonitor.circuitBreakers.clear();
  healthMonitor.performanceMetrics.clear();
  logger.info('All circuit breakers reset');
}

// Export both old and new functions for backward compatibility
module.exports = enhancedFallbackAPIs;
module.exports.fallbackAPIs = enhancedFallbackAPIs; // New enhanced version
module.exports.getAPIHealthStatus = getAPIHealthStatus;
module.exports.performHealthCheck = performHealthCheck;
module.exports.resetCircuitBreakers = resetCircuitBreakers;
module.exports.healthMonitor = healthMonitor;