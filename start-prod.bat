@echo off
setlocal

:: Set environment variables for production
set NODE_ENV=production
set PORT=3000
set LOG_LEVEL=info
set ENABLE_CACHING=true

:: Check if MongoDB is running locally
echo Checking MongoDB status...
timeout /t 1 >nul
net start | findstr /i "MongoDB" >nul
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: MongoDB service might not be running!
    echo Starting the application anyway, but data storage may fail.
    timeout /t 3 >nul
)

:: Check if Redis is running locally (if installed)
echo Checking Redis status...
timeout /t 1 >nul
net start | findstr /i "Redis" >nul
if %ERRORLEVEL% NEQ 0 (
    echo NOTE: Redis service not detected. App will use in-memory cache.
    timeout /t 2 >nul
)

:: Start the application
echo Starting Trading Signals App in PRODUCTION mode...
echo.
echo Press Ctrl+C to stop the application
echo.

node unified-server.js --mode production --optimize

:: Script will only reach here if the node process exits
echo.
echo Application has stopped.
pause 