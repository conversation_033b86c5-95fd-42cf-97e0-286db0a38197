import dotenv from 'dotenv';
import { z } from 'zod';

// Load environment variables
dotenv.config();

// Environment variable validation schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: Joi.string().default('3000'),
  JWT_SECRET: Joi.string().min(32),
  ALPHA_VANTAGE_API_KEY: Joi.string(),
  FRED_API_KEY: Joi.string(),
  POLYGON_API_KEY: Joi.string(),
  FINNHUB_API_KEY: Joi.string(),
  TWELVE_DATA_API_KEY: Joi.string().optional(),
  OPENAI_API_KEY: z.string().optional(),
  FMP_API_KEY: z.string().optional(),
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_PUBLISHABLE_KEY: z.string().optional(),
  STRIPE_WEBHOOK_SECRET: z.string().optional(),
  SENTRY_DSN: z.string().optional(),
  CORS_ORIGIN: z.string().default('*'),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
});

// Validate environment variables
const env = envSchema.parse({
  NODE_ENV: process.env.NODE_ENV,
  PORT: process.env.PORT,
  JWT_SECRET: process.env.JWT_SECRET,
  ALPHA_VANTAGE_API_KEY: process.env.ALPHA_VANTAGE_API_KEY,
  FRED_API_KEY: process.env.FRED_API_KEY,
  POLYGON_API_KEY: process.env.POLYGON_API_KEY,
  FINNHUB_API_KEY: process.env.FINNHUB_API_KEY,
  TWELVE_DATA_API_KEY: process.env.TWELVE_DATA_API_KEY,
  OPENAI_API_KEY: process.env.OPENAI_API_KEY,
  FMP_API_KEY: process.env.FMP_API_KEY,
  STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
  STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,
  SENTRY_DSN: process.env.SENTRY_DSN,
  CORS_ORIGIN: process.env.CORS_ORIGIN,
  LOG_LEVEL: process.env.LOG_LEVEL,
});

// API configurations
const apiConfig = {
  alphaVantage: {
    baseUrl: 'https://www.alphavantage.co/query',
    apiKey: env.ALPHA_VANTAGE_API_KEY,
  },
  fred: {
    baseUrl: 'https://api.stlouisfed.org/fred',
    apiKey: env.FRED_API_KEY,
    endpoints: {
      series: '/series/observations',
      releases: '/releases',
      calendar: '/releases/dates',
    },
  },
  polygon: {
    baseUrl: 'https://api.polygon.io',
    apiKey: env.POLYGON_API_KEY,
  },
  finnhub: {
    baseUrl: 'https://finnhub.io/api/v1',
    apiKey: env.FINNHUB_API_KEY,
  },
  twelveData: {
    baseUrl: 'https://api.twelvedata.com',
    apiKey: env.TWELVE_DATA_API_KEY || 'demo',
  },
  openai: {
    baseUrl: 'https://api.openai.com/v1',
    apiKey: env.OPENAI_API_KEY,
  },
  fmp: {
    baseUrl: 'https://financialmodelingprep.com/api/v3',
    apiKey: env.FMP_API_KEY,
  },
};

// Cache configuration
const cacheConfig = {
  stdTTL: 300, // 5 minutes
  checkperiod: 60, // Check for expired keys every minute
};

// Security configuration
const securityConfig = {
  cors: {
    origin: env.CORS_ORIGIN,
    credentials: true,
  },
  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: '24h',
  },
  rateLimiter: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
  },
};

export const config = {
  env: env.NODE_ENV,
  port: parseInt(env.PORT),
  api: apiConfig,
  cache: cacheConfig,
  security: securityConfig,
  logging: {
    level: env.LOG_LEVEL,
    format: env.NODE_ENV === 'production' ? 'json' : 'dev',
  },
  stripe: {
    secretKey: process.env.STRIPE_SECRET_KEY,
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
  },
};

export default config;