/**
 * Sentry Test Utility for Server
 * 
 * This file contains functions to test Sentry error tracking on the server.
 * It should only be used in development to verify that <PERSON><PERSON> is working correctly.
 */

const { captureException } = require('./sentry');

/**
 * Generate a test error to verify Sen<PERSON> is working
 */
const generateTestError = () => {
  try {
    // Intentionally cause an error
    const undefinedFunction = null;
    undefinedFunction();
  } catch (error) {
    console.error('Server test error generated:', error);
    captureException(error, { source: 'server/sentryTest.js', context: 'generateTestError' });
    return error;
  }
};

/**
 * Generate a test error with a custom message
 * @param {string} message - Custom error message
 */
const generateCustomError = (message) => {
  try {
    throw new Error(message || 'Custom server test error for Sentry');
  } catch (error) {
    console.error('Custom server test error generated:', error);
    captureException(error, { source: 'server/sentryTest.js', context: 'generateCustomError' });
    return error;
  }
};

module.exports = {
  generateTestError,
  generateCustomError
};
