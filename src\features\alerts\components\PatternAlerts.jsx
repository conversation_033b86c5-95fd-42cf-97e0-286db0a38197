import React, { useState, useEffect, useRef } from 'react';
import { alphaVantageAPI } from '../../../services/apiService';
import patternRecognitionService from '../../../services/patternRecognitionService';
import mlPatternService from '../../../services/mlPatternService';
import { getPatternInfo } from '../../../utils/candlestickPatterns';

/**
 * PatternAlerts Component
 * 
 * Component for real-time pattern detection and alerts
 */
const PatternAlerts = () => {
  // State for alert configuration
  const [config, setConfig] = useState({
    symbols: ['EURUSD', 'GBPUSD', 'USDJPY', 'BTCUSD'],
    timeframes: ['M15', 'H1', 'H4', 'D1'],
    patternTypes: ['bullish', 'bearish', 'reversal'],
    minSignificance: 7,
    useML: true,
    mlConfidenceThreshold: 0.8,
    enableNotifications: true,
    scanInterval: 5, // minutes
    maxAlerts: 50
  });
  
  // State for detected patterns
  const [alerts, setAlerts] = useState([]);
  
  // State for scanning status
  const [isScanning, setIsScanning] = useState(false);
  const [lastScanTime, setLastScanTime] = useState(null);
  const [nextScanTime, setNextScanTime] = useState(null);
  
  // Reference for interval timer
  const scanIntervalRef = useRef(null);
  
  // Initialize alerts from localStorage
  useEffect(() => {
    const savedAlerts = localStorage.getItem('patternAlerts');
    if (savedAlerts) {
      try {
        setAlerts(JSON.parse(savedAlerts));
      } catch (error) {
        console.error('Error loading saved alerts:', error);
      }
    }
  }, []);
  
  // Save alerts to localStorage when they change
  useEffect(() => {
    localStorage.setItem('patternAlerts', JSON.stringify(alerts));
  }, [alerts]);
  
  // Start/stop automatic scanning
  useEffect(() => {
    return () => {
      // Clean up interval on component unmount
      if (scanIntervalRef.current) {
        clearInterval(scanIntervalRef.current);
      }
    };
  }, []);
  
  // Handle configuration change
  const handleConfigChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (type === 'checkbox') {
      setConfig(prev => ({ ...prev, [name]: checked }));
    } else if (name === 'symbols' || name === 'timeframes' || name === 'patternTypes') {
      // Handle multi-select
      const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
      setConfig(prev => ({ ...prev, [name]: selectedOptions }));
    } else if (type === 'number') {
      setConfig(prev => ({ ...prev, [name]: parseFloat(value) }));
    } else {
      setConfig(prev => ({ ...prev, [name]: value }));
    }
  };
  
  // Start automatic scanning
  const startScanning = () => {
    if (scanIntervalRef.current) {
      clearInterval(scanIntervalRef.current);
    }
    
    // Run initial scan
    runScan();
    
    // Set up interval for automatic scanning
    scanIntervalRef.current = setInterval(() => {
      runScan();
    }, config.scanInterval * 60 * 1000);
    
    // Update next scan time
    updateNextScanTime();
  };
  
  // Stop automatic scanning
  const stopScanning = () => {
    if (scanIntervalRef.current) {
      clearInterval(scanIntervalRef.current);
      scanIntervalRef.current = null;
    }
    
    setIsScanning(false);
    setNextScanTime(null);
  };
  
  // Update next scan time
  const updateNextScanTime = () => {
    const next = new Date();
    next.setMinutes(next.getMinutes() + config.scanInterval);
    setNextScanTime(next);
  };
  
  // Run a scan for patterns
  const runScan = async () => {
    setIsScanning(true);
    setLastScanTime(new Date());
    
    try {
      const newAlerts = [];
      
      // Scan each symbol and timeframe combination
      for (const symbol of config.symbols) {
        for (const timeframe of config.timeframes) {
          // Fetch latest data
          const data = await fetchLatestData(symbol, timeframe);
          
          if (!data || data.length === 0) continue;
          
          // Detect patterns
          const patterns = await detectPatterns(data, config);
          
          // Filter patterns by significance
          const significantPatterns = patterns.filter(pattern => 
            pattern.significance >= config.minSignificance
          );
          
          // Add to alerts
          significantPatterns.forEach(pattern => {
            const patternInfo = getPatternInfo(pattern.pattern);
            const patternName = patternInfo ? patternInfo.name : pattern.pattern;
            
            newAlerts.push({
              id: `${symbol}_${timeframe}_${pattern.pattern}_${Date.now()}`,
              symbol,
              timeframe,
              pattern: pattern.pattern,
              patternName,
              type: pattern.type,
              significance: pattern.significance,
              price: data[data.length - 1].close,
              time: new Date().toISOString(),
              description: pattern.description || `${patternName} pattern detected on ${symbol} ${timeframe}`,
              mlDetected: pattern.mlDetected || false,
              confidence: pattern.confidence || null,
              read: false
            });
          });
        }
      }
      
      // Show browser notifications if enabled
      if (config.enableNotifications && newAlerts.length > 0) {
        showNotifications(newAlerts);
      }
      
      // Add new alerts to the list
      setAlerts(prev => {
        // Combine new alerts with existing ones
        const combined = [...newAlerts, ...prev];
        
        // Sort by time (newest first)
        combined.sort((a, b) => new Date(b.time) - new Date(a.time));
        
        // Limit to max alerts
        return combined.slice(0, config.maxAlerts);
      });
    } catch (error) {
      console.error('Error running pattern scan:', error);
    } finally {
      setIsScanning(false);
      updateNextScanTime();
    }
  };
  
  // Fetch latest data for a symbol and timeframe
  const fetchLatestData = async (symbol, timeframe) => {
    try {
      // Convert timeframe to Alpha Vantage interval
      const interval = convertTimeframeToInterval(timeframe);
      
      // Fetch data from Alpha Vantage
      const response = await alphaVantageAPI.getHistoricalData(symbol, interval);
      
      // Process and return the data
      return processHistoricalData(response);
    } catch (error) {
      console.error(`Error fetching data for ${symbol} ${timeframe}:`, error);
      return null;
    }
  };
  
  // Convert app timeframe to Alpha Vantage interval
  const convertTimeframeToInterval = (timeframe) => {
    const mapping = {
      'M1': '1min',
      'M5': '5min',
      'M15': '15min',
      'M30': '30min',
      'H1': '60min',
      'H4': '240min',
      'D1': 'daily',
      'W1': 'weekly',
      'MN': 'monthly'
    };
    
    return mapping[timeframe] || 'daily';
  };
  
  // Process historical data from API response
  const processHistoricalData = (response) => {
    // Extract time series data
    const timeSeries = response['Time Series (Daily)'] || response['Time Series FX (Daily)'] || {};
    
    // Convert to array format
    const data = Object.entries(timeSeries).map(([date, values]) => ({
      date,
      open: parseFloat(values['1. open']),
      high: parseFloat(values['2. high']),
      low: parseFloat(values['3. low']),
      close: parseFloat(values['4. close']),
      volume: parseFloat(values['5. volume'] || '0')
    }));
    
    // Sort by date (newest first)
    data.sort((a, b) => new Date(b.date) - new Date(a.date));
    
    // Return the 100 most recent candles
    return data.slice(0, 100);
  };
  
  // Detect patterns in the data
  const detectPatterns = async (data, config) => {
    const patterns = [];
    
    // Detect candlestick patterns
    for (let i = 0; i < Math.min(5, data.length - 5); i++) {
      // Get a slice of data for pattern detection
      const slice = data.slice(i, i + 5);
      
      // Detect candlestick patterns
      const candlestickPatterns = patternRecognitionService.detectCandlestickPatterns(slice);
      
      // Filter patterns by selected types
      const filteredPatterns = candlestickPatterns.filter(pattern => 
        config.patternTypes.includes(pattern.type)
      );
      
      // Add index to patterns and add to results
      filteredPatterns.forEach(pattern => {
        patterns.push({
          ...pattern,
          index: i
        });
      });
    }
    
    // Detect chart patterns
    const chartPatterns = patternRecognitionService.detectChartPatterns(data);
    patterns.push(...chartPatterns.filter(pattern => 
      config.patternTypes.includes(pattern.type)
    ));
    
    // Use machine learning for pattern detection if enabled
    if (config.useML) {
      try {
        // Configure ML options
        const mlOptions = {
          modelType: 'cnn',
          confidence: config.mlConfidenceThreshold
        };
        
        // Detect patterns using ML
        const mlPatterns = await mlPatternService.detectPatternsWithML(data, mlOptions);
        
        // Filter ML patterns by selected types
        const filteredMLPatterns = mlPatterns.filter(pattern => 
          config.patternTypes.includes(pattern.type)
        );
        
        // Add ML patterns to results
        patterns.push(...filteredMLPatterns);
      } catch (error) {
        console.error('Error detecting patterns with ML:', error);
      }
    }
    
    return patterns;
  };
  
  // Show browser notifications for new alerts
  const showNotifications = (newAlerts) => {
    // Check if browser notifications are supported and permission is granted
    if (!('Notification' in window)) {
      console.log('Browser does not support notifications');
      return;
    }
    
    if (Notification.permission === 'granted') {
      // Show notifications for each alert (max 3 to avoid spam)
      newAlerts.slice(0, 3).forEach(alert => {
        new Notification('Pattern Alert', {
          body: `${alert.patternName} detected on ${alert.symbol} ${alert.timeframe}`,
          icon: '/logo192.png'
        });
      });
      
      // If there are more alerts, show a summary notification
      if (newAlerts.length > 3) {
        new Notification('Pattern Alerts', {
          body: `${newAlerts.length - 3} more patterns detected`,
          icon: '/logo192.png'
        });
      }
    } else if (Notification.permission !== 'denied') {
      // Request permission
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          showNotifications(newAlerts);
        }
      });
    }
  };
  
  // Mark alert as read
  const markAsRead = (alertId) => {
    setAlerts(prev => 
      prev.map(alert => 
        alert.id === alertId ? { ...alert, read: true } : alert
      )
    );
  };
  
  // Mark all alerts as read
  const markAllAsRead = () => {
    setAlerts(prev => 
      prev.map(alert => ({ ...alert, read: true }))
    );
  };
  
  // Clear all alerts
  const clearAlerts = () => {
    setAlerts([]);
  };
  
  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  return (
    <div className="pattern-alerts p-4">
      <h2 className="text-xl font-bold mb-4">Pattern Alerts</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {/* Alert Configuration */}
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow col-span-1">
          <h3 className="text-lg font-medium mb-3">Configuration</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Symbols</label>
              <select
                name="symbols"
                value={config.symbols}
                onChange={handleConfigChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                multiple
                size="4"
              >
                <option value="EURUSD">EUR/USD</option>
                <option value="GBPUSD">GBP/USD</option>
                <option value="USDJPY">USD/JPY</option>
                <option value="AUDUSD">AUD/USD</option>
                <option value="USDCAD">USD/CAD</option>
                <option value="BTCUSD">BTC/USD</option>
                <option value="ETHUSD">ETH/USD</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Timeframes</label>
              <select
                name="timeframes"
                value={config.timeframes}
                onChange={handleConfigChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                multiple
                size="4"
              >
                <option value="M15">15 Minutes</option>
                <option value="M30">30 Minutes</option>
                <option value="H1">1 Hour</option>
                <option value="H4">4 Hours</option>
                <option value="D1">Daily</option>
                <option value="W1">Weekly</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Pattern Types</label>
              <select
                name="patternTypes"
                value={config.patternTypes}
                onChange={handleConfigChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                multiple
                size="3"
              >
                <option value="bullish">Bullish</option>
                <option value="bearish">Bearish</option>
                <option value="reversal">Reversal</option>
                <option value="continuation">Continuation</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Min. Significance ({config.minSignificance})
              </label>
              <input
                type="range"
                name="minSignificance"
                value={config.minSignificance}
                onChange={handleConfigChange}
                min="1"
                max="10"
                step="1"
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                name="useML"
                checked={config.useML}
                onChange={handleConfigChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Use Machine Learning
              </label>
            </div>
            
            {config.useML && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  ML Confidence ({(config.mlConfidenceThreshold * 100).toFixed(0)}%)
                </label>
                <input
                  type="range"
                  name="mlConfidenceThreshold"
                  value={config.mlConfidenceThreshold}
                  onChange={handleConfigChange}
                  min="0.5"
                  max="0.95"
                  step="0.05"
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>
            )}
            
            <div className="flex items-center">
              <input
                type="checkbox"
                name="enableNotifications"
                checked={config.enableNotifications}
                onChange={handleConfigChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Enable Notifications
              </label>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Scan Interval (minutes)
              </label>
              <input
                type="number"
                name="scanInterval"
                value={config.scanInterval}
                onChange={handleConfigChange}
                min="1"
                max="60"
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
            
            <div className="flex justify-between">
              {!isScanning && !scanIntervalRef.current ? (
                <button
                  onClick={startScanning}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded"
                >
                  Start Scanning
                </button>
              ) : (
                <button
                  onClick={stopScanning}
                  className="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded"
                >
                  Stop Scanning
                </button>
              )}
              
              <button
                onClick={runScan}
                className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded"
                disabled={isScanning}
              >
                Scan Now
              </button>
            </div>
          </div>
        </div>
        
        {/* Alert List */}
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow col-span-2">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-lg font-medium">Alerts</h3>
            
            <div className="flex space-x-2">
              <button
                onClick={markAllAsRead}
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Mark All as Read
              </button>
              
              <button
                onClick={clearAlerts}
                className="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
              >
                Clear All
              </button>
            </div>
          </div>
          
          {/* Scanning Status */}
          <div className="mb-3 text-sm text-gray-600 dark:text-gray-400">
            {isScanning ? (
              <p>Scanning in progress...</p>
            ) : (
              <>
                {lastScanTime && (
                  <p>Last scan: {formatDate(lastScanTime)}</p>
                )}
                
                {nextScanTime && (
                  <p>Next scan: {formatDate(nextScanTime)}</p>
                )}
              </>
            )}
          </div>
          
          {/* Alert List */}
          <div className="overflow-y-auto max-h-96">
            {alerts.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-4">No alerts yet</p>
            ) : (
              <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                {alerts.map(alert => (
                  <li
                    key={alert.id}
                    className={`py-3 ${alert.read ? 'opacity-70' : 'bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20'}`}
                    onClick={() => markAsRead(alert.id)}
                  >
                    <div className="flex justify-between">
                      <div>
                        <span className={`inline-block w-2 h-2 rounded-full mr-2 ${
                          alert.type === 'bullish' ? 'bg-green-500' :
                          alert.type === 'bearish' ? 'bg-red-500' :
                          'bg-yellow-500'
                        }`}></span>
                        <span className="font-medium">{alert.patternName}</span>
                        <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                          {alert.symbol} {alert.timeframe}
                        </span>
                      </div>
                      
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {formatDate(alert.time)}
                      </div>
                    </div>
                    
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">{alert.description}</p>
                    
                    <div className="mt-1 flex justify-between text-xs">
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">Price: </span>
                        <span>{alert.price.toFixed(4)}</span>
                      </div>
                      
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">Significance: </span>
                        <span>{alert.significance}/10</span>
                        
                        {alert.mlDetected && (
                          <>
                            <span className="ml-2 text-gray-500 dark:text-gray-400">ML Confidence: </span>
                            <span>{(alert.confidence * 100).toFixed(0)}%</span>
                          </>
                        )}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatternAlerts;
