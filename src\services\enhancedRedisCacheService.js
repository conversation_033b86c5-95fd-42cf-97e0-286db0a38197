const Redis = require('ioredis');

// Create a simple logger if not available
const logger = {
  info: (msg) => console.log(`[INFO] ${msg}`),
  error: (msg, err) => console.error(`[ERROR] ${msg}`, err),
  warn: (msg) => console.warn(`[WARN] ${msg}`),
  debug: (msg) => console.log(`[DEBUG] ${msg}`)
};

/**
 * Enhanced Redis Cache Service for Trading Signals App
 *
 * Provides high-performance caching with specific strategies for different data types:
 * - Market prices: 30-second TTL with real-time invalidation
 * - Technical indicators: 5-minute TTL with symbol-based keys
 * - AI analysis results: 10-minute TTL with model+symbol+timeframe composite keys
 * - User sessions: 24-hour TTL with automatic refresh
 * - Query performance monitoring and optimization
 *
 * @class RedisCacheService
 * @version 2.0.0
 */
class RedisCacheService {
  /**
   * Initialize Redis cache service with enhanced configuration
   *
   * @constructor
   */
  constructor() {
    this.redis = null;
    this.isConnected = false;
    this.connectionRetries = 0;
    this.maxRetries = 5;
    this.performanceMetrics = {
      hits: 0,
      misses: 0,
      errors: 0,
      totalRequests: 0,
      averageResponseTime: 0
    };

    // TTL configurations for different data types
    this.TTL_CONFIG = {
      MARKET_DATA: 30,        // 30 seconds for real-time market data
      TECHNICAL_INDICATORS: 300,  // 5 minutes for technical analysis
      AI_ANALYSIS: 600,       // 10 minutes for AI results
      USER_SESSIONS: 86400,   // 24 hours for user sessions
      API_RESPONSES: 180,     // 3 minutes for API responses
      ECONOMIC_CALENDAR: 3600, // 1 hour for economic events
      NEWS_DATA: 1800,        // 30 minutes for news
      PERFORMANCE_METRICS: 300 // 5 minutes for performance data
    };

    this.init();
  }

  /**
   * Initialize Redis connection with enhanced configuration
   */
  async init() {
    try {
      const redisConfig = {
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD,
        db: process.env.REDIS_DB || 0,
        retryStrategy: (times) => {
          const delay = Math.min(times * 50, 2000);
          return delay;
        },
        lazyConnect: true,
        maxRetriesPerRequest: 3,
        connectTimeout: 5000,
        commandTimeout: 3000,
        enableReadyCheck: true,
        maxMemoryPolicy: 'allkeys-lru'
      };

      this.redis = new Redis(redisConfig);

      // Set up event handlers
      this.redis.on('connect', () => {
        logger.info('Redis cache service connected');
        this.isConnected = true;
        this.connectionRetries = 0;
      });

      this.redis.on('error', (error) => {
        logger.error('Redis cache service error:', error);
        this.isConnected = false;
        this.performanceMetrics.errors++;
      });

      this.redis.on('close', () => {
        logger.warn('Redis cache service connection closed');
        this.isConnected = false;
      });

      // Test connection
      await this.redis.ping();
      logger.info('Redis cache service initialized successfully');

    } catch (error) {
      logger.error('Failed to initialize Redis cache service:', error);
      this.isConnected = false;
    }
  }

  /**
   * Enhanced get method with performance tracking
   * @param {string} key - Cache key
   * @param {string} dataType - Type of data for TTL selection
   * @returns {Promise<any>} Cached value or null
   */
  async get(key, dataType = 'DEFAULT') {
    const startTime = Date.now();
    this.performanceMetrics.totalRequests++;

    try {
      if (!this.isConnected) {
        this.performanceMetrics.misses++;
        return null;
      }

      const value = await this.redis.get(key);
      const responseTime = Date.now() - startTime;
      
      this.updateAverageResponseTime(responseTime);

      if (value !== null) {
        this.performanceMetrics.hits++;
        logger.debug(`Cache HIT for key: ${key} (${responseTime}ms)`);
        return JSON.parse(value);
      } else {
        this.performanceMetrics.misses++;
        logger.debug(`Cache MISS for key: ${key} (${responseTime}ms)`);
        return null;
      }
    } catch (error) {
      this.performanceMetrics.errors++;
      logger.error(`Cache GET error for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Enhanced set method with automatic TTL selection
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {string} dataType - Type of data for TTL selection
   * @param {number} customTTL - Custom TTL override
   * @returns {Promise<boolean>} Success status
   */
  async set(key, value, dataType = 'DEFAULT', customTTL = null) {
    const startTime = Date.now();

    try {
      if (!this.isConnected) {
        return false;
      }

      const ttl = customTTL || this.TTL_CONFIG[dataType] || this.TTL_CONFIG.API_RESPONSES;
      const serializedValue = JSON.stringify(value);
      
      await this.redis.setex(key, ttl, serializedValue);
      
      const responseTime = Date.now() - startTime;
      logger.debug(`Cache SET for key: ${key} with TTL: ${ttl}s (${responseTime}ms)`);
      
      return true;
    } catch (error) {
      this.performanceMetrics.errors++;
      logger.error(`Cache SET error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Set market data with optimized TTL
   * @param {string} symbol - Trading symbol
   * @param {string} timeframe - Timeframe
   * @param {any} data - Market data
   * @returns {Promise<boolean>} Success status
   */
  async setMarketData(symbol, timeframe, data) {
    const key = `market:${symbol}:${timeframe}`;
    return await this.set(key, data, 'MARKET_DATA');
  }

  /**
   * Get market data
   * @param {string} symbol - Trading symbol
   * @param {string} timeframe - Timeframe
   * @returns {Promise<any>} Market data or null
   */
  async getMarketData(symbol, timeframe) {
    const key = `market:${symbol}:${timeframe}`;
    return await this.get(key, 'MARKET_DATA');
  }

  /**
   * Set technical indicators with optimized TTL
   * @param {string} symbol - Trading symbol
   * @param {string} timeframe - Timeframe
   * @param {any} indicators - Technical indicators
   * @returns {Promise<boolean>} Success status
   */
  async setTechnicalIndicators(symbol, timeframe, indicators) {
    const key = `indicators:${symbol}:${timeframe}`;
    return await this.set(key, indicators, 'TECHNICAL_INDICATORS');
  }

  /**
   * Get technical indicators
   * @param {string} symbol - Trading symbol
   * @param {string} timeframe - Timeframe
   * @returns {Promise<any>} Technical indicators or null
   */
  async getTechnicalIndicators(symbol, timeframe) {
    const key = `indicators:${symbol}:${timeframe}`;
    return await this.get(key, 'TECHNICAL_INDICATORS');
  }

  /**
   * Set AI analysis results with optimized TTL
   * @param {string} symbol - Trading symbol
   * @param {string} model - AI model name
   * @param {string} timeframe - Timeframe
   * @param {any} analysis - AI analysis results
   * @returns {Promise<boolean>} Success status
   */
  async setAIAnalysis(symbol, model, timeframe, analysis) {
    const key = `ai:${model}:${symbol}:${timeframe}`;
    return await this.set(key, analysis, 'AI_ANALYSIS');
  }

  /**
   * Get AI analysis results
   * @param {string} symbol - Trading symbol
   * @param {string} model - AI model name
   * @param {string} timeframe - Timeframe
   * @returns {Promise<any>} AI analysis or null
   */
  async getAIAnalysis(symbol, model, timeframe) {
    const key = `ai:${model}:${symbol}:${timeframe}`;
    return await this.get(key, 'AI_ANALYSIS');
  }

  /**
   * Update average response time metric
   * @param {number} responseTime - Response time in milliseconds
   */
  updateAverageResponseTime(responseTime) {
    const totalTime = this.performanceMetrics.averageResponseTime * this.performanceMetrics.totalRequests;
    this.performanceMetrics.averageResponseTime = (totalTime + responseTime) / this.performanceMetrics.totalRequests;
  }

  /**
   * Get performance metrics
   * @returns {Object} Performance metrics
   */
  getMetrics() {
    const hitRate = this.performanceMetrics.totalRequests > 0 
      ? (this.performanceMetrics.hits / this.performanceMetrics.totalRequests * 100).toFixed(2) 
      : 0;

    return {
      ...this.performanceMetrics,
      hitRate: `${hitRate}%`,
      isConnected: this.isConnected
    };
  }

  /**
   * Clear all cache entries
   * @returns {Promise<boolean>} Success status
   */
  async clear() {
    try {
      if (!this.isConnected) {
        return false;
      }

      await this.redis.flushdb();
      logger.info('Cache cleared successfully');
      return true;
    } catch (error) {
      this.performanceMetrics.errors++;
      logger.error('Cache clear error:', error);
      return false;
    }
  }

  /**
   * Close Redis connection
   */
  async close() {
    try {
      if (this.redis && this.isConnected) {
        await this.redis.quit();
        this.isConnected = false;
        logger.info('Redis cache service connection closed');
      }
    } catch (error) {
      logger.error('Error closing Redis connection:', error);
    }
  }
}

// Create and export singleton instance
const redisCacheService = new RedisCacheService();
module.exports = { redisCacheService, RedisCacheService };
