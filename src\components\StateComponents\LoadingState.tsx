/**
 * Loading State Components
 * 
 * Standardized loading indicators for consistent user experience
 * across the entire application. Provides various loading styles
 * for different contexts and use cases.
 * 
 * Features:
 * - Multiple loading indicator styles
 * - Skeleton loading for content placeholders
 * - Progress indicators for long operations
 * - Accessible loading states
 * - Customizable sizing and colors
 * 
 * @version 1.0.0
 */

import React from 'react';

// ============================================================================
// INTERFACES
// ============================================================================

export interface LoadingStateProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'spinner' | 'dots' | 'pulse' | 'skeleton' | 'progress';
  message?: string;
  progress?: number; // 0-100 for progress variant
  color?: 'blue' | 'gray' | 'green' | 'red' | 'yellow';
  fullScreen?: boolean;
  overlay?: boolean;
  className?: string;
}

export interface SkeletonProps {
  lines?: number;
  height?: string;
  width?: string;
  className?: string;
  animate?: boolean;
}

export interface ProgressProps {
  value: number; // 0-100
  max?: number;
  label?: string;
  showPercentage?: boolean;
  color?: 'blue' | 'green' | 'yellow' | 'red';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

// ============================================================================
// LOADING SPINNER COMPONENT
// ============================================================================

const LoadingSpinner: React.FC<{ size: string; color: string }> = ({ size, color }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const colorClasses = {
    blue: 'text-blue-600',
    gray: 'text-gray-600',
    green: 'text-green-600',
    red: 'text-red-600',
    yellow: 'text-yellow-600'
  };

  return (
    <svg
      className={`animate-spin ${sizeClasses[size as keyof typeof sizeClasses]} ${colorClasses[color as keyof typeof colorClasses]}`}
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
};

// ============================================================================
// LOADING DOTS COMPONENT
// ============================================================================

const LoadingDots: React.FC<{ size: string; color: string }> = ({ size, color }) => {
  const sizeClasses = {
    sm: 'w-1 h-1',
    md: 'w-2 h-2',
    lg: 'w-3 h-3',
    xl: 'w-4 h-4'
  };

  const colorClasses = {
    blue: 'bg-blue-600',
    gray: 'bg-gray-600',
    green: 'bg-green-600',
    red: 'bg-red-600',
    yellow: 'bg-yellow-600'
  };

  const dotClass = `${sizeClasses[size as keyof typeof sizeClasses]} ${colorClasses[color as keyof typeof colorClasses]} rounded-full`;

  return (
    <div className="flex space-x-1">
      <div className={`${dotClass} animate-bounce`} style={{ animationDelay: '0ms' }} />
      <div className={`${dotClass} animate-bounce`} style={{ animationDelay: '150ms' }} />
      <div className={`${dotClass} animate-bounce`} style={{ animationDelay: '300ms' }} />
    </div>
  );
};

// ============================================================================
// LOADING PULSE COMPONENT
// ============================================================================

const LoadingPulse: React.FC<{ size: string; color: string }> = ({ size, color }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-20 h-20'
  };

  const colorClasses = {
    blue: 'bg-blue-600',
    gray: 'bg-gray-600',
    green: 'bg-green-600',
    red: 'bg-red-600',
    yellow: 'bg-yellow-600'
  };

  return (
    <div className={`${sizeClasses[size as keyof typeof sizeClasses]} ${colorClasses[color as keyof typeof colorClasses]} rounded-full animate-pulse opacity-75`} />
  );
};

// ============================================================================
// SKELETON COMPONENT
// ============================================================================

export const Skeleton: React.FC<SkeletonProps> = ({
  lines = 3,
  height = '1rem',
  width = '100%',
  className = '',
  animate = true
}) => {
  const skeletonLines = Array.from({ length: lines }, (_, index) => (
    <div
      key={index}
      className={`bg-gray-200 rounded ${animate ? 'animate-pulse' : ''} ${className}`}
      style={{
        height,
        width: index === lines - 1 ? '75%' : width // Last line is shorter
      }}
    />
  ));

  return (
    <div className="space-y-2">
      {skeletonLines}
    </div>
  );
};

// ============================================================================
// PROGRESS BAR COMPONENT
// ============================================================================

export const ProgressBar: React.FC<ProgressProps> = ({
  value,
  max = 100,
  label,
  showPercentage = true,
  color = 'blue',
  size = 'md',
  className = ''
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };

  const colorClasses = {
    blue: 'bg-blue-600',
    green: 'bg-green-600',
    yellow: 'bg-yellow-600',
    red: 'bg-red-600'
  };

  return (
    <div className={`w-full ${className}`}>
      {(label || showPercentage) && (
        <div className="flex justify-between items-center mb-1">
          {label && <span className="text-sm font-medium text-gray-700">{label}</span>}
          {showPercentage && <span className="text-sm text-gray-500">{Math.round(percentage)}%</span>}
        </div>
      )}
      <div className={`w-full bg-gray-200 rounded-full ${sizeClasses[size]}`}>
        <div
          className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full transition-all duration-300 ease-out`}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
};

// ============================================================================
// MAIN LOADING STATE COMPONENT
// ============================================================================

const LoadingState: React.FC<LoadingStateProps> = ({
  size = 'md',
  variant = 'spinner',
  message,
  progress,
  color = 'blue',
  fullScreen = false,
  overlay = false,
  className = ''
}) => {
  const renderLoadingIndicator = () => {
    switch (variant) {
      case 'spinner':
        return <LoadingSpinner size={size} color={color} />;
      case 'dots':
        return <LoadingDots size={size} color={color} />;
      case 'pulse':
        return <LoadingPulse size={size} color={color} />;
      case 'skeleton':
        return <Skeleton lines={3} animate={true} />;
      case 'progress':
        return (
          <ProgressBar
            value={progress || 0}
            color={color}
            size={size}
            showPercentage={true}
          />
        );
      default:
        return <LoadingSpinner size={size} color={color} />;
    }
  };

  const containerClasses = fullScreen
    ? 'fixed inset-0 flex items-center justify-center bg-white z-50'
    : overlay
    ? 'absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10'
    : 'flex items-center justify-center p-4';

  return (
    <div className={`${containerClasses} ${className}`}>
      <div className="flex flex-col items-center space-y-3">
        {renderLoadingIndicator()}
        
        {message && (
          <p className="text-sm text-gray-600 text-center max-w-xs">
            {message}
          </p>
        )}
        
        {variant === 'progress' && progress !== undefined && (
          <p className="text-xs text-gray-500">
            {Math.round(progress)}% complete
          </p>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// SPECIALIZED LOADING COMPONENTS
// ============================================================================

export const PageLoading: React.FC<{ message?: string }> = ({ message = 'Loading...' }) => (
  <LoadingState
    variant="spinner"
    size="lg"
    message={message}
    fullScreen={true}
    color="blue"
  />
);

export const ComponentLoading: React.FC<{ message?: string; overlay?: boolean }> = ({ 
  message = 'Loading...', 
  overlay = false 
}) => (
  <LoadingState
    variant="spinner"
    size="md"
    message={message}
    overlay={overlay}
    color="blue"
  />
);

export const InlineLoading: React.FC<{ message?: string; size?: 'sm' | 'md' }> = ({ 
  message, 
  size = 'sm' 
}) => (
  <div className="flex items-center space-x-2">
    <LoadingSpinner size={size} color="blue" />
    {message && <span className="text-sm text-gray-600">{message}</span>}
  </div>
);

export const TableLoading: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => (
  <div className="space-y-3">
    {Array.from({ length: rows }, (_, rowIndex) => (
      <div key={rowIndex} className="flex space-x-4">
        {Array.from({ length: columns }, (_, colIndex) => (
          <Skeleton
            key={colIndex}
            lines={1}
            height="1.5rem"
            width={colIndex === 0 ? '25%' : colIndex === columns - 1 ? '15%' : '20%'}
          />
        ))}
      </div>
    ))}
  </div>
);

export const CardLoading: React.FC = () => (
  <div className="p-4 border rounded-lg">
    <Skeleton lines={1} height="1.5rem" width="60%" className="mb-3" />
    <Skeleton lines={3} height="1rem" />
    <div className="mt-4 flex space-x-2">
      <Skeleton lines={1} height="2rem" width="5rem" />
      <Skeleton lines={1} height="2rem" width="5rem" />
    </div>
  </div>
);

export const ChartLoading: React.FC<{ height?: string }> = ({ height = '300px' }) => (
  <div className="border rounded-lg p-4" style={{ height }}>
    <Skeleton lines={1} height="1.5rem" width="40%" className="mb-4" />
    <div className="flex items-end space-x-2 h-48">
      {Array.from({ length: 12 }, (_, index) => (
        <div
          key={index}
          className="bg-gray-200 animate-pulse rounded-t"
          style={{
            height: `${Math.random() * 80 + 20}%`,
            width: '100%'
          }}
        />
      ))}
    </div>
  </div>
);

// ============================================================================
// LOADING WITH TIMEOUT
// ============================================================================

export const LoadingWithTimeout: React.FC<{
  timeout?: number;
  onTimeout?: () => void;
  children: React.ReactNode;
}> = ({ timeout = 30000, onTimeout, children }) => {
  const [hasTimedOut, setHasTimedOut] = React.useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setHasTimedOut(true);
      onTimeout?.();
    }, timeout);

    return () => clearTimeout(timer);
  }, [timeout, onTimeout]);

  if (hasTimedOut) {
    return (
      <div className="text-center p-8">
        <div className="text-yellow-600 mb-2">
          <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <p className="text-gray-600">This is taking longer than expected...</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-blue-600 hover:text-blue-800 text-sm"
        >
          Try refreshing the page
        </button>
      </div>
    );
  }

  return <>{children}</>;
};

export default LoadingState;
