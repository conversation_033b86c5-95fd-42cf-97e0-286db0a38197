/**
 * Application Configuration
 * 
 * This file contains centralized configuration settings for the Trading Signals App.
 * Environment-specific settings should be placed in .env file.
 */

// Default API priority by market type (fallback order)
const API_PRIORITY = {
  stocks: ['alphaVantage', 'twelveData', 'finnhub', 'polygon', 'fmp'],
  forex: ['alphaVantage', 'twelveData', 'fmp', 'finnhub'],
  crypto: ['twelveData', 'alphaVantage', 'finnhub', 'fmp'],
  commodities: ['twelveData', 'alphaVantage', 'fmp'],
  economics: ['fred', 'fmp', 'alphaVantage'],
  news: ['finnhub', 'alphaVantage', 'fmp']
};

// Timeframe settings and their corresponding intervals in seconds
const TIMEFRAMES = {
  M1: { seconds: 60, name: '1 Minute' },
  M5: { seconds: 300, name: '5 Minutes' },
  M15: { seconds: 900, name: '15 Minutes' },
  M30: { seconds: 1800, name: '30 Minutes' },
  H1: { seconds: 3600, name: '1 Hour' },
  H4: { seconds: 14400, name: '4 Hours' },
  D1: { seconds: 86400, name: '1 Day' },
  W1: { seconds: 604800, name: '1 Week' },
  MN: { seconds: 2592000, name: '1 Month' }
};

// Cache TTL settings based on timeframe (in seconds)
const CACHE_TTL_BY_TIMEFRAME = {
  M1: 60,              // 1 minute data - cache for 1 minute
  M5: 300,             // 5 minute data - cache for 5 minutes
  M15: 900,            // 15 minute data - cache for 15 minutes
  M30: 1800,           // 30 minute data - cache for 30 minutes
  H1: 3600,            // 1 hour data - cache for 1 hour
  H4: 7200,            // 4 hour data - cache for 2 hours
  D1: 21600,           // Daily data - cache for 6 hours
  W1: 86400,           // Weekly data - cache for 24 hours
  MN: 259200           // Monthly data - cache for 3 days
};

// Supported market types
const MARKET_TYPES = [
  { id: 'stocks', name: 'Stocks' },
  { id: 'forex', name: 'Forex' },
  { id: 'crypto', name: 'Cryptocurrency' },
  { id: 'commodities', name: 'Commodities' },
  { id: 'economics', name: 'Economic Indicators' },
  { id: 'news', name: 'Financial News' }
];

// Common symbols by market type for quick access
const COMMON_SYMBOLS = {
  stocks: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA'],
  forex: ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD'],
  crypto: ['BTCUSD', 'ETHUSD', 'LTCUSD', 'XRPUSD', 'ADAUSD'],
  commodities: ['GOLD', 'SILVER', 'OIL', 'NATGAS', 'COPPER'],
  economics: ['GDP', 'UNEMPLOYMENT', 'CPI', 'INTERESTRATE', 'RETAILSALES'],
  news: ['MARKET', 'ECONOMY', 'EARNINGS', 'CRYPTO', 'FOREX']
};

// Technical indicators configuration
const INDICATORS = {
  sma: {
    name: 'Simple Moving Average',
    defaultPeriods: [20, 50, 200]
  },
  ema: {
    name: 'Exponential Moving Average',
    defaultPeriods: [12, 26, 50]
  },
  rsi: {
    name: 'Relative Strength Index',
    defaultPeriod: 14,
    overbought: 70,
    oversold: 30
  },
  macd: {
    name: 'Moving Average Convergence Divergence',
    defaultSettings: {
      fastPeriod: 12,
      slowPeriod: 26,
      signalPeriod: 9
    }
  },
  bollinger: {
    name: 'Bollinger Bands',
    defaultPeriod: 20,
    defaultStdDev: 2
  }
};

// Application settings
const APP_SETTINGS = {
  maxDataPoints: 500,
  defaultTimeframe: 'D1',
  apiRequestTimeout: 30000, // 30 seconds
  rateLimiting: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequestsPerWindow: 100
  },
  security: {
    helmet: {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'", "cdn.jsdelivr.net"],
          styleSrc: ["'self'", "'unsafe-inline'", "cdn.jsdelivr.net", "fonts.googleapis.com"],
          imgSrc: ["'self'", "data:", "cdn.jsdelivr.net"],
          fontSrc: ["'self'", "fonts.gstatic.com", "cdn.jsdelivr.net"],
          connectSrc: ["'self'", "api.example.com"]
        }
      }
    },
    cors: {
      allowedOrigins: process.env.CORS_ALLOWED_ORIGINS ? 
        process.env.CORS_ALLOWED_ORIGINS.split(',') : ['*'],
      allowedMethods: ['GET', 'POST', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      maxAge: 86400 // 24 hours
    }
  }
};

// Database settings
const DB_SETTINGS = {
  mongodb: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/trading-signals',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000,
      connectTimeoutMS: 10000,
    },
    reconnection: {
      maxAttempts: 5,
      initialDelay: 1000, // 1 second
      maxDelay: 10000 // 10 seconds
    }
  }
};

// Cache settings
const CACHE_SETTINGS = {
  redis: {
    enabled: process.env.USE_REDIS === 'true',
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    defaultTTL: parseInt(process.env.CACHE_TTL || '3600', 10) // 1 hour in seconds
  },
  nodeCache: {
    stdTTL: parseInt(process.env.CACHE_TTL || '3600', 10), // 1 hour in seconds
    checkperiod: 120, // Check for expired keys every 120 seconds
  }
};

// Logging settings
const LOGGING_SETTINGS = {
  level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug'),
  fileRotation: {
    maxFiles: '30d', // Keep logs for 30 days
    maxSize: '20m', // 20 MB per file
    datePattern: 'YYYY-MM-DD'
  },
  console: {
    enabled: process.env.NODE_ENV !== 'production'
  }
};

module.exports = {
  API_PRIORITY,
  TIMEFRAMES,
  CACHE_TTL_BY_TIMEFRAME,
  MARKET_TYPES,
  COMMON_SYMBOLS,
  INDICATORS,
  APP_SETTINGS,
  DB_SETTINGS,
  CACHE_SETTINGS,
  LOGGING_SETTINGS
}; 