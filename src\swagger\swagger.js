/**
 * Swagger Configuration for Trading Signals App
 * 
 * This file contains the Swagger configuration for API documentation.
 */

const swaggerJsdoc = require('swagger-jsdoc');
const logger = require('../utils/logger');

// Swagger definition
const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'Trading Signals App API',
    version: '1.0.0',
    description: 'API documentation for the Trading Signals App',
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT',
    },
    contact: {
      name: 'Trading Signals App Support',
      email: '<EMAIL>',
    },
  },
  servers: [
    {
      url: 'http://localhost:3000',
      description: 'Development server',
    },
    {
      url: 'https://api.tradingsignalsapp.com',
      description: 'Production server',
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
      },
    },
    schemas: {
      User: {
        type: 'object',
        required: ['email', 'username', 'password'],
        properties: {
          _id: {
            type: 'string',
            description: 'User ID',
            example: '60d21b4667d0d8992e610c85',
          },
          email: {
            type: 'string',
            format: 'email',
            description: 'User email',
            example: '<EMAIL>',
          },
          username: {
            type: 'string',
            description: 'Username',
            example: 'johndoe',
          },
          password: {
            type: 'string',
            format: 'password',
            description: 'User password',
            example: 'Password123',
          },
          name: {
            type: 'string',
            description: 'User full name',
            example: 'John Doe',
          },
          role: {
            type: 'string',
            enum: ['user', 'admin'],
            description: 'User role',
            example: 'user',
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Creation date',
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Last update date',
          },
        },
      },
      UserPreferences: {
        type: 'object',
        properties: {
          theme: {
            type: 'string',
            enum: ['light', 'dark', 'system'],
            description: 'UI theme',
            example: 'dark',
          },
          language: {
            type: 'string',
            enum: ['en', 'ar'],
            description: 'UI language',
            example: 'en',
          },
          notifications: {
            type: 'object',
            properties: {
              signals: {
                type: 'boolean',
                description: 'Enable signal notifications',
                example: true,
              },
              news: {
                type: 'boolean',
                description: 'Enable news notifications',
                example: true,
              },
              economic: {
                type: 'boolean',
                description: 'Enable economic calendar notifications',
                example: true,
              },
            },
          },
          trading: {
            type: 'object',
            properties: {
              defaultTimeframe: {
                type: 'string',
                enum: ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN'],
                description: 'Default chart timeframe',
                example: 'H1',
              },
              defaultSymbol: {
                type: 'string',
                description: 'Default trading symbol',
                example: 'EURUSD',
              },
              defaultLotSize: {
                type: 'number',
                description: 'Default lot size',
                example: 0.01,
              },
              riskPercentage: {
                type: 'number',
                description: 'Risk percentage per trade',
                example: 2,
              },
            },
          },
        },
      },
      TradingSignal: {
        type: 'object',
        required: ['symbol', 'type', 'timeframe', 'entryPrice'],
        properties: {
          _id: {
            type: 'string',
            description: 'Signal ID',
            example: '60d21b4667d0d8992e610c85',
          },
          userId: {
            type: 'string',
            description: 'User ID',
            example: '60d21b4667d0d8992e610c85',
          },
          symbol: {
            type: 'string',
            description: 'Trading symbol',
            example: 'EURUSD',
          },
          type: {
            type: 'string',
            enum: ['buy', 'sell'],
            description: 'Signal type',
            example: 'buy',
          },
          strength: {
            type: 'integer',
            minimum: 0,
            maximum: 100,
            description: 'Signal strength (0-100)',
            example: 75,
          },
          timeframe: {
            type: 'string',
            enum: ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN'],
            description: 'Chart timeframe',
            example: 'H1',
          },
          entryPrice: {
            type: 'number',
            description: 'Entry price',
            example: 1.0875,
          },
          stopLoss: {
            type: 'number',
            description: 'Stop loss price',
            example: 1.0850,
          },
          takeProfit: {
            type: 'number',
            description: 'Take profit price',
            example: 1.0925,
          },
          notes: {
            type: 'string',
            description: 'Signal notes',
            example: 'Strong bullish momentum',
          },
          status: {
            type: 'string',
            enum: ['active', 'executed', 'expired'],
            description: 'Signal status',
            example: 'active',
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Creation date',
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Last update date',
          },
        },
      },
      MarketData: {
        type: 'object',
        required: ['symbol', 'timeframe', 'timestamp', 'open', 'high', 'low', 'close'],
        properties: {
          _id: {
            type: 'string',
            description: 'Market data ID',
            example: '60d21b4667d0d8992e610c85',
          },
          symbol: {
            type: 'string',
            description: 'Trading symbol',
            example: 'EURUSD',
          },
          timeframe: {
            type: 'string',
            enum: ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN'],
            description: 'Chart timeframe',
            example: 'H1',
          },
          timestamp: {
            type: 'string',
            format: 'date-time',
            description: 'Candle timestamp',
          },
          open: {
            type: 'number',
            description: 'Open price',
            example: 1.0875,
          },
          high: {
            type: 'number',
            description: 'High price',
            example: 1.0890,
          },
          low: {
            type: 'number',
            description: 'Low price',
            example: 1.0860,
          },
          close: {
            type: 'number',
            description: 'Close price',
            example: 1.0880,
          },
          volume: {
            type: 'integer',
            description: 'Volume',
            example: 1000,
          },
        },
      },
      Error: {
        type: 'object',
        properties: {
          status: {
            type: 'string',
            description: 'Error status',
            example: 'error',
          },
          message: {
            type: 'string',
            description: 'Error message',
            example: 'An error occurred',
          },
          errors: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                field: {
                  type: 'string',
                  description: 'Field with error',
                  example: 'email',
                },
                message: {
                  type: 'string',
                  description: 'Error message for field',
                  example: 'Email must be a valid email address',
                },
              },
            },
          },
        },
      },
    },
    responses: {
      UnauthorizedError: {
        description: 'Access token is missing or invalid',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error',
            },
          },
        },
      },
      ValidationError: {
        description: 'Validation failed',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error',
            },
          },
        },
      },
      NotFoundError: {
        description: 'Resource not found',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error',
            },
          },
        },
      },
      ServerError: {
        description: 'Internal server error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error',
            },
          },
        },
      },
    },
  },
  security: [
    {
      bearerAuth: [],
    },
  ],
};

// Options for the swagger docs
const options = {
  swaggerDefinition,
  // Paths to files containing OpenAPI definitions
  apis: [
    './src/swagger/routes/*.js',
    './src/mongodb-integration.js',
  ],
};

// Initialize swagger-jsdoc
const swaggerSpec = swaggerJsdoc(options);

module.exports = swaggerSpec;
