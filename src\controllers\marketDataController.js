import marketDataService from '../services/marketDataService.js';
import logger from '../utils/logger.js';
import { APIError } from '../middleware/errorHandler.js';

// Get real-time price for a symbol
export const getRealTimePrice = async (req, res, next) => {
  try {
    const { symbol } = req.params;
    logger.debug('Fetching real-time price for symbol:', { symbol });

    const data = await marketDataService.getRealTimePrice(symbol);
    res.json({
      status: 'success',
      data,
    });
  } catch (error) {
    next(error);
  }
};

// Get historical data for a symbol
export const getHistoricalData = async (req, res, next) => {
  try {
    const { symbol } = req.params;
    const { timeframe, limit } = req.query;
    logger.debug('Fetching historical data:', { symbol, timeframe, limit });

    const data = await marketDataService.getHistoricalData(symbol, timeframe, parseInt(limit));
    res.json({
      status: 'success',
      data,
    });
  } catch (error) {
    next(error);
  }
};

// Get market overview
export const getMarketOverview = async (req, res, next) => {
  try {
    logger.debug('Fetching market overview');
    const data = await marketDataService.getMarketOverview();
    res.json({
      status: 'success',
      data,
    });
  } catch (error) {
    next(error);
  }
};

// Search for symbols
export const searchSymbols = async (req, res, next) => {
  try {
    const { query } = req.query;
    if (!query) {
      throw new APIError(400, 'Search query is required');
    }

    logger.debug('Searching for symbols:', { query });
    // TODO: Implement symbol search functionality
    res.json({
      status: 'success',
      data: [],
    });
  } catch (error) {
    next(error);
  }
};

export default {
  getRealTimePrice,
  getHistoricalData,
  getMarketOverview,
  searchSymbols,
}; 