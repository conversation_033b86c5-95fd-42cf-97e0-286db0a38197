/**
 * Log Aggregation and Alerting Service for Trading Signals App
 * 
 * Provides real-time log analysis, pattern detection, and automated alerting
 * for critical failures in real-time data processing and system health.
 */

const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');
const { structuredLogger } = require('../utils/structuredLogger');

class LogAggregationService extends EventEmitter {
  constructor() {
    super();
    
    this.alertRules = new Map();
    this.logPatterns = new Map();
    this.alertHistory = [];
    this.metrics = {
      totalLogs: 0,
      errorLogs: 0,
      warningLogs: 0,
      alertsTriggered: 0,
      lastProcessedTime: null
    };
    
    this.config = {
      logDirectory: 'logs',
      alertCooldown: 300000, // 5 minutes
      maxAlertHistory: 1000,
      batchSize: 100,
      processingInterval: 5000 // 5 seconds
    };

    this.setupDefaultAlertRules();
    this.setupLogPatterns();
    this.startLogProcessing();
  }

  /**
   * Setup default alert rules for critical system events
   */
  setupDefaultAlertRules() {
    // AI Service Failures
    this.addAlertRule('ai_parsing_failures', {
      pattern: /AI response parsing failed/,
      threshold: 5,
      timeWindow: 300000, // 5 minutes
      severity: 'high',
      description: 'Multiple AI response parsing failures detected',
      action: 'investigate_ai_service'
    });

    // API Key Rotation Issues
    this.addAlertRule('api_key_exhaustion', {
      pattern: /key_rotation_failed/,
      threshold: 3,
      timeWindow: 600000, // 10 minutes
      severity: 'critical',
      description: 'API key rotation failures - service may be degraded',
      action: 'check_api_keys'
    });

    // Real-time Data Processing Failures
    this.addAlertRule('realtime_data_failures', {
      pattern: /Real-time data processing failure/,
      threshold: 10,
      timeWindow: 300000, // 5 minutes
      severity: 'critical',
      description: 'High volume of real-time data processing failures',
      action: 'check_data_sources'
    });

    // Database Performance Issues
    this.addAlertRule('slow_database_queries', {
      pattern: /Query execution time: (\d+)ms/,
      threshold: 5,
      timeWindow: 300000,
      severity: 'medium',
      description: 'Multiple slow database queries detected',
      action: 'optimize_database',
      extractValue: (match) => parseInt(match[1]),
      condition: (value) => value > 1000 // > 1 second
    });

    // Memory Usage Alerts
    this.addAlertRule('high_memory_usage', {
      pattern: /Memory usage: (\d+)MB/,
      threshold: 3,
      timeWindow: 600000,
      severity: 'high',
      description: 'High memory usage detected',
      action: 'check_memory_leaks',
      extractValue: (match) => parseInt(match[1]),
      condition: (value) => value > 1024 // > 1GB
    });

    // WebSocket Connection Issues
    this.addAlertRule('websocket_connection_failures', {
      pattern: /WebSocket connection failed/,
      threshold: 20,
      timeWindow: 300000,
      severity: 'medium',
      description: 'High number of WebSocket connection failures',
      action: 'check_websocket_service'
    });

    // Trading Signal Generation Failures
    this.addAlertRule('signal_generation_failures', {
      pattern: /signal_generation_failed/,
      threshold: 5,
      timeWindow: 600000,
      severity: 'high',
      description: 'Multiple trading signal generation failures',
      action: 'investigate_signal_service'
    });
  }

  /**
   * Setup log patterns for analysis
   */
  setupLogPatterns() {
    // Error patterns
    this.logPatterns.set('error_patterns', [
      /ECONNREFUSED/,
      /ETIMEDOUT/,
      /ENOTFOUND/,
      /Rate limit exceeded/,
      /Quota exceeded/,
      /Internal server error/,
      /Service unavailable/
    ]);

    // Performance patterns
    this.logPatterns.set('performance_patterns', [
      /Response time: (\d+)ms/,
      /Query execution time: (\d+)ms/,
      /Memory usage: (\d+)MB/,
      /CPU usage: (\d+)%/
    ]);

    // Security patterns
    this.logPatterns.set('security_patterns', [
      /Authentication failed/,
      /Invalid API key/,
      /Unauthorized access/,
      /Suspicious activity/
    ]);
  }

  /**
   * Add custom alert rule
   */
  addAlertRule(name, rule) {
    this.alertRules.set(name, {
      ...rule,
      occurrences: [],
      lastTriggered: null,
      totalTriggers: 0
    });
  }

  /**
   * Start continuous log processing
   */
  startLogProcessing() {
    setInterval(async () => {
      try {
        await this.processLogs();
      } catch (error) {
        structuredLogger.error('Log processing failed', {
          component: 'log-aggregation',
          error: error.message,
          stack: error.stack
        });
      }
    }, this.config.processingInterval);
  }

  /**
   * Process logs from all log files
   */
  async processLogs() {
    const logFiles = [
      'app.log',
      'error.log',
      'ai-operations.log',
      'trading-signals.log'
    ];

    for (const logFile of logFiles) {
      const filePath = path.join(this.config.logDirectory, logFile);
      
      try {
        await this.processLogFile(filePath);
      } catch (error) {
        if (error.code !== 'ENOENT') {
          structuredLogger.error('Failed to process log file', {
            component: 'log-aggregation',
            file: logFile,
            error: error.message
          });
        }
      }
    }

    this.metrics.lastProcessedTime = new Date();
    this.cleanupOldAlerts();
  }

  /**
   * Process individual log file
   */
  async processLogFile(filePath) {
    const content = await fs.readFile(filePath, 'utf8');
    const lines = content.split('\n').filter(line => line.trim());

    // Process only recent lines (last 1000 lines to avoid reprocessing)
    const recentLines = lines.slice(-1000);

    for (const line of recentLines) {
      try {
        const logEntry = JSON.parse(line);
        await this.processLogEntry(logEntry);
        this.metrics.totalLogs++;
      } catch (error) {
        // Skip non-JSON lines
        continue;
      }
    }
  }

  /**
   * Process individual log entry
   */
  async processLogEntry(logEntry) {
    const { level, message, timestamp, correlationId } = logEntry;

    // Update metrics
    if (level === 'ERROR') this.metrics.errorLogs++;
    if (level === 'WARN') this.metrics.warningLogs++;

    // Check against alert rules
    for (const [ruleName, rule] of this.alertRules) {
      await this.checkAlertRule(ruleName, rule, logEntry);
    }

    // Analyze patterns
    this.analyzeLogPatterns(logEntry);

    // Emit log event for real-time monitoring
    this.emit('logProcessed', {
      level,
      message,
      timestamp,
      correlationId,
      patterns: this.extractPatterns(message)
    });
  }

  /**
   * Check if log entry matches alert rule
   */
  async checkAlertRule(ruleName, rule, logEntry) {
    const { message, timestamp } = logEntry;
    const match = message.match(rule.pattern);

    if (!match) return;

    const now = new Date(timestamp);
    const windowStart = new Date(now.getTime() - rule.timeWindow);

    // Clean old occurrences outside time window
    rule.occurrences = rule.occurrences.filter(
      occurrence => occurrence.timestamp > windowStart
    );

    // Check condition if specified
    if (rule.condition && rule.extractValue) {
      const value = rule.extractValue(match);
      if (!rule.condition(value)) return;
    }

    // Add current occurrence
    rule.occurrences.push({
      timestamp: now,
      message,
      correlationId: logEntry.correlationId,
      match: match[0]
    });

    // Check if threshold is exceeded
    if (rule.occurrences.length >= rule.threshold) {
      await this.triggerAlert(ruleName, rule, logEntry);
    }
  }

  /**
   * Trigger alert when rule threshold is exceeded
   */
  async triggerAlert(ruleName, rule, logEntry) {
    const now = Date.now();
    
    // Check cooldown period
    if (rule.lastTriggered && (now - rule.lastTriggered) < this.config.alertCooldown) {
      return;
    }

    const alert = {
      id: `alert_${now}_${ruleName}`,
      ruleName,
      severity: rule.severity,
      description: rule.description,
      action: rule.action,
      timestamp: new Date(),
      occurrences: rule.occurrences.length,
      timeWindow: rule.timeWindow,
      correlationIds: rule.occurrences.map(o => o.correlationId),
      sampleMessages: rule.occurrences.slice(-3).map(o => o.message),
      context: {
        component: logEntry.component,
        environment: process.env.NODE_ENV,
        service: 'trading-signals-app'
      }
    };

    // Update rule state
    rule.lastTriggered = now;
    rule.totalTriggers++;

    // Store alert
    this.alertHistory.push(alert);
    this.metrics.alertsTriggered++;

    // Log the alert
    structuredLogger.error('Alert triggered', {
      component: 'log-aggregation',
      alert,
      correlationId: logEntry.correlationId
    });

    // Emit alert event
    this.emit('alertTriggered', alert);

    // Execute alert action
    await this.executeAlertAction(alert);

    // Clear occurrences after triggering
    rule.occurrences = [];
  }

  /**
   * Execute alert action
   */
  async executeAlertAction(alert) {
    switch (alert.action) {
      case 'investigate_ai_service':
        await this.investigateAIService(alert);
        break;
      case 'check_api_keys':
        await this.checkAPIKeys(alert);
        break;
      case 'check_data_sources':
        await this.checkDataSources(alert);
        break;
      case 'optimize_database':
        await this.optimizeDatabase(alert);
        break;
      case 'check_memory_leaks':
        await this.checkMemoryLeaks(alert);
        break;
      case 'check_websocket_service':
        await this.checkWebSocketService(alert);
        break;
      case 'investigate_signal_service':
        await this.investigateSignalService(alert);
        break;
      default:
        structuredLogger.warn('Unknown alert action', {
          component: 'log-aggregation',
          action: alert.action,
          alertId: alert.id
        });
    }
  }

  /**
   * Alert action implementations
   */
  async investigateAIService(alert) {
    structuredLogger.info('Investigating AI service issues', {
      component: 'log-aggregation',
      alertId: alert.id,
      action: 'ai_investigation_started'
    });

    // Check AI service health
    // Analyze recent AI response patterns
    // Generate diagnostic report
  }

  async checkAPIKeys(alert) {
    structuredLogger.info('Checking API key status', {
      component: 'log-aggregation',
      alertId: alert.id,
      action: 'api_key_check_started'
    });

    // Verify API key validity
    // Check rotation status
    // Alert if keys are exhausted
  }

  async checkDataSources(alert) {
    structuredLogger.info('Checking data source connectivity', {
      component: 'log-aggregation',
      alertId: alert.id,
      action: 'data_source_check_started'
    });

    // Test data source connections
    // Check API rate limits
    // Verify data quality
  }

  async optimizeDatabase(alert) {
    structuredLogger.info('Initiating database optimization', {
      component: 'log-aggregation',
      alertId: alert.id,
      action: 'database_optimization_started'
    });

    // Analyze slow queries
    // Check index usage
    // Suggest optimizations
  }

  async checkMemoryLeaks(alert) {
    structuredLogger.info('Checking for memory leaks', {
      component: 'log-aggregation',
      alertId: alert.id,
      action: 'memory_check_started'
    });

    // Analyze memory usage patterns
    // Check for memory leaks
    // Generate memory report
  }

  async checkWebSocketService(alert) {
    structuredLogger.info('Checking WebSocket service health', {
      component: 'log-aggregation',
      alertId: alert.id,
      action: 'websocket_check_started'
    });

    // Check WebSocket server status
    // Analyze connection patterns
    // Test connection stability
  }

  async investigateSignalService(alert) {
    structuredLogger.info('Investigating signal generation service', {
      component: 'log-aggregation',
      alertId: alert.id,
      action: 'signal_investigation_started'
    });

    // Check signal generation pipeline
    // Analyze failure patterns
    // Verify data inputs
  }

  /**
   * Analyze log patterns for insights
   */
  analyzeLogPatterns(logEntry) {
    const { message } = logEntry;

    for (const [patternType, patterns] of this.logPatterns) {
      for (const pattern of patterns) {
        const match = message.match(pattern);
        if (match) {
          this.emit('patternDetected', {
            type: patternType,
            pattern: pattern.source,
            match: match[0],
            logEntry
          });
        }
      }
    }
  }

  /**
   * Extract patterns from log message
   */
  extractPatterns(message) {
    const patterns = [];

    // Extract correlation IDs
    const correlationMatch = message.match(/correlationId['":\s]+([a-f0-9-]+)/i);
    if (correlationMatch) {
      patterns.push({ type: 'correlationId', value: correlationMatch[1] });
    }

    // Extract response times
    const responseTimeMatch = message.match(/(\d+)ms/);
    if (responseTimeMatch) {
      patterns.push({ type: 'responseTime', value: parseInt(responseTimeMatch[1]) });
    }

    // Extract error codes
    const errorCodeMatch = message.match(/error['":\s]+([A-Z_]+)/i);
    if (errorCodeMatch) {
      patterns.push({ type: 'errorCode', value: errorCodeMatch[1] });
    }

    return patterns;
  }

  /**
   * Clean up old alerts
   */
  cleanupOldAlerts() {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours
    this.alertHistory = this.alertHistory.filter(
      alert => alert.timestamp.getTime() > cutoff
    );

    // Keep only recent alerts
    if (this.alertHistory.length > this.config.maxAlertHistory) {
      this.alertHistory = this.alertHistory.slice(-this.config.maxAlertHistory);
    }
  }

  /**
   * Get aggregation metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      activeAlertRules: this.alertRules.size,
      recentAlerts: this.alertHistory.slice(-10),
      alertsByRule: Array.from(this.alertRules.entries()).map(([name, rule]) => ({
        name,
        totalTriggers: rule.totalTriggers,
        lastTriggered: rule.lastTriggered,
        currentOccurrences: rule.occurrences.length
      }))
    };
  }

  /**
   * Get alert history
   */
  getAlertHistory(limit = 50) {
    return this.alertHistory.slice(-limit).reverse();
  }

  /**
   * Get active alerts (recent alerts within cooldown period)
   */
  getActiveAlerts() {
    const cutoff = Date.now() - this.config.alertCooldown;
    return this.alertHistory.filter(
      alert => alert.timestamp.getTime() > cutoff
    );
  }
}

// Create singleton instance
const logAggregationService = new LogAggregationService();

module.exports = { logAggregationService, LogAggregationService };
