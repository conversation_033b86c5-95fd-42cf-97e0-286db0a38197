/**
 * Financial Data Client for Trading Signals App
 * 
 * This client provides a simple interface for fetching financial data
 * from the integrated API service.
 */

// Financial Data Client
const FinancialDataClient = (function() {
  // API base URL
  const API_BASE_URL = '/api/financial';
  
  // Cache for API responses
  const cache = new Map();
  const CACHE_TTL = 60 * 1000; // 1 minute in milliseconds
  
  /**
   * Fetch data from the API
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Query parameters
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async function fetchData(endpoint, params = {}, options = {}) {
    // Default options
    const defaultOptions = {
      useCache: true,
      cacheTTL: CACHE_TTL
    };
    
    const opts = { ...defaultOptions, ...options };
    
    // Build URL
    let url = `${API_BASE_URL}${endpoint}`;
    
    // Add query parameters
    if (Object.keys(params).length > 0) {
      const queryString = Object.entries(params)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&');
      
      url += `?${queryString}`;
    }
    
    // Generate cache key
    const cacheKey = url;
    
    // Check cache if enabled
    if (opts.useCache) {
      const cachedData = cache.get(cacheKey);
      if (cachedData && cachedData.expiry > Date.now()) {
        console.log(`Using cached data for ${url}`);
        return cachedData.data;
      }
    }
    
    try {
      console.log(`Fetching data from ${url}`);
      
      // Make request
      const response = await fetch(url);
      
      // Check if response is OK
      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
      
      // Parse response
      const data = await response.json();
      
      // Cache response if enabled
      if (opts.useCache) {
        cache.set(cacheKey, {
          data,
          expiry: Date.now() + opts.cacheTTL
        });
      }
      
      return data;
    } catch (error) {
      console.error(`Error fetching data from ${url}:`, error);
      throw error;
    }
  }
  
  /**
   * Fetch data from all sources
   * @param {Object} params - Query parameters
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Results from all sources
   */
  async function fetchAllData(params = {}, options = {}) {
    return fetchData('/all', params, options);
  }
  
  /**
   * Fetch data from a specific source
   * @param {string} source - API source
   * @param {Object} params - Query parameters
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Data from the specified source
   */
  async function fetchFromSource(source, params = {}, options = {}) {
    return fetchData(`/${source}`, params, options);
  }
  
  /**
   * Get market data for a symbol
   * @param {string} symbol - Trading symbol
   * @param {string} interval - Time interval
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Market data
   */
  async function getMarketData(symbol, interval = '5min', options = {}) {
    return fetchData(`/market/${symbol}`, { interval }, options);
  }
  
  /**
   * Get economic data
   * @param {string} series - FRED series ID
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Economic data
   */
  async function getEconomicData(series, options = {}) {
    return fetchData(`/economic/${series}`, {}, options);
  }
  
  /**
   * Get API status
   * @returns {Promise<Object>} API status
   */
  async function getStatus() {
    return fetchData('/status');
  }
  
  /**
   * Clear the cache
   */
  function clearCache() {
    cache.clear();
    console.log('Cache cleared');
  }
  
  // Return public API
  return {
    fetchAllData,
    fetchFromSource,
    getMarketData,
    getEconomicData,
    getStatus,
    clearCache
  };
})();

// Make the client available globally
window.FinancialDataClient = FinancialDataClient;

console.log('Financial Data Client loaded');
