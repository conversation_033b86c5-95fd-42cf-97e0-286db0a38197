import logger from '../utils/logger.js';

/**
 * Standardized Error Handler Service
 * Provides consistent error handling across all API calls and services
 */
export class StandardizedErrorHandler {
  constructor() {
    this.errorCategories = {
      NETWORK: 'network',
      AUTHENTICATION: 'authentication',
      AUTHORIZATION: 'authorization',
      VALIDATION: 'validation',
      RATE_LIMIT: 'rate_limit',
      SERVER_ERROR: 'server_error',
      TIMEOUT: 'timeout',
      NOT_FOUND: 'not_found',
      UNKNOWN: 'unknown'
    };

    this.retryableCategories = new Set([
      this.errorCategories.NETWORK,
      this.errorCategories.TIMEOUT,
      this.errorCategories.SERVER_ERROR,
      this.errorCategories.RATE_LIMIT
    ]);

    this.errorHandlers = new Map();
    this.globalErrorCallback = null;
    this.errorMetrics = {
      totalErrors: 0,
      errorsByCategory: {},
      errorsByEndpoint: {},
      lastError: null
    };
  }

  /**
   * Register error handler for specific category
   */
  registerErrorHandler(category, handler) {
    if (!this.errorCategories[category.toUpperCase()]) {
      throw new Error(`Invalid error category: ${category}`);
    }
    this.errorHandlers.set(category, handler);
  }

  /**
   * Set global error callback
   */
  setGlobalErrorCallback(callback) {
    this.globalErrorCallback = callback;
  }

  /**
   * Standardize error format
   */
  standardizeError(error, context = {}) {
    const standardError = {
      id: this.generateErrorId(),
      message: error.message || 'An unknown error occurred',
      category: this.categorizeError(error),
      status: error.response?.status || error.status || 0,
      statusText: error.response?.statusText || error.statusText || 'Unknown',
      timestamp: new Date().toISOString(),
      endpoint: context.endpoint || error.config?.url || 'unknown',
      method: context.method || error.config?.method?.toUpperCase() || 'unknown',
      retryable: this.isRetryableError(error),
      context: {
        ...context,
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Server',
        url: typeof window !== 'undefined' ? window.location.href : 'N/A'
      },
      originalError: {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    };

    // Add category-specific details
    this.addCategorySpecificDetails(standardError, error);

    // Update metrics
    this.updateErrorMetrics(standardError);

    return standardError;
  }

  /**
   * Categorize error based on type and status
   */
  categorizeError(error) {
    // Network errors
    if (!error.response) {
      if (error.code === 'ECONNABORTED') return this.errorCategories.TIMEOUT;
      if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        return this.errorCategories.NETWORK;
      }
      return this.errorCategories.NETWORK;
    }

    const status = error.response.status;

    // HTTP status-based categorization
    if (status === 401) return this.errorCategories.AUTHENTICATION;
    if (status === 403) return this.errorCategories.AUTHORIZATION;
    if (status === 404) return this.errorCategories.NOT_FOUND;
    if (status === 422 || status === 400) return this.errorCategories.VALIDATION;
    if (status === 429) return this.errorCategories.RATE_LIMIT;
    if (status >= 500) return this.errorCategories.SERVER_ERROR;

    return this.errorCategories.UNKNOWN;
  }

  /**
   * Check if error is retryable
   */
  isRetryableError(error) {
    const category = this.categorizeError(error);
    return this.retryableCategories.has(category);
  }

  /**
   * Add category-specific error details
   */
  addCategorySpecificDetails(standardError, originalError) {
    switch (standardError.category) {
      case this.errorCategories.VALIDATION:
        standardError.validationErrors = originalError.response?.data?.errors || [];
        standardError.fields = originalError.response?.data?.fields || [];
        break;

      case this.errorCategories.RATE_LIMIT:
        standardError.retryAfter = originalError.response?.headers['retry-after'] || 
                                  originalError.response?.headers['x-ratelimit-reset'] || 60;
        standardError.rateLimitInfo = {
          limit: originalError.response?.headers['x-ratelimit-limit'],
          remaining: originalError.response?.headers['x-ratelimit-remaining'],
          reset: originalError.response?.headers['x-ratelimit-reset']
        };
        break;

      case this.errorCategories.SERVER_ERROR:
        standardError.serverMessage = originalError.response?.data?.message || 'Internal server error';
        standardError.serverCode = originalError.response?.data?.code;
        break;

      case this.errorCategories.AUTHENTICATION:
        standardError.authType = originalError.response?.headers['www-authenticate'];
        break;

      case this.errorCategories.NETWORK:
        standardError.networkDetails = {
          code: originalError.code,
          errno: originalError.errno,
          syscall: originalError.syscall,
          hostname: originalError.hostname
        };
        break;
    }
  }

  /**
   * Handle error with appropriate strategy
   */
  async handleError(error, context = {}) {
    const standardError = this.standardizeError(error, context);

    // Log error
    logger.error('Standardized Error:', {
      id: standardError.id,
      category: standardError.category,
      message: standardError.message,
      endpoint: standardError.endpoint,
      context: standardError.context
    });

    // Call category-specific handler if registered
    const categoryHandler = this.errorHandlers.get(standardError.category);
    if (categoryHandler) {
      try {
        await categoryHandler(standardError);
      } catch (handlerError) {
        logger.error('Error handler failed:', handlerError);
      }
    }

    // Call global error callback
    if (this.globalErrorCallback) {
      try {
        await this.globalErrorCallback(standardError);
      } catch (callbackError) {
        logger.error('Global error callback failed:', callbackError);
      }
    }

    return standardError;
  }

  /**
   * Generate unique error ID
   */
  generateErrorId() {
    return `err-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
  }

  /**
   * Update error metrics
   */
  updateErrorMetrics(error) {
    this.errorMetrics.totalErrors++;
    this.errorMetrics.lastError = error;

    // Update category metrics
    if (!this.errorMetrics.errorsByCategory[error.category]) {
      this.errorMetrics.errorsByCategory[error.category] = 0;
    }
    this.errorMetrics.errorsByCategory[error.category]++;

    // Update endpoint metrics
    if (!this.errorMetrics.errorsByEndpoint[error.endpoint]) {
      this.errorMetrics.errorsByEndpoint[error.endpoint] = 0;
    }
    this.errorMetrics.errorsByEndpoint[error.endpoint]++;
  }

  /**
   * Get error metrics
   */
  getErrorMetrics() {
    return {
      ...this.errorMetrics,
      topErrorCategories: Object.entries(this.errorMetrics.errorsByCategory)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5),
      topErrorEndpoints: Object.entries(this.errorMetrics.errorsByEndpoint)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
    };
  }

  /**
   * Get user-friendly error message
   */
  getUserFriendlyMessage(error) {
    const messages = {
      [this.errorCategories.NETWORK]: 'Connection problem. Please check your internet connection and try again.',
      [this.errorCategories.AUTHENTICATION]: 'Authentication failed. Please log in again.',
      [this.errorCategories.AUTHORIZATION]: 'You don\'t have permission to access this resource.',
      [this.errorCategories.VALIDATION]: 'Please check your input and try again.',
      [this.errorCategories.RATE_LIMIT]: 'Too many requests. Please wait a moment and try again.',
      [this.errorCategories.SERVER_ERROR]: 'Server error. Please try again later.',
      [this.errorCategories.TIMEOUT]: 'Request timed out. Please try again.',
      [this.errorCategories.NOT_FOUND]: 'The requested resource was not found.',
      [this.errorCategories.UNKNOWN]: 'An unexpected error occurred. Please try again.'
    };

    return messages[error.category] || messages[this.errorCategories.UNKNOWN];
  }

  /**
   * Clear error metrics
   */
  clearMetrics() {
    this.errorMetrics = {
      totalErrors: 0,
      errorsByCategory: {},
      errorsByEndpoint: {},
      lastError: null
    };
  }
}

// Create singleton instance
export const standardizedErrorHandler = new StandardizedErrorHandler();
export default standardizedErrorHandler;
