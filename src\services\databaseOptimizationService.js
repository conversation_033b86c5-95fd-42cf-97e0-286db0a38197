/**
 * Enhanced Database Optimization Service
 *
 * Provides comprehensive database optimization including:
 * - Query performance monitoring with Redis caching integration
 * - Index management and optimization
 * - Connection pooling configuration with monitoring
 * - Performance metrics and alerting
 * - Cursor-based pagination for large datasets
 * - Real-time performance dashboards
 *
 * @version 2.0.0
 * <AUTHOR> Signals App Team
 */

import mongoose from 'mongoose';
import logger from '../utils/logger.js';
import { redisCacheService } from './redisCacheService.js';

/**
 * Enhanced Database Optimization Service Class
 */
export class DatabaseOptimizationService {
  constructor() {
    this.queryMetrics = new Map();
    this.slowQueryThreshold = parseInt(process.env.DB_SLOW_QUERY_THRESHOLD || '100', 10); // 100ms
    this.criticalQueryThreshold = parseInt(process.env.DB_CRITICAL_QUERY_THRESHOLD || '500', 10); // 500ms
    this.performanceAlerts = [];
    this.indexRecommendations = [];

    // Enhanced connection pool settings
    this.poolConfig = {
      minPoolSize: parseInt(process.env.DB_MIN_POOL_SIZE || '5', 10),
      maxPoolSize: parseInt(process.env.DB_MAX_POOL_SIZE || '20', 10),
      maxIdleTimeMS: 30000,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferMaxEntries: 0,
      retryWrites: true,
      w: 'majority'
    };

    // Performance monitoring configuration
    this.monitoringConfig = {
      enabled: process.env.DB_MONITORING_ENABLED !== 'false',
      metricsInterval: parseInt(process.env.DB_METRICS_INTERVAL || '30000', 10),
      alertsEnabled: process.env.DB_ALERTS_ENABLED !== 'false'
    };

    // Performance statistics
    this.performanceStats = {
      totalQueries: 0,
      slowQueries: 0,
      criticalQueries: 0,
      averageQueryTime: 0,
      totalQueryTime: 0,
      cacheHits: 0,
      cacheMisses: 0,
      connectionPoolStats: {
        activeConnections: 0,
        availableConnections: 0,
        totalConnections: 0
      }
    };

    // Collection-specific metrics
    this.collectionMetrics = new Map();

    // Initialize performance monitoring
    this.initializePerformanceMonitoring();
  }

  /**
   * Initialize enhanced performance monitoring for MongoDB queries
   */
  initializePerformanceMonitoring() {
    // Enable MongoDB profiling for slow queries
    this.enableMongoDBProfiling();

    // Set up Mongoose query middleware for performance tracking
    this.setupQueryMiddleware();

    // Set up connection pool monitoring
    this.setupConnectionPoolMonitoring();

    // Set up cache integration monitoring
    this.setupCacheIntegration();

    // Start periodic performance monitoring
    this.startPerformanceMonitoring();

    logger.info('Enhanced database performance monitoring initialized');
  }

  /**
   * Set up connection pool monitoring
   */
  setupConnectionPoolMonitoring() {
    if (mongoose.connection.readyState === 1) {
      // Monitor connection pool events
      mongoose.connection.on('connected', () => {
        logger.info('MongoDB connection established');
        this.updateConnectionPoolStats();
      });

      mongoose.connection.on('disconnected', () => {
        logger.warn('MongoDB connection lost');
        this.updateConnectionPoolStats();
      });

      mongoose.connection.on('error', (error) => {
        logger.error('MongoDB connection error:', error);
        this.updateConnectionPoolStats();
      });

      mongoose.connection.on('reconnected', () => {
        logger.info('MongoDB reconnected');
        this.updateConnectionPoolStats();
      });
    }
  }

  /**
   * Set up cache integration monitoring
   */
  setupCacheIntegration() {
    // Monitor cache performance periodically
    setInterval(() => {
      if (redisCacheService.isConnected) {
        const cacheMetrics = redisCacheService.getMetrics();
        this.performanceStats.cacheHits = cacheMetrics.hits;
        this.performanceStats.cacheMisses = cacheMetrics.misses;
      }
    }, 10000); // Every 10 seconds
  }

  /**
   * Start periodic performance monitoring
   */
  startPerformanceMonitoring() {
    if (!this.monitoringConfig.enabled) {
      logger.info('Database performance monitoring disabled');
      return;
    }

    setInterval(() => {
      this.updateConnectionPoolStats();
      this.checkPerformanceAlerts();
      this.generateIndexRecommendations();
    }, this.monitoringConfig.metricsInterval);

    logger.info(`Performance monitoring started with ${this.monitoringConfig.metricsInterval}ms interval`);
  }

  /**
   * Update connection pool statistics
   */
  updateConnectionPoolStats() {
    try {
      if (mongoose.connection.readyState === 1) {
        const db = mongoose.connection.db;
        const admin = db.admin();

        // Get server status for connection info
        admin.serverStatus().then(status => {
          if (status.connections) {
            this.performanceStats.connectionPoolStats = {
              activeConnections: status.connections.current || 0,
              availableConnections: status.connections.available || 0,
              totalConnections: status.connections.totalCreated || 0
            };
          }
        }).catch(error => {
          logger.debug('Could not get server status:', error.message);
        });
      }
    } catch (error) {
      logger.debug('Error updating connection pool stats:', error.message);
    }
  }

  /**
   * Check for performance alerts
   */
  checkPerformanceAlerts() {
    if (!this.monitoringConfig.alertsEnabled) return;

    const summary = this._generatePerformanceSummary();

    // Alert on high percentage of slow queries
    if (parseFloat(summary.slowQueryPercentage) > 10) {
      this._createAlert('high_slow_queries', {
        percentage: summary.slowQueryPercentage,
        threshold: '10%',
        severity: 'warning'
      });
    }

    // Alert on critical queries
    if (parseFloat(summary.criticalQueryPercentage) > 1) {
      this._createAlert('critical_queries_detected', {
        percentage: summary.criticalQueryPercentage,
        threshold: '1%',
        severity: 'critical'
      });
    }

    // Alert on high average query time
    const avgTime = parseFloat(summary.averageQueryTime);
    if (avgTime > this.slowQueryThreshold) {
      this._createAlert('high_average_query_time', {
        averageTime: summary.averageQueryTime,
        threshold: `${this.slowQueryThreshold}ms`,
        severity: 'warning'
      });
    }
  }

  /**
   * Create performance alert
   *
   * @private
   * @param {string} type - Alert type
   * @param {Object} data - Alert data
   */
  _createAlert(type, data) {
    const alert = {
      id: `${type}_${Date.now()}`,
      type,
      timestamp: new Date(),
      data,
      acknowledged: false
    };

    this.performanceAlerts.push(alert);

    // Keep only last 100 alerts
    if (this.performanceAlerts.length > 100) {
      this.performanceAlerts = this.performanceAlerts.slice(-100);
    }

    logger.warn(`Database performance alert: ${type}`, alert);
  }

  /**
   * Enable MongoDB profiling for slow operations
   */
  async enableMongoDBProfiling() {
    try {
      const db = mongoose.connection.db;

      // Set profiling level to capture slow operations (>100ms)
      await db.admin().command({
        profile: 2,
        slowms: this.slowQueryThreshold,
        sampleRate: 1.0
      });

      logger.info(`MongoDB profiling enabled for queries > ${this.slowQueryThreshold}ms`);
    } catch (error) {
      logger.error('Failed to enable MongoDB profiling:', error);
    }
  }

  /**
   * Set up Mongoose middleware to track query performance
   */
  setupQueryMiddleware() {
    // Pre-hook to capture query start time
    mongoose.plugin(function(schema) {
      schema.pre(/^find/, function() {
        this._startTime = Date.now();
      });

      schema.pre(/^aggregate/, function() {
        this._startTime = Date.now();
      });

      schema.pre(/^count/, function() {
        this._startTime = Date.now();
      });

      // Post-hook to calculate and log query execution time
      schema.post(/^find/, function(result) {
        if (this._startTime) {
          const duration = Date.now() - this._startTime;
          this._trackQueryPerformance(this, duration, 'find');
        }
      });

      schema.post(/^aggregate/, function(result) {
        if (this._startTime) {
          const duration = Date.now() - this._startTime;
          this._trackQueryPerformance(this, duration, 'aggregate');
        }
      });

      schema.post(/^count/, function(result) {
        if (this._startTime) {
          const duration = Date.now() - this._startTime;
          this._trackQueryPerformance(this, duration, 'count');
        }
      });
    });
  }

  /**
   * Track query performance metrics
   *
   * @private
   * @param {Object} query - Mongoose query object
   * @param {number} duration - Query execution time in ms
   * @param {string} operation - Type of operation
   */
  _trackQueryPerformance(query, duration, operation) {
    const collectionName = query.model?.collection?.name || 'unknown';
    const queryKey = `${collectionName}_${operation}`;

    // Update metrics
    if (!this.queryMetrics.has(queryKey)) {
      this.queryMetrics.set(queryKey, {
        collection: collectionName,
        operation,
        totalQueries: 0,
        totalDuration: 0,
        averageDuration: 0,
        slowQueries: 0,
        criticalQueries: 0,
        lastExecuted: null
      });
    }

    const metrics = this.queryMetrics.get(queryKey);
    metrics.totalQueries++;
    metrics.totalDuration += duration;
    metrics.averageDuration = metrics.totalDuration / metrics.totalQueries;
    metrics.lastExecuted = new Date();

    // Track slow and critical queries
    if (duration > this.slowQueryThreshold) {
      metrics.slowQueries++;

      if (duration > this.criticalQueryThreshold) {
        metrics.criticalQueries++;
        this._handleCriticalQuery(query, duration, operation);
      }

      logger.warn(`Slow query detected: ${queryKey} took ${duration}ms`, {
        collection: collectionName,
        operation,
        duration,
        query: query.getQuery ? query.getQuery() : 'unknown'
      });
    }
  }

  /**
   * Handle critical query performance issues
   *
   * @private
   * @param {Object} query - Mongoose query object
   * @param {number} duration - Query execution time in ms
   * @param {string} operation - Type of operation
   */
  _handleCriticalQuery(query, duration, operation) {
    const alert = {
      timestamp: new Date(),
      collection: query.model?.collection?.name || 'unknown',
      operation,
      duration,
      query: query.getQuery ? query.getQuery() : {},
      severity: 'critical'
    };

    this.performanceAlerts.push(alert);

    // Keep only last 100 alerts
    if (this.performanceAlerts.length > 100) {
      this.performanceAlerts = this.performanceAlerts.slice(-100);
    }

    logger.error(`Critical query performance: ${alert.collection}_${operation} took ${duration}ms`, alert);
  }

  /**
   * Create optimized indexes for all collections
   */
  async createOptimizedIndexes() {
    try {
      const db = mongoose.connection.db;

      logger.info('Creating optimized database indexes...');

      // Users collection indexes
      await this._createUsersIndexes(db);

      // Market data collection indexes
      await this._createMarketDataIndexes(db);

      // Trading signals collection indexes
      await this._createTradingSignalsIndexes(db);

      // Sentiment history collection indexes
      await this._createSentimentHistoryIndexes(db);

      // AI response history collection indexes
      await this._createAIResponseHistoryIndexes(db);

      logger.info('All optimized indexes created successfully');
    } catch (error) {
      logger.error('Error creating optimized indexes:', error);
      throw error;
    }
  }

  /**
   * Create indexes for users collection
   *
   * @private
   * @param {Object} db - MongoDB database instance
   */
  async _createUsersIndexes(db) {
    const collection = db.collection('users');

    // Existing indexes
    await collection.createIndex({ email: 1 }, { unique: true });
    await collection.createIndex({ username: 1 }, { unique: true, sparse: true });

    // New optimized indexes
    await collection.createIndex({ role: 1 });
    await collection.createIndex({ createdAt: -1 });
    await collection.createIndex({ lastLogin: -1 });
    await collection.createIndex({ 'preferences.theme': 1 }, { sparse: true });

    // Compound indexes for common queries
    await collection.createIndex({ role: 1, createdAt: -1 });
    await collection.createIndex({ email: 1, role: 1 });

    logger.info('Users collection indexes created');
  }

  /**
   * Create indexes for market data collection
   *
   * @private
   * @param {Object} db - MongoDB database instance
   */
  async _createMarketDataIndexes(db) {
    const collection = db.collection('marketdata');

    // Primary compound index for time-series queries
    await collection.createIndex({ symbol: 1, timestamp: -1 }, { unique: true });

    // Additional optimized indexes
    await collection.createIndex({ symbol: 1, timeframe: 1, timestamp: -1 });
    await collection.createIndex({ timestamp: -1 });
    await collection.createIndex({ timeframe: 1 });
    await collection.createIndex({ createdAt: -1 });

    // Compound indexes for common query patterns
    await collection.createIndex({ symbol: 1, timeframe: 1 });
    await collection.createIndex({ symbol: 1, timestamp: -1, timeframe: 1 });

    logger.info('Market data collection indexes created');
  }

  /**
   * Create indexes for trading signals collection
   *
   * @private
   * @param {Object} db - MongoDB database instance
   */
  async _createTradingSignalsIndexes(db) {
    const collection = db.collection('signals');

    // Basic indexes
    await collection.createIndex({ symbol: 1 });
    await collection.createIndex({ createdAt: -1 });
    await collection.createIndex({ type: 1 });
    await collection.createIndex({ strength: -1 });
    await collection.createIndex({ timeframe: 1 });

    // User-related indexes
    await collection.createIndex({ user: 1 });
    await collection.createIndex({ user: 1, createdAt: -1 });

    // Compound indexes for complex queries
    await collection.createIndex({ symbol: 1, type: 1, createdAt: -1 });
    await collection.createIndex({ user: 1, symbol: 1, createdAt: -1 });
    await collection.createIndex({ symbol: 1, timeframe: 1, createdAt: -1 });

    logger.info('Trading signals collection indexes created');
  }

  /**
   * Create indexes for sentiment history collection
   *
   * @private
   * @param {Object} db - MongoDB database instance
   */
  async _createSentimentHistoryIndexes(db) {
    const collection = db.collection('sentimenthistories');

    // Basic indexes
    await collection.createIndex({ symbol: 1 });
    await collection.createIndex({ date: -1 });
    await collection.createIndex({ sentiment: 1 });

    // Compound indexes
    await collection.createIndex({ symbol: 1, date: -1 });
    await collection.createIndex({ symbol: 1, sentiment: 1, date: -1 });

    logger.info('Sentiment history collection indexes created');
  }

  /**
   * Create indexes for AI response history collection
   *
   * @private
   * @param {Object} db - MongoDB database instance
   */
  async _createAIResponseHistoryIndexes(db) {
    const collection = db.collection('airesponsehistories');

    // Basic indexes
    await collection.createIndex({ symbol: 1 });
    await collection.createIndex({ analysisType: 1 });
    await collection.createIndex({ createdAt: -1 });
    await collection.createIndex({ model: 1 });

    // Compound indexes for AI analysis queries
    await collection.createIndex({ symbol: 1, analysisType: 1, createdAt: -1 });
    await collection.createIndex({ model: 1, analysisType: 1, createdAt: -1 });
    await collection.createIndex({ symbol: 1, model: 1, createdAt: -1 });

    logger.info('AI response history collection indexes created');
  }

  /**
   * Get query performance metrics
   *
   * @returns {Object} Performance metrics summary
   */
  getPerformanceMetrics() {
    const metrics = {};

    for (const [key, data] of this.queryMetrics.entries()) {
      metrics[key] = {
        ...data,
        slowQueryPercentage: data.totalQueries > 0 ?
          (data.slowQueries / data.totalQueries * 100).toFixed(2) + '%' : '0%',
        criticalQueryPercentage: data.totalQueries > 0 ?
          (data.criticalQueries / data.totalQueries * 100).toFixed(2) + '%' : '0%'
      };
    }

    return {
      collections: metrics,
      alerts: this.performanceAlerts.slice(-10), // Last 10 alerts
      summary: this._generatePerformanceSummary()
    };
  }

  /**
   * Generate performance summary
   *
   * @private
   * @returns {Object} Performance summary
   */
  _generatePerformanceSummary() {
    let totalQueries = 0;
    let totalSlowQueries = 0;
    let totalCriticalQueries = 0;
    let totalDuration = 0;

    for (const [, data] of this.queryMetrics.entries()) {
      totalQueries += data.totalQueries;
      totalSlowQueries += data.slowQueries;
      totalCriticalQueries += data.criticalQueries;
      totalDuration += data.totalDuration;
    }

    return {
      totalQueries,
      totalSlowQueries,
      totalCriticalQueries,
      averageQueryTime: totalQueries > 0 ? (totalDuration / totalQueries).toFixed(2) + 'ms' : '0ms',
      slowQueryPercentage: totalQueries > 0 ? (totalSlowQueries / totalQueries * 100).toFixed(2) + '%' : '0%',
      criticalQueryPercentage: totalQueries > 0 ? (totalCriticalQueries / totalQueries * 100).toFixed(2) + '%' : '0%',
      healthStatus: totalCriticalQueries === 0 ? 'healthy' : 'needs_attention'
    };
  }

  /**
   * Generate index recommendations based on query patterns
   */
  generateIndexRecommendations() {
    const recommendations = [];

    for (const [key, metrics] of this.queryMetrics.entries()) {
      const [collection, operation] = key.split('_');

      // Recommend indexes for slow queries
      if (metrics.slowQueries > 0 && metrics.averageDuration > this.slowQueryThreshold) {
        recommendations.push({
          collection,
          operation,
          recommendation: `Consider adding compound index for ${collection} collection`,
          reason: `${metrics.slowQueries} slow queries detected with average duration ${metrics.averageDuration.toFixed(2)}ms`,
          priority: metrics.criticalQueries > 0 ? 'high' : 'medium',
          timestamp: new Date()
        });
      }
    }

    this.indexRecommendations = recommendations;
    return recommendations;
  }

  /**
   * Implement cursor-based pagination for large datasets
   *
   * @param {Object} model - Mongoose model
   * @param {Object} query - Query conditions
   * @param {Object} options - Pagination options
   * @returns {Promise<Object>} Paginated results
   */
  async paginateWithCursor(model, query = {}, options = {}) {
    const {
      limit = 50,
      cursor = null,
      sortField = '_id',
      sortOrder = 1,
      select = null
    } = options;

    try {
      // Build the query with cursor
      let mongoQuery = { ...query };

      if (cursor) {
        const cursorCondition = sortOrder === 1
          ? { [sortField]: { $gt: cursor } }
          : { [sortField]: { $lt: cursor } };

        mongoQuery = { ...mongoQuery, ...cursorCondition };
      }

      // Execute query with performance tracking
      const startTime = Date.now();

      let queryBuilder = model.find(mongoQuery)
        .sort({ [sortField]: sortOrder })
        .limit(limit + 1); // Get one extra to check if there are more results

      if (select) {
        queryBuilder = queryBuilder.select(select);
      }

      const results = await queryBuilder.exec();
      const duration = Date.now() - startTime;

      // Track pagination performance
      this._trackPaginationPerformance(model.collection.name, duration, results.length);

      // Check if there are more results
      const hasMore = results.length > limit;
      if (hasMore) {
        results.pop(); // Remove the extra result
      }

      // Get next cursor
      const nextCursor = results.length > 0 ? results[results.length - 1][sortField] : null;

      return {
        data: results,
        pagination: {
          hasMore,
          nextCursor,
          limit,
          count: results.length
        },
        performance: {
          queryTime: duration,
          cached: false
        }
      };
    } catch (error) {
      logger.error('Error in cursor-based pagination:', error);
      throw error;
    }
  }

  /**
   * Track pagination performance
   *
   * @private
   * @param {string} collection - Collection name
   * @param {number} duration - Query duration
   * @param {number} resultCount - Number of results
   */
  _trackPaginationPerformance(collection, duration, resultCount) {
    const key = `${collection}_pagination`;

    if (!this.collectionMetrics.has(key)) {
      this.collectionMetrics.set(key, {
        totalQueries: 0,
        totalDuration: 0,
        averageDuration: 0,
        totalResults: 0,
        averageResults: 0
      });
    }

    const metrics = this.collectionMetrics.get(key);
    metrics.totalQueries++;
    metrics.totalDuration += duration;
    metrics.averageDuration = metrics.totalDuration / metrics.totalQueries;
    metrics.totalResults += resultCount;
    metrics.averageResults = metrics.totalResults / metrics.totalQueries;
  }

  /**
   * Get comprehensive performance dashboard data
   *
   * @returns {Object} Dashboard data
   */
  getPerformanceDashboard() {
    const summary = this._generatePerformanceSummary();
    const cacheMetrics = redisCacheService.getMetrics();

    return {
      summary,
      queryMetrics: this.getPerformanceMetrics(),
      cacheMetrics,
      connectionPool: this.performanceStats.connectionPoolStats,
      alerts: this.performanceAlerts.slice(-10),
      recommendations: this.indexRecommendations.slice(-10),
      collectionMetrics: Object.fromEntries(this.collectionMetrics),
      systemHealth: this._getSystemHealth(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get system health status
   *
   * @private
   * @returns {Object} System health information
   */
  _getSystemHealth() {
    const summary = this._generatePerformanceSummary();
    const cacheMetrics = redisCacheService.getMetrics();

    let healthScore = 100;
    const issues = [];

    // Deduct points for performance issues
    if (parseFloat(summary.criticalQueryPercentage) > 1) {
      healthScore -= 30;
      issues.push('Critical queries detected');
    }

    if (parseFloat(summary.slowQueryPercentage) > 10) {
      healthScore -= 20;
      issues.push('High percentage of slow queries');
    }

    if (!redisCacheService.isConnected) {
      healthScore -= 25;
      issues.push('Redis cache disconnected');
    }

    if (parseFloat(cacheMetrics.hitRate) < 50) {
      healthScore -= 15;
      issues.push('Low cache hit rate');
    }

    return {
      score: Math.max(0, healthScore),
      status: healthScore >= 80 ? 'healthy' : healthScore >= 60 ? 'warning' : 'critical',
      issues,
      recommendations: this.indexRecommendations.length > 0 ?
        ['Review index recommendations'] : []
    };
  }

  /**
   * Acknowledge performance alert
   *
   * @param {string} alertId - Alert ID to acknowledge
   * @returns {boolean} Success status
   */
  acknowledgeAlert(alertId) {
    const alert = this.performanceAlerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      alert.acknowledgedAt = new Date();
      logger.info(`Performance alert acknowledged: ${alertId}`);
      return true;
    }
    return false;
  }

  /**
   * Clear performance metrics
   */
  clearMetrics() {
    this.queryMetrics.clear();
    this.performanceAlerts = [];
    this.indexRecommendations = [];
    this.collectionMetrics.clear();

    // Reset performance stats
    this.performanceStats = {
      totalQueries: 0,
      slowQueries: 0,
      criticalQueries: 0,
      averageQueryTime: 0,
      totalQueryTime: 0,
      cacheHits: 0,
      cacheMisses: 0,
      connectionPoolStats: {
        activeConnections: 0,
        availableConnections: 0,
        totalConnections: 0
      }
    };

    logger.info('Database performance metrics cleared');
  }

  /**
   * Export performance data for analysis
   *
   * @param {string} format - Export format ('json' or 'csv')
   * @returns {string} Exported data
   */
  exportPerformanceData(format = 'json') {
    const data = this.getPerformanceDashboard();

    if (format === 'csv') {
      // Convert to CSV format for spreadsheet analysis
      const csvRows = [];
      csvRows.push('Collection,Operation,Total Queries,Slow Queries,Critical Queries,Average Duration');

      for (const [key, metrics] of this.queryMetrics.entries()) {
        const [collection, operation] = key.split('_');
        csvRows.push([
          collection,
          operation,
          metrics.totalQueries,
          metrics.slowQueries,
          metrics.criticalQueries,
          metrics.averageDuration.toFixed(2)
        ].join(','));
      }

      return csvRows.join('\n');
    }

    return JSON.stringify(data, null, 2);
  }
}

// Create and export singleton instance
export const databaseOptimizationService = new DatabaseOptimizationService();
export default databaseOptimizationService;
