/**
 * Enhanced Notification System
 * Provides rich notifications with different types and auto-dismiss functionality
 */

class EnhancedNotificationSystem {
  constructor() {
    this.notifications = [];
    this.container = null;
    this.defaultDuration = 5000; // 5 seconds
    this.maxNotifications = 5;
    
    this.init();
  }

  /**
   * Initialize the notification system
   */
  init() {
    // Create notification container
    this.createContainer();
    
    // Setup offline indicator
    this.setupOfflineIndicator();
    
    // Make globally available
    window.showNotification = this.show.bind(this);
    window.enhancedNotifications = this;
  }

  /**
   * Create notification container
   */
  createContainer() {
    this.container = document.createElement('div');
    this.container.id = 'enhanced-notifications-container';
    this.container.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      max-width: 400px;
      pointer-events: none;
    `;
    
    document.body.appendChild(this.container);
  }

  /**
   * Setup offline indicator
   */
  setupOfflineIndicator() {
    const offlineIndicator = document.createElement('div');
    offlineIndicator.id = 'offline-indicator';
    offlineIndicator.className = 'offline-indicator';
    offlineIndicator.innerHTML = `
      <i class="fas fa-wifi-slash me-2"></i>
      You are offline
    `;
    
    document.body.appendChild(offlineIndicator);

    // Show/hide based on network status
    const updateOfflineStatus = () => {
      if (navigator.onLine) {
        offlineIndicator.classList.remove('show');
      } else {
        offlineIndicator.classList.add('show');
      }
    };

    window.addEventListener('online', updateOfflineStatus);
    window.addEventListener('offline', updateOfflineStatus);
    
    // Initial check
    updateOfflineStatus();
  }

  /**
   * Show notification
   */
  show(message, type = 'info', options = {}) {
    const notification = this.createNotification(message, type, options);
    
    // Add to container
    this.container.appendChild(notification);
    this.notifications.push(notification);
    
    // Trigger animation
    setTimeout(() => {
      notification.classList.add('show');
    }, 10);
    
    // Auto dismiss
    const duration = options.duration !== undefined ? options.duration : this.defaultDuration;
    if (duration > 0) {
      setTimeout(() => {
        this.dismiss(notification);
      }, duration);
    }
    
    // Limit number of notifications
    this.limitNotifications();
    
    return notification;
  }

  /**
   * Create notification element
   */
  createNotification(message, type, options) {
    const notification = document.createElement('div');
    notification.className = `enhanced-notification ${type}`;
    notification.style.pointerEvents = 'auto';
    
    const icon = this.getIcon(type);
    const title = options.title || this.getTitle(type);
    
    notification.innerHTML = `
      <div class="d-flex align-items-start">
        <div class="me-3">
          <i class="${icon}"></i>
        </div>
        <div class="flex-grow-1">
          ${title ? `<div class="fw-bold mb-1">${title}</div>` : ''}
          <div>${message}</div>
          ${options.actions ? this.createActions(options.actions) : ''}
        </div>
        <button type="button" class="btn-close btn-close-white ms-2" aria-label="Close"></button>
      </div>
    `;
    
    // Add close functionality
    const closeBtn = notification.querySelector('.btn-close');
    closeBtn.addEventListener('click', () => {
      this.dismiss(notification);
    });
    
    // Add action handlers
    if (options.actions) {
      options.actions.forEach((action, index) => {
        const actionBtn = notification.querySelector(`[data-action="${index}"]`);
        if (actionBtn && action.handler) {
          actionBtn.addEventListener('click', () => {
            action.handler();
            if (action.dismissOnClick !== false) {
              this.dismiss(notification);
            }
          });
        }
      });
    }
    
    return notification;
  }

  /**
   * Create action buttons
   */
  createActions(actions) {
    return `
      <div class="mt-2">
        ${actions.map((action, index) => `
          <button type="button" 
                  class="btn btn-sm ${action.class || 'btn-outline-light'} me-2" 
                  data-action="${index}">
            ${action.text}
          </button>
        `).join('')}
      </div>
    `;
  }

  /**
   * Get icon for notification type
   */
  getIcon(type) {
    const icons = {
      success: 'fas fa-check-circle',
      error: 'fas fa-exclamation-circle',
      warning: 'fas fa-exclamation-triangle',
      info: 'fas fa-info-circle'
    };
    
    return icons[type] || icons.info;
  }

  /**
   * Get title for notification type
   */
  getTitle(type) {
    const titles = {
      success: 'Success',
      error: 'Error',
      warning: 'Warning',
      info: 'Information'
    };
    
    return titles[type] || '';
  }

  /**
   * Dismiss notification
   */
  dismiss(notification) {
    notification.classList.remove('show');
    
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
      
      const index = this.notifications.indexOf(notification);
      if (index > -1) {
        this.notifications.splice(index, 1);
      }
    }, 300);
  }

  /**
   * Limit number of notifications
   */
  limitNotifications() {
    while (this.notifications.length > this.maxNotifications) {
      const oldest = this.notifications[0];
      this.dismiss(oldest);
    }
  }

  /**
   * Clear all notifications
   */
  clearAll() {
    this.notifications.forEach(notification => {
      this.dismiss(notification);
    });
  }

  /**
   * Show success notification
   */
  success(message, options = {}) {
    return this.show(message, 'success', options);
  }

  /**
   * Show error notification
   */
  error(message, options = {}) {
    return this.show(message, 'error', options);
  }

  /**
   * Show warning notification
   */
  warning(message, options = {}) {
    return this.show(message, 'warning', options);
  }

  /**
   * Show info notification
   */
  info(message, options = {}) {
    return this.show(message, 'info', options);
  }

  /**
   * Show loading notification
   */
  loading(message, options = {}) {
    const loadingOptions = {
      ...options,
      duration: 0, // Don't auto-dismiss
      title: 'Loading...'
    };
    
    const notification = this.show(message, 'info', loadingOptions);
    
    // Add loading spinner
    const icon = notification.querySelector('i');
    if (icon) {
      icon.className = 'fas fa-spinner fa-spin';
    }
    
    return notification;
  }

  /**
   * Show API error notification with retry option
   */
  apiError(message, retryHandler, options = {}) {
    const apiErrorOptions = {
      ...options,
      title: 'API Error',
      duration: 0, // Don't auto-dismiss
      actions: [
        {
          text: 'Retry',
          class: 'btn-outline-light',
          handler: retryHandler
        },
        {
          text: 'Dismiss',
          class: 'btn-outline-secondary',
          handler: () => {} // Will auto-dismiss
        }
      ]
    };
    
    return this.show(message, 'error', apiErrorOptions);
  }

  /**
   * Show network status notification
   */
  networkStatus(isOnline) {
    if (isOnline) {
      this.success('Connection restored', {
        duration: 3000
      });
    } else {
      this.warning('You are offline. Some features may be limited.', {
        duration: 0 // Don't auto-dismiss
      });
    }
  }

  /**
   * Show enhanced service status
   */
  serviceStatus(serviceName, isHealthy, details = '') {
    const message = `${serviceName} is ${isHealthy ? 'healthy' : 'experiencing issues'}${details ? ': ' + details : ''}`;
    const type = isHealthy ? 'success' : 'warning';
    
    return this.show(message, type, {
      title: 'Service Status',
      duration: isHealthy ? 3000 : 8000
    });
  }

  /**
   * Show AI analysis notification
   */
  aiAnalysis(result, confidence) {
    const confidenceText = confidence ? ` (${confidence}% confidence)` : '';
    const message = `AI analysis complete: ${result}${confidenceText}`;
    
    return this.show(message, 'success', {
      title: 'AI Analysis',
      duration: 5000
    });
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.enhancedNotificationSystem = new EnhancedNotificationSystem();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedNotificationSystem;
}
