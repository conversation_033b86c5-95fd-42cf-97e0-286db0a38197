/**
 * User Model - TypeScript Implementation
 * 
 * Mongoose schema for user management aligned with TypeScript interfaces.
 * This replaces the JavaScript version to ensure type safety and consistency.
 * 
 * @version 2.0.0
 */

import mongoose, { Schema, Document } from 'mongoose';
import bcrypt from 'bcrypt';
import {
  User as IUser,
  UserRole,
  UserPreferences,
  UserMetadata,
  SubscriptionInfo,
  NotificationPreferences,
  TradingPreferences,
  DisplayPreferences
} from '../types';

// ============================================================================
// INTERFACE EXTENSIONS FOR MONGOOSE
// ============================================================================

/**
 * User document interface extending Mongoose Document
 */
export interface UserDocument extends IUser, Document {
  // Mongoose-specific methods
  comparePassword(candidatePassword: string): Promise<boolean>;
  generateReferralCode(): string;
  updateLastLogin(): Promise<void>;
  incrementFailedLogins(): Promise<void>;
  resetFailedLogins(): Promise<void>;
  lockAccount(duration?: number): Promise<void>;
  unlockAccount(): Promise<void>;
  isAccountLocked(): boolean;
  
  // Virtual properties
  fullName: string;
  isActive: boolean;
  displayName: string;
}

// ============================================================================
// SUBDOCUMENT SCHEMAS
// ============================================================================

const NotificationPreferencesSchema = new Schema<NotificationPreferences>({
  email: { type: Boolean, default: true },
  push: { type: Boolean, default: true },
  sms: { type: Boolean, default: false },
  signalAlerts: { type: Boolean, default: true },
  marketUpdates: { type: Boolean, default: true },
  systemNotifications: { type: Boolean, default: true }
}, { _id: false });

const TradingPreferencesSchema = new Schema<TradingPreferences>({
  defaultTimeframe: { 
    type: String, 
    enum: ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN'],
    default: 'H1'
  },
  riskTolerance: { 
    type: String, 
    enum: ['LOW', 'MEDIUM', 'HIGH'],
    default: 'MEDIUM'
  },
  preferredMarkets: [{
    type: String,
    enum: ['stocks', 'forex', 'crypto', 'commodities', 'economics']
  }],
  autoTrading: { type: Boolean, default: false },
  maxPositionSize: { type: Number, default: 1000, min: 0 }
}, { _id: false });

const DisplayPreferencesSchema = new Schema<DisplayPreferences>({
  chartType: { 
    type: String, 
    enum: ['candlestick', 'line', 'bar'],
    default: 'candlestick'
  },
  indicators: [{ type: String }],
  layout: { 
    type: String, 
    enum: ['compact', 'standard', 'expanded'],
    default: 'standard'
  },
  currency: { type: String, default: 'USD', maxlength: 3 }
}, { _id: false });

const UserPreferencesSchema = new Schema<UserPreferences>({
  theme: { 
    type: String, 
    enum: ['light', 'dark', 'auto'],
    default: 'light'
  },
  notifications: { 
    type: NotificationPreferencesSchema,
    default: () => ({})
  },
  trading: { 
    type: TradingPreferencesSchema,
    default: () => ({})
  },
  display: { 
    type: DisplayPreferencesSchema,
    default: () => ({})
  }
}, { _id: false });

const SubscriptionLimitsSchema = new Schema({
  signalsPerDay: { type: Number, default: 10 },
  aiAnalysisPerDay: { type: Number, default: 5 },
  historicalDataMonths: { type: Number, default: 1 },
  realTimeUpdates: { type: Boolean, default: false },
  advancedIndicators: { type: Boolean, default: false }
}, { _id: false });

const SubscriptionInfoSchema = new Schema<SubscriptionInfo>({
  plan: { 
    type: String, 
    enum: ['free', 'basic', 'premium', 'enterprise'],
    default: 'free'
  },
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'cancelled', 'expired'],
    default: 'active'
  },
  startDate: { type: String, default: () => new Date().toISOString() },
  endDate: { type: String },
  features: [{ type: String }],
  limits: { 
    type: SubscriptionLimitsSchema,
    default: () => ({})
  }
}, { _id: false });

const UserMetadataSchema = new Schema<UserMetadata>({
  lastLogin: { type: String },
  loginCount: { type: Number, default: 0 },
  preferences: { type: UserPreferencesSchema },
  subscription: { type: SubscriptionInfoSchema }
}, { _id: false });

// ============================================================================
// MAIN USER SCHEMA
// ============================================================================

const UserSchema = new Schema<UserDocument>({
  // Core identification
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    index: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  
  // Basic information
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters']
  },
  
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot exceed 50 characters']
  },
  
  displayName: {
    type: String,
    trim: true,
    maxlength: [100, 'Display name cannot exceed 100 characters']
  },
  
  avatar: {
    type: String,
    validate: {
      validator: function(v: string) {
        return !v || /^https?:\/\/.+/.test(v) || /^data:image\/.+/.test(v);
      },
      message: 'Avatar must be a valid URL or base64 data URI'
    }
  },
  
  // Authentication
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters'],
    select: false // Don't include in queries by default
  },
  
  emailVerified: {
    type: Boolean,
    default: false,
    index: true
  },
  
  emailVerificationToken: {
    type: String,
    select: false
  },
  
  // Authorization
  role: {
    type: String,
    enum: Object.values(UserRole),
    default: UserRole.USER,
    index: true
  },
  
  permissions: [{
    type: String,
    trim: true
  }],
  
  // Account status
  active: {
    type: Boolean,
    default: true,
    index: true
  },
  
  suspended: {
    type: Boolean,
    default: false,
    index: true
  },
  
  suspensionReason: {
    type: String,
    maxlength: [500, 'Suspension reason cannot exceed 500 characters']
  },
  
  // Security
  twoFactorEnabled: {
    type: Boolean,
    default: false
  },
  
  twoFactorSecret: {
    type: String,
    select: false
  },
  
  lastPasswordChange: {
    type: String // ISO timestamp
  },
  
  failedLoginAttempts: {
    type: Number,
    default: 0
  },
  
  lockedUntil: {
    type: String // ISO timestamp
  },
  
  // Social features
  referralCode: {
    type: String,
    unique: true,
    sparse: true,
    uppercase: true,
    index: true
  },
  
  referredBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  
  referralCount: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // Preferences and settings
  preferences: {
    type: UserPreferencesSchema,
    default: () => ({})
  },
  
  // Subscription and billing
  subscription: {
    type: SubscriptionInfoSchema,
    default: () => ({})
  },
  
  // Metadata
  metadata: {
    type: UserMetadataSchema,
    default: () => ({})
  }
}, {
  timestamps: true, // Automatically adds createdAt and updatedAt
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      // Convert timestamps to ISO strings
      ret.createdAt = ret.createdAt?.toISOString();
      ret.updatedAt = ret.updatedAt?.toISOString();
      
      // Remove sensitive fields
      delete ret.password;
      delete ret.twoFactorSecret;
      delete ret.emailVerificationToken;
      delete ret.__v;
      
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// ============================================================================
// INDEXES
// ============================================================================

UserSchema.index({ email: 1 });
UserSchema.index({ role: 1, active: 1 });
UserSchema.index({ referralCode: 1 });
UserSchema.index({ createdAt: -1 });
UserSchema.index({ 'subscription.plan': 1, 'subscription.status': 1 });

// ============================================================================
// VIRTUAL PROPERTIES
// ============================================================================

UserSchema.virtual('fullName').get(function(this: UserDocument) {
  return `${this.firstName} ${this.lastName}`.trim();
});

UserSchema.virtual('isActive').get(function(this: UserDocument) {
  return this.active && !this.suspended && (!this.lockedUntil || new Date(this.lockedUntil) < new Date());
});

UserSchema.virtual('id').get(function(this: UserDocument) {
  return this._id.toHexString();
});

// ============================================================================
// MIDDLEWARE
// ============================================================================

// Hash password before saving
UserSchema.pre('save', async function(this: UserDocument, next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    this.lastPasswordChange = new Date().toISOString();
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Update displayName if not set
UserSchema.pre('save', function(this: UserDocument, next) {
  if (!this.displayName) {
    this.displayName = this.fullName;
  }
  next();
});

// Generate referral code if not set
UserSchema.pre('save', function(this: UserDocument, next) {
  if (!this.referralCode && this.isNew) {
    this.referralCode = this.generateReferralCode();
  }
  next();
});

// ============================================================================
// INSTANCE METHODS
// ============================================================================

UserSchema.methods.comparePassword = async function(this: UserDocument, candidatePassword: string): Promise<boolean> {
  if (!this.password) return false;
  return bcrypt.compare(candidatePassword, this.password);
};

UserSchema.methods.generateReferralCode = function(this: UserDocument): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `${this.id.substring(0, 4)}${timestamp}${random}`.toUpperCase();
};

UserSchema.methods.updateLastLogin = async function(this: UserDocument): Promise<void> {
  this.metadata.lastLogin = new Date().toISOString();
  this.metadata.loginCount = (this.metadata.loginCount || 0) + 1;
  this.failedLoginAttempts = 0;
  await this.save();
};

UserSchema.methods.incrementFailedLogins = async function(this: UserDocument): Promise<void> {
  this.failedLoginAttempts += 1;
  
  // Lock account after 5 failed attempts
  if (this.failedLoginAttempts >= 5) {
    await this.lockAccount(30 * 60 * 1000); // 30 minutes
  } else {
    await this.save();
  }
};

UserSchema.methods.resetFailedLogins = async function(this: UserDocument): Promise<void> {
  this.failedLoginAttempts = 0;
  this.lockedUntil = undefined;
  await this.save();
};

UserSchema.methods.lockAccount = async function(this: UserDocument, duration: number = 30 * 60 * 1000): Promise<void> {
  this.lockedUntil = new Date(Date.now() + duration).toISOString();
  await this.save();
};

UserSchema.methods.unlockAccount = async function(this: UserDocument): Promise<void> {
  this.lockedUntil = undefined;
  this.failedLoginAttempts = 0;
  await this.save();
};

UserSchema.methods.isAccountLocked = function(this: UserDocument): boolean {
  return !!(this.lockedUntil && new Date(this.lockedUntil) > new Date());
};

// ============================================================================
// STATIC METHODS
// ============================================================================

UserSchema.statics.findByEmail = function(email: string) {
  return this.findOne({ email: email.toLowerCase() });
};

UserSchema.statics.findActiveUsers = function() {
  return this.find({ active: true, suspended: false });
};

UserSchema.statics.findByReferralCode = function(code: string) {
  return this.findOne({ referralCode: code.toUpperCase() });
};

// ============================================================================
// EXPORT
// ============================================================================

export const User = mongoose.model<UserDocument>('User', UserSchema);
export default User;
