import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';

/**
 * BacktestResults Component
 * 
 * Displays the results of a backtest including performance metrics and equity curve
 */
const BacktestResults = ({ results }) => {
  // Chart references
  const equityChartRef = useRef(null);
  const equityChartInstance = useRef(null);
  
  const monthlyReturnsChartRef = useRef(null);
  const monthlyReturnsChartInstance = useRef(null);
  
  // Initialize equity chart
  useEffect(() => {
    if (!results || !equityChartRef.current) return;
    
    // Destroy existing chart if it exists
    if (equityChartInstance.current) {
      equityChartInstance.current.destroy();
    }
    
    // Create new chart
    const ctx = equityChartRef.current.getContext('2d');
    
    equityChartInstance.current = new Chart(ctx, {
      type: 'line',
      data: {
        labels: results.equityCurve.map(point => new Date(point.date).toLocaleDateString()),
        datasets: [
          {
            label: 'Equity Curve',
            data: results.equityCurve.map(point => point.equity),
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            borderWidth: 2,
            fill: true,
            tension: 0.1
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: function(context) {
                return `Equity: $${context.raw.toFixed(2)}`;
              }
            }
          },
          legend: {
            display: true,
            position: 'top'
          },
          title: {
            display: true,
            text: 'Equity Curve'
          }
        },
        scales: {
          x: {
            title: {
              display: true,
              text: 'Date'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Equity ($)'
            },
            beginAtZero: false
          }
        }
      }
    });
    
    return () => {
      if (equityChartInstance.current) {
        equityChartInstance.current.destroy();
      }
    };
  }, [results]);
  
  // Initialize monthly returns chart
  useEffect(() => {
    if (!results || !results.monthlyReturns || !monthlyReturnsChartRef.current) return;
    
    // Destroy existing chart if it exists
    if (monthlyReturnsChartInstance.current) {
      monthlyReturnsChartInstance.current.destroy();
    }
    
    // Create new chart
    const ctx = monthlyReturnsChartRef.current.getContext('2d');
    
    monthlyReturnsChartInstance.current = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: Object.keys(results.monthlyReturns),
        datasets: [
          {
            label: 'Monthly Returns (%)',
            data: Object.values(results.monthlyReturns),
            backgroundColor: Object.values(results.monthlyReturns).map(value => 
              value >= 0 ? 'rgba(75, 192, 192, 0.7)' : 'rgba(255, 99, 132, 0.7)'
            ),
            borderColor: Object.values(results.monthlyReturns).map(value => 
              value >= 0 ? 'rgba(75, 192, 192, 1)' : 'rgba(255, 99, 132, 1)'
            ),
            borderWidth: 1
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: function(context) {
                return `Return: ${context.raw.toFixed(2)}%`;
              }
            }
          },
          legend: {
            display: true,
            position: 'top'
          },
          title: {
            display: true,
            text: 'Monthly Returns'
          }
        },
        scales: {
          x: {
            title: {
              display: true,
              text: 'Month'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Return (%)'
            }
          }
        }
      }
    });
    
    return () => {
      if (monthlyReturnsChartInstance.current) {
        monthlyReturnsChartInstance.current.destroy();
      }
    };
  }, [results]);
  
  if (!results) {
    return (
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow text-center">
        <p className="text-gray-500 dark:text-gray-400">No backtest results to display. Run a backtest to see results here.</p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow" aria-label="Backtest Results" role="region">
      <h2 className="text-xl font-semibold mb-4">Backtest Results</h2>
      
      {/* Summary Section */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg text-center" tabIndex="0" aria-label="Net Profit">
            <p className="text-sm text-gray-500 dark:text-gray-400">Net Profit</p>
            <p className={`text-xl font-bold ${results.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>${results.netProfit.toFixed(2)}</p>
          </div>
          
          <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg text-center" tabIndex="0" aria-label="Win Rate">
            <p className="text-sm text-gray-500 dark:text-gray-400">Win Rate</p>
            <p className="text-xl font-bold">{results.winRate.toFixed(2)}%</p>
          </div>
          
          <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg text-center" tabIndex="0" aria-label="Profit Factor">
            <p className="text-sm text-gray-500 dark:text-gray-400">Profit Factor</p>
            <p className="text-xl font-bold">{results.profitFactor.toFixed(2)}</p>
          </div>
          
          <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg text-center" tabIndex="0" aria-label="Max Drawdown">
            <p className="text-sm text-gray-500 dark:text-gray-400">Max Drawdown</p>
            <p className="text-xl font-bold text-red-600">{results.maxDrawdown.toFixed(2)}%</p>
          </div>
        </div>
      </div>
      
      {/* Performance Metrics Section */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">Performance Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700" aria-label="Performance Metrics" role="table">
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                <tr>
                  <th scope="row" className="py-2 text-sm text-gray-500 dark:text-gray-400">Total Trades</th>
                  <td className="py-2 text-sm font-medium text-right">{results.totalTrades}</td>
                </tr>
                <tr>
                  <th scope="row" className="py-2 text-sm text-gray-500 dark:text-gray-400">Winning Trades</th>
                  <td className="py-2 text-sm font-medium text-right">{results.winningTrades}</td>
                </tr>
                <tr>
                  <th scope="row" className="py-2 text-sm text-gray-500 dark:text-gray-400">Losing Trades</th>
                  <td className="py-2 text-sm font-medium text-right">{results.losingTrades}</td>
                </tr>
                <tr>
                  <th scope="row" className="py-2 text-sm text-gray-500 dark:text-gray-400">Average Win</th>
                  <td className="py-2 text-sm font-medium text-right">${results.averageWin.toFixed(2)}</td>
                </tr>
                <tr>
                  <th scope="row" className="py-2 text-sm text-gray-500 dark:text-gray-400">Average Loss</th>
                  <td className="py-2 text-sm font-medium text-right">${results.averageLoss.toFixed(2)}</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700" aria-label="Risk Metrics" role="table">
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                <tr>
                  <th scope="row" className="py-2 text-sm text-gray-500 dark:text-gray-400">Sharpe Ratio</th>
                  <td className="py-2 text-sm font-medium text-right">{results.sharpeRatio.toFixed(2)}</td>
                </tr>
                <tr>
                  <th scope="row" className="py-2 text-sm text-gray-500 dark:text-gray-400">Sortino Ratio</th>
                  <td className="py-2 text-sm font-medium text-right">{results.sortinoRatio.toFixed(2)}</td>
                </tr>
                <tr>
                  <th scope="row" className="py-2 text-sm text-gray-500 dark:text-gray-400">Avg. Return per Trade</th>
                  <td className="py-2 text-sm font-medium text-right">${results.averageReturn.toFixed(2)}</td>
                </tr>
                <tr>
                  <th scope="row" className="py-2 text-sm text-gray-500 dark:text-gray-400">Avg. Holding Time</th>
                  <td className="py-2 text-sm font-medium text-right">{results.averageHoldingTime}</td>
                </tr>
                <tr>
                  <th scope="row" className="py-2 text-sm text-gray-500 dark:text-gray-400">Expectancy</th>
                  <td className="py-2 text-sm font-medium text-right">${results.expectancy.toFixed(2)}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      {/* Equity Curve Chart */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">Equity Curve</h3>
        <div className="relative h-64 w-full" role="img" aria-label="Equity Curve Chart">
          <canvas ref={equityChartRef} tabIndex="0" aria-label="Equity Curve Chart" />
        </div>
      </div>
      
      {/* Monthly Returns Chart */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">Monthly Returns</h3>
        <div className="relative h-64 w-full" role="img" aria-label="Monthly Returns Chart">
          <canvas ref={monthlyReturnsChartRef} tabIndex="0" aria-label="Monthly Returns Chart" />
        </div>
      </div>
      
      {/* Trade List Section */}
      <div>
        <h3 className="text-lg font-medium mb-3">Trade List</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">No.</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Entry Date</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Exit Date</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Entry Price</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Exit Price</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Profit/Loss</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {results.trades.slice(0, 10).map((trade, index) => (
                <tr key={index}>
                  <td className="px-4 py-2 whitespace-nowrap text-sm">{index + 1}</td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      trade.type === 'BUY' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                      {trade.type}
                    </span>
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm">{new Date(trade.entryDate).toLocaleString()}</td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm">{new Date(trade.exitDate).toLocaleString()}</td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm">{trade.entryPrice.toFixed(5)}</td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm">{trade.exitPrice.toFixed(5)}</td>
                  <td className={`px-4 py-2 whitespace-nowrap text-sm font-medium ${trade.profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    ${trade.profit.toFixed(2)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          {results.trades.length > 10 && (
            <div className="mt-2 text-center text-sm text-gray-500 dark:text-gray-400">
              Showing 10 of {results.trades.length} trades
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BacktestResults;
