const logger = require('../logging');

/**
 * Validate timeframe format
 * @param {string} timeframe - Timeframe to validate
 * @returns {boolean} - Validation result
 */
const isValidTimeframe = (timeframe) => {
  const validTimeframes = ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN'];
  return validTimeframes.includes(timeframe);
};

/**
 * Validate market type
 * @param {string} type - Market type to validate
 * @returns {boolean} - Validation result
 */
const isValidMarketType = (type) => {
  const validTypes = ['stocks', 'forex', 'commodities', 'crypto', 'economics', 'news', 'analysis'];
  return validTypes.includes(type.toLowerCase());
};

/**
 * Validate stock symbol format
 * @param {string} symbol - Symbol to validate
 * @returns {boolean} - Validation result
 */
const isValidStockSymbol = (symbol) => {
  // Basic validation for stock symbols (letters, numbers, dots)
  return /^[A-Z0-9.]{1,10}$/.test(symbol);
};

/**
 * Validate forex symbol format
 * @param {string} symbol - Symbol to validate
 * @returns {boolean} - Validation result
 */
const isValidForexSymbol = (symbol) => {
  // Forex pairs should be 6 characters, e.g., EURUSD
  return /^[A-Z]{6}$/.test(symbol);
};

/**
 * Validate cryptocurrency symbol format
 * @param {string} symbol - Symbol to validate
 * @returns {boolean} - Validation result
 */
const isValidCryptoSymbol = (symbol) => {
  // Crypto symbols can contain letters and may end with USDT, BTC, etc.
  return /^[A-Z]{2,8}(USD|USDT|BTC|ETH)?$/.test(symbol);
};

/**
 * Validate commodity symbol format
 * @param {string} symbol - Symbol to validate
 * @returns {boolean} - Validation result
 */
const isValidCommoditySymbol = (symbol) => {
  // Common commodity symbols
  const validCommodities = ['GOLD', 'SILVER', 'COPPER', 'OIL', 'NATGAS', 'CORN', 'WHEAT', 'SOYBEAN', 'COFFEE', 'SUGAR'];
  return validCommodities.includes(symbol.toUpperCase());
};

/**
 * Validate symbol based on market type
 * @param {string} symbol - Symbol to validate
 * @param {string} type - Market type
 * @returns {boolean} - Validation result
 */
const isValidSymbol = (symbol, type) => {
  if (!symbol) return false;
  
  const formattedType = type.toLowerCase();
  
  switch (formattedType) {
    case 'stocks':
      return isValidStockSymbol(symbol.toUpperCase());
    case 'forex':
      return isValidForexSymbol(symbol.toUpperCase());
    case 'crypto':
      return isValidCryptoSymbol(symbol.toUpperCase());
    case 'commodities':
      return isValidCommoditySymbol(symbol);
    case 'economics':
      // Economic indicators can have different formats
      return symbol.trim().length > 0;
    case 'news':
      // News symbols can be any valid stock symbol
      return isValidStockSymbol(symbol.toUpperCase());
    case 'analysis':
      // Analysis can be done on any market type
      return symbol.trim().length > 0;
    default:
      return false;
  }
};

/**
 * Validate indicators list
 * @param {string} indicators - Comma-separated indicators list
 * @returns {boolean} - Validation result
 */
const isValidIndicatorsList = (indicators) => {
  if (!indicators) return true; // Optional parameter
  
  const validIndicators = [
    'sma', 'ema', 'rsi', 'macd', 'bollinger', 'stochastic', 'atr', 'adx', 'obv', 'fibonacci',
    'ichimoku', 'pivot', 'williamsr', 'roc', 'momentum', 'cmf', 'mfi', 'vwap', 'psar', 'dmi'
  ];
  
  const indicatorList = indicators.split(',').map(i => i.trim().toLowerCase());
  return indicatorList.every(indicator => validIndicators.includes(indicator));
};

/**
 * Middleware to validate API input parameters
 */
const validateInputs = (req, res, next) => {
  const { type, symbol } = req.params;
  const { timeframe = 'D1', indicators } = req.query;
  
  // Validate market type
  if (!isValidMarketType(type)) {
    logger.warn(`Invalid market type requested: ${type}`);
    return res.status(400).json({
      error: 'Invalid market type',
      message: 'Market type must be one of: stocks, forex, commodities, crypto, economics, news, analysis',
      code: 'INVALID_MARKET_TYPE'
    });
  }
  
  // Validate symbol format based on market type
  if (!isValidSymbol(symbol, type)) {
    logger.warn(`Invalid symbol format for ${type}: ${symbol}`);
    return res.status(400).json({
      error: 'Invalid symbol format',
      message: `Symbol format is invalid for market type: ${type}`,
      code: 'INVALID_SYMBOL_FORMAT'
    });
  }
  
  // Validate timeframe if provided
  if (timeframe && !isValidTimeframe(timeframe)) {
    logger.warn(`Invalid timeframe requested: ${timeframe}`);
    return res.status(400).json({
      error: 'Invalid timeframe',
      message: 'Timeframe must be one of: M1, M5, M15, M30, H1, H4, D1, W1, MN',
      code: 'INVALID_TIMEFRAME'
    });
  }
  
  // Validate indicators if provided
  if (indicators && !isValidIndicatorsList(indicators)) {
    logger.warn(`Invalid indicators requested: ${indicators}`);
    return res.status(400).json({
      error: 'Invalid indicators',
      message: 'One or more of the requested indicators are not supported',
      code: 'INVALID_INDICATORS'
    });
  }
  
  next();
};

module.exports = {
  validateInputs,
  isValidTimeframe,
  isValidMarketType,
  isValidSymbol,
  isValidIndicatorsList
}; 