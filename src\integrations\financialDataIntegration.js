/**
 * Financial Data Integration for Trading Signals App
 * 
 * This file initializes and integrates all the financial data components:
 * - Enhanced API Service with retry queues
 * - MongoDB data storage
 * - WebSocket real-time updates
 * - Webhook integrations
 * - Dynamic parameter customization
 */

const FinancialDataService = require('../services/financialDataService');
const mongodbService = require('../services/mongodbService');
const logger = require('../utils/logger');
const financialDataRoutes = require('../routes/financialDataRoutes');

/**
 * Initialize financial data integration
 * @param {Object} app - Express app
 * @param {Object} server - HTTP server
 * @param {string} mongoUri - MongoDB connection URI
 * @returns {Promise<Object>} Initialized services
 */
async function initializeFinancialDataIntegration(app, server, mongoUri) {
  try {
    logger.info('Initializing financial data integration');
    
    // Initialize MongoDB connection
    const { db } = await mongodbService.initialize(mongoUri);
    logger.info('MongoDB connection initialized');
    
    // Initialize Financial Data Service
    const financialDataService = new FinancialDataService({
      db,
      server,
      enableWebSockets: true,
      enableWebhooks: true,
      enableDataStorage: true
    });
    logger.info('Financial Data Service initialized');
    
    // Initialize routes
    financialDataRoutes(app, financialDataService);
    logger.info('Financial data routes initialized');
    
    // Set up event listeners
    setupEventListeners(financialDataService);
    
    logger.info('Financial data integration completed successfully');
    
    return {
      financialDataService
    };
  } catch (error) {
    logger.error('Error initializing financial data integration:', error);
    throw error;
  }
}

/**
 * Set up event listeners for the financial data service
 * @param {FinancialDataService} financialDataService - Financial Data Service instance
 */
function setupEventListeners(financialDataService) {
  // Listen for data events
  financialDataService.on('data', (event) => {
    logger.debug(`Financial data event: ${event.provider}/${event.endpoint}`);
  });
  
  // Listen for rate limit events
  financialDataService.on('rateLimited', (event) => {
    logger.warn(`Rate limit event: ${event.provider} until ${new Date(event.resetTime).toISOString()}`);
  });
  
  // Listen for webhook events
  financialDataService.on('webhook', (event) => {
    logger.info(`Webhook event: ${event.event}`);
  });
  
  // Listen for WebSocket events
  financialDataService.on('websocket', (event) => {
    logger.debug(`WebSocket event: ${event.type}`);
  });
}

/**
 * Initialize financial data integration with default MongoDB URI
 * @param {Object} app - Express app
 * @param {Object} server - HTTP server
 * @returns {Promise<Object>} Initialized services
 */
async function initialize(app, server) {
  // Get MongoDB URI from environment or use default
  const mongoUri = process.env.MONGODB_URI || 
    "mongodb+srv://malaknagy03:${process.env.MONGODB_PASSWORD}@cluster0.au8xlc4.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0";
  
  return initializeFinancialDataIntegration(app, server, mongoUri);
}

module.exports = {
  initialize,
  initializeFinancialDataIntegration
};
