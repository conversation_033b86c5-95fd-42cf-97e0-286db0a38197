import React, { useState, useEffect } from 'react';
import { ALERT_TYPES, ALERT_PRIORITIES } from '../../services/alertsService';

/**
 * AlertNotification Component
 * 
 * Displays a notification for triggered alerts
 */
const AlertNotification = ({ alert, onClose, autoClose = true, autoCloseDelay = 5000 }) => {
  const [isVisible, setIsVisible] = useState(true);
  
  // Auto-close notification after delay
  useEffect(() => {
    if (autoClose) {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, autoCloseDelay);
      
      return () => clearTimeout(timer);
    }
  }, [autoClose, autoCloseDelay]);
  
  // Handle animation end
  const handleAnimationEnd = () => {
    if (!isVisible) {
      onClose();
    }
  };
  
  // Get alert type icon
  const getAlertIcon = () => {
    switch (alert.type) {
      case ALERT_TYPES.PRICE:
        return '💰';
      case ALERT_TYPES.PATTERN:
        return '📊';
      case ALERT_TYPES.INDICATOR:
        return '📈';
      case ALERT_TYPES.ECONOMIC:
        return '📅';
      case ALERT_TYPES.NEWS:
        return '📰';
      default:
        return '🔔';
    }
  };
  
  // Get alert priority class
  const getPriorityClass = () => {
    switch (alert.priority) {
      case ALERT_PRIORITIES.HIGH:
        return 'bg-red-100 border-red-500 text-red-800 dark:bg-red-900 dark:border-red-700 dark:text-red-200';
      case ALERT_PRIORITIES.MEDIUM:
        return 'bg-yellow-100 border-yellow-500 text-yellow-800 dark:bg-yellow-900 dark:border-yellow-700 dark:text-yellow-200';
      case ALERT_PRIORITIES.LOW:
        return 'bg-blue-100 border-blue-500 text-blue-800 dark:bg-blue-900 dark:border-blue-700 dark:text-blue-200';
      default:
        return 'bg-gray-100 border-gray-500 text-gray-800 dark:bg-gray-900 dark:border-gray-700 dark:text-gray-200';
    }
  };
  
  // Get alert title
  const getAlertTitle = () => {
    switch (alert.type) {
      case ALERT_TYPES.PRICE:
        return 'Price Alert';
      case ALERT_TYPES.PATTERN:
        return 'Pattern Alert';
      case ALERT_TYPES.INDICATOR:
        return 'Indicator Alert';
      case ALERT_TYPES.ECONOMIC:
        return 'Economic Alert';
      case ALERT_TYPES.NEWS:
        return 'News Alert';
      default:
        return 'Alert';
    }
  };
  
  // Format alert message
  const formatAlertMessage = () => {
    // If alert has a custom message, use it
    if (alert.message) {
      return alert.message;
    }
    
    // Otherwise, generate a message based on alert type and conditions
    switch (alert.type) {
      case ALERT_TYPES.PRICE:
        if (alert.conditions.above) {
          return `${alert.symbol} price is above ${alert.conditions.above}`;
        }
        if (alert.conditions.below) {
          return `${alert.symbol} price is below ${alert.conditions.below}`;
        }
        if (alert.conditions.changePercent) {
          const direction = alert.conditions.changeDirection === 'up' ? 'increased' : 'decreased';
          return `${alert.symbol} price has ${direction} by ${alert.conditions.changePercent}%`;
        }
        return `Price alert for ${alert.symbol}`;
      
      case ALERT_TYPES.PATTERN:
        if (alert.conditions.pattern) {
          return `${alert.conditions.pattern.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} pattern detected on ${alert.symbol}`;
        }
        if (alert.conditions.patternType) {
          return `${alert.conditions.patternType.charAt(0).toUpperCase() + alert.conditions.patternType.slice(1)} pattern detected on ${alert.symbol}`;
        }
        return `Pattern alert for ${alert.symbol}`;
      
      case ALERT_TYPES.INDICATOR:
        if (alert.conditions.indicator === 'rsi') {
          if (alert.conditions.above) {
            return `RSI is above ${alert.conditions.above} on ${alert.symbol}`;
          }
          if (alert.conditions.below) {
            return `RSI is below ${alert.conditions.below} on ${alert.symbol}`;
          }
        }
        if (alert.conditions.indicator === 'macd') {
          if (alert.conditions.crossover === 'bullish') {
            return `Bullish MACD crossover on ${alert.symbol}`;
          }
          if (alert.conditions.crossover === 'bearish') {
            return `Bearish MACD crossover on ${alert.symbol}`;
          }
        }
        return `Indicator alert for ${alert.symbol}`;
      
      case ALERT_TYPES.ECONOMIC:
        if (alert.conditions.eventType) {
          return `${alert.conditions.eventType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} economic event`;
        }
        if (alert.conditions.country) {
          return `Economic event for ${alert.conditions.country}`;
        }
        return 'Economic calendar alert';
      
      case ALERT_TYPES.NEWS:
        if (alert.conditions.keywords && alert.conditions.keywords.length) {
          return `News alert: ${alert.conditions.keywords.join(', ')}`;
        }
        return 'News alert';
      
      default:
        return 'Alert triggered';
    }
  };

  return (
    <div 
      className={`fixed bottom-4 right-4 max-w-sm p-4 border-l-4 rounded shadow-lg transition-opacity duration-300 z-50 ${
        getPriorityClass()
      } ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
      onAnimationEnd={handleAnimationEnd}
    >
      <div className="flex items-start">
        <div className="flex-shrink-0 mr-3">
          <span className="text-2xl">{getAlertIcon()}</span>
        </div>
        
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <p className="font-bold">{getAlertTitle()}</p>
            <button 
              onClick={() => setIsVisible(false)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none"
            >
              &times;
            </button>
          </div>
          
          <p className="mt-1">{formatAlertMessage()}</p>
          
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            {alert.timeframe && (
              <span className="mr-2">{alert.timeframe}</span>
            )}
            <span>{new Date(alert.lastTriggered || alert.createdAt).toLocaleString()}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AlertNotification;
