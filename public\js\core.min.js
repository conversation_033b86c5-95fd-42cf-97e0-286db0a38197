/**
 * Trading Signals App - Core JavaScript
 * This file combines essential functionality from:
 * - auth.js
 * - market-data-service.js
 * - technical-analysis.js
 * - chart.js
 */

// Global state
const authState = {
    token: localStorage.getItem('auth_token'),
    user: localStorage.getItem('auth_user') ? JSON.parse(localStorage.getItem('auth_user')) : null,
    isAuthenticated: !!localStorage.getItem('auth_token')
};

// ==============================================
// Authentication Module
// ==============================================

// Initialize authentication
function initAuth() {
    // Check token and update UI
    if (authState.token) {
        updateAuthUI();
    }
}

// Update UI based on authentication state
function updateAuthUI() {
    const authNav = document.getElementById('auth-nav');
    const userInfo = document.getElementById('user-info');
    
    if (!authNav) return; // Not on a page with auth elements
    
    if (authState.isAuthenticated) {
        // User is logged in
        const usernameElement = document.getElementById('username');
        if (usernameElement) {
            usernameElement.textContent = authState.user?.name || authState.user?.username || 'User';
        }
        
        // Add logout event listener
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', logoutUser);
        }
    }
}

// Handle logout
function logoutUser() {
    // Save user settings before logout
    saveUserSettings();
    
    // Clear authentication data
    clearAuth();
    
    // Redirect to login page
    setTimeout(() => {
        window.location.href = 'login.html';
    }, 500);
}

// Clear authentication data
function clearAuth() {
    authState.token = null;
    authState.user = null;
    authState.isAuthenticated = false;
    
    // Remove from localStorage
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
    
    // Update UI
    updateAuthUI();
}

// Save user settings
function saveUserSettings() {
    const user = localStorage.getItem('user');

    if (!user) {
        return;
    }

    try {
        const userData = JSON.parse(user);

        // Collect settings
        const settings = {
            marketType: document.getElementById('marketType')?.value,
            symbol: document.getElementById('symbol')?.value,
            timeframe: document.getElementById('timeframe')?.value,
            accountBalance: document.getElementById('accountBalance')?.value,
            riskPercentage: document.getElementById('riskPercentage')?.value,
            lastUpdated: new Date().toISOString()
        };

        // Save settings to localStorage
        localStorage.setItem(`settings_${userData.id}`, JSON.stringify(settings));
    } catch (error) {
        console.error('Error saving user settings:', error);
    }
}

// ==============================================
// Market Data Service Module
// ==============================================

// Market Data Configuration
const marketConfig = {
    baseUrl: 'https://api.example.com/market-data',
    apiKey: '',
    defaultSymbol: 'EURUSD',
    defaultTimeframe: 'H1',
    updateInterval: 60000
};

// Get market data for specified symbol and timeframe
async function getMarketData(symbol, timeframe) {
    try {
        // In a real app, this would fetch data from an API
        // This is a placeholder that returns mock data
        return getMockMarketData(symbol, timeframe);
    } catch (error) {
        console.error('Error fetching market data:', error);
        throw error;
    }
}

// Generate mock market data
function getMockMarketData(symbol = 'EURUSD', timeframe = 'H1') {
    const now = new Date();
    const data = [];
    
    // Generate 100 data points
    for (let i = 99; i >= 0; i--) {
        const date = new Date(now);
        date.setHours(date.getHours() - i);
        
        // Create random price movements
        const open = 1.0850 + (Math.random() * 0.01);
        const high = open + (Math.random() * 0.005);
        const low = open - (Math.random() * 0.005);
        const close = low + (Math.random() * (high - low));
        const volume = Math.floor(Math.random() * 1000) + 500;
        
        data.push({
            time: date,
            open,
            high,
            low,
            close,
            volume
        });
    }
    
    return {
        symbol,
        timeframe,
        lastUpdated: now,
        data
    };
}

// ==============================================
// Technical Analysis Module
// ==============================================

// Calculate Moving Average
function calculateMA(data, period = 14) {
    if (!data || data.length < period) {
        return [];
    }
    
    const result = [];
    
    for (let i = 0; i < data.length; i++) {
        if (i < period - 1) {
            result.push(null);
            continue;
        }
        
        let sum = 0;
        for (let j = 0; j < period; j++) {
            sum += data[i - j].close;
        }
        
        result.push(sum / period);
    }
    
    return result;
}

// Calculate RSI (Relative Strength Index)
function calculateRSI(data, period = 14) {
    if (!data || data.length < period + 1) {
        return [];
    }
    
    const result = [];
    let gains = 0;
    let losses = 0;
    
    // Initial RSI calculation
    for (let i = 1; i <= period; i++) {
        const change = data[i].close - data[i - 1].close;
        if (change >= 0) {
            gains += change;
        } else {
            losses -= change;
        }
    }
    
    let avgGain = gains / period;
    let avgLoss = losses / period;
    
    // First RSI value
    let rs = avgGain / avgLoss;
    let rsi = 100 - (100 / (1 + rs));
    result.push(null, ...Array(period - 1).fill(null), rsi);
    
    // Calculate RSI for the rest of the data
    for (let i = period + 1; i < data.length; i++) {
        const change = data[i].close - data[i - 1].close;
        let currentGain = 0;
        let currentLoss = 0;
        
        if (change >= 0) {
            currentGain = change;
        } else {
            currentLoss = -change;
        }
        
        // Smoothed averages
        avgGain = ((avgGain * (period - 1)) + currentGain) / period;
        avgLoss = ((avgLoss * (period - 1)) + currentLoss) / period;
        
        rs = avgGain / avgLoss;
        rsi = 100 - (100 / (1 + rs));
        result.push(rsi);
    }
    
    return result;
}

// ==============================================
// Chart Module
// ==============================================

// Initialize chart
function initializeChart(chartId, data, type = 'candlestick') {
    const ctx = document.getElementById(chartId);
    
    if (!ctx) {
        console.error(`Chart element with ID ${chartId} not found`);
        return null;
    }
    
    // Convert data for Chart.js
    const chartData = prepareChartData(data, type);
    
    // Chart configuration
    const config = {
        type: type === 'candlestick' ? 'candlestick' : 'line',
        data: {
            datasets: [
                {
                    label: data.symbol,
                    data: chartData,
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.2)',
                    borderWidth: 1,
                    pointRadius: 0,
                    pointHoverRadius: 3
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'hour'
                    }
                },
                y: {
                    position: 'right'
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const point = context.raw;
                            if (type === 'candlestick') {
                                return [
                                    `O: ${point.o.toFixed(5)}`,
                                    `H: ${point.h.toFixed(5)}`,
                                    `L: ${point.l.toFixed(5)}`,
                                    `C: ${point.c.toFixed(5)}`
                                ];
                            } else {
                                return `${context.dataset.label}: ${point.y.toFixed(5)}`;
                            }
                        }
                    }
                }
            }
        }
    };
    
    return new Chart(ctx, config);
}

// Prepare data for Chart.js
function prepareChartData(marketData, type) {
    if (!marketData || !marketData.data) {
        return [];
    }
    
    return marketData.data.map(item => {
        if (type === 'candlestick') {
            return {
                x: item.time,
                o: item.open,
                h: item.high,
                l: item.low,
                c: item.close
            };
        } else {
            return {
                x: item.time,
                y: item.close
            };
        }
    });
}

// Add technical indicator to chart
function addIndicatorToChart(chart, indicator, data, period, color) {
    if (!chart || !chart.data || !chart.data.datasets) {
        return;
    }
    
    let indicatorData = [];
    
    switch (indicator) {
        case 'ma':
            indicatorData = calculateMA(data.data, period);
            break;
        case 'rsi':
            indicatorData = calculateRSI(data.data, period);
            break;
        default:
            console.error(`Unsupported indicator: ${indicator}`);
            return;
    }
    
    // Create dataset for the indicator
    const dataset = {
        label: `${indicator.toUpperCase()}(${period})`,
        data: data.data.map((item, index) => ({
            x: item.time,
            y: indicatorData[index]
        })),
        borderColor: color || '#FF5722',
        backgroundColor: 'transparent',
        borderWidth: 1.5,
        pointRadius: 0,
        pointHoverRadius: 3,
        yAxisID: indicator === 'rsi' ? 'rsi' : 'y'
    };
    
    // Add RSI scale if needed
    if (indicator === 'rsi') {
        chart.options.scales.rsi = {
            position: 'right',
            min: 0,
            max: 100,
            grid: {
                drawOnChartArea: false
            }
        };
    }
    
    // Add dataset to chart
    chart.data.datasets.push(dataset);
    chart.update();
}

// ==============================================
// Initialize App
// ==============================================

document.addEventListener('DOMContentLoaded', function() {
    // Initialize authentication
    initAuth();
    
    // Initialize chart if elements exist
    const priceChart = document.getElementById('priceChart');
    if (priceChart) {
        getMarketData('EURUSD', 'H1').then(data => {
            const chart = initializeChart('priceChart', data);
            
            // Add event listeners for chart controls
            const indicatorButtons = document.querySelectorAll('[data-indicator]');
            indicatorButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const indicator = this.getAttribute('data-indicator');
                    addIndicatorToChart(chart, indicator, data, 14, '#FF5722');
                });
            });
        });
    }
    
    // Add event listener for logout button
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(event) {
            event.preventDefault();
            logoutUser();
        });
    }
});

// Export modules for global access
window.App = {
    Auth: {
        login: function() { /* placeholder */ },
        logout: logoutUser,
        isAuthenticated: () => authState.isAuthenticated
    },
    Market: {
        getData: getMarketData
    },
    Analysis: {
        calculateMA,
        calculateRSI
    },
    Chart: {
        initialize: initializeChart,
        addIndicator: addIndicatorToChart
    }
}; 