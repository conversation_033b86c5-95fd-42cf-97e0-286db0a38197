/**
 * Economic Calendar Service for Trading Signals App (Minified)
 */
const ECONOMIC_CALENDAR_CONFIG={DEFAULT_DAYS_RANGE:14,IMPORTANCE_LEVELS:{HIGH:"high",MEDIUM:"medium",LOW:"low"},COUNTRY_CODES:{US:"United States",EU:"Euro Area",GB:"United Kingdom",JP:"Japan",CN:"China",CA:"Canada",AU:"Australia",NZ:"New Zealand",CH:"Switzerland"},EVENT_CATEGORIES:{INTEREST_RATE:"Interest Rate",GDP:"GDP",EMPLOYMENT:"Employment",INFLATION:"Inflation",TRADE:"Trade",MANUFACTURING:"Manufacturing",HOUSING:"Housing",CONSUMER:"Consumer",CENTRAL_BANK:"Central Bank"},IMPORTANT_INDICATORS:{US:[{id:"UNRATE",name:"Unemployment Rate",importance:"high",category:"EMPLOYMENT"},{id:"PAYEMS",name:"Nonfarm Payrolls",importance:"high",category:"EMPLOYMENT"},{id:"CPIAUCSL",name:"Consumer Price Index",importance:"high",category:"INFLATION"},{id:"GDPC1",name:"GDP",importance:"high",category:"GDP"},{id:"FEDFUNDS",name:"Federal Funds Rate",importance:"high",category:"INTEREST_RATE"},{id:"INDPRO",name:"Industrial Production",importance:"medium",category:"MANUFACTURING"},{id:"RSAFS",name:"Retail Sales",importance:"medium",category:"CONSUMER"},{id:"HOUST",name:"Housing Starts",importance:"medium",category:"HOUSING"}],EU:[{id:"LRHUTTTTEZM156S",name:"Unemployment Rate",importance:"high",category:"EMPLOYMENT"},{id:"CP0000EZ19M086NEST",name:"Consumer Price Index",importance:"high",category:"INFLATION"},{id:"CLVMEURSCAB1GQEA19",name:"GDP",importance:"high",category:"GDP"},{id:"INTDSREZM193N",name:"ECB Interest Rate",importance:"high",category:"INTEREST_RATE"}]}};class EconomicCalendarService{constructor(){this.events=[],this.lastUpdate=null,this.eventListeners={dataUpdated:[],error:[]},console.log("Economic Calendar Service initialized")}async getEconomicCalendar(e={}){try{const t=new Date,a=e.startDate||new Date(t.getTime()-6048e5).toISOString().split("T")[0],n=e.endDate||new Date(t.getTime()+6048e5).toISOString().split("T")[0],r=await window.unifiedAPIService.fetchData({provider:"FRED",endpoint:"releases/dates",params:{realtime_start:a,realtime_end:n,limit:1e3,order_by:"release_date",sort_order:"asc"},useCache:!0,cacheTTL:36e5}),i=this.processEconomicCalendarData(r.data,e);return this.events=i,this.lastUpdate=new Date,this.triggerEvent("dataUpdated",i),i}catch(t){return console.error("Error fetching economic calendar data:",t),this.triggerEvent("error",{message:`Failed to fetch economic calendar data: ${t.message}`,error:t}),this.getMockEconomicCalendarEvents(e)}}processEconomicCalendarData(e,t={}){try{if(!e||!e.release_dates||!Array.isArray(e.release_dates))throw new Error("Invalid FRED API response format");const a=e.release_dates.map(e=>{let t="US";e.name.includes("Euro")||e.name.includes("ECB")?t="EU":e.name.includes("UK")||e.name.includes("British")?t="GB":e.name.includes("Japan")||e.name.includes("BOJ")?t="JP":e.name.includes("China")?t="CN":e.name.includes("Canada")?t="CA":e.name.includes("Australia")&&(t="AU");let a=ECONOMIC_CALENDAR_CONFIG.IMPORTANCE_LEVELS.MEDIUM;e.name.includes("GDP")||e.name.includes("Unemployment")||e.name.includes("CPI")||e.name.includes("Interest Rate")||e.name.includes("Nonfarm Payrolls")?a=ECONOMIC_CALENDAR_CONFIG.IMPORTANCE_LEVELS.HIGH:e.name.includes("Building Permits")||e.name.includes("Retail Sales")||e.name.includes("Manufacturing")?a=ECONOMIC_CALENDAR_CONFIG.IMPORTANCE_LEVELS.MEDIUM:a=ECONOMIC_CALENDAR_CONFIG.IMPORTANCE_LEVELS.LOW;let n="Other";e.name.includes("GDP")?n=ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.GDP:e.name.includes("Unemployment")||e.name.includes("Payrolls")||e.name.includes("Employment")?n=ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.EMPLOYMENT:e.name.includes("CPI")||e.name.includes("Inflation")||e.name.includes("Price Index")?n=ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.INFLATION:e.name.includes("Interest Rate")||e.name.includes("Fed")||e.name.includes("ECB")?n=ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.INTEREST_RATE:e.name.includes("Trade")||e.name.includes("Export")||e.name.includes("Import")?n=ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.TRADE:e.name.includes("Manufacturing")||e.name.includes("Industrial")?n=ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.MANUFACTURING:e.name.includes("Housing")||e.name.includes("Home")||e.name.includes("Building")?n=ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.HOUSING:e.name.includes("Consumer")||e.name.includes("Retail")&&(n=ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.CONSUMER);const r=new Date(e.release_date),i=r.toISOString().split("T")[0],s=r.toTimeString().split(" ")[0].substring(0,5);return{id:e.release_id,title:e.name,date:i,time:s,country:t,importance:a,category:n,forecast:null,previous:null,actual:null,url:`https://fred.stlouisfed.org/release?rid=${e.release_id}`}});let n=a;return t.country&&"all"!==t.country&&(n=n.filter(e=>e.country===t.country)),t.importance&&"all"!==t.importance&&(n=n.filter(e=>e.importance===t.importance)),t.category&&"all"!==t.category&&(n=n.filter(e=>e.category===t.category)),n}catch(t){throw console.error("Error processing economic calendar data:",t),t}}getMockEconomicCalendarEvents(e={}){console.log("Generating mock economic calendar events");const t=new Date,a=e.startDate?new Date(e.startDate):new Date(t.getTime()-6048e5),n=e.endDate?new Date(e.endDate):new Date(t.getTime()+6048e5),r=[{id:1,title:"US Non-Farm Payrolls",date:this.getNextFriday(t).toISOString().split("T")[0],time:"08:30",country:"US",importance:"high",category:"EMPLOYMENT",forecast:"+180K",previous:"+236K",actual:null},{id:2,title:"US CPI m/m",date:this.getNextWednesday(t).toISOString().split("T")[0],time:"08:30",country:"US",importance:"high",category:"INFLATION",forecast:"0.2%",previous:"0.4%",actual:null},{id:3,title:"ECB Interest Rate Decision",date:this.getNextThursday(t).toISOString().split("T")[0],time:"07:45",country:"EU",importance:"high",category:"INTEREST_RATE",forecast:"3.75%",previous:"3.50%",actual:null},{id:4,title:"UK GDP m/m",date:this.getNextTuesday(t).toISOString().split("T")[0],time:"02:00",country:"GB",importance:"medium",category:"GDP",forecast:"0.2%",previous:"-0.3%",actual:null},{id:5,title:"US Retail Sales m/m",date:this.getNextTuesday(t).toISOString().split("T")[0],time:"08:30",country:"US",importance:"medium",category:"CONSUMER",forecast:"0.3%",previous:"0.7%",actual:null},{id:6,title:"Japan GDP q/q",date:this.getNextMonday(t).toISOString().split("T")[0],time:"19:50",country:"JP",importance:"high",category:"GDP",forecast:"0.5%",previous:"0.1%",actual:null},{id:7,title:"China Manufacturing PMI",date:this.getNextMonday(t).toISOString().split("T")[0],time:"01:00",country:"CN",importance:"high",category:"MANUFACTURING",forecast:"50.2",previous:"49.8",actual:null},{id:8,title:"US Building Permits",date:this.getNextWednesday(t).toISOString().split("T")[0],time:"08:30",country:"US",importance:"low",category:"HOUSING",forecast:"1.42M",previous:"1.39M",actual:null}],i=r.filter(e=>{const t=new Date(e.date);return t>=a&&t<=n});let s=i;return e.country&&"all"!==e.country&&(s=s.filter(t=>t.country===e.country)),e.importance&&"all"!==e.importance&&(s=s.filter(t=>t.importance===e.importance)),s}getNextMonday(e){const t=new Date(e);return t.setDate(e.getDate()+(8-e.getDay())%7),t}getNextTuesday(e){const t=new Date(e);return t.setDate(e.getDate()+(9-e.getDay())%7),t}getNextWednesday(e){const t=new Date(e);return t.setDate(e.getDate()+(10-e.getDay())%7),t}getNextThursday(e){const t=new Date(e);return t.setDate(e.getDate()+(11-e.getDay())%7),t}getNextFriday(e){const t=new Date(e);return t.setDate(e.getDate()+(12-e.getDay())%7),t}addEventListener(e,t){this.eventListeners[e]||(this.eventListeners[e]=[]),this.eventListeners[e].push(t)}removeEventListener(e,t){this.eventListeners[e]&&(this.eventListeners[e]=this.eventListeners[e].filter(e=>e!==t))}triggerEvent(e,t){this.eventListeners[e]&&this.eventListeners[e].forEach(e=>{try{e(t)}catch(t){console.error(`Error in ${e} event listener:`,t)}})}}const economicCalendarService=new EconomicCalendarService;"undefined"!=typeof window&&(window.economicCalendarService=economicCalendarService);export default economicCalendarService;
