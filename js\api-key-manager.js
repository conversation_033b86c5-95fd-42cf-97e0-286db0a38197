/**
 * API Key Manager for Trading Signals App
 * 
 * This class manages API keys for different providers, handling rotation,
 * cooldown periods for rate-limited keys, and fallback mechanisms.
 */

class ApiKeyManager {
  /**
   * Create a new API Key Manager
   * 
   * @param {string} provider - The API provider name
   * @param {Array<string>} keys - Array of API keys
   * @param {Object} options - Configuration options
   * @param {number} options.cooldownPeriod - Cooldown period in milliseconds (default: 60 minutes)
   * @param {number} options.maxFailures - Maximum failures before cooldown (default: 3)
   * @param {string} options.logLevel - Log level (default: 'warn')
   */
  constructor(provider, keys, options = {}) {
    this.provider = provider;
    this.keys = keys.map(key => ({
      key,
      failures: 0,
      lastFailure: null,
      cooldownUntil: null
    }));
    
    this.options = {
      cooldownPeriod: 60 * 60 * 1000, // 60 minutes
      maxFailures: 3,
      logLevel: 'warn',
      ...options
    };
    
    this.currentKeyIndex = 0;
    
    this.log('info', `API Key Manager initialized for ${provider} with ${keys.length} keys`);
  }
  
  /**
   * Get the next available API key
   * 
   * @returns {string} - API key
   */
  getKey() {
    // Check if we have any keys
    if (this.keys.length === 0) {
      this.log('error', `No API keys available for ${this.provider}`);
      throw new Error(`No API keys available for ${this.provider}`);
    }
    
    // Find the next available key
    const now = Date.now();
    let attempts = 0;
    
    while (attempts < this.keys.length) {
      const keyInfo = this.keys[this.currentKeyIndex];
      
      // Check if key is in cooldown
      if (keyInfo.cooldownUntil && keyInfo.cooldownUntil > now) {
        this.log('debug', `Key ${this.maskKey(keyInfo.key)} is in cooldown until ${new Date(keyInfo.cooldownUntil).toISOString()}`);
        
        // Move to next key
        this.currentKeyIndex = (this.currentKeyIndex + 1) % this.keys.length;
        attempts++;
        continue;
      }
      
      // Key is available
      this.log('debug', `Using key ${this.maskKey(keyInfo.key)} for ${this.provider}`);
      return keyInfo.key;
    }
    
    // All keys are in cooldown
    this.log('warn', `All keys for ${this.provider} are in cooldown`);
    
    // Use the key with the earliest cooldown end
    let earliestCooldownKey = this.keys[0];
    
    for (const keyInfo of this.keys) {
      if (keyInfo.cooldownUntil < earliestCooldownKey.cooldownUntil) {
        earliestCooldownKey = keyInfo;
      }
    }
    
    this.log('warn', `Using key ${this.maskKey(earliestCooldownKey.key)} despite cooldown`);
    return earliestCooldownKey.key;
  }
  
  /**
   * Mark a key as failed
   * 
   * @param {string} key - API key
   */
  markKeyAsFailed(key) {
    // Find the key
    const keyInfo = this.keys.find(k => k.key === key);
    
    if (!keyInfo) {
      this.log('warn', `Key ${this.maskKey(key)} not found in ${this.provider} keys`);
      return;
    }
    
    // Increment failures
    keyInfo.failures++;
    keyInfo.lastFailure = Date.now();
    
    this.log('info', `Key ${this.maskKey(key)} failed (${keyInfo.failures}/${this.options.maxFailures})`);
    
    // Check if key should be put in cooldown
    if (keyInfo.failures >= this.options.maxFailures) {
      keyInfo.cooldownUntil = Date.now() + this.options.cooldownPeriod;
      keyInfo.failures = 0;
      
      this.log('warn', `Key ${this.maskKey(key)} put in cooldown until ${new Date(keyInfo.cooldownUntil).toISOString()}`);
      
      // Move to next key
      this.currentKeyIndex = (this.currentKeyIndex + 1) % this.keys.length;
    }
  }
  
  /**
   * Mark a key as successful
   * 
   * @param {string} key - API key
   */
  markKeyAsSuccessful(key) {
    // Find the key
    const keyInfo = this.keys.find(k => k.key === key);
    
    if (!keyInfo) {
      this.log('warn', `Key ${this.maskKey(key)} not found in ${this.provider} keys`);
      return;
    }
    
    // Reset failures
    if (keyInfo.failures > 0) {
      keyInfo.failures = 0;
      this.log('debug', `Key ${this.maskKey(key)} failures reset`);
    }
  }
  
  /**
   * Reset all keys
   */
  resetKeys() {
    for (const keyInfo of this.keys) {
      keyInfo.failures = 0;
      keyInfo.lastFailure = null;
      keyInfo.cooldownUntil = null;
    }
    
    this.currentKeyIndex = 0;
    this.log('info', `All keys for ${this.provider} reset`);
  }
  
  /**
   * Add a new API key
   * 
   * @param {string} key - API key
   */
  addKey(key) {
    // Check if key already exists
    if (this.keys.some(k => k.key === key)) {
      this.log('warn', `Key ${this.maskKey(key)} already exists for ${this.provider}`);
      return;
    }
    
    // Add key
    this.keys.push({
      key,
      failures: 0,
      lastFailure: null,
      cooldownUntil: null
    });
    
    this.log('info', `Key ${this.maskKey(key)} added for ${this.provider}`);
  }
  
  /**
   * Remove an API key
   * 
   * @param {string} key - API key
   */
  removeKey(key) {
    // Find key index
    const index = this.keys.findIndex(k => k.key === key);
    
    if (index === -1) {
      this.log('warn', `Key ${this.maskKey(key)} not found in ${this.provider} keys`);
      return;
    }
    
    // Remove key
    this.keys.splice(index, 1);
    
    // Adjust current key index if needed
    if (this.currentKeyIndex >= this.keys.length) {
      this.currentKeyIndex = 0;
    }
    
    this.log('info', `Key ${this.maskKey(key)} removed from ${this.provider}`);
  }
  
  /**
   * Get the status of all keys
   * 
   * @returns {Array<Object>} - Key status information
   */
  getKeyStatus() {
    return this.keys.map(keyInfo => ({
      key: this.maskKey(keyInfo.key),
      failures: keyInfo.failures,
      lastFailure: keyInfo.lastFailure,
      cooldownUntil: keyInfo.cooldownUntil,
      inCooldown: keyInfo.cooldownUntil && keyInfo.cooldownUntil > Date.now()
    }));
  }
  
  /**
   * Mask an API key for logging
   * 
   * @param {string} key - API key
   * @returns {string} - Masked key
   */
  maskKey(key) {
    if (!key) return 'undefined';
    
    if (key.length <= 8) {
      return '****';
    }
    
    return key.substring(0, 4) + '****' + key.substring(key.length - 4);
  }
  
  /**
   * Log a message
   * 
   * @param {string} level - Log level
   * @param {string} message - Log message
   */
  log(level, message) {
    const logLevels = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };
    
    // Check if we should log this message
    if (logLevels[level] < logLevels[this.options.logLevel]) {
      return;
    }
    
    // Log message
    const timestamp = new Date().toISOString();
    const formattedMessage = `[${timestamp}] [${this.provider}] [${level.toUpperCase()}] ${message}`;
    
    switch (level) {
      case 'debug':
        console.debug(formattedMessage);
        break;
      case 'info':
        console.info(formattedMessage);
        break;
      case 'warn':
        console.warn(formattedMessage);
        break;
      case 'error':
        console.error(formattedMessage);
        break;
      default:
        console.log(formattedMessage);
    }
  }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
  window.ApiKeyManager = ApiKeyManager;
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = ApiKeyManager;
}
