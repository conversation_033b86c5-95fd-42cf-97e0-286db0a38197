/**
 * WebSocket Context Provider
 * 
 * React Context for managing WebSocket connections across the application.
 * Provides centralized WebSocket state management and real-time updates.
 * 
 * Features:
 * - Centralized WebSocket connection management
 * - Real-time signal updates
 * - Market data streaming
 * - User notifications
 * - Connection state management
 * - Type-safe message handling
 * 
 * @version 1.0.0
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import {
  WebSocketMessage,
  WebSocketMessageType,
  WebSocketConnectionState,
  WebSocketChannel
} from '../types/websocket';
import { UnifiedSignal } from '../types/signals';
import { Quote, MarketData } from '../types/market';
import { UserNotification } from '../types/users';
import { useWebSocket, UseWebSocketReturn } from '../hooks/useWebSocket';

// ============================================================================
// CONTEXT INTERFACES
// ============================================================================

export interface WebSocketContextValue extends UseWebSocketReturn {
  // Real-time data
  latestSignals: UnifiedSignal[];
  latestQuotes: Map<string, Quote>;
  latestMarketData: Map<string, MarketData>;
  notifications: UserNotification[];
  
  // Signal-specific methods
  subscribeToSignals: (symbols?: string[]) => void;
  unsubscribeFromSignals: () => void;
  
  // Market data methods
  subscribeToMarketData: (symbols: string[]) => void;
  unsubscribeFromMarketData: (symbols: string[]) => void;
  
  // Notification methods
  subscribeToNotifications: () => void;
  unsubscribeFromNotifications: () => void;
  markNotificationAsRead: (notificationId: string) => void;
  
  // Connection status
  connectionStatus: 'connected' | 'connecting' | 'disconnected' | 'error';
  connectionError: string | null;
}

export interface WebSocketProviderProps {
  children: ReactNode;
  url?: string;
  autoConnect?: boolean;
  userId?: string;
  authToken?: string;
}

// ============================================================================
// CONTEXT CREATION
// ============================================================================

const WebSocketContext = createContext<WebSocketContextValue | null>(null);

// ============================================================================
// WEBSOCKET PROVIDER COMPONENT
// ============================================================================

export function WebSocketProvider({
  children,
  url = process.env.REACT_APP_WS_URL || 'ws://localhost:3000/ws',
  autoConnect = true,
  userId,
  authToken
}: WebSocketProviderProps) {
  // Real-time data state
  const [latestSignals, setLatestSignals] = useState<UnifiedSignal[]>([]);
  const [latestQuotes, setLatestQuotes] = useState<Map<string, Quote>>(new Map());
  const [latestMarketData, setLatestMarketData] = useState<Map<string, MarketData>>(new Map());
  const [notifications, setNotifications] = useState<UserNotification[]>([]);

  // WebSocket hook
  const webSocket = useWebSocket({
    url,
    reconnect: true,
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000,
    onConnect: handleConnect,
    onDisconnect: handleDisconnect,
    onError: handleError,
    onMessage: handleMessage
  });

  // ========================================================================
  // EVENT HANDLERS
  // ========================================================================

  function handleConnect() {
    console.log('WebSocket connected');
    
    // Authenticate if token is available
    if (authToken && userId) {
      webSocket.sendTypedMessage(WebSocketMessageType.AUTH_REQUIRED, {
        token: authToken,
        userId
      });
    }
  }

  function handleDisconnect(reason: string) {
    console.log('WebSocket disconnected:', reason);
  }

  function handleError(error: Event) {
    console.error('WebSocket error:', error);
  }

  function handleMessage(message: WebSocketMessage) {
    switch (message.type) {
      case WebSocketMessageType.SIGNAL_UPDATE:
      case WebSocketMessageType.SIGNAL_CREATED:
        handleSignalUpdate(message);
        break;
      
      case WebSocketMessageType.SIGNAL_DELETED:
        handleSignalDeleted(message);
        break;
      
      case WebSocketMessageType.MARKET_UPDATE:
      case WebSocketMessageType.QUOTE_UPDATE:
        handleMarketUpdate(message);
        break;
      
      case WebSocketMessageType.NOTIFICATION:
        handleNotification(message);
        break;
      
      case WebSocketMessageType.AUTH_SUCCESS:
        console.log('WebSocket authentication successful');
        break;
      
      case WebSocketMessageType.AUTH_FAILED:
        console.error('WebSocket authentication failed');
        break;
      
      default:
        console.log('Unhandled WebSocket message:', message);
    }
  }

  // ========================================================================
  // MESSAGE HANDLERS
  // ========================================================================

  function handleSignalUpdate(message: WebSocketMessage) {
    const signal = message.data.signal as UnifiedSignal;
    
    if (!signal) return;

    setLatestSignals(prev => {
      const existingIndex = prev.findIndex(s => s.id === signal.id);
      
      if (existingIndex >= 0) {
        // Update existing signal
        const updated = [...prev];
        updated[existingIndex] = signal;
        return updated;
      } else {
        // Add new signal (keep only latest 50)
        return [signal, ...prev].slice(0, 50);
      }
    });
  }

  function handleSignalDeleted(message: WebSocketMessage) {
    const signalId = message.data.signalId;
    
    if (!signalId) return;

    setLatestSignals(prev => prev.filter(s => s.id !== signalId));
  }

  function handleMarketUpdate(message: WebSocketMessage) {
    const { symbol, quote, marketData } = message.data;
    
    if (quote) {
      setLatestQuotes(prev => new Map(prev.set(symbol, quote)));
    }
    
    if (marketData) {
      setLatestMarketData(prev => new Map(prev.set(symbol, marketData)));
    }
  }

  function handleNotification(message: WebSocketMessage) {
    const notification = message.data as UserNotification;
    
    if (!notification) return;

    setNotifications(prev => [notification, ...prev].slice(0, 100)); // Keep latest 100
  }

  // ========================================================================
  // SUBSCRIPTION METHODS
  // ========================================================================

  const subscribeToSignals = (symbols?: string[]) => {
    const channels = [WebSocketChannel.SIGNALS];
    
    if (userId) {
      channels.push(WebSocketChannel.SIGNALS_USER);
    }
    
    webSocket.subscribe(channels, symbols);
  };

  const unsubscribeFromSignals = () => {
    const channels = [WebSocketChannel.SIGNALS, WebSocketChannel.SIGNALS_USER];
    webSocket.unsubscribe(channels);
  };

  const subscribeToMarketData = (symbols: string[]) => {
    webSocket.subscribe([WebSocketChannel.MARKET_DATA, WebSocketChannel.QUOTES], symbols);
  };

  const unsubscribeFromMarketData = (symbols: string[]) => {
    webSocket.unsubscribe([WebSocketChannel.MARKET_DATA, WebSocketChannel.QUOTES]);
  };

  const subscribeToNotifications = () => {
    if (userId) {
      webSocket.subscribe([WebSocketChannel.USER_NOTIFICATIONS]);
    }
  };

  const unsubscribeFromNotifications = () => {
    webSocket.unsubscribe([WebSocketChannel.USER_NOTIFICATIONS]);
  };

  const markNotificationAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, read: true, readAt: new Date().toISOString() } : n
      )
    );
    
    // Send read status to server
    webSocket.sendTypedMessage(WebSocketMessageType.NOTIFICATION, {
      action: 'mark_read',
      notificationId
    });
  };

  // ========================================================================
  // COMPUTED VALUES
  // ========================================================================

  const connectionStatus: 'connected' | 'connecting' | 'disconnected' | 'error' = 
    webSocket.connectionState.connected ? 'connected' :
    webSocket.connectionState.connecting ? 'connecting' :
    webSocket.connectionState.error ? 'error' : 'disconnected';

  const connectionError = webSocket.connectionState.error || null;

  // ========================================================================
  // EFFECTS
  // ========================================================================

  // Auto-subscribe to user-specific channels when connected and authenticated
  useEffect(() => {
    if (webSocket.isConnected && userId) {
      subscribeToSignals();
      subscribeToNotifications();
    }
  }, [webSocket.isConnected, userId]);

  // ========================================================================
  // CONTEXT VALUE
  // ========================================================================

  const contextValue: WebSocketContextValue = {
    // WebSocket hook values
    ...webSocket,
    
    // Real-time data
    latestSignals,
    latestQuotes,
    latestMarketData,
    notifications,
    
    // Signal-specific methods
    subscribeToSignals,
    unsubscribeFromSignals,
    
    // Market data methods
    subscribeToMarketData,
    unsubscribeFromMarketData,
    
    // Notification methods
    subscribeToNotifications,
    unsubscribeFromNotifications,
    markNotificationAsRead,
    
    // Connection status
    connectionStatus,
    connectionError
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
}

// ============================================================================
// CONTEXT HOOK
// ============================================================================

export function useWebSocketContext(): WebSocketContextValue {
  const context = useContext(WebSocketContext);
  
  if (!context) {
    throw new Error('useWebSocketContext must be used within a WebSocketProvider');
  }
  
  return context;
}

// ============================================================================
// CONVENIENCE HOOKS
// ============================================================================

/**
 * Hook for subscribing to real-time signals
 */
export function useRealTimeSignals(symbols?: string[]) {
  const { latestSignals, subscribeToSignals, unsubscribeFromSignals, isConnected } = useWebSocketContext();
  
  useEffect(() => {
    if (isConnected) {
      subscribeToSignals(symbols);
    }
    
    return () => {
      unsubscribeFromSignals();
    };
  }, [isConnected, symbols]);
  
  return latestSignals;
}

/**
 * Hook for subscribing to real-time market data
 */
export function useRealTimeMarketData(symbols: string[]) {
  const { 
    latestQuotes, 
    latestMarketData, 
    subscribeToMarketData, 
    unsubscribeFromMarketData, 
    isConnected 
  } = useWebSocketContext();
  
  useEffect(() => {
    if (isConnected && symbols.length > 0) {
      subscribeToMarketData(symbols);
    }
    
    return () => {
      if (symbols.length > 0) {
        unsubscribeFromMarketData(symbols);
      }
    };
  }, [isConnected, symbols]);
  
  return { quotes: latestQuotes, marketData: latestMarketData };
}

/**
 * Hook for real-time notifications
 */
export function useRealTimeNotifications() {
  const { 
    notifications, 
    subscribeToNotifications, 
    unsubscribeFromNotifications, 
    markNotificationAsRead,
    isConnected 
  } = useWebSocketContext();
  
  useEffect(() => {
    if (isConnected) {
      subscribeToNotifications();
    }
    
    return () => {
      unsubscribeFromNotifications();
    };
  }, [isConnected]);
  
  const unreadCount = notifications.filter(n => !n.read).length;
  
  return {
    notifications,
    unreadCount,
    markAsRead: markNotificationAsRead
  };
}

export default WebSocketProvider;
