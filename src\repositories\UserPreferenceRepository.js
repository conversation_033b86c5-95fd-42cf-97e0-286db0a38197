/**
 * User Preference Repository for MongoDB
 * 
 * This repository handles all user preference-related database operations.
 */

const BaseRepository = require('./BaseRepository');
const { ObjectId } = require('mongodb');
const logger = require('../utils/logger');

class UserPreferenceRepository extends BaseRepository {
  /**
   * Create a new UserPreferenceRepository instance
   * @param {Object} db - MongoDB database instance
   */
  constructor(db) {
    super(db, 'userPreferences');
    logger.debug('UserPreferenceRepository initialized');
  }

  /**
   * Find preferences by user ID
   * @param {string} userId - User ID
   * @returns {Promise<Object|null>} User preferences or null if not found
   */
  async findByUserId(userId) {
    try {
      const userObjectId = this._toObjectId(userId);
      return await this.findOne({ userId: userObjectId });
    } catch (error) {
      logger.error('Error finding preferences by user ID:', error);
      throw error;
    }
  }

  /**
   * Save user preferences
   * @param {string} userId - User ID
   * @param {Object} preferences - User preferences
   * @returns {Promise<Object>} Saved preferences
   */
  async savePreferences(userId, preferences) {
    try {
      const userObjectId = this._toObjectId(userId);
      
      // Find existing preferences
      const existingPrefs = await this.findOne({ userId: userObjectId });
      
      if (existingPrefs) {
        // Update existing preferences
        await this.updateById(existingPrefs._id, preferences);
        return { ...existingPrefs, ...preferences, updatedAt: new Date() };
      } else {
        // Create new preferences
        const prefsToInsert = {
          userId: userObjectId,
          ...preferences,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        return await this.insertOne(prefsToInsert);
      }
    } catch (error) {
      logger.error('Error saving user preferences:', error);
      throw error;
    }
  }

  /**
   * Get default preferences
   * @returns {Object} Default preferences
   */
  getDefaultPreferences() {
    return {
      theme: 'light',
      language: 'en',
      notifications: {
        signals: true,
        news: true,
        economic: true
      },
      trading: {
        defaultTimeframe: 'H1',
        defaultSymbol: 'EURUSD',
        defaultLotSize: 0.01,
        riskPercentage: 2
      },
      charts: {
        showVolume: true,
        showGrid: true,
        defaultIndicators: ['MA', 'RSI']
      },
      updatedAt: new Date()
    };
  }

  /**
   * Get or create user preferences
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User preferences
   */
  async getOrCreatePreferences(userId) {
    try {
      const userObjectId = this._toObjectId(userId);
      
      // Find existing preferences
      const existingPrefs = await this.findOne({ userId: userObjectId });
      
      if (existingPrefs) {
        return existingPrefs;
      } else {
        // Create default preferences
        const defaultPrefs = {
          userId: userObjectId,
          ...this.getDefaultPreferences(),
          createdAt: new Date()
        };
        
        return await this.insertOne(defaultPrefs);
      }
    } catch (error) {
      logger.error('Error getting or creating user preferences:', error);
      throw error;
    }
  }

  /**
   * Update specific preference
   * @param {string} userId - User ID
   * @param {string} key - Preference key (e.g., 'theme', 'language')
   * @param {any} value - Preference value
   * @returns {Promise<Object>} Update result
   */
  async updatePreference(userId, key, value) {
    try {
      const userObjectId = this._toObjectId(userId);
      
      // Find existing preferences
      const existingPrefs = await this.findOne({ userId: userObjectId });
      
      if (existingPrefs) {
        // Update specific preference
        const updateData = { updatedAt: new Date() };
        updateData[key] = value;
        
        return await this.updateById(existingPrefs._id, updateData);
      } else {
        // Create default preferences with the specified preference
        const defaultPrefs = this.getDefaultPreferences();
        defaultPrefs[key] = value;
        
        const prefsToInsert = {
          userId: userObjectId,
          ...defaultPrefs,
          createdAt: new Date()
        };
        
        return await this.insertOne(prefsToInsert);
      }
    } catch (error) {
      logger.error('Error updating specific preference:', error);
      throw error;
    }
  }
}

module.exports = UserPreferenceRepository;
