import mongoose from 'mongoose';

const cotHistorySchema = new mongoose.Schema({
  symbol: { type: String, required: true },
  reportDate: { type: Date, required: true },
  commercials: {
    long: Number,
    short: Number
  },
  nonCommercials: {
    long: Number,
    short: Number
  },
  openInterest: Number
});

const COTHistory = mongoose.model('COTHistory', cotHistorySchema);
export default COTHistory; 