/**
 * WebSocket React Hook
 * 
 * Custom React hook for managing WebSocket connections with TypeScript support.
 * Provides automatic connection management, reconnection logic, and message handling.
 * 
 * Features:
 * - Automatic connection and reconnection
 * - Type-safe message handling
 * - Connection state management
 * - Message queuing during disconnections
 * - Error handling and recovery
 * - Subscription management
 * 
 * @version 1.0.0
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import {
  WebSocketMessage,
  WebSocketMessageType,
  WebSocketConnectionState,
  WebSocketChannel,
  createWebSocketMessage,
  isWebSocketMessage
} from '../types/websocket';
import { formatTimestamp } from '../types/common';

// ============================================================================
// HOOK CONFIGURATION INTERFACE
// ============================================================================

export interface UseWebSocketConfig {
  url: string;
  protocols?: string[];
  reconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  timeout?: number;
  onConnect?: () => void;
  onDisconnect?: (reason: string) => void;
  onError?: (error: Event) => void;
  onMessage?: (message: WebSocketMessage) => void;
}

// ============================================================================
// HOOK RETURN INTERFACE
// ============================================================================

export interface UseWebSocketReturn {
  // Connection state
  connectionState: WebSocketConnectionState;
  isConnected: boolean;
  isConnecting: boolean;
  
  // Message handling
  sendMessage: (message: WebSocketMessage) => boolean;
  sendTypedMessage: <T>(type: WebSocketMessageType, data: T) => boolean;
  
  // Subscription management
  subscribe: (channels: string[], symbols?: string[]) => void;
  unsubscribe: (channels: string[]) => void;
  subscriptions: Set<string>;
  
  // Connection management
  connect: () => void;
  disconnect: () => void;
  reconnect: () => void;
  
  // Metrics
  messagesSent: number;
  messagesReceived: number;
  lastMessage: WebSocketMessage | null;
}

// ============================================================================
// WEBSOCKET HOOK IMPLEMENTATION
// ============================================================================

export function useWebSocket(config: UseWebSocketConfig): UseWebSocketReturn {
  // Connection state
  const [connectionState, setConnectionState] = useState<WebSocketConnectionState>({
    connected: false,
    connecting: false,
    error: undefined,
    lastConnected: undefined,
    reconnectAttempts: 0
  });

  // Message handling state
  const [messagesSent, setMessagesSent] = useState(0);
  const [messagesReceived, setMessagesReceived] = useState(0);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [subscriptions, setSubscriptions] = useState<Set<string>>(new Set());

  // Refs for persistent values
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const messageQueueRef = useRef<WebSocketMessage[]>([]);
  const reconnectAttemptsRef = useRef(0);

  // Configuration with defaults
  const configWithDefaults = {
    reconnect: true,
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000,
    timeout: 60000,
    ...config
  };

  // ========================================================================
  // CONNECTION MANAGEMENT
  // ========================================================================

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setConnectionState(prev => ({ ...prev, connecting: true, error: undefined }));

    try {
      const ws = new WebSocket(configWithDefaults.url, configWithDefaults.protocols);
      wsRef.current = ws;

      // Connection opened
      ws.onopen = () => {
        setConnectionState({
          connected: true,
          connecting: false,
          error: undefined,
          lastConnected: formatTimestamp(),
          reconnectAttempts: 0
        });

        reconnectAttemptsRef.current = 0;

        // Send queued messages
        while (messageQueueRef.current.length > 0) {
          const queuedMessage = messageQueueRef.current.shift();
          if (queuedMessage) {
            ws.send(JSON.stringify(queuedMessage));
          }
        }

        // Start heartbeat
        startHeartbeat();

        // Call onConnect callback
        configWithDefaults.onConnect?.();
      };

      // Message received
      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          
          if (isWebSocketMessage(message)) {
            setLastMessage(message);
            setMessagesReceived(prev => prev + 1);

            // Handle system messages
            handleSystemMessage(message);

            // Call onMessage callback
            configWithDefaults.onMessage?.(message);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      // Connection closed
      ws.onclose = (event) => {
        setConnectionState(prev => ({
          ...prev,
          connected: false,
          connecting: false,
          error: event.reason || 'Connection closed'
        }));

        stopHeartbeat();

        // Call onDisconnect callback
        configWithDefaults.onDisconnect?.(event.reason || 'Connection closed');

        // Attempt reconnection if enabled
        if (configWithDefaults.reconnect && reconnectAttemptsRef.current < configWithDefaults.maxReconnectAttempts) {
          scheduleReconnect();
        }
      };

      // Connection error
      ws.onerror = (error) => {
        setConnectionState(prev => ({
          ...prev,
          connected: false,
          connecting: false,
          error: 'Connection error'
        }));

        // Call onError callback
        configWithDefaults.onError?.(error);
      };

    } catch (error) {
      setConnectionState(prev => ({
        ...prev,
        connected: false,
        connecting: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  }, [configWithDefaults]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    stopHeartbeat();

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    setConnectionState({
      connected: false,
      connecting: false,
      error: undefined,
      lastConnected: undefined,
      reconnectAttempts: 0
    });

    reconnectAttemptsRef.current = 0;
  }, []);

  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(connect, 100);
  }, [connect, disconnect]);

  // ========================================================================
  // RECONNECTION LOGIC
  // ========================================================================

  const scheduleReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    reconnectAttemptsRef.current++;
    
    setConnectionState(prev => ({
      ...prev,
      reconnectAttempts: reconnectAttemptsRef.current
    }));

    const delay = Math.min(
      configWithDefaults.reconnectInterval * Math.pow(2, reconnectAttemptsRef.current - 1),
      30000 // Max 30 seconds
    );

    reconnectTimeoutRef.current = setTimeout(() => {
      connect();
    }, delay);
  }, [connect, configWithDefaults.reconnectInterval]);

  // ========================================================================
  // HEARTBEAT MANAGEMENT
  // ========================================================================

  const startHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }

    heartbeatIntervalRef.current = setInterval(() => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        const heartbeatMessage = createWebSocketMessage(
          WebSocketMessageType.HEARTBEAT,
          { timestamp: formatTimestamp() }
        );
        wsRef.current.send(JSON.stringify(heartbeatMessage));
      }
    }, configWithDefaults.heartbeatInterval);
  }, [configWithDefaults.heartbeatInterval]);

  const stopHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  }, []);

  // ========================================================================
  // MESSAGE HANDLING
  // ========================================================================

  const sendMessage = useCallback((message: WebSocketMessage): boolean => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      try {
        wsRef.current.send(JSON.stringify(message));
        setMessagesSent(prev => prev + 1);
        return true;
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
        return false;
      }
    } else {
      // Queue message for later sending
      messageQueueRef.current.push(message);
      return false;
    }
  }, []);

  const sendTypedMessage = useCallback(<T,>(type: WebSocketMessageType, data: T): boolean => {
    const message = createWebSocketMessage(type, data);
    return sendMessage(message);
  }, [sendMessage]);

  const handleSystemMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case WebSocketMessageType.SUBSCRIPTION_CONFIRMED:
        if (message.data.channels) {
          setSubscriptions(new Set(message.data.channels));
        }
        break;
      
      case WebSocketMessageType.PONG:
        // Handle pong response
        break;
      
      case WebSocketMessageType.ERROR:
        console.error('WebSocket error:', message.data);
        break;
    }
  }, []);

  // ========================================================================
  // SUBSCRIPTION MANAGEMENT
  // ========================================================================

  const subscribe = useCallback((channels: string[], symbols?: string[]) => {
    const subscribeMessage = createWebSocketMessage(
      WebSocketMessageType.SUBSCRIBE,
      { channels, symbols }
    );
    sendMessage(subscribeMessage);
  }, [sendMessage]);

  const unsubscribe = useCallback((channels: string[]) => {
    const unsubscribeMessage = createWebSocketMessage(
      WebSocketMessageType.UNSUBSCRIBE,
      { channels }
    );
    sendMessage(unsubscribeMessage);
  }, [sendMessage]);

  // ========================================================================
  // EFFECTS
  // ========================================================================

  // Auto-connect on mount
  useEffect(() => {
    connect();
    
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      stopHeartbeat();
    };
  }, [stopHeartbeat]);

  // ========================================================================
  // RETURN HOOK INTERFACE
  // ========================================================================

  return {
    // Connection state
    connectionState,
    isConnected: connectionState.connected,
    isConnecting: connectionState.connecting,
    
    // Message handling
    sendMessage,
    sendTypedMessage,
    
    // Subscription management
    subscribe,
    unsubscribe,
    subscriptions,
    
    // Connection management
    connect,
    disconnect,
    reconnect,
    
    // Metrics
    messagesSent,
    messagesReceived,
    lastMessage
  };
}

export default useWebSocket;
