/**
 * Input Validation Middleware
 * 
 * This middleware provides validation for API request payloads using express-validator.
 */

const { body, query, param, validationResult } = require('express-validator');
const logger = require('../utils/logger');

// Format error response for validation errors
const formatErrorResponse = (errors) => {
  return {
    status: 'error',
    message: 'Validation failed',
    errors: errors.array().map(error => ({
      field: error.path,
      message: error.msg
    }))
  };
};

// Middleware to check for validation errors
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    logger.warn(`Validation errors: ${JSON.stringify(errors.array())}`);
    return res.status(400).json(formatErrorResponse(errors));
  }
  
  next();
};

// Signal creation/update validation
const signalValidationRules = [
  body('symbol')
    .trim()
    .notEmpty().withMessage('Symbol is required')
    .isString().withMessage('Symbol must be a string')
    .isLength({ min: 1, max: 20 }).withMessage('Symbol must be between 1 and 20 characters'),
  
  body('type')
    .trim()
    .notEmpty().withMessage('Type is required')
    .isIn(['buy', 'sell']).withMessage('Type must be either "buy" or "sell"'),
  
  body('entryPrice')
    .notEmpty().withMessage('Entry price is required')
    .isFloat({ min: 0 }).withMessage('Entry price must be a positive number'),
  
  body('stopLoss')
    .notEmpty().withMessage('Stop loss is required')
    .isFloat({ min: 0 }).withMessage('Stop loss must be a positive number'),
  
  body('takeProfit')
    .notEmpty().withMessage('Take profit is required')
    .isFloat({ min: 0 }).withMessage('Take profit must be a positive number'),
  
  body('timeframe')
    .trim()
    .notEmpty().withMessage('Timeframe is required')
    .isIn(['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN']).withMessage('Invalid timeframe'),
  
  body('analysis')
    .optional()
    .isString().withMessage('Analysis must be a string')
    .isLength({ max: 1000 }).withMessage('Analysis must be less than 1000 characters'),
  
  body('status')
    .optional()
    .isIn(['active', 'executed', 'expired']).withMessage('Status must be either "active", "executed", or "expired"')
];

// Signal filter validation
const signalFilterValidationRules = [
  query('symbol')
    .optional()
    .isString().withMessage('Symbol must be a string'),
  
  query('type')
    .optional()
    .isIn(['buy', 'sell']).withMessage('Type must be either "buy" or "sell"'),
  
  query('timeframe')
    .optional()
    .isIn(['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN']).withMessage('Invalid timeframe'),
  
  query('status')
    .optional()
    .isIn(['active', 'executed', 'expired']).withMessage('Status must be either "active", "executed", or "expired"')
];

// Signal ID validation
const signalIdValidationRules = [
  param('id')
    .notEmpty().withMessage('Signal ID is required')
    .isMongoId().withMessage('Invalid signal ID format')
];

// Authentication validation
const authValidationRules = [
  body('email')
    .trim()
    .notEmpty().withMessage('Email is required')
    .isEmail().withMessage('Invalid email format'),
  
  body('password')
    .trim()
    .notEmpty().withMessage('Password is required')
    .isLength({ min: 8 }).withMessage('Password must be at least 8 characters')
];

// Registration validation
const registrationValidationRules = [
  ...authValidationRules,
  
  body('username')
    .trim()
    .notEmpty().withMessage('Username is required')
    .isLength({ min: 3, max: 30 }).withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/).withMessage('Username can only contain letters, numbers, and underscores'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Password confirmation does not match password');
      }
      return true;
    })
];

// Market data validation
const marketDataValidationRules = [
  query('symbol')
    .trim()
    .notEmpty().withMessage('Symbol is required')
    .isString().withMessage('Symbol must be a string'),
  
  query('interval')
    .trim()
    .notEmpty().withMessage('Interval is required')
    .isIn(['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1']).withMessage('Invalid interval')
];

module.exports = {
  validateRequest,
  signalValidationRules,
  signalFilterValidationRules,
  signalIdValidationRules,
  authValidationRules,
  registrationValidationRules,
  marketDataValidationRules
}; 