import React, { useState } from 'react';
import { exportToPDF, exportToExcel, exportToCSV } from '../../../utils/exportUtils';

/**
 * ExportResults Component
 *
 * Component for exporting backtest results to CSV or PDF
 */
const ExportResults = ({ results }) => {
  const [exportFormat, setExportFormat] = useState('pdf');
  const [exportContent, setExportContent] = useState(['summary', 'patterns', 'trades']);
  const [isExporting, setIsExporting] = useState(false);

  // Get config from results
  const getConfig = () => {
    return {
      symbol: results.symbol,
      timeframe: results.timeframe,
      startDate: results.startDate,
      endDate: results.endDate,
      initialCapital: results.initialCapital,
      positionSize: results.positionSize || 'fixed',
      fixedSize: results.fixedSize || 0.1,
      percentageSize: results.percentageSize || 2,
      riskPercentage: results.riskPercentage || 1,
      stopLoss: results.stopLoss || 50,
      takeProfit: results.takeProfit || 100,
      patternTypes: results.patternTypes || ['bullish', 'bearish'],
      minPatternSignificance: results.minPatternSignificance || 5,
      entryDelay: results.entryDelay || 1,
      exitStrategy: results.exitStrategy || 'take_profit_stop_loss'
    };
  };

  // Handle export format change
  const handleFormatChange = (e) => {
    setExportFormat(e.target.value);
  };

  // Handle export content change
  const handleContentChange = (e) => {
    const { value, checked } = e.target;

    if (checked) {
      setExportContent([...exportContent, value]);
    } else {
      setExportContent(exportContent.filter(item => item !== value));
    }
  };

  // Export results
  const exportResults = async () => {
    setIsExporting(true);

    try {
      const config = getConfig();
      let blob;

      switch (exportFormat) {
        case 'csv':
          blob = exportToCSV(results);
          break;
        case 'excel':
          blob = exportToExcel(results, config);
          break;
        case 'pdf':
        default:
          blob = exportToPDF(results, config);
          break;
      }

      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${results.symbol}_${results.timeframe}_backtest_results.${exportFormat}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting results:', error);
      alert('Failed to export results. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };



  return (
    <div className="export-results">
      <h3 className="text-lg font-medium mb-4">Export Results</h3>

      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Export Format</label>
            <div className="flex space-x-4">
              <div className="flex items-center">
                <input
                  type="radio"
                  id="format-pdf"
                  name="exportFormat"
                  value="pdf"
                  checked={exportFormat === 'pdf'}
                  onChange={handleFormatChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <label htmlFor="format-pdf" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  PDF
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="radio"
                  id="format-excel"
                  name="exportFormat"
                  value="excel"
                  checked={exportFormat === 'excel'}
                  onChange={handleFormatChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <label htmlFor="format-excel" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Excel
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="radio"
                  id="format-csv"
                  name="exportFormat"
                  value="csv"
                  checked={exportFormat === 'csv'}
                  onChange={handleFormatChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <label htmlFor="format-csv" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  CSV
                </label>
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Content to Include</label>
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="content-summary"
                  value="summary"
                  checked={exportContent.includes('summary')}
                  onChange={handleContentChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="content-summary" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Summary
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="content-patterns"
                  value="patterns"
                  checked={exportContent.includes('patterns')}
                  onChange={handleContentChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="content-patterns" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Pattern Performance
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="content-trades"
                  value="trades"
                  checked={exportContent.includes('trades')}
                  onChange={handleContentChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="content-trades" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Trades
                </label>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={exportResults}
            disabled={isExporting || exportContent.length === 0}
          >
            {isExporting ? 'Exporting...' : `Export as ${exportFormat.toUpperCase()}`}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExportResults;
