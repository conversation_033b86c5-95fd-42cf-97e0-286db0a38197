/**
 * Trading Signals Routes for Trading Signals App
 */

const axios = require('axios');
const logger = require('../utils/logger');

module.exports = function(app, apiCache, API_CONFIG) {
  // Get trading signals for a symbol
  app.get('/api/trading-signals/:symbol', (req, res) => {
    const symbol = req.params.symbol;
    
    // Create cache key
    const cacheKey = `trading_signals_${symbol}`;
    
    // Check cache first
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      return res.json(cachedData);
    }
    
    // Mock trading signals data
    const signals = [
      {
        type: 'buy',
        entryPoint: '1.0880',
        stopLoss: '1.0850',
        takeProfit: '1.0930',
        analysis: 'Price broke above the 200 EMA with increasing volume. RSI shows bullish momentum at 65.',
        strength: 'strong',
        timeframe: 'H1'
      },
      {
        type: 'sell',
        entryPoint: '1.0840',
        stopLoss: '1.0870',
        takeProfit: '1.0790',
        analysis: 'Alternative scenario: If price fails to hold above 1.0850 support, expect a reversal. MACD showing potential bearish crossover.',
        strength: 'moderate',
        timeframe: 'H1'
      }
    ];
    
    // Cache the response
    apiCache.set(cacheKey, { signals }, 300); // 5 minutes TTL
    
    return res.json({ signals });
  });
  
  // Analyze a symbol for trading signals
  app.get('/api/analyze/:symbol/:timeframe', async (req, res) => {
    try {
      const symbol = req.params.symbol;
      const timeframe = req.params.timeframe;
      
      // Create cache key
      const cacheKey = `analyze_${symbol}_${timeframe}`;
      
      // Check cache first
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        return res.json(cachedData);
      }
      
      // Get market data (either real or mock)
      let marketData;
      try {
        // Try to get market data from the market-data endpoint
        const port = process.env.PORT || 3000;
        const response = await axios.get(`http://localhost:${port}/api/market-data/${symbol}?interval=${timeframe}`);
        marketData = response.data;
      } catch (error) {
        logger.warn(`Failed to get market data for ${symbol}, using mock data:`, error.message);
        // Use mock data as fallback
        marketData = getMockMarketData(symbol, timeframe);
      }
      
      // Calculate entry, target and stop prices
      const currentPrice = marketData.currentPrice;
      const sentiment = marketData.sentiment;
      
      // Determine if bullish or bearish based on sentiment
      const isBullish = sentiment > 50;
      
      // Calculate entry, target and stop prices
      const entryPrice = currentPrice;
      const targetPrice = isBullish ? currentPrice * 1.02 : currentPrice * 0.98;
      const stopLoss = isBullish ? currentPrice * 0.99 : currentPrice * 1.01;
      
      // Create trade analysis
      const trade = {
        symbol: symbol,
        entry_price: entryPrice,
        target_price: targetPrice,
        stop_loss: stopLoss,
        confidence: sentiment / 100,
        sentiment: sentiment > 65 ? 'Bullish' : (sentiment < 35 ? 'Bearish' : 'Neutral')
      };
      
      // Cache the response
      apiCache.set(cacheKey, trade, 300); // 5 minutes TTL
      
      res.json(trade);
    } catch (error) {
      logger.error('Analyze API error:', error.message);
      res.status(500).json({ error: 'Failed to analyze trade data' });
    }
  });
  
  // Helper function to get mock market data
  function getMockMarketData(symbol, timeframe) {
    // Set starting price based on symbol
    let price;
    let dailyChange;
    let sentiment;
    
    switch (symbol) {
      case 'EURUSD':
        price = 1.0875;
        dailyChange = 0.25;
        sentiment = 65;
        break;
      case 'GBPUSD':
        price = 1.2650;
        dailyChange = -0.15;
        sentiment = 45;
        break;
      case 'USDJPY':
        price = 149.50;
        dailyChange = 0.10;
        sentiment = 55;
        break;
      case 'XAUUSD':
        price = 2340.50;
        dailyChange = 0.45;
        sentiment = 75;
        break;
      case 'XAGUSD':
        price = 27.85;
        dailyChange = 0.30;
        sentiment = 70;
        break;
      case 'USOIL':
        price = 78.35;
        dailyChange = -0.65;
        sentiment = 40;
        break;
      case 'BTCUSD':
        price = 62450.25;
        dailyChange = 1.25;
        sentiment = 80;
        break;
      case 'ETHUSD':
        price = 3050.75;
        dailyChange = 0.95;
        sentiment = 75;
        break;
      default:
        price = 100.00;
        dailyChange = 0.00;
        sentiment = 50;
    }
    
    return {
      currentPrice: price,
      dailyChange,
      sentiment
    };
  }
};
