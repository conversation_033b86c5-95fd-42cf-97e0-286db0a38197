/**
 * Repository Factory for MongoDB
 * 
 * This factory creates and manages repository instances.
 */

const UserRepository = require('./UserRepository');
const SignalRepository = require('./SignalRepository');
const MarketDataRepository = require('./MarketDataRepository');
const UserPreferenceRepository = require('./UserPreferenceRepository');
const logger = require('../utils/logger');

class RepositoryFactory {
  /**
   * Create a new RepositoryFactory instance
   * @param {Object} db - MongoDB database instance
   */
  constructor(db) {
    this.db = db;
    this.repositories = {};
    logger.debug('RepositoryFactory initialized');
  }

  /**
   * Get a repository instance
   * @param {string} name - Repository name
   * @returns {Object} Repository instance
   */
  getRepository(name) {
    if (!this.repositories[name]) {
      switch (name) {
        case 'user':
          this.repositories[name] = new UserRepository(this.db);
          break;
        case 'signal':
          this.repositories[name] = new SignalRepository(this.db);
          break;
        case 'marketData':
          this.repositories[name] = new MarketDataRepository(this.db);
          break;
        case 'userPreference':
          this.repositories[name] = new UserPreferenceRepository(this.db);
          break;
        default:
          throw new Error(`Unknown repository: ${name}`);
      }
    }
    
    return this.repositories[name];
  }

  /**
   * Get user repository
   * @returns {UserRepository} User repository
   */
  getUserRepository() {
    return this.getRepository('user');
  }

  /**
   * Get signal repository
   * @returns {SignalRepository} Signal repository
   */
  getSignalRepository() {
    return this.getRepository('signal');
  }

  /**
   * Get market data repository
   * @returns {MarketDataRepository} Market data repository
   */
  getMarketDataRepository() {
    return this.getRepository('marketData');
  }

  /**
   * Get user preference repository
   * @returns {UserPreferenceRepository} User preference repository
   */
  getUserPreferenceRepository() {
    return this.getRepository('userPreference');
  }
}

// Singleton instance
let instance = null;

/**
 * Initialize the repository factory
 * @param {Object} db - MongoDB database instance
 * @returns {RepositoryFactory} Repository factory instance
 */
function initialize(db) {
  instance = new RepositoryFactory(db);
  return instance;
}

/**
 * Get the repository factory instance
 * @returns {RepositoryFactory} Repository factory instance
 */
function getInstance() {
  if (!instance) {
    throw new Error('RepositoryFactory not initialized');
  }
  return instance;
}

module.exports = {
  initialize,
  getInstance
};
