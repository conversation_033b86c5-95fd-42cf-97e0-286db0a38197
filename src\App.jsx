import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import MainLayout from './components/MainLayout.jsx';
import AlertsManager from './components/notifications/AlertsManager.jsx';

// Lazy-loaded components
const Dashboard = lazy(() => import('./features/dashboard/components/Dashboard.jsx'));
const TradingSignals = lazy(() => import('./features/signals/components/TradingSignals.jsx'));
const TechnicalAnalysis = lazy(() => import('./features/analysis/components/TechnicalAnalysis.jsx'));
const EconomicCalendar = lazy(() => import('./features/calendar/components/EconomicCalendar.jsx'));
const MarketNews = lazy(() => import('./features/news/components/MarketNews.jsx'));
const Backtesting = lazy(() => import('./features/backtesting/components/Backtesting.jsx'));
const PatternAlerts = lazy(() => import('./features/alerts/components/PatternAlerts.jsx'));
const UserSettings = lazy(() => import('./features/user/components/UserSettings.jsx'));
const FeedbackWidget = lazy(() => import('./components/FeedbackWidget.jsx'));
const ChangelogWidget = lazy(() => import('./components/ChangelogWidget.jsx'));

// Loading fallback
const LoadingFallback = () => (
  <div className="flex items-center justify-center h-screen">
    <div className="animate-pulse flex flex-col items-center">
      <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      <p className="mt-4 text-lg text-gray-700">Loading...</p>
    </div>
  </div>
);

export default function App() {
  return (
    <Router>
      <Suspense fallback={<LoadingFallback />}>
        <MainLayout>
          <Routes>
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/signals" element={<TradingSignals />} />
            <Route path="/analysis" element={<TechnicalAnalysis />} />
            <Route path="/calendar" element={<EconomicCalendar />} />
            <Route path="/news" element={<MarketNews />} />
            <Route path="/backtesting" element={<Backtesting />} />
            <Route path="/alerts" element={<PatternAlerts />} />
            <Route path="/settings" element={<UserSettings />} />
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </MainLayout>

        {/* These widgets are loaded asynchronously but don't block the main content */}
        <Suspense fallback={null}>
          <FeedbackWidget />
        </Suspense>

        <Suspense fallback={null}>
          <ChangelogWidget />
        </Suspense>

        {/* Alerts Manager for pattern and price notifications */}
        <Suspense fallback={null}>
          <AlertsManager />
        </Suspense>
      </Suspense>
    </Router>
  );
}