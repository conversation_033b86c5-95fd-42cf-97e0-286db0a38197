import Notification from '../models/Notification.js';

class NotificationService {
  async createNotification({ user, type = 'info', message, link }) {
    return Notification.create({ user, type, message, link });
  }

  async getUserNotifications(userId, limit = 20) {
    return Notification.find({ user: userId }).sort({ createdAt: -1 }).limit(limit);
  }

  async markAsRead(notificationId, userId) {
    return Notification.findOneAndUpdate({ _id: notificationId, user: userId }, { read: true }, { new: true });
  }

  async markAllAsRead(userId) {
    return Notification.updateMany({ user: userId, read: false }, { read: true });
  }

  async deleteNotification(notificationId, userId) {
    return Notification.findOneAndDelete({ _id: notificationId, user: userId });
  }
}

export default new NotificationService();
