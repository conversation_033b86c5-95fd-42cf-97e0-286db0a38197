/**
 * API Documentation Routes for Trading Signals App
 * 
 * Provides comprehensive API documentation using Swagger/OpenAPI
 * with interactive testing interface and detailed examples.
 */

const express = require('express');
const { swaggerSpec, swaggerUi, swaggerUiOptions } = require('../services/swaggerDocumentationService');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   - name: Authentication
 *     description: User authentication and authorization
 *   - name: Market Data
 *     description: Real-time and historical market data
 *   - name: Trading Signals
 *     description: AI-generated trading signals and analysis
 *   - name: Technical Analysis
 *     description: Technical indicators and chart analysis
 *   - name: Economic Calendar
 *     description: Economic events and news
 *   - name: User Management
 *     description: User profile and preferences
 *   - name: Performance
 *     description: System performance and metrics
 */

/**
 * Serve Swagger UI documentation
 */
router.use('/api-docs', swaggerUi.serve);
router.get('/api-docs', swaggerUi.setup(swaggerSpec, swaggerUiOptions));

/**
 * @swagger
 * /api/docs/spec:
 *   get:
 *     summary: Get OpenAPI specification
 *     description: Returns the complete OpenAPI specification in JSON format
 *     tags: [Documentation]
 *     responses:
 *       200:
 *         description: OpenAPI specification
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 */
router.get('/spec', (req, res) => {
  res.json(swaggerSpec);
});

/**
 * @swagger
 * /api/docs/health:
 *   get:
 *     summary: Documentation service health check
 *     description: Check if the documentation service is running properly
 *     tags: [Documentation]
 *     responses:
 *       200:
 *         description: Service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: healthy
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 version:
 *                   type: string
 *                   example: 2.0.0
 */
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: swaggerSpec.info.version,
    endpoints: {
      documentation: '/api/docs/api-docs',
      specification: '/api/docs/spec',
      health: '/api/docs/health'
    }
  });
});

/**
 * @swagger
 * /api/docs/examples:
 *   get:
 *     summary: Get API usage examples
 *     description: Returns comprehensive examples for all API endpoints
 *     tags: [Documentation]
 *     responses:
 *       200:
 *         description: API usage examples
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 */
router.get('/examples', (req, res) => {
  const examples = {
    authentication: {
      login: {
        method: 'POST',
        url: '/api/auth/login',
        headers: {
          'Content-Type': 'application/json'
        },
        body: {
          email: '<EMAIL>',
          password: 'securePassword123'
        },
        response: {
          status: 'success',
          token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          user: {
            email: '<EMAIL>',
            username: 'trader123',
            role: 'user'
          }
        }
      },
      register: {
        method: 'POST',
        url: '/api/auth/register',
        headers: {
          'Content-Type': 'application/json'
        },
        body: {
          email: '<EMAIL>',
          username: 'newtrader',
          password: 'securePassword123',
          fullName: 'New Trader'
        }
      }
    },
    marketData: {
      getCurrentPrice: {
        method: 'GET',
        url: '/api/market-data/EURUSD/latest',
        headers: {
          'Authorization': 'Bearer YOUR_JWT_TOKEN'
        },
        response: {
          status: 'success',
          symbol: 'EURUSD',
          data: {
            symbol: 'EURUSD',
            timestamp: '2024-01-15T10:30:00Z',
            open: 1.0850,
            high: 1.0870,
            low: 1.0840,
            close: 1.0860,
            volume: 150000
          }
        }
      },
      getHistoricalData: {
        method: 'GET',
        url: '/api/market-data/EURUSD?interval=H1&limit=100',
        headers: {
          'Authorization': 'Bearer YOUR_JWT_TOKEN'
        },
        response: {
          status: 'success',
          symbol: 'EURUSD',
          interval: 'H1',
          data: [
            {
              timestamp: '2024-01-15T10:00:00Z',
              open: 1.0850,
              high: 1.0870,
              low: 1.0840,
              close: 1.0860,
              volume: 150000
            }
          ]
        }
      }
    },
    tradingSignals: {
      getSignals: {
        method: 'GET',
        url: '/api/signals?symbol=EURUSD&limit=10',
        headers: {
          'Authorization': 'Bearer YOUR_JWT_TOKEN'
        },
        response: {
          status: 'success',
          signals: [
            {
              _id: '507f1f77bcf86cd799439011',
              symbol: 'EURUSD',
              type: 'buy',
              strength: 85,
              timeframe: 'H1',
              entryPrice: 1.0850,
              stopLoss: 1.0820,
              takeProfit: 1.0920,
              analysis: 'Strong bullish momentum with RSI oversold recovery',
              createdAt: '2024-01-15T10:30:00Z'
            }
          ]
        }
      },
      generateSignal: {
        method: 'POST',
        url: '/api/signals/generate',
        headers: {
          'Authorization': 'Bearer YOUR_JWT_TOKEN',
          'Content-Type': 'application/json'
        },
        body: {
          symbol: 'EURUSD',
          timeframe: 'H1',
          indicators: {
            rsi: 65,
            macd: {
              macd: 0.001,
              signal: 0.0005,
              histogram: 0.0005
            },
            ema: 1.0845
          }
        }
      }
    },
    technicalAnalysis: {
      getIndicators: {
        method: 'GET',
        url: '/api/technical-analysis/EURUSD/indicators?timeframe=H1',
        headers: {
          'Authorization': 'Bearer YOUR_JWT_TOKEN'
        },
        response: {
          status: 'success',
          symbol: 'EURUSD',
          timeframe: 'H1',
          indicators: {
            rsi: 65.5,
            macd: {
              macd: 0.0015,
              signal: 0.0010,
              histogram: 0.0005
            },
            ema: 1.0845,
            bollingerBands: {
              upper: 1.0890,
              middle: 1.0850,
              lower: 1.0810
            }
          }
        }
      }
    },
    webSocket: {
      connection: {
        url: 'ws://localhost:3000/ws?token=YOUR_JWT_TOKEN',
        description: 'Connect to WebSocket for real-time updates',
        messages: {
          subscribe: {
            type: 'subscribe',
            channel: 'market-data',
            params: { symbol: 'EURUSD', timeframe: 'H1' }
          },
          unsubscribe: {
            type: 'unsubscribe',
            channel: 'market-data',
            params: { symbol: 'EURUSD', timeframe: 'H1' }
          }
        },
        responses: {
          connection: {
            type: 'connection',
            status: 'connected',
            clientId: 'client_1642248600000_abc123',
            authenticated: true,
            timestamp: '2024-01-15T10:30:00Z'
          },
          update: {
            type: 'update',
            channel: 'market-data',
            params: { symbol: 'EURUSD', timeframe: 'H1' },
            data: {
              symbol: 'EURUSD',
              timestamp: '2024-01-15T10:30:00Z',
              price: 1.0860,
              change: 0.0010,
              changePercent: 0.09
            }
          }
        }
      }
    }
  };

  res.json({
    status: 'success',
    message: 'API usage examples',
    examples,
    notes: [
      'Replace YOUR_JWT_TOKEN with actual JWT token from login',
      'All timestamps are in ISO 8601 format',
      'WebSocket connections require valid JWT token in query parameter',
      'Rate limiting applies to all endpoints - see documentation for limits'
    ]
  });
});

/**
 * @swagger
 * /api/docs/postman:
 *   get:
 *     summary: Get Postman collection
 *     description: Returns a Postman collection for easy API testing
 *     tags: [Documentation]
 *     responses:
 *       200:
 *         description: Postman collection
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 */
router.get('/postman', (req, res) => {
  const postmanCollection = {
    info: {
      name: 'Trading Signals App API',
      description: 'Complete API collection for Trading Signals App',
      version: swaggerSpec.info.version,
      schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
    },
    auth: {
      type: 'bearer',
      bearer: [
        {
          key: 'token',
          value: '{{jwt_token}}',
          type: 'string'
        }
      ]
    },
    variable: [
      {
        key: 'base_url',
        value: 'http://localhost:3000',
        type: 'string'
      },
      {
        key: 'jwt_token',
        value: '',
        type: 'string'
      }
    ],
    item: [
      {
        name: 'Authentication',
        item: [
          {
            name: 'Login',
            request: {
              method: 'POST',
              header: [
                {
                  key: 'Content-Type',
                  value: 'application/json'
                }
              ],
              body: {
                mode: 'raw',
                raw: JSON.stringify({
                  email: '<EMAIL>',
                  password: 'password123'
                })
              },
              url: {
                raw: '{{base_url}}/api/auth/login',
                host: ['{{base_url}}'],
                path: ['api', 'auth', 'login']
              }
            }
          }
        ]
      },
      {
        name: 'Market Data',
        item: [
          {
            name: 'Get Latest Price',
            request: {
              method: 'GET',
              header: [],
              url: {
                raw: '{{base_url}}/api/market-data/EURUSD/latest',
                host: ['{{base_url}}'],
                path: ['api', 'market-data', 'EURUSD', 'latest']
              }
            }
          }
        ]
      }
    ]
  };

  res.json(postmanCollection);
});

module.exports = router;
