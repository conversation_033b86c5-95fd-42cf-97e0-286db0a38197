/**
 * Pagination Utilities for MongoDB Queries
 * 
 * This module provides utilities for paginating MongoDB queries.
 */

const { ObjectId } = require('mongodb');
const logger = require('./logger');

/**
 * Default pagination options
 */
const DEFAULT_OPTIONS = {
  page: 1,
  limit: 20,
  maxLimit: 100
};

/**
 * Parse pagination parameters from request query
 * @param {Object} query - Request query object
 * @param {Object} options - Pagination options
 * @returns {Object} Parsed pagination parameters
 */
function parsePaginationParams(query, options = {}) {
  const { 
    page: defaultPage, 
    limit: defaultLimit, 
    maxLimit 
  } = { ...DEFAULT_OPTIONS, ...options };
  
  // Parse page and limit
  let page = parseInt(query.page, 10) || defaultPage;
  let limit = parseInt(query.limit, 10) || defaultLimit;
  
  // Ensure page is at least 1
  page = Math.max(1, page);
  
  // Ensure limit is within bounds
  limit = Math.min(Math.max(1, limit), maxLimit);
  
  // Calculate skip
  const skip = (page - 1) * limit;
  
  return { page, limit, skip };
}

/**
 * Create pagination result
 * @param {Array} data - Data array
 * @param {number} total - Total count
 * @param {Object} params - Pagination parameters
 * @returns {Object} Pagination result
 */
function createPaginationResult(data, total, params) {
  const { page, limit } = params;
  const totalPages = Math.ceil(total / limit);
  
  return {
    data,
    pagination: {
      page,
      limit,
      totalItems: total,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1
    }
  };
}

/**
 * Create cursor-based pagination parameters
 * @param {Object} query - Request query object
 * @param {Object} options - Pagination options
 * @returns {Object} Cursor pagination parameters
 */
function parseCursorPaginationParams(query, options = {}) {
  const { limit: defaultLimit, maxLimit } = { ...DEFAULT_OPTIONS, ...options };
  
  // Parse limit
  let limit = parseInt(query.limit, 10) || defaultLimit;
  limit = Math.min(Math.max(1, limit), maxLimit);
  
  // Parse cursor
  let cursor = null;
  if (query.cursor && query.cursor !== 'null' && query.cursor !== 'undefined') {
    try {
      cursor = query.cursor;
      // If cursor is an ObjectId, convert it
      if (ObjectId.isValid(cursor)) {
        cursor = new ObjectId(cursor);
      }
    } catch (error) {
      logger.warn('Invalid cursor format, ignoring cursor', error);
      cursor = null;
    }
  }
  
  return { cursor, limit };
}

/**
 * Create cursor-based pagination result
 * @param {Array} data - Data array
 * @param {number} limit - Limit
 * @param {string} cursorField - Field to use as cursor
 * @returns {Object} Cursor pagination result
 */
function createCursorPaginationResult(data, limit, cursorField = '_id') {
  const hasMore = data.length > limit;
  
  // If we have more items than the limit, remove the extra item
  // that we fetched to determine if there are more items
  const items = hasMore ? data.slice(0, limit) : data;
  
  // Get the cursor for the next page
  const nextCursor = hasMore && items.length > 0 
    ? items[items.length - 1][cursorField]
    : null;
  
  return {
    data: items,
    pagination: {
      hasMore,
      nextCursor: nextCursor ? nextCursor.toString() : null
    }
  };
}

/**
 * Build MongoDB query for cursor-based pagination
 * @param {Object} baseQuery - Base MongoDB query
 * @param {any} cursor - Cursor value
 * @param {string} cursorField - Field to use as cursor
 * @param {boolean} sortAscending - Sort direction
 * @returns {Object} MongoDB query with cursor condition
 */
function buildCursorQuery(baseQuery, cursor, cursorField = '_id', sortAscending = false) {
  if (!cursor) {
    return baseQuery;
  }
  
  const cursorQuery = { ...baseQuery };
  const cursorOperator = sortAscending ? '$gt' : '$lt';
  
  cursorQuery[cursorField] = { [cursorOperator]: cursor };
  
  return cursorQuery;
}

/**
 * Execute paginated query
 * @param {Function} queryFn - Function that returns a MongoDB cursor
 * @param {Object} paginationParams - Pagination parameters
 * @returns {Promise<Object>} Pagination result
 */
async function executePaginatedQuery(queryFn, paginationParams) {
  try {
    const { page, limit, skip } = paginationParams;
    
    // Execute count query
    const total = await queryFn().count();
    
    // Execute data query with pagination
    const data = await queryFn().skip(skip).limit(limit).toArray();
    
    // Create pagination result
    return createPaginationResult(data, total, { page, limit });
  } catch (error) {
    logger.error('Error executing paginated query', error);
    throw error;
  }
}

/**
 * Execute cursor-based paginated query
 * @param {Function} queryFn - Function that returns a MongoDB query object
 * @param {Object} cursorParams - Cursor pagination parameters
 * @param {Object} options - Options
 * @returns {Promise<Object>} Cursor pagination result
 */
async function executeCursorQuery(queryFn, cursorParams, options = {}) {
  try {
    const { cursor, limit } = cursorParams;
    const { 
      cursorField = '_id', 
      sortField = cursorField, 
      sortAscending = false 
    } = options;
    
    // Build query with cursor condition
    const query = buildCursorQuery(
      queryFn(), 
      cursor, 
      cursorField, 
      sortAscending
    );
    
    // Determine sort direction
    const sortDirection = sortAscending ? 1 : -1;
    
    // Execute query with limit + 1 to determine if there are more items
    const data = await query
      .sort({ [sortField]: sortDirection })
      .limit(limit + 1)
      .toArray();
    
    // Create cursor pagination result
    return createCursorPaginationResult(data, limit, cursorField);
  } catch (error) {
    logger.error('Error executing cursor-based query', error);
    throw error;
  }
}

module.exports = {
  DEFAULT_OPTIONS,
  parsePaginationParams,
  createPaginationResult,
  parseCursorPaginationParams,
  createCursorPaginationResult,
  buildCursorQuery,
  executePaginatedQuery,
  executeCursorQuery
};
