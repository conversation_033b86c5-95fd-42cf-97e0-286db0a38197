/**
 * Analytics Service for Trading Signals App
 * 
 * This service provides analytics functionality using MongoDB aggregation pipelines.
 */

const { ObjectId } = require('mongodb');
const mongodbService = require('./mongodbService');
const cacheService = require('./cacheService');
const logger = require('../utils/logger');

/**
 * Get signal performance statistics
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Signal performance statistics
 */
async function getSignalPerformance(userId) {
  try {
    const cacheKey = cacheService.generateKey('signal_performance', { userId });
    
    return await cacheService.getOrSet(cacheKey, async () => {
      const db = mongodbService.getDatabase();
      const userObjectId = new ObjectId(userId);
      
      const pipeline = [
        { $match: { userId: userObjectId } },
        { $group: {
            _id: '$status',
            count: { $sum: 1 },
            avgStrength: { $avg: '$strength' }
          }
        },
        { $project: {
            _id: 0,
            status: '$_id',
            count: 1,
            avgStrength: { $round: ['$avgStrength', 1] }
          }
        },
        { $sort: { status: 1 } }
      ];
      
      const results = await db.collection('tradingSignals').aggregate(pipeline).toArray();
      
      // Format results
      const stats = {
        total: 0,
        active: { count: 0, avgStrength: 0 },
        executed: { count: 0, avgStrength: 0 },
        expired: { count: 0, avgStrength: 0 }
      };
      
      results.forEach(result => {
        stats[result.status] = {
          count: result.count,
          avgStrength: result.avgStrength
        };
        stats.total += result.count;
      });
      
      return stats;
    }, cacheService.DEFAULT_TTL.MEDIUM);
  } catch (error) {
    logger.error('Error getting signal performance', error);
    throw error;
  }
}

/**
 * Get signal distribution by symbol
 * @param {string} userId - User ID
 * @returns {Promise<Array>} Signal distribution by symbol
 */
async function getSignalDistributionBySymbol(userId) {
  try {
    const cacheKey = cacheService.generateKey('signal_distribution_symbol', { userId });
    
    return await cacheService.getOrSet(cacheKey, async () => {
      const db = mongodbService.getDatabase();
      const userObjectId = new ObjectId(userId);
      
      const pipeline = [
        { $match: { userId: userObjectId } },
        { $group: {
            _id: '$symbol',
            count: { $sum: 1 },
            buyCount: { 
              $sum: { $cond: [{ $eq: ['$type', 'buy'] }, 1, 0] }
            },
            sellCount: { 
              $sum: { $cond: [{ $eq: ['$type', 'sell'] }, 1, 0] }
            }
          }
        },
        { $project: {
            _id: 0,
            symbol: '$_id',
            count: 1,
            buyCount: 1,
            sellCount: 1,
            buyPercentage: { 
              $round: [{ $multiply: [{ $divide: ['$buyCount', '$count'] }, 100] }, 1]
            },
            sellPercentage: { 
              $round: [{ $multiply: [{ $divide: ['$sellCount', '$count'] }, 100] }, 1]
            }
          }
        },
        { $sort: { count: -1 } }
      ];
      
      return await db.collection('tradingSignals').aggregate(pipeline).toArray();
    }, cacheService.DEFAULT_TTL.MEDIUM);
  } catch (error) {
    logger.error('Error getting signal distribution by symbol', error);
    throw error;
  }
}

/**
 * Get signal distribution by timeframe
 * @param {string} userId - User ID
 * @returns {Promise<Array>} Signal distribution by timeframe
 */
async function getSignalDistributionByTimeframe(userId) {
  try {
    const cacheKey = cacheService.generateKey('signal_distribution_timeframe', { userId });
    
    return await cacheService.getOrSet(cacheKey, async () => {
      const db = mongodbService.getDatabase();
      const userObjectId = new ObjectId(userId);
      
      const pipeline = [
        { $match: { userId: userObjectId } },
        { $group: {
            _id: '$timeframe',
            count: { $sum: 1 }
          }
        },
        { $project: {
            _id: 0,
            timeframe: '$_id',
            count: 1
          }
        },
        { $sort: { 
            timeframe: 1
          } 
        }
      ];
      
      // Define timeframe order for sorting
      const timeframeOrder = ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN'];
      
      const results = await db.collection('tradingSignals').aggregate(pipeline).toArray();
      
      // Sort by timeframe order
      return results.sort((a, b) => {
        return timeframeOrder.indexOf(a.timeframe) - timeframeOrder.indexOf(b.timeframe);
      });
    }, cacheService.DEFAULT_TTL.MEDIUM);
  } catch (error) {
    logger.error('Error getting signal distribution by timeframe', error);
    throw error;
  }
}

/**
 * Get signal trend over time
 * @param {string} userId - User ID
 * @param {number} days - Number of days to look back
 * @returns {Promise<Array>} Signal trend over time
 */
async function getSignalTrendOverTime(userId, days = 30) {
  try {
    const cacheKey = cacheService.generateKey('signal_trend', { userId, days });
    
    return await cacheService.getOrSet(cacheKey, async () => {
      const db = mongodbService.getDatabase();
      const userObjectId = new ObjectId(userId);
      
      // Calculate start date
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      
      const pipeline = [
        { $match: { 
            userId: userObjectId,
            createdAt: { $gte: startDate }
          } 
        },
        // Add hint to use index if available
        { $hint: { userId: 1, createdAt: 1 } },
        { $group: {
            _id: { 
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' },
              day: { $dayOfMonth: '$createdAt' }
            },
            count: { $sum: 1 },
            buyCount: { 
              $sum: { $cond: [{ $eq: ['$type', 'buy'] }, 1, 0] }
            },
            sellCount: { 
              $sum: { $cond: [{ $eq: ['$type', 'sell'] }, 1, 0] }
            }
          }
        },
        { $project: {
            _id: 0,
            date: { 
              $dateFromParts: { 
                year: '$_id.year', 
                month: '$_id.month', 
                day: '$_id.day' 
              } 
            },
            count: 1,
            buyCount: 1,
            sellCount: 1
          }
        },
        { $sort: { date: 1 } },
        // Limit result size for better performance
        { $limit: 100 }
      ];
      
      return await db.collection('tradingSignals').aggregate(pipeline).toArray();
    }, cacheService.DEFAULT_TTL.MEDIUM);
  } catch (error) {
    logger.error('Error getting signal trend over time', error);
    throw error;
  }
}

/**
 * Get market data statistics
 * @param {string} symbol - Symbol
 * @param {string} timeframe - Timeframe
 * @returns {Promise<Object>} Market data statistics
 */
async function getMarketDataStatistics(symbol, timeframe) {
  try {
    const cacheKey = cacheService.generateKey('market_data_stats', { symbol, timeframe });
    
    return await cacheService.getOrSet(cacheKey, async () => {
      const db = mongodbService.getDatabase();
      
      const pipeline = [
        { $match: { symbol, timeframe } },
        { $sort: { timestamp: -1 } },
        { $limit: 100 },
        { $group: {
            _id: null,
            count: { $sum: 1 },
            avgVolume: { $avg: '$volume' },
            maxHigh: { $max: '$high' },
            minLow: { $min: '$low' },
            avgClose: { $avg: '$close' },
            latestClose: { $first: '$close' },
            latestTimestamp: { $first: '$timestamp' }
          }
        },
        { $project: {
            _id: 0,
            count: 1,
            avgVolume: { $round: ['$avgVolume', 2] },
            maxHigh: { $round: ['$maxHigh', 5] },
            minLow: { $round: ['$minLow', 5] },
            avgClose: { $round: ['$avgClose', 5] },
            latestClose: { $round: ['$latestClose', 5] },
            latestTimestamp: 1
          }
        }
      ];
      
      const results = await db.collection('marketData').aggregate(pipeline).toArray();
      
      return results.length > 0 ? results[0] : {
        count: 0,
        avgVolume: 0,
        maxHigh: 0,
        minLow: 0,
        avgClose: 0,
        latestClose: 0,
        latestTimestamp: null
      };
    }, cacheService.DEFAULT_TTL.SHORT);
  } catch (error) {
    logger.error('Error getting market data statistics', error);
    throw error;
  }
}

module.exports = {
  getSignalPerformance,
  getSignalDistributionBySymbol,
  getSignalDistributionByTimeframe,
  getSignalTrendOverTime,
  getMarketDataStatistics
};
