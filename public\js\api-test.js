/**
 * API Test Script for Trading Signals App
 * 
 * This script provides functions to test the API endpoints of the Trading Signals App.
 */

// API configuration
const API_CONFIG = {
    BASE_URL: '/api',
    ENDPOINTS: {
        TEST: '/test',
        DIAGNOSTIC: '/diagnostic',
        MARKET_DATA: '/market-data',
        MARKET_DATA_TEST: '/market-data/test',
        TECHNICAL_INDICATORS: '/technical-indicators',
        ECONOMIC_CALENDAR: '/economic-calendar',
        TRADING_SIGNALS: '/trading-signals',
        ALPHA_VANTAGE_TEST: '/test/alpha-vantage'
    }
};

// Test results container
const testResults = {
    basic: null,
    diagnostic: null,
    marketData: null,
    alphaVantage: null
};

// Helper function to display test results
function displayTestResult(testName, result) {
    const resultElement = document.getElementById(`${testName}-result`);
    if (!resultElement) return;
    
    const statusElement = document.getElementById(`${testName}-status`);
    const responseElement = document.getElementById(`${testName}-response`);
    
    if (statusElement) {
        statusElement.textContent = result.success ? 'Success' : 'Error';
        statusElement.className = result.success ? 'success' : 'error';
    }
    
    if (responseElement) {
        responseElement.textContent = typeof result.data === 'object' ? 
            JSON.stringify(result.data, null, 2) : result.data;
    }
    
    resultElement.style.display = 'block';
}

// Test basic API
async function testBasicAPI() {
    try {
        console.log('Testing basic API...');
        const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.TEST}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        testResults.basic = {
            success: true,
            data: data
        };
        
        displayTestResult('basic', testResults.basic);
        return true;
    } catch (error) {
        console.error('Error testing basic API:', error);
        
        testResults.basic = {
            success: false,
            data: error.message
        };
        
        displayTestResult('basic', testResults.basic);
        return false;
    }
}

// Test API diagnostic
async function testDiagnostic() {
    try {
        console.log('Testing API diagnostic...');
        const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.DIAGNOSTIC}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        testResults.diagnostic = {
            success: true,
            data: data
        };
        
        displayTestResult('diagnostic', testResults.diagnostic);
        return true;
    } catch (error) {
        console.error('Error testing API diagnostic:', error);
        
        testResults.diagnostic = {
            success: false,
            data: error.message
        };
        
        displayTestResult('diagnostic', testResults.diagnostic);
        return false;
    }
}

// Test market data API
async function testMarketDataAPI() {
    try {
        console.log('Testing market data API...');
        const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.MARKET_DATA_TEST}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        testResults.marketData = {
            success: true,
            data: data
        };
        
        displayTestResult('market-data', testResults.marketData);
        return true;
    } catch (error) {
        console.error('Error testing market data API:', error);
        
        testResults.marketData = {
            success: false,
            data: error.message
        };
        
        displayTestResult('market-data', testResults.marketData);
        return false;
    }
}

// Test Alpha Vantage API
async function testAlphaVantageAPI() {
    try {
        console.log('Testing Alpha Vantage API...');
        const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.ALPHA_VANTAGE_TEST}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        testResults.alphaVantage = {
            success: true,
            data: data
        };
        
        displayTestResult('alpha-vantage', testResults.alphaVantage);
        return true;
    } catch (error) {
        console.error('Error testing Alpha Vantage API:', error);
        
        testResults.alphaVantage = {
            success: false,
            data: error.message
        };
        
        displayTestResult('alpha-vantage', testResults.alphaVantage);
        return false;
    }
}

// Run all tests
async function runAllTests() {
    await testBasicAPI();
    await testDiagnostic();
    await testMarketDataAPI();
    await testAlphaVantageAPI();
    
    console.log('All tests completed:', testResults);
}

// Export functions for external use
window.APITest = {
    testBasicAPI,
    testDiagnostic,
    testMarketDataAPI,
    testAlphaVantageAPI,
    runAllTests,
    getResults: () => testResults
};

console.log('API test script loaded');
