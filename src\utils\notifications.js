/**
 * Notifications Module for Trading Signals App
 * 
 * This module provides functionality for in-app and browser notifications.
 */

// Notification configuration
const notificationConfig = {
  enabled: true,
  types: {
    signals: { enabled: true, title: 'إشارة تداول جديدة' },
    economic: { enabled: true, title: 'حدث اقتصادي قادم' },
    price: { enabled: true, title: 'تنبيه سعر' },
    system: { enabled: true, title: 'إشعار النظام' }
  },
  defaultIcon: '/icon-192.png',
  defaultDuration: 5000, // 5 seconds
  position: 'top-right',
  sound: true,
  vibration: true
};

// Notification permission status
let notificationPermission = 'default';

// Initialize notifications when DOM is loaded
document.addEventListener('DOMContentLoaded', initNotifications);

/**
 * Initialize notifications
 */
function initNotifications() {
  // Check if browser supports notifications
  if (!('Notification' in window)) {
    console.warn('This browser does not support notifications');
    updateNotificationStatus('not-supported');
    return;
  }
  
  // Get current permission status
  notificationPermission = Notification.permission;
  updateNotificationStatus(notificationPermission);
  
  // Load notification settings from localStorage
  loadNotificationSettings();
  
  // Add event listener for notification toggle
  const notificationToggle = document.getElementById('notificationToggle');
  if (notificationToggle) {
    notificationToggle.checked = notificationConfig.enabled;
    
    notificationToggle.addEventListener('change', function() {
      notificationConfig.enabled = this.checked;
      
      if (this.checked && notificationPermission !== 'granted') {
        requestNotificationPermission();
      }
      
      saveNotificationSettings();
    });
  }
  
  // Add event listeners for notification type toggles
  Object.keys(notificationConfig.types).forEach(type => {
    const toggle = document.getElementById(`${type}Notifications`);
    if (toggle) {
      toggle.checked = notificationConfig.types[type].enabled;
      
      toggle.addEventListener('change', function() {
        notificationConfig.types[type].enabled = this.checked;
        saveNotificationSettings();
      });
    }
  });
  
  console.log('Notifications initialized');
}

/**
 * Load notification settings from localStorage
 */
function loadNotificationSettings() {
  const savedSettings = localStorage.getItem('notification_settings');
  
  if (savedSettings) {
    try {
      const settings = JSON.parse(savedSettings);
      
      // Update config with saved settings
      notificationConfig.enabled = settings.enabled !== undefined ? settings.enabled : notificationConfig.enabled;
      notificationConfig.sound = settings.sound !== undefined ? settings.sound : notificationConfig.sound;
      notificationConfig.vibration = settings.vibration !== undefined ? settings.vibration : notificationConfig.vibration;
      
      // Update notification types
      if (settings.types) {
        Object.keys(settings.types).forEach(type => {
          if (notificationConfig.types[type]) {
            notificationConfig.types[type].enabled = settings.types[type].enabled;
          }
        });
      }
      
      console.log('Notification settings loaded from localStorage');
    } catch (error) {
      console.error('Error loading notification settings:', error);
    }
  }
}

/**
 * Save notification settings to localStorage
 */
function saveNotificationSettings() {
  try {
    const settings = {
      enabled: notificationConfig.enabled,
      sound: notificationConfig.sound,
      vibration: notificationConfig.vibration,
      types: {}
    };
    
    // Save notification types
    Object.keys(notificationConfig.types).forEach(type => {
      settings.types[type] = {
        enabled: notificationConfig.types[type].enabled
      };
    });
    
    localStorage.setItem('notification_settings', JSON.stringify(settings));
    console.log('Notification settings saved to localStorage');
  } catch (error) {
    console.error('Error saving notification settings:', error);
  }
}

/**
 * Request notification permission
 */
function requestNotificationPermission() {
  Notification.requestPermission()
    .then(permission => {
      notificationPermission = permission;
      updateNotificationStatus(permission);
      
      if (permission !== 'granted') {
        showInAppNotification({
          title: 'تنبيه الإشعارات',
          message: 'لم يتم منح إذن الإشعارات. لن تتلقى إشعارات عندما يكون التطبيق مغلقًا.',
          type: 'warning',
          duration: 10000
        });
      }
    });
}

/**
 * Update notification status in UI
 * @param {string} status - Notification permission status
 */
function updateNotificationStatus(status) {
  const notificationStatus = document.getElementById('notificationStatus');
  if (!notificationStatus) return;
  
  switch (status) {
    case 'granted':
      notificationStatus.textContent = 'مفعل';
      notificationStatus.className = 'badge bg-success';
      break;
    case 'denied':
      notificationStatus.textContent = 'محظور';
      notificationStatus.className = 'badge bg-danger';
      break;
    case 'default':
      notificationStatus.textContent = 'غير محدد';
      notificationStatus.className = 'badge bg-secondary';
      break;
    case 'not-supported':
      notificationStatus.textContent = 'غير مدعوم';
      notificationStatus.className = 'badge bg-warning text-dark';
      break;
  }
}

/**
 * Show browser notification
 * @param {Object} options - Notification options
 * @param {string} options.title - Notification title
 * @param {string} options.message - Notification message
 * @param {string} options.type - Notification type (signals, economic, price, system)
 * @param {string} [options.icon] - Notification icon URL
 * @param {number} [options.duration] - Notification duration in milliseconds
 * @param {Function} [options.onClick] - Notification click handler
 */
function showBrowserNotification(options) {
  // Check if notifications are enabled
  if (!notificationConfig.enabled) {
    return;
  }
  
  // Check if notification type is enabled
  const type = options.type || 'system';
  if (notificationConfig.types[type] && !notificationConfig.types[type].enabled) {
    return;
  }
  
  // Check if notification permission is granted
  if (notificationPermission !== 'granted') {
    // Fall back to in-app notification
    showInAppNotification(options);
    return;
  }
  
  // Create notification
  const notification = new Notification(options.title || notificationConfig.types[type].title, {
    body: options.message,
    icon: options.icon || notificationConfig.defaultIcon,
    vibrate: notificationConfig.vibration ? [200, 100, 200] : undefined,
    silent: !notificationConfig.sound
  });
  
  // Add click handler
  if (typeof options.onClick === 'function') {
    notification.addEventListener('click', options.onClick);
  } else {
    notification.addEventListener('click', function() {
      window.focus();
      notification.close();
    });
  }
  
  // Auto close after duration
  if (options.duration || notificationConfig.defaultDuration) {
    setTimeout(() => {
      notification.close();
    }, options.duration || notificationConfig.defaultDuration);
  }
}

/**
 * Show in-app notification
 * @param {Object} options - Notification options
 * @param {string} options.title - Notification title
 * @param {string} options.message - Notification message
 * @param {string} options.type - Notification type (success, info, warning, danger)
 * @param {number} [options.duration] - Notification duration in milliseconds
 * @param {Function} [options.onClick] - Notification click handler
 */
function showInAppNotification(options) {
  // Create notification container if not exists
  let notificationsContainer = document.getElementById('notifications-container');
  
  if (!notificationsContainer) {
    notificationsContainer = document.createElement('div');
    notificationsContainer.id = 'notifications-container';
    notificationsContainer.className = 'notifications-container';
    notificationsContainer.style.position = 'fixed';
    notificationsContainer.style.top = '20px';
    notificationsContainer.style.right = '20px';
    notificationsContainer.style.zIndex = '9999';
    notificationsContainer.style.maxWidth = '400px';
    
    document.body.appendChild(notificationsContainer);
  }
  
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `alert alert-${options.type || 'info'} notification`;
  notification.style.marginBottom = '10px';
  notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
  notification.style.transition = 'all 0.3s ease';
  notification.style.opacity = '0';
  notification.style.transform = 'translateY(-20px)';
  
  // Set notification content
  notification.innerHTML = `
    <div class="d-flex justify-content-between align-items-center">
      <strong>${options.title}</strong>
      <button type="button" class="btn-close" aria-label="Close"></button>
    </div>
    <p class="mb-0 mt-1">${options.message}</p>
  `;
  
  // Add to container
  notificationsContainer.appendChild(notification);
  
  // Animate in
  setTimeout(() => {
    notification.style.opacity = '1';
    notification.style.transform = 'translateY(0)';
  }, 10);
  
  // Add click handler
  if (typeof options.onClick === 'function') {
    notification.addEventListener('click', options.onClick);
  }
  
  // Add close button handler
  const closeButton = notification.querySelector('.btn-close');
  if (closeButton) {
    closeButton.addEventListener('click', () => {
      closeNotification(notification);
    });
  }
  
  // Auto close after duration
  if (options.duration || notificationConfig.defaultDuration) {
    setTimeout(() => {
      closeNotification(notification);
    }, options.duration || notificationConfig.defaultDuration);
  }
}

/**
 * Close in-app notification
 * @param {HTMLElement} notification - Notification element
 */
function closeNotification(notification) {
  // Animate out
  notification.style.opacity = '0';
  notification.style.transform = 'translateY(-20px)';
  
  // Remove after animation
  setTimeout(() => {
    notification.remove();
  }, 300);
}

/**
 * Show trading signal notification
 * @param {Object} signal - Trading signal data
 */
function showSignalNotification(signal) {
  showBrowserNotification({
    title: `إشارة ${signal.type === 'buy' ? 'شراء' : 'بيع'} جديدة`,
    message: `${signal.symbol}: ${signal.entryPoint} (قوة الإشارة: ${Math.round(signal.confidence * 100)}%)`,
    type: 'signals',
    onClick: function() {
      window.focus();
      // Scroll to signals section
      const signalsContainer = document.getElementById('signalsContainer');
      if (signalsContainer) {
        signalsContainer.scrollIntoView({ behavior: 'smooth' });
      }
    }
  });
}

/**
 * Show economic event notification
 * @param {Object} event - Economic event data
 */
function showEconomicEventNotification(event) {
  showBrowserNotification({
    title: `حدث اقتصادي قادم: ${event.currency}`,
    message: `${event.event} في ${event.time}`,
    type: 'economic',
    onClick: function() {
      window.focus();
      // Scroll to calendar section
      const calendarSection = document.querySelector('.card:has(#calendarBody)');
      if (calendarSection) {
        calendarSection.scrollIntoView({ behavior: 'smooth' });
      }
    }
  });
}

/**
 * Show price alert notification
 * @param {Object} alert - Price alert data
 */
function showPriceAlertNotification(alert) {
  showBrowserNotification({
    title: `تنبيه سعر: ${alert.symbol}`,
    message: `السعر ${alert.condition === 'above' ? 'أعلى من' : 'أقل من'} ${alert.price}`,
    type: 'price',
    onClick: function() {
      window.focus();
      // Scroll to market overview section
      const marketOverview = document.getElementById('marketOverview');
      if (marketOverview) {
        marketOverview.scrollIntoView({ behavior: 'smooth' });
      }
    }
  });
}

// Export functions
window.notifications = {
  showBrowserNotification,
  showInAppNotification,
  showSignalNotification,
  showEconomicEventNotification,
  showPriceAlertNotification,
  requestNotificationPermission
};
