import mongoose from 'mongoose';
import logger from '../utils/logger.js';
import { redisCacheService } from './redisCacheService.js';
import { databaseOptimizationService } from './databaseOptimizationService.js';
import { consolidatedOpenAIService } from './consolidatedOpenAIService.js';

/**
 * Cache-Integrated Data Service
 * 
 * Provides a unified interface for data operations with intelligent caching,
 * performance monitoring, and automatic cache invalidation strategies.
 * 
 * Features:
 * - Automatic Redis caching with TTL management
 * - Database query optimization with cursor-based pagination
 * - Real-time cache invalidation on data updates
 * - Performance monitoring and metrics
 * - AI analysis result caching
 * 
 * @class CacheIntegratedDataService
 * @version 1.0.0
 */
export class CacheIntegratedDataService {
  constructor() {
    this.cacheStrategies = {
      MARKET_DATA: {
        ttl: 30, // 30 seconds
        invalidateOnUpdate: true,
        keyPattern: 'mp:{symbol}:{timeframe}'
      },
      TECHNICAL_INDICATORS: {
        ttl: 300, // 5 minutes
        invalidateOnUpdate: true,
        keyPattern: 'ti:{symbol}:{timeframe}'
      },
      AI_ANALYSIS: {
        ttl: 600, // 10 minutes
        invalidateOnUpdate: false,
        keyPattern: 'ai:{symbol}:{timeframe}:{model}:{type}'
      },
      USER_DATA: {
        ttl: 3600, // 1 hour
        invalidateOnUpdate: true,
        keyPattern: 'user:{userId}'
      }
    };
  }

  /**
   * Get market data with intelligent caching
   * 
   * @param {string} symbol - Trading symbol
   * @param {string} timeframe - Chart timeframe
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Market data with cache metadata
   */
  async getMarketData(symbol, timeframe, options = {}) {
    const { limit = 100, useCache = true } = options;
    
    try {
      // Try cache first
      if (useCache) {
        const cached = await redisCacheService.getMarketPrices(symbol, timeframe);
        if (cached) {
          logger.debug(`Cache hit for market data ${symbol}:${timeframe}`);
          return {
            ...cached,
            fromCache: true,
            cacheAge: Date.now() - cached.timestamp
          };
        }
      }

      // Query database with optimization
      const startTime = Date.now();
      const collection = mongoose.connection.db.collection('marketdata');
      
      const query = {
        symbol: symbol.toUpperCase(),
        timeframe
      };

      const data = await collection
        .find(query)
        .sort({ timestamp: -1 })
        .limit(limit)
        .toArray();

      const queryTime = Date.now() - startTime;
      
      // Cache the result
      if (useCache && data.length > 0) {
        await redisCacheService.cacheMarketPrices(symbol, timeframe, data);
      }

      logger.debug(`Database query for market data ${symbol}:${timeframe} took ${queryTime}ms`);

      return {
        data,
        fromCache: false,
        queryTime,
        count: data.length,
        timestamp: Date.now()
      };
    } catch (error) {
      logger.error('Error getting market data:', error);
      throw error;
    }
  }

  /**
   * Get technical indicators with caching
   * 
   * @param {string} symbol - Trading symbol
   * @param {string} timeframe - Chart timeframe
   * @param {Array} indicatorTypes - Types of indicators to calculate
   * @param {Object} options - Calculation options
   * @returns {Promise<Object>} Technical indicators with cache metadata
   */
  async getTechnicalIndicators(symbol, timeframe, indicatorTypes = [], options = {}) {
    const { useCache = true, recalculate = false } = options;
    
    try {
      // Try cache first (unless recalculation is forced)
      if (useCache && !recalculate) {
        const cached = await redisCacheService.getTechnicalIndicators(symbol, timeframe);
        if (cached) {
          logger.debug(`Cache hit for technical indicators ${symbol}:${timeframe}`);
          return {
            ...cached,
            fromCache: true,
            cacheAge: Date.now() - cached.timestamp
          };
        }
      }

      // Get market data for calculation
      const marketData = await this.getMarketData(symbol, timeframe, { useCache });
      
      if (!marketData.data || marketData.data.length === 0) {
        throw new Error(`No market data available for ${symbol}:${timeframe}`);
      }

      // Calculate indicators (implement your indicator calculation logic here)
      const startTime = Date.now();
      const indicators = await this.calculateIndicators(marketData.data, indicatorTypes);
      const calculationTime = Date.now() - startTime;

      // Cache the result
      if (useCache) {
        await redisCacheService.cacheTechnicalIndicators(symbol, timeframe, indicators);
      }

      logger.debug(`Technical indicators calculation for ${symbol}:${timeframe} took ${calculationTime}ms`);

      return {
        indicators,
        fromCache: false,
        calculationTime,
        timestamp: Date.now(),
        symbol: symbol.toUpperCase(),
        timeframe
      };
    } catch (error) {
      logger.error('Error getting technical indicators:', error);
      throw error;
    }
  }

  /**
   * Get AI analysis with caching
   * 
   * @param {string} symbol - Trading symbol
   * @param {string} timeframe - Chart timeframe
   * @param {string} analysisType - Type of analysis
   * @param {Object} options - Analysis options
   * @returns {Promise<Object>} AI analysis with cache metadata
   */
  async getAIAnalysis(symbol, timeframe, analysisType, options = {}) {
    const { model = 'gpt-3.5-turbo', useCache = true, forceRefresh = false } = options;
    
    try {
      // Try cache first (unless refresh is forced)
      if (useCache && !forceRefresh) {
        const cached = await redisCacheService.getAIAnalysis(symbol, timeframe, model, analysisType);
        if (cached) {
          logger.debug(`Cache hit for AI analysis ${symbol}:${timeframe}:${model}:${analysisType}`);
          return {
            ...cached,
            fromCache: true,
            cacheAge: Date.now() - cached.timestamp
          };
        }
      }

      // Get required data for analysis
      const [marketData, indicators] = await Promise.all([
        this.getMarketData(symbol, timeframe, { useCache }),
        this.getTechnicalIndicators(symbol, timeframe, ['rsi', 'macd', 'ema'], { useCache })
      ]);

      // Perform AI analysis
      const startTime = Date.now();
      let analysisResult;

      switch (analysisType) {
        case 'market_analysis':
          analysisResult = await consolidatedOpenAIService.generateMarketAnalysis({
            symbol,
            timeframe,
            prices: marketData.data,
            indicators: indicators.indicators
          }, { model });
          break;
          
        case 'trading_signal':
          analysisResult = await consolidatedOpenAIService.generateTradingSignal({
            symbol,
            currentPrice: marketData.data[0]?.close,
            volume: marketData.data[0]?.volume
          }, indicators.indicators, { model });
          break;
          
        default:
          throw new Error(`Unsupported analysis type: ${analysisType}`);
      }

      const analysisTime = Date.now() - startTime;

      // Cache the result
      if (useCache) {
        await redisCacheService.cacheAIAnalysis(symbol, timeframe, model, analysisType, analysisResult);
      }

      logger.debug(`AI analysis ${analysisType} for ${symbol}:${timeframe} took ${analysisTime}ms`);

      return {
        result: analysisResult,
        fromCache: false,
        analysisTime,
        timestamp: Date.now(),
        symbol: symbol.toUpperCase(),
        timeframe,
        model,
        analysisType
      };
    } catch (error) {
      logger.error('Error getting AI analysis:', error);
      throw error;
    }
  }

  /**
   * Get paginated data with caching support
   * 
   * @param {Object} model - Mongoose model
   * @param {Object} query - Query conditions
   * @param {Object} options - Pagination and cache options
   * @returns {Promise<Object>} Paginated results with cache metadata
   */
  async getPaginatedData(model, query = {}, options = {}) {
    const {
      limit = 50,
      cursor = null,
      sortField = '_id',
      sortOrder = 1,
      select = null,
      useCache = true,
      cacheKey = null
    } = options;

    try {
      // Generate cache key if not provided
      const generatedCacheKey = cacheKey || 
        `paginated:${model.collection.name}:${JSON.stringify(query)}:${cursor || 'start'}:${limit}`;

      // Try cache first
      if (useCache) {
        const cached = await redisCacheService.redis?.get(generatedCacheKey);
        if (cached) {
          const parsedCache = JSON.parse(cached);
          logger.debug(`Cache hit for paginated data: ${generatedCacheKey}`);
          return {
            ...parsedCache,
            fromCache: true,
            cacheAge: Date.now() - parsedCache.timestamp
          };
        }
      }

      // Use database optimization service for pagination
      const result = await databaseOptimizationService.paginateWithCursor(
        model, 
        query, 
        { limit, cursor, sortField, sortOrder, select }
      );

      // Cache the result
      if (useCache && redisCacheService.isConnected) {
        const cacheData = {
          ...result,
          timestamp: Date.now()
        };
        await redisCacheService.redis.setex(
          generatedCacheKey, 
          300, // 5 minutes TTL for paginated data
          JSON.stringify(cacheData)
        );
      }

      return {
        ...result,
        fromCache: false
      };
    } catch (error) {
      logger.error('Error getting paginated data:', error);
      throw error;
    }
  }

  /**
   * Invalidate cache for specific data types
   * 
   * @param {string} type - Data type to invalidate
   * @param {Object} params - Parameters for cache invalidation
   * @returns {Promise<number>} Number of cache entries invalidated
   */
  async invalidateCache(type, params = {}) {
    try {
      let invalidated = 0;

      switch (type) {
        case 'MARKET_DATA':
          if (params.symbol) {
            invalidated = await redisCacheService.invalidateMarketData(params.symbol);
          }
          break;
          
        case 'TECHNICAL_INDICATORS':
          if (params.symbol) {
            invalidated = await redisCacheService.invalidateTechnicalIndicators(params.symbol);
          }
          break;
          
        case 'AI_ANALYSIS':
          if (params.symbol) {
            const pattern = `ai:${params.symbol.toLowerCase()}:*`;
            invalidated = await redisCacheService.invalidateByPattern(pattern);
          }
          break;
          
        case 'ALL':
          await redisCacheService.flushAll();
          invalidated = -1; // Indicates full flush
          break;
          
        default:
          logger.warn(`Unknown cache invalidation type: ${type}`);
      }

      logger.info(`Invalidated ${invalidated} cache entries for type: ${type}`);
      return invalidated;
    } catch (error) {
      logger.error('Error invalidating cache:', error);
      throw error;
    }
  }

  /**
   * Calculate technical indicators (placeholder implementation)
   * 
   * @private
   * @param {Array} marketData - Market data array
   * @param {Array} indicatorTypes - Types of indicators to calculate
   * @returns {Promise<Object>} Calculated indicators
   */
  async calculateIndicators(marketData, indicatorTypes) {
    // This is a placeholder implementation
    // In a real application, you would use a technical analysis library
    const indicators = {};
    
    if (indicatorTypes.includes('rsi')) {
      indicators.rsi = 50; // Placeholder value
    }
    
    if (indicatorTypes.includes('macd')) {
      indicators.macd = { signal: 0, histogram: 0, macd: 0 };
    }
    
    if (indicatorTypes.includes('ema')) {
      indicators.ema = marketData[0]?.close || 0;
    }
    
    return indicators;
  }

  /**
   * Get cache and performance statistics
   * 
   * @returns {Promise<Object>} Statistics
   */
  async getStatistics() {
    try {
      const [cacheInfo, dbMetrics] = await Promise.all([
        redisCacheService.getCacheInfo(),
        databaseOptimizationService.getPerformanceDashboard()
      ]);

      return {
        cache: cacheInfo,
        database: dbMetrics,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Error getting statistics:', error);
      throw error;
    }
  }
}

// Create and export singleton instance
export const cacheIntegratedDataService = new CacheIntegratedDataService();
export default cacheIntegratedDataService;
