const NodeCache = require('node-cache');
const Redis = require('ioredis');
const logger = require('../logging');

// Environment variables
const USE_REDIS = process.env.USE_REDIS === 'true';
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
const CACHE_TTL = parseInt(process.env.CACHE_TTL || '3600', 10); // Default 1 hour

let redisClient = null;
let nodeCache = null;

/**
 * Initialize the appropriate cache provider
 */
function initializeCache() {
  if (USE_REDIS) {
    try {
      redisClient = new Redis(REDIS_URL, {
        lazyConnect: true,
        maxRetriesPerRequest: 3,
        connectTimeout: 5000,
        retryStrategy: (times) => Math.min(times * 50, 2000)
      });

      redisClient.on('connect', () => {
        logger.info('Successfully connected to Redis server');
      });

      redisClient.on('error', (err) => {
        logger.error(`Redis connection error: ${err.message}`);
        // Fallback to NodeCache if Redis fails
        if (!nodeCache) {
          logger.info('Falling back to local NodeCache');
          nodeCache = new NodeCache({ stdTTL: CACHE_TTL, checkperiod: 120 });
        }
      });

      return true;
    } catch (error) {
      logger.error(`Redis initialization error: ${error.message}`);
      // Initialize local cache as fallback
      nodeCache = new NodeCache({ stdTTL: CACHE_TTL, checkperiod: 120 });
      return false;
    }
  } else {
    // Use local cache
    nodeCache = new NodeCache({ stdTTL: CACHE_TTL, checkperiod: 120 });
    logger.info('Using local NodeCache for caching');
    return true;
  }
}

/**
 * Set a value in the cache
 * @param {string} key - The cache key
 * @param {any} value - The value to cache
 * @param {number} ttl - Time to live in seconds
 * @returns {Promise<boolean>} - Success status
 */
async function set(key, value, ttl = CACHE_TTL) {
  try {
    if (USE_REDIS && redisClient) {
      // Redis requires string values
      const stringValue = JSON.stringify(value);
      await redisClient.set(key, stringValue, 'EX', ttl);
      logger.debug(`Cache set: ${key}`);
      return true;
    } else if (nodeCache) {
      nodeCache.set(key, value, ttl);
      logger.debug(`Cache set: ${key}`);
      return true;
    }
    return false;
  } catch (error) {
    logger.error(`Cache set error: ${error.message}`);
    return false;
  }
}

/**
 * Get a value from the cache
 * @param {string} key - The cache key
 * @returns {Promise<any>} - The cached value or null
 */
async function get(key) {
  try {
    if (USE_REDIS && redisClient) {
      const value = await redisClient.get(key);
      if (value) {
        try {
          const parsed = JSON.parse(value);
          logger.debug(`Cache hit: ${key}`);
          return parsed;
        } catch (e) {
          // If not JSON, return the raw value
          return value;
        }
      }
      logger.debug(`Cache miss: ${key}`);
      return null;
    } else if (nodeCache) {
      const value = nodeCache.get(key);
      if (value) {
        logger.debug(`Cache hit: ${key}`);
        return value;
      }
      logger.debug(`Cache miss: ${key}`);
      return null;
    }
    return null;
  } catch (error) {
    logger.error(`Cache get error: ${error.message}`);
    return null;
  }
}

/**
 * Delete a value from the cache
 * @param {string} key - The cache key
 * @returns {Promise<boolean>} - Success status
 */
async function del(key) {
  try {
    if (USE_REDIS && redisClient) {
      await redisClient.del(key);
      logger.debug(`Cache deleted: ${key}`);
      return true;
    } else if (nodeCache) {
      nodeCache.del(key);
      logger.debug(`Cache deleted: ${key}`);
      return true;
    }
    return false;
  } catch (error) {
    logger.error(`Cache delete error: ${error.message}`);
    return false;
  }
}

/**
 * Clear all cache entries
 * @returns {Promise<boolean>} - Success status
 */
async function clear() {
  try {
    if (USE_REDIS && redisClient) {
      await redisClient.flushall();
      logger.info('Redis cache cleared');
      return true;
    } else if (nodeCache) {
      nodeCache.flushAll();
      logger.info('Local cache cleared');
      return true;
    }
    return false;
  } catch (error) {
    logger.error(`Cache clear error: ${error.message}`);
    return false;
  }
}

/**
 * Get cache stats
 * @returns {Object} - Cache statistics
 */
function getStats() {
  if (USE_REDIS && redisClient) {
    // Limited stats for Redis
    return { provider: 'Redis', connected: redisClient.status === 'ready' };
  } else if (nodeCache) {
    return {
      provider: 'NodeCache',
      stats: nodeCache.getStats(),
      keys: nodeCache.keys().length
    };
  }
  return { provider: 'None' };
}

// Create a utility function for caching API results
/**
 * Cache wrapper for API calls
 * @param {string} key - Cache key
 * @param {function} fetchFunction - Function to fetch data if cache miss
 * @param {number} ttl - Time to live in seconds
 * @returns {Promise<any>} - Data from cache or fetch function
 */
async function cacheWrapper(key, fetchFunction, ttl = CACHE_TTL) {
  // Try to get from cache first
  const cachedData = await get(key);
  if (cachedData) {
    return cachedData;
  }

  // If not in cache, fetch fresh data
  try {
    const freshData = await fetchFunction();
    // Store in cache only if we got valid data
    if (freshData) {
      await set(key, freshData, ttl);
    }
    return freshData;
  } catch (error) {
    logger.error(`Error in cache wrapper for key ${key}: ${error.message}`);
    throw error; // Re-throw to let caller handle the error
  }
}

// Initialize cache on module load (commented out to prevent hanging)
// initializeCache();

module.exports = {
  initializeCache,
  set,
  get,
  del,
  clear,
  getStats,
  cacheWrapper
};