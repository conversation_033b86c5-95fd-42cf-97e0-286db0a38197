/**
 * Real-Time Signals List Component
 * 
 * Example component demonstrating how to integrate WebSocket real-time updates
 * with React components using our standardized WebSocket system.
 * 
 * Features:
 * - Real-time signal updates without page refresh
 * - Connection status indicator
 * - Automatic subscription management
 * - Error handling and fallback states
 * - Performance optimized rendering
 * 
 * @version 1.0.0
 */

import React, { useState, useEffect, useMemo } from 'react';
import { UnifiedSignal, SignalType, SignalStatus } from '../types/signals';
import { useRealTimeSignals, useWebSocketContext } from '../context/WebSocketContext';

// ============================================================================
// COMPONENT INTERFACES
// ============================================================================

interface RealTimeSignalsListProps {
  symbols?: string[];
  maxSignals?: number;
  showConnectionStatus?: boolean;
  onSignalClick?: (signal: UnifiedSignal) => void;
  filterByStatus?: SignalStatus[];
  filterByType?: SignalType[];
}

interface SignalItemProps {
  signal: UnifiedSignal;
  onClick?: (signal: UnifiedSignal) => void;
  isNew?: boolean;
}

// ============================================================================
// SIGNAL ITEM COMPONENT
// ============================================================================

const SignalItem: React.FC<SignalItemProps> = ({ signal, onClick, isNew = false }) => {
  const getSignalTypeColor = (type: SignalType): string => {
    switch (type) {
      case SignalType.BUY:
        return 'text-green-600 bg-green-50';
      case SignalType.SELL:
        return 'text-red-600 bg-red-50';
      case SignalType.HOLD:
        return 'text-yellow-600 bg-yellow-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusColor = (status: SignalStatus): string => {
    switch (status) {
      case SignalStatus.ACTIVE:
        return 'text-blue-600 bg-blue-50';
      case SignalStatus.CLOSED:
        return 'text-gray-600 bg-gray-50';
      case SignalStatus.EXPIRED:
        return 'text-orange-600 bg-orange-50';
      case SignalStatus.CANCELLED:
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4
    }).format(price);
  };

  const formatTimestamp = (timestamp: string): string => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div
      className={`
        p-4 border rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md
        ${isNew ? 'ring-2 ring-blue-500 bg-blue-50' : 'bg-white'}
        ${onClick ? 'hover:bg-gray-50' : ''}
      `}
      onClick={() => onClick?.(signal)}
    >
      <div className="flex justify-between items-start mb-2">
        <div className="flex items-center space-x-2">
          <span className="font-semibold text-lg">{signal.symbol}</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSignalTypeColor(signal.type)}`}>
            {signal.type}
          </span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(signal.status)}`}>
            {signal.status}
          </span>
          {isNew && (
            <span className="px-2 py-1 rounded-full text-xs font-medium text-blue-600 bg-blue-100 animate-pulse">
              NEW
            </span>
          )}
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-500">{formatTimestamp(signal.createdAt)}</div>
          <div className="text-xs text-gray-400">{signal.timeframe}</div>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-2">
        <div>
          <div className="text-xs text-gray-500">Entry Price</div>
          <div className="font-medium">{formatPrice(signal.entryPrice)}</div>
        </div>
        
        {signal.stopLoss && (
          <div>
            <div className="text-xs text-gray-500">Stop Loss</div>
            <div className="font-medium text-red-600">{formatPrice(signal.stopLoss)}</div>
          </div>
        )}
        
        {signal.takeProfit && (
          <div>
            <div className="text-xs text-gray-500">Take Profit</div>
            <div className="font-medium text-green-600">{formatPrice(signal.takeProfit)}</div>
          </div>
        )}
        
        <div>
          <div className="text-xs text-gray-500">Confidence</div>
          <div className="font-medium">{signal.confidence}%</div>
        </div>
      </div>

      {signal.analysis && (
        <div className="mt-2 p-2 bg-gray-50 rounded text-sm text-gray-700">
          {signal.analysis}
        </div>
      )}

      <div className="mt-2 flex justify-between items-center text-xs text-gray-500">
        <span>Source: {signal.source}</span>
        {signal.performance && (
          <span className={signal.performance.pnl && signal.performance.pnl > 0 ? 'text-green-600' : 'text-red-600'}>
            P&L: {signal.performance.pnl ? formatPrice(signal.performance.pnl) : 'N/A'}
          </span>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// CONNECTION STATUS COMPONENT
// ============================================================================

const ConnectionStatus: React.FC = () => {
  const { connectionStatus, connectionError, isConnected } = useWebSocketContext();

  const getStatusColor = (): string => {
    switch (connectionStatus) {
      case 'connected':
        return 'text-green-600 bg-green-50';
      case 'connecting':
        return 'text-yellow-600 bg-yellow-50';
      case 'error':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (): string => {
    switch (connectionStatus) {
      case 'connected':
        return '🟢';
      case 'connecting':
        return '🟡';
      case 'error':
        return '🔴';
      default:
        return '⚪';
    }
  };

  return (
    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor()}`}>
      <span className="mr-2">{getStatusIcon()}</span>
      <span className="capitalize">{connectionStatus}</span>
      {connectionError && (
        <span className="ml-2 text-xs" title={connectionError}>
          ⚠️
        </span>
      )}
    </div>
  );
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

const RealTimeSignalsList: React.FC<RealTimeSignalsListProps> = ({
  symbols,
  maxSignals = 50,
  showConnectionStatus = true,
  onSignalClick,
  filterByStatus,
  filterByType
}) => {
  // WebSocket integration
  const realTimeSignals = useRealTimeSignals(symbols);
  const { isConnected, messagesSent, messagesReceived } = useWebSocketContext();

  // Local state for tracking new signals
  const [newSignalIds, setNewSignalIds] = useState<Set<string>>(new Set());
  const [lastSignalCount, setLastSignalCount] = useState(0);

  // Filter and sort signals
  const filteredSignals = useMemo(() => {
    let filtered = realTimeSignals;

    // Apply status filter
    if (filterByStatus && filterByStatus.length > 0) {
      filtered = filtered.filter(signal => filterByStatus.includes(signal.status));
    }

    // Apply type filter
    if (filterByType && filterByType.length > 0) {
      filtered = filtered.filter(signal => filterByType.includes(signal.type));
    }

    // Sort by creation time (newest first)
    filtered = filtered.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    // Limit number of signals
    return filtered.slice(0, maxSignals);
  }, [realTimeSignals, filterByStatus, filterByType, maxSignals]);

  // Track new signals
  useEffect(() => {
    if (filteredSignals.length > lastSignalCount) {
      const newSignals = filteredSignals.slice(0, filteredSignals.length - lastSignalCount);
      const newIds = new Set(newSignals.map(signal => signal.id));
      
      setNewSignalIds(prev => new Set([...prev, ...newIds]));
      
      // Remove "new" status after 5 seconds
      setTimeout(() => {
        setNewSignalIds(prev => {
          const updated = new Set(prev);
          newIds.forEach(id => updated.delete(id));
          return updated;
        });
      }, 5000);
    }
    
    setLastSignalCount(filteredSignals.length);
  }, [filteredSignals.length, lastSignalCount]);

  // Loading state
  if (!isConnected && filteredSignals.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-500">Connecting to real-time signals...</p>
      </div>
    );
  }

  // Empty state
  if (filteredSignals.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-400 text-4xl mb-4">📊</div>
        <p className="text-gray-500">No signals available</p>
        {symbols && symbols.length > 0 && (
          <p className="text-sm text-gray-400 mt-2">
            Watching: {symbols.join(', ')}
          </p>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with connection status */}
      {showConnectionStatus && (
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">
            Real-Time Signals ({filteredSignals.length})
          </h2>
          <div className="flex items-center space-x-4">
            <ConnectionStatus />
            {isConnected && (
              <div className="text-xs text-gray-500">
                Sent: {messagesSent} | Received: {messagesReceived}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Signals list */}
      <div className="space-y-3">
        {filteredSignals.map((signal) => (
          <SignalItem
            key={signal.id}
            signal={signal}
            onClick={onSignalClick}
            isNew={newSignalIds.has(signal.id)}
          />
        ))}
      </div>

      {/* Load more indicator */}
      {filteredSignals.length >= maxSignals && (
        <div className="text-center py-4">
          <p className="text-sm text-gray-500">
            Showing latest {maxSignals} signals
          </p>
        </div>
      )}
    </div>
  );
};

export default RealTimeSignalsList;
