/**
 * User Repository for MongoDB
 * 
 * This repository handles all user-related database operations.
 */

const BaseRepository = require('./BaseRepository');
const bcrypt = require('bcrypt');
const logger = require('../utils/logger');

class UserRepository extends BaseRepository {
  /**
   * Create a new UserRepository instance
   * @param {Object} db - MongoDB database instance
   */
  constructor(db) {
    super(db, 'users');
    logger.debug('UserRepository initialized');
  }

  /**
   * Find a user by email
   * @param {string} email - User email
   * @returns {Promise<Object|null>} User document or null if not found
   */
  async findByEmail(email) {
    try {
      return await this.findOne({ email: email.toLowerCase() });
    } catch (error) {
      logger.error('Error finding user by email:', error);
      throw error;
    }
  }

  /**
   * Find a user by username
   * @param {string} username - Username
   * @returns {Promise<Object|null>} User document or null if not found
   */
  async findByUsername(username) {
    try {
      return await this.findOne({ username });
    } catch (error) {
      logger.error('Error finding user by username:', error);
      throw error;
    }
  }

  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise<Object>} Created user document
   */
  async createUser(userData) {
    try {
      // Validate required fields
      if (!userData.email || !userData.username || !userData.password) {
        throw new Error('Email, username, and password are required');
      }

      // Check if user already exists
      const existingUser = await this.findOne({
        $or: [
          { email: userData.email.toLowerCase() },
          { username: userData.username }
        ]
      });

      if (existingUser) {
        throw new Error('User with this email or username already exists');
      }

      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(userData.password, salt);

      // Create user object
      const userToInsert = {
        email: userData.email.toLowerCase(),
        username: userData.username,
        password: hashedPassword,
        name: userData.name || userData.username,
        role: userData.role || 'user',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Insert user
      const result = await this.insertOne(userToInsert);

      // Return user without password
      const { password, ...userWithoutPassword } = result;
      return userWithoutPassword;
    } catch (error) {
      logger.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Verify user credentials
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} User document without password
   */
  async verifyCredentials(email, password) {
    try {
      // Find user
      const user = await this.findOne({ email: email.toLowerCase() });
      if (!user) {
        throw new Error('Invalid email or password');
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        throw new Error('Invalid email or password');
      }

      // Update last login
      await this.updateById(user._id, { lastLogin: new Date() });

      // Return user without password
      const { password: _, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      logger.error('Error verifying credentials:', error);
      throw error;
    }
  }

  /**
   * Change user password
   * @param {string} userId - User ID
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise<boolean>} True if password was changed
   */
  async changePassword(userId, currentPassword, newPassword) {
    try {
      // Find user
      const user = await this.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Verify current password
      const isPasswordValid = await bcrypt.compare(currentPassword, user.password);
      if (!isPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      // Hash new password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);

      // Update password
      await this.updateById(userId, { password: hashedPassword });
      return true;
    } catch (error) {
      logger.error('Error changing password:', error);
      throw error;
    }
  }
}

module.exports = UserRepository;
