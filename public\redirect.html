<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecting... - Trading Signals App</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#0d6efd">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/basic.css">
    <link rel="stylesheet" href="src/styles/styles.css">

    <style>
        body {
            background-color: var(--body-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .redirect-container {
            max-width: 500px;
            width: 100%;
            padding: 30px;
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
            margin-bottom: 20px;
        }

        .redirect-message {
            margin-bottom: 20px;
        }

        .redirect-countdown {
            font-size: 24px;
            font-weight: bold;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>

        <h2>Redirecting...</h2>

        <div class="redirect-message">
            <p>You are being redirected to the Trading Signals App dashboard.</p>
            <p>If you are not redirected automatically, please click the button below.</p>
        </div>

        <div class="redirect-countdown" id="countdown">5</div>

        <a href="index_en.html" class="btn btn-primary">Go to Dashboard</a>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Theme Switcher -->
    <script src="./src/utils/theme-switcher.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get redirect parameters from URL
            const urlParams = new URLSearchParams(window.location.search);
            const redirectTo = urlParams.get('to') || 'index_en.html';
            const message = urlParams.get('message') || 'You are being redirected to the Trading Signals App.';

            // Update message
            const redirectMessage = document.querySelector('.redirect-message p:first-child');
            if (redirectMessage) {
                redirectMessage.textContent = message;
            }

            // Update button
            const redirectButton = document.querySelector('.btn-primary');
            if (redirectButton) {
                redirectButton.href = redirectTo;
            }

            // Countdown and redirect
            let countdown = 5;
            const countdownElement = document.getElementById('countdown');

            const countdownInterval = setInterval(function() {
                countdown--;

                if (countdownElement) {
                    countdownElement.textContent = countdown;
                }

                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                    window.location.href = redirectTo;
                }
            }, 1000);
        });
    </script>
</body>
</html>
