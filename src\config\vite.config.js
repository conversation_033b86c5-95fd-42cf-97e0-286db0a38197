import { defineConfig } from 'vite';
import { resolve } from 'path';
import { VitePWA } from 'vite-plugin-pwa';

export default defineConfig({
  plugins: [
    VitePWA({
      registerType: 'autoUpdate',
      manifest: {
        name: 'Trading Signals App',
        short_name: 'Signals',
        start_url: '.',
        display: 'standalone',
        background_color: '#0d6efd',
        theme_color: '#0d6efd',
        description: 'Real-time trading signals and market analysis.',
        icons: [
          {
            src: 'icon-192.png',
            sizes: '192x192',
            type: 'image/png'
          },
          {
            src: 'icon-512.png',
            sizes: '512x512',
            type: 'image/png'
          }
        ]
      }
    })
  ],
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    minify: true,
    sourcemap: process.env.NODE_ENV !== 'production',
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
        en: resolve(__dirname, 'index_en.html'),
      },
      output: {
        manualChunks: {
          // Split vendor code into separate chunks
          'vendor': ['react', 'react-dom', 'react-router-dom'],
          'chart-vendor': ['chart.js', 'chartjs-adapter-date-fns'],
          'ui-vendor': ['bootstrap', 'tailwindcss'],
          // Split app code into logical chunks
          'auth': ['./src/features/auth'],
          'dashboard': ['./src/features/dashboard'],
          'signals': ['./src/features/signals'],
          'calendar': ['./src/features/calendar'],
          'news': ['./src/features/news'],
        }
      }
    },
    // Enable chunk size warnings
    chunkSizeWarningLimit: 1000,
  },
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
      }
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
});
