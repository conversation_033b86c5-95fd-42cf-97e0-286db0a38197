@echo off
setlocal

:: Set environment variables for development
set NODE_ENV=development
set PORT=3000
set LOG_LEVEL=debug
set ENABLE_CACHING=true
set ENABLE_MOCK_DATA=true

:: Check if node<PERSON> is installed
echo Checking for nodemon...
call npx nodemon --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Installing nodemon...
    call npm install nodemon --save-dev
)

:: Check for MongoDB and Redis
echo Checking services...
echo.

net start | findstr /i "MongoDB" >nul
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: MongoDB service not detected!
    echo The application will use mock data instead.
    set ENABLE_MOCK_DATA=true
    timeout /t 2 >nul
) else (
    echo MongoDB: Running
)

net start | findstr /i "Redis" >nul
if %ERRORLEVEL% NEQ 0 (
    echo Redis: Not detected (will use in-memory cache)
) else (
    echo Redis: Running
)

echo.
echo Starting Trading Signals App in DEVELOPMENT mode...
echo.
echo Your app will be available at: http://localhost:3000
echo Changes to files will automatically restart the server
echo.
echo Press Ctrl+C to stop the application
echo.

npx nodemon --ignore node_modules/ --ignore public/ --ignore logs/ unified-server.js --mode development 