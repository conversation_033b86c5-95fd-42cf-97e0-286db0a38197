import express from 'express';
import { enhancedSecurityService } from '../services/enhancedSecurityService.js';
import {
  register,
  login,
  refreshToken,
  logout,
  getProfile,
  updateProfile,
  getSecurityMetrics
} from '../controllers/enhancedAuthController.js';

const router = express.Router();

/**
 * Enhanced Security Routes - Phase 2
 * 
 * Provides comprehensive authentication and authorization routes with:
 * - JWT refresh token mechanism
 * - RBAC system integration
 * - Rate limiting
 * - Input validation
 * - Session management
 * 
 * @version 2.0.0
 */

// Apply rate limiting to all auth routes
router.use(enhancedSecurityService.rateLimiters.basic);

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register new user
 *     description: Register a new user with enhanced security validation
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - firstName
 *               - lastName
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 minLength: 8
 *                 pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]'
 *                 example: SecurePass123!
 *               firstName:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 50
 *                 example: John
 *               lastName:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 50
 *                 example: Doe
 *               role:
 *                 type: string
 *                 enum: [basic, premium]
 *                 default: basic
 *     responses:
 *       201:
 *         description: User registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 user:
 *                   type: object
 *       400:
 *         description: Validation failed
 *       409:
 *         description: User already exists
 */
router.post('/register', register);

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Login user
 *     description: Authenticate user with enhanced security features
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *               rememberMe:
 *                 type: boolean
 *                 default: false
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 accessToken:
 *                   type: string
 *                 user:
 *                   type: object
 *                 sessionId:
 *                   type: string
 *         headers:
 *           Set-Cookie:
 *             description: HttpOnly refresh token cookie
 *             schema:
 *               type: string
 *       401:
 *         description: Invalid credentials
 *       423:
 *         description: Account locked
 */
router.post('/login', login);

/**
 * @swagger
 * /api/auth/refresh:
 *   post:
 *     summary: Refresh access token
 *     description: Get new access token using refresh token
 *     tags: [Authentication]
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               refreshToken:
 *                 type: string
 *                 description: Optional - can also be provided via httpOnly cookie
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 accessToken:
 *                   type: string
 *       401:
 *         description: Invalid or expired refresh token
 */
router.post('/refresh', refreshToken);

/**
 * @swagger
 * /api/auth/logout:
 *   post:
 *     summary: Logout user
 *     description: Invalidate session and refresh token
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logout successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
router.post('/logout', 
  enhancedSecurityService.authenticateToken.bind(enhancedSecurityService),
  logout
);

/**
 * @swagger
 * /api/auth/profile:
 *   get:
 *     summary: Get user profile
 *     description: Get current user profile with permissions and features
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 user:
 *                   type: object
 *                 permissions:
 *                   type: array
 *                   items:
 *                     type: string
 *                 features:
 *                   type: array
 *                   items:
 *                     type: string
 *       401:
 *         description: Authentication required
 *       404:
 *         description: User not found
 */
router.get('/profile',
  enhancedSecurityService.authenticateToken.bind(enhancedSecurityService),
  getProfile
);

/**
 * @swagger
 * /api/auth/profile:
 *   put:
 *     summary: Update user profile
 *     description: Update current user profile information
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 50
 *               lastName:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 50
 *               preferences:
 *                 type: object
 *                 properties:
 *                   defaultTimeframe:
 *                     type: string
 *                     enum: [1m, 5m, 15m, 30m, 1h, 4h, 1d, 1w]
 *                   notifications:
 *                     type: boolean
 *                   theme:
 *                     type: string
 *                     enum: [light, dark]
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 user:
 *                   type: object
 *       400:
 *         description: Validation failed
 *       401:
 *         description: Authentication required
 */
router.put('/profile',
  enhancedSecurityService.authenticateToken.bind(enhancedSecurityService),
  updateProfile
);

/**
 * @swagger
 * /api/auth/security-metrics:
 *   get:
 *     summary: Get security metrics (Admin only)
 *     description: Get comprehensive security and user metrics
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Security metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 security:
 *                   type: object
 *                 users:
 *                   type: object
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Admin access required
 */
router.get('/security-metrics',
  enhancedSecurityService.authenticateToken.bind(enhancedSecurityService),
  enhancedSecurityService.requirePermissions('*'), // Admin only
  getSecurityMetrics
);

// Protected routes with different permission levels
/**
 * @swagger
 * /api/auth/test/basic:
 *   get:
 *     summary: Test basic access
 *     description: Test endpoint for basic user access
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Basic access granted
 *       401:
 *         description: Authentication required
 */
router.get('/test/basic',
  enhancedSecurityService.authenticateToken.bind(enhancedSecurityService),
  (req, res) => {
    res.json({
      message: 'Basic access granted',
      user: {
        id: req.user.id,
        email: req.user.email,
        role: req.user.role
      },
      timestamp: new Date().toISOString()
    });
  }
);

/**
 * @swagger
 * /api/auth/test/premium:
 *   get:
 *     summary: Test premium access
 *     description: Test endpoint for premium user access
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Premium access granted
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Premium access required
 */
router.get('/test/premium',
  enhancedSecurityService.authenticateToken.bind(enhancedSecurityService),
  enhancedSecurityService.requireFeature('ai_advanced'),
  (req, res) => {
    res.json({
      message: 'Premium access granted',
      user: {
        id: req.user.id,
        email: req.user.email,
        role: req.user.role
      },
      features: enhancedSecurityService.roles[req.user.role.toUpperCase()]?.features || [],
      timestamp: new Date().toISOString()
    });
  }
);

/**
 * @swagger
 * /api/auth/test/admin:
 *   get:
 *     summary: Test admin access
 *     description: Test endpoint for admin user access
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Admin access granted
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Admin access required
 */
router.get('/test/admin',
  enhancedSecurityService.authenticateToken.bind(enhancedSecurityService),
  enhancedSecurityService.requirePermissions('*'), // Admin only
  (req, res) => {
    res.json({
      message: 'Admin access granted',
      user: {
        id: req.user.id,
        email: req.user.email,
        role: req.user.role
      },
      permissions: enhancedSecurityService.roles[req.user.role.toUpperCase()]?.permissions || [],
      timestamp: new Date().toISOString()
    });
  }
);

export default router;
