import mongoose from 'mongoose';

const sentimentHistorySchema = new mongoose.Schema({
  symbol: { type: String, required: true },
  date: { type: Date, required: true },
  sentiment: { type: String, enum: ['bullish', 'bearish', 'neutral'] },
  score: { type: Number },
  summary: { type: String }
});

const SentimentHistory = mongoose.model('SentimentHistory', sentimentHistorySchema);
export default SentimentHistory; 