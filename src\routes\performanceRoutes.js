import express from 'express';
import {
  getPerformanceDashboard,
  getDatabaseMetrics,
  getCacheMetrics,
  getAIMetrics,
  getSystemHealth,
  getPerformanceAlerts,
  acknowledgeAlert,
  clearCache,
  exportPerformanceData
} from '../controllers/performanceController.js';

const router = express.Router();

/**
 * Performance Monitoring Routes
 * 
 * Provides endpoints for monitoring system performance including:
 * - Database query performance
 * - Redis cache metrics
 * - AI service performance
 * - System health overview
 * - Performance alerts management
 * 
 * @version 1.0.0
 */

/**
 * @swagger
 * /api/performance/dashboard:
 *   get:
 *     summary: Get comprehensive performance dashboard
 *     description: Returns complete performance metrics including database, cache, and AI services
 *     tags: [Performance]
 *     responses:
 *       200:
 *         description: Performance dashboard data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     summary:
 *                       type: object
 *                     queryMetrics:
 *                       type: object
 *                     cacheMetrics:
 *                       type: object
 *                     connectionPool:
 *                       type: object
 *                     alerts:
 *                       type: array
 *                     recommendations:
 *                       type: array
 *                     systemHealth:
 *                       type: object
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
router.get('/dashboard', getPerformanceDashboard);

/**
 * @swagger
 * /api/performance/database:
 *   get:
 *     summary: Get database performance metrics
 *     description: Returns detailed database query performance metrics
 *     tags: [Performance]
 *     parameters:
 *       - in: query
 *         name: collection
 *         schema:
 *           type: string
 *         description: Filter by specific collection
 *       - in: query
 *         name: operation
 *         schema:
 *           type: string
 *         description: Filter by specific operation
 *     responses:
 *       200:
 *         description: Database performance metrics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     metrics:
 *                       type: object
 *                     summary:
 *                       type: object
 *                     connectionPool:
 *                       type: object
 */
router.get('/database', getDatabaseMetrics);

/**
 * @swagger
 * /api/performance/cache:
 *   get:
 *     summary: Get cache performance metrics
 *     description: Returns Redis cache performance and health metrics
 *     tags: [Performance]
 *     responses:
 *       200:
 *         description: Cache performance metrics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     info:
 *                       type: object
 *                     metrics:
 *                       type: object
 *                     performance:
 *                       type: object
 */
router.get('/cache', getCacheMetrics);

/**
 * @swagger
 * /api/performance/ai:
 *   get:
 *     summary: Get AI service performance metrics
 *     description: Returns AI service performance including model metrics and circuit breaker status
 *     tags: [Performance]
 *     parameters:
 *       - in: query
 *         name: model
 *         schema:
 *           type: string
 *         description: Filter by specific AI model
 *       - in: query
 *         name: operation
 *         schema:
 *           type: string
 *         description: Filter by specific operation type
 *     responses:
 *       200:
 *         description: AI service performance metrics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     modelMetrics:
 *                       type: object
 *                     circuitBreaker:
 *                       type: object
 *                     responseHistory:
 *                       type: array
 *                     systemHealth:
 *                       type: object
 */
router.get('/ai', getAIMetrics);

/**
 * @swagger
 * /api/performance/health:
 *   get:
 *     summary: Get system health overview
 *     description: Returns overall system health status and component health
 *     tags: [Performance]
 *     responses:
 *       200:
 *         description: System health overview
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     overall:
 *                       type: string
 *                       enum: [healthy, warning, critical]
 *                     score:
 *                       type: number
 *                       minimum: 0
 *                       maximum: 100
 *                     components:
 *                       type: object
 *                     alerts:
 *                       type: array
 *                     recommendations:
 *                       type: array
 */
router.get('/health', getSystemHealth);

/**
 * @swagger
 * /api/performance/alerts:
 *   get:
 *     summary: Get performance alerts
 *     description: Returns performance alerts with filtering options
 *     tags: [Performance]
 *     parameters:
 *       - in: query
 *         name: acknowledged
 *         schema:
 *           type: string
 *           enum: [true, false, all]
 *           default: false
 *         description: Filter by acknowledgment status
 *       - in: query
 *         name: severity
 *         schema:
 *           type: string
 *           enum: [critical, warning, info]
 *         description: Filter by alert severity
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Maximum number of alerts to return
 *     responses:
 *       200:
 *         description: Performance alerts
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     alerts:
 *                       type: array
 *                     summary:
 *                       type: object
 */
router.get('/alerts', getPerformanceAlerts);

/**
 * @swagger
 * /api/performance/alerts/{alertId}/acknowledge:
 *   post:
 *     summary: Acknowledge a performance alert
 *     description: Mark a specific performance alert as acknowledged
 *     tags: [Performance]
 *     parameters:
 *       - in: path
 *         name: alertId
 *         required: true
 *         schema:
 *           type: string
 *         description: Alert ID to acknowledge
 *     responses:
 *       200:
 *         description: Alert acknowledged successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                 alertId:
 *                   type: string
 *       404:
 *         description: Alert not found
 */
router.post('/alerts/:alertId/acknowledge', acknowledgeAlert);

/**
 * @swagger
 * /api/performance/cache/clear:
 *   post:
 *     summary: Clear cache entries
 *     description: Clear cache entries by type and optional symbol filter
 *     tags: [Performance]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [MARKET_DATA, TECHNICAL_INDICATORS, AI_ANALYSIS, ALL]
 *                 default: ALL
 *                 description: Type of cache to clear
 *               symbol:
 *                 type: string
 *                 description: Optional symbol filter
 *     responses:
 *       200:
 *         description: Cache cleared successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     type:
 *                       type: string
 *                     symbol:
 *                       type: string
 *                     entriesInvalidated:
 *                       type: number
 */
router.post('/cache/clear', clearCache);

/**
 * @swagger
 * /api/performance/export:
 *   get:
 *     summary: Export performance data
 *     description: Export performance metrics in JSON or CSV format
 *     tags: [Performance]
 *     parameters:
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, csv]
 *           default: json
 *         description: Export format
 *     responses:
 *       200:
 *         description: Performance data export
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *           text/csv:
 *             schema:
 *               type: string
 */
router.get('/export', exportPerformanceData);

export default router;
