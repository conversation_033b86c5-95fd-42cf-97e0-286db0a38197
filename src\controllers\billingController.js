import Stripe from 'stripe';
import config from '../config/config.js';
import User from '../models/User.js';

const stripe = new Stripe(config.stripe.secretKey);

// Create a Stripe Checkout session for premium upgrade
export const createCheckoutSession = async (req, res, next) => {
  try {
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'subscription',
      line_items: [
        {
          price: 'price_XXXXXXXXXXXX', // TODO: Replace with your Stripe price ID
          quantity: 1,
        },
      ],
      customer_email: req.user.email,
      success_url: 'https://yourapp.com/profile?upgrade=success',
      cancel_url: 'https://yourapp.com/profile?upgrade=cancel',
      metadata: { userId: req.user.id },
    });
    res.json({ url: session.url });
  } catch (err) {
    next(err);
  }
};

// Stripe webhook to upgrade user plan after successful payment
export const handleStripeWebhook = async (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;
  try {
    event = stripe.webhooks.constructEvent(req.rawBody, sig, config.stripe.webhookSecret);
  } catch (err) {
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  if (event.type === 'checkout.session.completed') {
    const session = event.data.object;
    const userId = session.metadata.userId;
    await User.findByIdAndUpdate(userId, { plan: 'pro' });
  }
  res.json({ received: true });
}; 