import { openaiAPI } from './apiService.js';
import logger from '../utils/logger.js';
import { standardizedErrorHandler } from './standardizedErrorHandler.js';

/**
 * Consolidated OpenAI Integration Service
 *
 * This service provides a unified interface for all AI-related functionality with:
 * - Centralized error handling and recovery
 * - Circuit breaker pattern for API resilience
 * - Model versioning and performance tracking
 * - Sophisticated caching and rate limiting
 * - Standardized prompt templates with versioning
 *
 * @class ConsolidatedOpenAIService
 * @version 2.0.0
 * <AUTHOR> Signals App Team
 *
 * @example
 * ```javascript
 * const aiService = new ConsolidatedOpenAIService();
 * const analysis = await aiService.generateMarketAnalysis(marketData, {
 *   model: 'gpt-4',
 *   temperature: 0.2
 * });
 * ```
 */
export class ConsolidatedOpenAIService {
  /**
   * Initialize the OpenAI service with enhanced configuration
   *
   * @constructor
   */
  constructor() {
    /**
     * Available AI models with their capabilities
     * @type {Object.<string, Object>}
     */
    this.models = {
      CHAT: {
        name: 'gpt-3.5-turbo',
        version: '0613',
        maxTokens: 4096,
        costPer1kTokens: 0.002,
        capabilities: ['analysis', 'signals', 'sentiment']
      },
      CHAT_16K: {
        name: 'gpt-3.5-turbo-16k',
        version: '0613',
        maxTokens: 16384,
        costPer1kTokens: 0.004,
        capabilities: ['analysis', 'signals', 'sentiment', 'long_context']
      },
      GPT4: {
        name: 'gpt-4',
        version: '0613',
        maxTokens: 8192,
        costPer1kTokens: 0.03,
        capabilities: ['analysis', 'signals', 'sentiment', 'advanced_reasoning']
      },
      GPT4_TURBO: {
        name: 'gpt-4-turbo-preview',
        version: '1106',
        maxTokens: 128000,
        costPer1kTokens: 0.01,
        capabilities: ['analysis', 'signals', 'sentiment', 'advanced_reasoning', 'long_context']
      }
    };

    /**
     * Default configuration for AI requests
     * @type {Object}
     */
    this.defaultConfig = {
      temperature: 0.3,
      max_tokens: 800,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0
    };

    /**
     * Versioned prompt templates
     * @type {Object.<string, Object>}
     */
    this.prompts = {
      MARKET_ANALYSIS: {
        template: this.createMarketAnalysisPrompt(),
        version: '2.0',
        lastUpdated: '2024-01-15'
      },
      SENTIMENT_ANALYSIS: {
        template: this.createSentimentAnalysisPrompt(),
        version: '2.0',
        lastUpdated: '2024-01-15'
      },
      TRADING_SIGNAL: {
        template: this.createTradingSignalPrompt(),
        version: '2.0',
        lastUpdated: '2024-01-15'
      },
      RISK_ASSESSMENT: {
        template: this.createRiskAssessmentPrompt(),
        version: '2.0',
        lastUpdated: '2024-01-15'
      },
      NEWS_SUMMARY: {
        template: this.createNewsSummaryPrompt(),
        version: '2.0',
        lastUpdated: '2024-01-15'
      }
    };

    // Enhanced caching and rate limiting
    this.cache = new Map();
    this.cacheTTL = 10 * 60 * 1000; // 10 minutes
    this.requestQueue = [];
    this.isProcessing = false;
    this.rateLimitDelay = 1000; // 1 second between requests

    // Circuit breaker configuration
    this.circuitBreaker = {
      state: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
      failureCount: 0,
      failureThreshold: 5,
      recoveryTimeout: 60000, // 1 minute
      lastFailureTime: null,
      halfOpenMaxCalls: 3,
      halfOpenCalls: 0
    };

    // Model performance tracking
    this.modelMetrics = new Map();
    this.responseHistory = [];
    this.maxHistorySize = 1000;

    // Initialize fallback strategies
    this.fallbackStrategies = this.initializeFallbackStrategies();
  }

  /**
   * Initialize fallback strategies for different scenarios
   *
   * @private
   * @returns {Object} Fallback strategy configuration
   */
  initializeFallbackStrategies() {
    return {
      modelFallback: {
        'gpt-4-turbo-preview': ['gpt-4', 'gpt-3.5-turbo-16k', 'gpt-3.5-turbo'],
        'gpt-4': ['gpt-3.5-turbo-16k', 'gpt-3.5-turbo'],
        'gpt-3.5-turbo-16k': ['gpt-3.5-turbo'],
        'gpt-3.5-turbo': []
      },
      responseStrategies: {
        'rate_limit': 'exponential_backoff',
        'server_error': 'retry_with_fallback_model',
        'context_length': 'use_smaller_model',
        'invalid_response': 'retry_with_different_temperature'
      }
    };
  }

  /**
   * Check if circuit breaker allows the request
   *
   * @private
   * @returns {boolean} Whether the request should be allowed
   */
  shouldAllowRequest() {
    const now = Date.now();

    switch (this.circuitBreaker.state) {
      case 'CLOSED':
        return true;

      case 'OPEN':
        if (now - this.circuitBreaker.lastFailureTime > this.circuitBreaker.recoveryTimeout) {
          this.circuitBreaker.state = 'HALF_OPEN';
          this.circuitBreaker.halfOpenCalls = 0;
          logger.info('Circuit breaker transitioning to HALF_OPEN state');
          return true;
        }
        return false;

      case 'HALF_OPEN':
        return this.circuitBreaker.halfOpenCalls < this.circuitBreaker.halfOpenMaxCalls;

      default:
        return true;
    }
  }

  /**
   * Record the result of an API call for circuit breaker
   *
   * @private
   * @param {boolean} success - Whether the call was successful
   * @param {Error} [error] - Error object if call failed
   */
  recordApiCall(success, error = null) {
    if (success) {
      if (this.circuitBreaker.state === 'HALF_OPEN') {
        this.circuitBreaker.halfOpenCalls++;
        if (this.circuitBreaker.halfOpenCalls >= this.circuitBreaker.halfOpenMaxCalls) {
          this.circuitBreaker.state = 'CLOSED';
          this.circuitBreaker.failureCount = 0;
          logger.info('Circuit breaker closed - service recovered');
        }
      } else if (this.circuitBreaker.state === 'CLOSED') {
        this.circuitBreaker.failureCount = Math.max(0, this.circuitBreaker.failureCount - 1);
      }
    } else {
      this.circuitBreaker.failureCount++;
      this.circuitBreaker.lastFailureTime = Date.now();

      if (this.circuitBreaker.state === 'CLOSED' &&
          this.circuitBreaker.failureCount >= this.circuitBreaker.failureThreshold) {
        this.circuitBreaker.state = 'OPEN';
        logger.warn('Circuit breaker opened due to repeated failures');
      } else if (this.circuitBreaker.state === 'HALF_OPEN') {
        this.circuitBreaker.state = 'OPEN';
        this.circuitBreaker.halfOpenCalls = 0;
        logger.warn('Circuit breaker reopened - recovery failed');
      }
    }
  }

  /**
   * Generate market analysis using AI with enhanced error handling
   *
   * @param {Object} marketData - Market data including symbol, prices, indicators
   * @param {Object} [options={}] - Configuration options
   * @param {string} [options.model] - AI model to use
   * @param {number} [options.temperature] - Response creativity (0-1)
   * @param {number} [options.max_tokens] - Maximum response length
   * @param {boolean} [options.enableFallback=true] - Enable model fallback
   * @returns {Promise<Object>} Analysis result with metadata
   *
   * @example
   * ```javascript
   * const analysis = await aiService.generateMarketAnalysis({
   *   symbol: 'AAPL',
   *   timeframe: '1D',
   *   prices: [...],
   *   indicators: { rsi: 65, macd: 0.5 }
   * }, {
   *   model: 'gpt-4',
   *   temperature: 0.2
   * });
   * ```
   */
  async generateMarketAnalysis(marketData, options = {}) {
    const startTime = Date.now();
    const config = { ...this.defaultConfig, ...options };
    const enableFallback = options.enableFallback !== false;

    // Validate input
    if (!marketData || !marketData.symbol) {
      throw new Error('Market data with symbol is required');
    }

    try {
      // Check circuit breaker
      if (!this.shouldAllowRequest()) {
        throw new Error('Service temporarily unavailable - circuit breaker is open');
      }

      const prompt = this.prompts.MARKET_ANALYSIS.template
        .replace('{SYMBOL}', marketData.symbol || 'Unknown')
        .replace('{TIMEFRAME}', marketData.timeframe || '1D')
        .replace('{PRICE_DATA}', this.formatPriceData(marketData.prices || []))
        .replace('{INDICATORS}', this.formatIndicators(marketData.indicators || {}));

      const modelName = this.getModelName(config.model || 'CHAT');
      const response = await this.makeRequestWithFallback({
        model: modelName,
        messages: [
          { role: 'system', content: 'You are a professional financial market analyst with expertise in technical and fundamental analysis.' },
          { role: 'user', content: prompt }
        ],
        ...config
      }, enableFallback);

      const result = this.parseAnalysisResponse(response);

      // Record successful call
      this.recordApiCall(true);

      // Track model performance
      this.trackModelPerformance(modelName, 'market_analysis', startTime, true);

      // Add metadata
      result._metadata = {
        model: modelName,
        promptVersion: this.prompts.MARKET_ANALYSIS.version,
        responseTime: Date.now() - startTime,
        timestamp: new Date().toISOString(),
        symbol: marketData.symbol
      };

      return result;
    } catch (error) {
      // Record failed call
      this.recordApiCall(false, error);

      // Track model performance
      const modelName = this.getModelName(config.model || 'CHAT');
      this.trackModelPerformance(modelName, 'market_analysis', startTime, false, error);

      const standardError = await standardizedErrorHandler.handleError(error, {
        service: 'OpenAI',
        operation: 'generateMarketAnalysis',
        symbol: marketData.symbol,
        model: modelName
      });
      throw standardError;
    }
  }

  /**
   * Analyze sentiment from news headlines
   */
  async analyzeSentiment(headlines, options = {}) {
    const config = { ...this.defaultConfig, ...options };

    try {
      const prompt = this.prompts.SENTIMENT_ANALYSIS
        .replace('{HEADLINES}', headlines.map((h, i) => `${i + 1}. ${h}`).join('\n'));

      const response = await this.makeRequest({
        model: config.model || this.models.CHAT,
        messages: [
          { role: 'system', content: 'You are a financial sentiment analysis expert. Analyze news headlines and provide structured sentiment data.' },
          { role: 'user', content: prompt }
        ],
        ...config
      });

      return this.parseSentimentResponse(response);
    } catch (error) {
      const standardError = await standardizedErrorHandler.handleError(error, {
        service: 'OpenAI',
        operation: 'analyzeSentiment',
        headlineCount: headlines.length
      });
      throw standardError;
    }
  }

  /**
   * Generate trading signals
   */
  async generateTradingSignal(marketData, indicators, options = {}) {
    const config = { ...this.defaultConfig, ...options };

    try {
      const prompt = this.prompts.TRADING_SIGNAL
        .replace('{SYMBOL}', marketData.symbol || 'Unknown')
        .replace('{CURRENT_PRICE}', marketData.currentPrice || 'N/A')
        .replace('{INDICATORS}', this.formatIndicators(indicators))
        .replace('{VOLUME}', marketData.volume || 'N/A');

      const response = await this.makeRequest({
        model: config.model || this.models.CHAT,
        messages: [
          { role: 'system', content: 'You are a trading signal generator. Provide clear buy/sell/hold recommendations with confidence levels and reasoning.' },
          { role: 'user', content: prompt }
        ],
        ...config
      });

      return this.parseSignalResponse(response);
    } catch (error) {
      const standardError = await standardizedErrorHandler.handleError(error, {
        service: 'OpenAI',
        operation: 'generateTradingSignal',
        symbol: marketData.symbol
      });
      throw standardError;
    }
  }

  /**
   * Assess risk for a trading position
   */
  async assessRisk(position, marketConditions, options = {}) {
    const config = { ...this.defaultConfig, ...options };

    try {
      const prompt = this.prompts.RISK_ASSESSMENT
        .replace('{POSITION}', JSON.stringify(position))
        .replace('{MARKET_CONDITIONS}', JSON.stringify(marketConditions));

      const response = await this.makeRequest({
        model: config.model || this.models.CHAT,
        messages: [
          { role: 'system', content: 'You are a risk management expert. Analyze trading positions and provide risk assessments with mitigation strategies.' },
          { role: 'user', content: prompt }
        ],
        ...config
      });

      return this.parseRiskResponse(response);
    } catch (error) {
      const standardError = await standardizedErrorHandler.handleError(error, {
        service: 'OpenAI',
        operation: 'assessRisk',
        symbol: position.symbol
      });
      throw standardError;
    }
  }

  /**
   * Summarize news articles
   */
  async summarizeNews(articles, options = {}) {
    const config = { ...this.defaultConfig, ...options };

    try {
      const prompt = this.prompts.NEWS_SUMMARY
        .replace('{ARTICLES}', articles.map((article, i) =>
          `${i + 1}. ${article.title}\n${article.content || article.summary || ''}`
        ).join('\n\n'));

      const response = await this.makeRequest({
        model: config.model || this.models.CHAT,
        messages: [
          { role: 'system', content: 'You are a financial news analyst. Summarize news articles and extract key market-moving information.' },
          { role: 'user', content: prompt }
        ],
        ...config
      });

      return this.parseNewsResponse(response);
    } catch (error) {
      const standardError = await standardizedErrorHandler.handleError(error, {
        service: 'OpenAI',
        operation: 'summarizeNews',
        articleCount: articles.length
      });
      throw standardError;
    }
  }

  /**
   * Get model name from model key or return the name directly
   *
   * @private
   * @param {string} modelKey - Model key or name
   * @returns {string} Model name
   */
  getModelName(modelKey) {
    if (this.models[modelKey]) {
      return this.models[modelKey].name;
    }
    return modelKey;
  }

  /**
   * Track model performance metrics
   *
   * @private
   * @param {string} modelName - Name of the model used
   * @param {string} operation - Type of operation performed
   * @param {number} startTime - Request start timestamp
   * @param {boolean} success - Whether the request was successful
   * @param {Error} [error] - Error object if request failed
   */
  trackModelPerformance(modelName, operation, startTime, success, error = null) {
    const responseTime = Date.now() - startTime;
    const key = `${modelName}_${operation}`;

    if (!this.modelMetrics.has(key)) {
      this.modelMetrics.set(key, {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        totalResponseTime: 0,
        averageResponseTime: 0,
        lastUsed: null,
        errorTypes: new Map()
      });
    }

    const metrics = this.modelMetrics.get(key);
    metrics.totalRequests++;
    metrics.totalResponseTime += responseTime;
    metrics.averageResponseTime = metrics.totalResponseTime / metrics.totalRequests;
    metrics.lastUsed = new Date().toISOString();

    if (success) {
      metrics.successfulRequests++;
    } else {
      metrics.failedRequests++;
      if (error) {
        const errorType = error.name || 'UnknownError';
        metrics.errorTypes.set(errorType, (metrics.errorTypes.get(errorType) || 0) + 1);
      }
    }

    // Add to response history
    this.responseHistory.push({
      timestamp: new Date().toISOString(),
      model: modelName,
      operation,
      success,
      responseTime,
      error: error ? error.message : null
    });

    // Limit history size
    if (this.responseHistory.length > this.maxHistorySize) {
      this.responseHistory = this.responseHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Make request with fallback model support
   *
   * @private
   * @param {Object} requestData - Request configuration
   * @param {boolean} enableFallback - Whether to enable model fallback
   * @returns {Promise<Object>} API response
   */
  async makeRequestWithFallback(requestData, enableFallback = true) {
    const originalModel = requestData.model;
    let lastError = null;

    // Try primary model first
    try {
      return await this.makeRequest(requestData);
    } catch (error) {
      lastError = error;

      if (!enableFallback) {
        throw error;
      }

      // Try fallback models
      const fallbackModels = this.fallbackStrategies.modelFallback[originalModel] || [];

      for (const fallbackModel of fallbackModels) {
        try {
          logger.warn(`Trying fallback model ${fallbackModel} after ${originalModel} failed`);

          const fallbackRequest = { ...requestData, model: fallbackModel };
          return await this.makeRequest(fallbackRequest);
        } catch (fallbackError) {
          lastError = fallbackError;
          logger.warn(`Fallback model ${fallbackModel} also failed:`, fallbackError.message);
        }
      }

      // All models failed
      throw lastError;
    }
  }

  /**
   * Make request to OpenAI API with caching and rate limiting
   *
   * @private
   * @param {Object} requestData - Request configuration
   * @returns {Promise<Object>} API response
   */
  async makeRequest(requestData) {
    const cacheKey = this.generateCacheKey(requestData);

    // Check cache first
    const cachedResponse = this.cache.get(cacheKey);
    if (cachedResponse && Date.now() - cachedResponse.timestamp < this.cacheTTL) {
      logger.debug('Returning cached OpenAI response');
      return cachedResponse.data;
    }

    // Add to queue for rate limiting
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ requestData, resolve, reject });
      this.processQueue();
    });
  }

  /**
   * Process request queue with rate limiting
   */
  async processQueue() {
    if (this.isProcessing || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.requestQueue.length > 0) {
      const { requestData, resolve, reject } = this.requestQueue.shift();

      try {
        const response = await openaiAPI.post('chat/completions', requestData);

        // Cache the response
        const cacheKey = this.generateCacheKey(requestData);
        this.cache.set(cacheKey, {
          data: response,
          timestamp: Date.now()
        });

        resolve(response);
      } catch (error) {
        reject(error);
      }

      // Rate limiting delay
      if (this.requestQueue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, this.rateLimitDelay));
      }
    }

    this.isProcessing = false;
  }

  /**
   * Generate cache key for request
   */
  generateCacheKey(requestData) {
    const key = JSON.stringify({
      model: requestData.model,
      messages: requestData.messages,
      temperature: requestData.temperature,
      max_tokens: requestData.max_tokens
    });
    return Buffer.from(key).toString('base64').substring(0, 50);
  }

  /**
   * Create market analysis prompt template
   */
  createMarketAnalysisPrompt() {
    return `Analyze the following market data for {SYMBOL} on {TIMEFRAME} timeframe:

Price Data: {PRICE_DATA}
Technical Indicators: {INDICATORS}

Please provide:
1. Current market trend analysis
2. Key support and resistance levels
3. Technical pattern identification
4. Volume analysis
5. Overall market sentiment
6. Trading recommendations with reasoning

Format your response as structured JSON with the following fields:
- trend: "bullish" | "bearish" | "neutral"
- confidence: number (0-100)
- support_levels: array of numbers
- resistance_levels: array of numbers
- patterns: array of pattern names
- recommendation: "buy" | "sell" | "hold"
- reasoning: detailed explanation
- risk_level: "low" | "medium" | "high"`;
  }

  /**
   * Create sentiment analysis prompt template
   */
  createSentimentAnalysisPrompt() {
    return `Analyze the sentiment of these financial news headlines:

{HEADLINES}

Provide a JSON response with:
- overall_sentiment: "bullish" | "bearish" | "neutral"
- sentiment_score: number (-1 to 1, where -1 is very bearish, 1 is very bullish)
- confidence: number (0-100)
- key_themes: array of main themes
- market_impact: "high" | "medium" | "low"
- summary: brief explanation of the sentiment analysis`;
  }

  /**
   * Create trading signal prompt template
   */
  createTradingSignalPrompt() {
    return `Generate a trading signal for {SYMBOL}:

Current Price: {CURRENT_PRICE}
Technical Indicators: {INDICATORS}
Volume: {VOLUME}

Provide a JSON response with:
- signal: "buy" | "sell" | "hold"
- confidence: number (0-100)
- entry_price: suggested entry price
- stop_loss: suggested stop loss level
- take_profit: suggested take profit level
- timeframe: recommended holding period
- reasoning: detailed explanation
- risk_reward_ratio: calculated ratio`;
  }

  /**
   * Create risk assessment prompt template
   */
  createRiskAssessmentPrompt() {
    return `Assess the risk for this trading position:

Position: {POSITION}
Market Conditions: {MARKET_CONDITIONS}

Provide a JSON response with:
- risk_level: "low" | "medium" | "high" | "extreme"
- risk_score: number (0-100)
- key_risks: array of main risk factors
- mitigation_strategies: array of risk mitigation suggestions
- position_sizing: recommended position size
- monitoring_points: key levels to watch
- exit_strategy: recommended exit conditions`;
  }

  /**
   * Create news summary prompt template
   */
  createNewsSummaryPrompt() {
    return `Summarize these financial news articles and extract key market information:

{ARTICLES}

Provide a JSON response with:
- summary: concise summary of all articles
- key_points: array of most important points
- market_impact: potential impact on markets
- affected_sectors: array of sectors that might be affected
- sentiment: overall sentiment from the news
- urgency: "high" | "medium" | "low"
- actionable_insights: trading or investment insights`;
  }

  /**
   * Parse market analysis response
   */
  parseAnalysisResponse(response) {
    try {
      const content = response.choices[0].message.content;
      const jsonMatch = content.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      // Fallback to text parsing
      return {
        trend: this.extractValue(content, 'trend') || 'neutral',
        confidence: parseInt(this.extractValue(content, 'confidence')) || 50,
        recommendation: this.extractValue(content, 'recommendation') || 'hold',
        reasoning: content,
        risk_level: this.extractValue(content, 'risk_level') || 'medium'
      };
    } catch (error) {
      logger.error('Error parsing analysis response:', error);
      return { error: 'Failed to parse analysis response', raw: response };
    }
  }

  /**
   * Parse sentiment analysis response
   */
  parseSentimentResponse(response) {
    try {
      const content = response.choices[0].message.content;
      const jsonMatch = content.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      return {
        overall_sentiment: 'neutral',
        sentiment_score: 0,
        confidence: 50,
        summary: content
      };
    } catch (error) {
      logger.error('Error parsing sentiment response:', error);
      return { error: 'Failed to parse sentiment response', raw: response };
    }
  }

  /**
   * Parse trading signal response
   */
  parseSignalResponse(response) {
    try {
      const content = response.choices[0].message.content;
      const jsonMatch = content.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      return {
        signal: 'hold',
        confidence: 50,
        reasoning: content,
        timeframe: '1D'
      };
    } catch (error) {
      logger.error('Error parsing signal response:', error);
      return { error: 'Failed to parse signal response', raw: response };
    }
  }

  /**
   * Parse risk assessment response
   */
  parseRiskResponse(response) {
    try {
      const content = response.choices[0].message.content;
      const jsonMatch = content.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      return {
        risk_level: 'medium',
        risk_score: 50,
        key_risks: [],
        mitigation_strategies: []
      };
    } catch (error) {
      logger.error('Error parsing risk response:', error);
      return { error: 'Failed to parse risk response', raw: response };
    }
  }

  /**
   * Parse news summary response
   */
  parseNewsResponse(response) {
    try {
      const content = response.choices[0].message.content;
      const jsonMatch = content.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      return {
        summary: content,
        key_points: [],
        market_impact: 'medium',
        sentiment: 'neutral'
      };
    } catch (error) {
      logger.error('Error parsing news response:', error);
      return { error: 'Failed to parse news response', raw: response };
    }
  }

  // ===== UTILITY METHODS =====

  /**
   * Extract value from text using regex
   *
   * @private
   * @param {string} text - Text to search in
   * @param {string} key - Key to search for
   * @returns {string|null} Extracted value or null
   */
  extractValue(text, key) {
    const regex = new RegExp(`${key}[:\\s]*([^\\n,}]+)`, 'i');
    const match = text.match(regex);
    return match ? match[1].trim().replace(/['"]/g, '') : null;
  }

  /**
   * Format price data for prompts
   *
   * @private
   * @param {Array} prices - Array of price objects
   * @returns {string} Formatted price data string
   */
  formatPriceData(prices) {
    if (!Array.isArray(prices) || prices.length === 0) {
      return 'No price data available';
    }

    const recent = prices.slice(-10);
    return recent.map(p =>
      `${p.timestamp}: O:${p.open} H:${p.high} L:${p.low} C:${p.close} V:${p.volume}`
    ).join('\n');
  }

  /**
   * Format indicators for prompts
   *
   * @private
   * @param {Object} indicators - Technical indicators object
   * @returns {string} Formatted indicators string
   */
  formatIndicators(indicators) {
    if (!indicators || typeof indicators !== 'object') {
      return 'No indicators available';
    }

    return Object.entries(indicators)
      .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
      .join('\n');
  }

  // ===== PUBLIC MANAGEMENT METHODS =====

  /**
   * Get model performance metrics
   *
   * @param {string} [modelName] - Specific model name (optional)
   * @param {string} [operation] - Specific operation (optional)
   * @returns {Object} Performance metrics
   *
   * @example
   * ```javascript
   * // Get all metrics
   * const allMetrics = aiService.getModelMetrics();
   *
   * // Get metrics for specific model
   * const gpt4Metrics = aiService.getModelMetrics('gpt-4');
   * ```
   */
  getModelMetrics(modelName = null, operation = null) {
    const result = {};

    for (const [key, metrics] of this.modelMetrics.entries()) {
      const [model, op] = key.split('_');

      if (modelName && model !== modelName) continue;
      if (operation && op !== operation) continue;

      result[key] = {
        ...metrics,
        successRate: metrics.totalRequests > 0 ?
          (metrics.successfulRequests / metrics.totalRequests * 100).toFixed(2) + '%' : '0%',
        errorTypes: Object.fromEntries(metrics.errorTypes)
      };
    }

    return result;
  }

  /**
   * Get response history
   *
   * @param {number} [limit=100] - Maximum number of entries to return
   * @param {string} [modelName] - Filter by model name
   * @param {string} [operation] - Filter by operation
   * @returns {Array} Response history entries
   */
  getResponseHistory(limit = 100, modelName = null, operation = null) {
    let history = this.responseHistory;

    if (modelName) {
      history = history.filter(entry => entry.model === modelName);
    }

    if (operation) {
      history = history.filter(entry => entry.operation === operation);
    }

    return history.slice(-limit);
  }

  /**
   * Get circuit breaker status
   *
   * @returns {Object} Circuit breaker status and metrics
   */
  getCircuitBreakerStatus() {
    return {
      state: this.circuitBreaker.state,
      failureCount: this.circuitBreaker.failureCount,
      failureThreshold: this.circuitBreaker.failureThreshold,
      lastFailureTime: this.circuitBreaker.lastFailureTime,
      recoveryTimeout: this.circuitBreaker.recoveryTimeout,
      halfOpenCalls: this.circuitBreaker.halfOpenCalls,
      halfOpenMaxCalls: this.circuitBreaker.halfOpenMaxCalls,
      isHealthy: this.circuitBreaker.state === 'CLOSED'
    };
  }

  /**
   * Reset circuit breaker (admin function)
   *
   * @returns {void}
   */
  resetCircuitBreaker() {
    this.circuitBreaker.state = 'CLOSED';
    this.circuitBreaker.failureCount = 0;
    this.circuitBreaker.lastFailureTime = null;
    this.circuitBreaker.halfOpenCalls = 0;
    logger.info('Circuit breaker manually reset');
  }

  /**
   * Get cache statistics
   *
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      ttl: this.cacheTTL,
      entries: Array.from(this.cache.keys()).map(key => ({
        key: key.substring(0, 20) + '...',
        timestamp: this.cache.get(key).timestamp,
        age: Date.now() - this.cache.get(key).timestamp
      }))
    };
  }

  /**
   * Compare model outputs for the same input
   *
   * @param {Object} marketData - Market data for analysis
   * @param {Array<string>} models - Models to compare
   * @param {string} operation - Operation to perform
   * @returns {Promise<Object>} Comparison results
   *
   * @example
   * ```javascript
   * const comparison = await aiService.compareModels(marketData,
   *   ['gpt-3.5-turbo', 'gpt-4'], 'market_analysis');
   * ```
   */
  async compareModels(marketData, models, operation = 'market_analysis') {
    const results = {};
    const startTime = Date.now();

    for (const model of models) {
      try {
        let result;
        switch (operation) {
          case 'market_analysis':
            result = await this.generateMarketAnalysis(marketData, {
              model,
              enableFallback: false
            });
            break;
          case 'trading_signal':
            result = await this.generateTradingSignal(marketData, marketData.indicators || {}, {
              model,
              enableFallback: false
            });
            break;
          default:
            throw new Error(`Unsupported operation: ${operation}`);
        }

        results[model] = {
          success: true,
          result,
          responseTime: result._metadata?.responseTime || 0
        };
      } catch (error) {
        results[model] = {
          success: false,
          error: error.message,
          responseTime: 0
        };
      }
    }

    return {
      comparison: results,
      totalTime: Date.now() - startTime,
      timestamp: new Date().toISOString(),
      operation,
      symbol: marketData.symbol
    };
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
    logger.info('OpenAI service cache cleared');
  }

  /**
   * Get service statistics
   */
  getStats() {
    return {
      cacheSize: this.cache.size,
      queueLength: this.requestQueue.length,
      isProcessing: this.isProcessing,
      cacheTTL: this.cacheTTL,
      rateLimitDelay: this.rateLimitDelay
    };
  }
}

// Create singleton instance
export const consolidatedOpenAIService = new ConsolidatedOpenAIService();
export default consolidatedOpenAIService;
