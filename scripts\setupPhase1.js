#!/usr/bin/env node

/**
 * Phase 1 Setup Script
 * 
 * Initializes database optimization and caching services for the Trading Signals App.
 * This script:
 * - Creates optimized database indexes
 * - Tests Redis connection
 * - Validates configuration
 * - Runs initial performance checks
 * 
 * @version 1.0.0
 */

import mongoose from 'mongoose';
import Redis from 'ioredis';
import logger from '../src/utils/logger.js';
import { createOptimizedIndexes } from './createOptimizedIndexes.js';
import dotenv from 'dotenv';
import chalk from 'chalk';

// Load environment variables
dotenv.config();

/**
 * Setup configuration
 */
const setupConfig = {
  mongodb: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/trading-signals',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      minPoolSize: parseInt(process.env.DB_MIN_POOL_SIZE || '5', 10),
      maxPoolSize: parseInt(process.env.DB_MAX_POOL_SIZE || '20', 10)
    }
  },
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    db: parseInt(process.env.REDIS_DB || '0', 10)
  },
  performance: {
    slowQueryThreshold: parseInt(process.env.DB_SLOW_QUERY_THRESHOLD || '100', 10),
    criticalQueryThreshold: parseInt(process.env.DB_CRITICAL_QUERY_THRESHOLD || '500', 10)
  }
};

/**
 * Print setup header
 */
function printHeader() {
  console.log(chalk.blue.bold('\n🚀 Trading Signals App - Phase 1 Setup'));
  console.log(chalk.blue('Database Optimization & Caching Implementation\n'));
  console.log(chalk.gray('This script will:'));
  console.log(chalk.gray('• Test database connection'));
  console.log(chalk.gray('• Create optimized indexes'));
  console.log(chalk.gray('• Test Redis connection'));
  console.log(chalk.gray('• Validate configuration'));
  console.log(chalk.gray('• Run performance checks\n'));
}

/**
 * Test MongoDB connection
 */
async function testMongoConnection() {
  try {
    console.log(chalk.yellow('📊 Testing MongoDB connection...'));
    
    await mongoose.connect(setupConfig.mongodb.uri, setupConfig.mongodb.options);
    
    // Test database operations
    const db = mongoose.connection.db;
    const adminDb = db.admin();
    const serverStatus = await adminDb.serverStatus();
    
    console.log(chalk.green('✓ MongoDB connection successful'));
    console.log(chalk.gray(`  Version: ${serverStatus.version}`));
    console.log(chalk.gray(`  Uptime: ${Math.round(serverStatus.uptime / 3600)} hours`));
    console.log(chalk.gray(`  Connections: ${serverStatus.connections.current}/${serverStatus.connections.available}`));
    
    return true;
  } catch (error) {
    console.log(chalk.red('✗ MongoDB connection failed:'), error.message);
    return false;
  }
}

/**
 * Test Redis connection
 */
async function testRedisConnection() {
  let redis = null;
  
  try {
    console.log(chalk.yellow('🔄 Testing Redis connection...'));
    
    redis = new Redis(setupConfig.redis.url, {
      db: setupConfig.redis.db,
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
      connectTimeout: 10000
    });
    
    // Test Redis operations
    await redis.ping();
    await redis.set('test:setup', 'phase1', 'EX', 10);
    const testValue = await redis.get('test:setup');
    await redis.del('test:setup');
    
    if (testValue !== 'phase1') {
      throw new Error('Redis read/write test failed');
    }
    
    const info = await redis.info('memory');
    const memoryLines = info.split('\r\n').filter(line => line.includes('used_memory_human'));
    const memoryUsage = memoryLines[0] ? memoryLines[0].split(':')[1] : 'Unknown';
    
    console.log(chalk.green('✓ Redis connection successful'));
    console.log(chalk.gray(`  Memory usage: ${memoryUsage}`));
    console.log(chalk.gray(`  Database: ${setupConfig.redis.db}`));
    
    return true;
  } catch (error) {
    console.log(chalk.red('✗ Redis connection failed:'), error.message);
    console.log(chalk.yellow('  Note: Redis is optional but recommended for optimal performance'));
    return false;
  } finally {
    if (redis) {
      await redis.quit();
    }
  }
}

/**
 * Create database indexes
 */
async function setupDatabaseIndexes() {
  try {
    console.log(chalk.yellow('🔧 Creating optimized database indexes...'));
    
    await createOptimizedIndexes();
    
    console.log(chalk.green('✓ Database indexes created successfully'));
    return true;
  } catch (error) {
    console.log(chalk.red('✗ Index creation failed:'), error.message);
    return false;
  }
}

/**
 * Validate configuration
 */
function validateConfiguration() {
  console.log(chalk.yellow('⚙️  Validating configuration...'));
  
  const requiredEnvVars = [
    'MONGODB_URI',
    'DB_MIN_POOL_SIZE',
    'DB_MAX_POOL_SIZE',
    'DB_SLOW_QUERY_THRESHOLD'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.log(chalk.red('✗ Missing required environment variables:'));
    missingVars.forEach(varName => {
      console.log(chalk.red(`  - ${varName}`));
    });
    return false;
  }
  
  // Validate numeric values
  const numericVars = {
    DB_MIN_POOL_SIZE: parseInt(process.env.DB_MIN_POOL_SIZE, 10),
    DB_MAX_POOL_SIZE: parseInt(process.env.DB_MAX_POOL_SIZE, 10),
    DB_SLOW_QUERY_THRESHOLD: parseInt(process.env.DB_SLOW_QUERY_THRESHOLD, 10)
  };
  
  for (const [varName, value] of Object.entries(numericVars)) {
    if (isNaN(value) || value <= 0) {
      console.log(chalk.red(`✗ Invalid value for ${varName}: ${process.env[varName]}`));
      return false;
    }
  }
  
  // Validate pool size configuration
  if (numericVars.DB_MIN_POOL_SIZE >= numericVars.DB_MAX_POOL_SIZE) {
    console.log(chalk.red('✗ DB_MIN_POOL_SIZE must be less than DB_MAX_POOL_SIZE'));
    return false;
  }
  
  console.log(chalk.green('✓ Configuration validation passed'));
  console.log(chalk.gray(`  Pool size: ${numericVars.DB_MIN_POOL_SIZE}-${numericVars.DB_MAX_POOL_SIZE} connections`));
  console.log(chalk.gray(`  Slow query threshold: ${numericVars.DB_SLOW_QUERY_THRESHOLD}ms`));
  
  return true;
}

/**
 * Run performance checks
 */
async function runPerformanceChecks() {
  try {
    console.log(chalk.yellow('🏃 Running performance checks...'));
    
    const db = mongoose.connection.db;
    
    // Check collection stats
    const collections = ['marketdata', 'users', 'tradingsignals', 'sentimenthistory'];
    const stats = {};
    
    for (const collectionName of collections) {
      try {
        const collection = db.collection(collectionName);
        const collectionStats = await collection.stats();
        stats[collectionName] = {
          count: collectionStats.count || 0,
          size: Math.round((collectionStats.size || 0) / 1024 / 1024 * 100) / 100, // MB
          indexes: collectionStats.nindexes || 0
        };
      } catch (error) {
        stats[collectionName] = { count: 0, size: 0, indexes: 0, error: 'Collection not found' };
      }
    }
    
    console.log(chalk.green('✓ Performance check completed'));
    console.log(chalk.gray('  Collection statistics:'));
    
    for (const [name, stat] of Object.entries(stats)) {
      if (stat.error) {
        console.log(chalk.gray(`    ${name}: ${stat.error}`));
      } else {
        console.log(chalk.gray(`    ${name}: ${stat.count} docs, ${stat.size}MB, ${stat.indexes} indexes`));
      }
    }
    
    return true;
  } catch (error) {
    console.log(chalk.red('✗ Performance check failed:'), error.message);
    return false;
  }
}

/**
 * Print setup summary
 */
function printSummary(results) {
  console.log(chalk.blue.bold('\n📋 Setup Summary'));
  console.log(chalk.blue('================\n'));
  
  const checks = [
    { name: 'MongoDB Connection', status: results.mongodb },
    { name: 'Redis Connection', status: results.redis },
    { name: 'Database Indexes', status: results.indexes },
    { name: 'Configuration', status: results.config },
    { name: 'Performance Checks', status: results.performance }
  ];
  
  checks.forEach(check => {
    const icon = check.status ? '✓' : '✗';
    const color = check.status ? chalk.green : chalk.red;
    console.log(color(`${icon} ${check.name}`));
  });
  
  const successCount = checks.filter(c => c.status).length;
  const totalCount = checks.length;
  
  console.log(chalk.blue(`\nSetup completed: ${successCount}/${totalCount} checks passed`));
  
  if (successCount === totalCount) {
    console.log(chalk.green.bold('\n🎉 Phase 1 setup completed successfully!'));
    console.log(chalk.green('Your Trading Signals App is now optimized with:'));
    console.log(chalk.green('• Database connection pooling'));
    console.log(chalk.green('• Optimized indexes for fast queries'));
    console.log(chalk.green('• Redis caching layer'));
    console.log(chalk.green('• Performance monitoring'));
  } else {
    console.log(chalk.yellow.bold('\n⚠️  Phase 1 setup completed with warnings'));
    console.log(chalk.yellow('Some optional features may not be available.'));
    console.log(chalk.yellow('Check the failed items above and update your configuration.'));
  }
  
  console.log(chalk.blue('\nNext steps:'));
  console.log(chalk.blue('• Start your application: npm start'));
  console.log(chalk.blue('• Monitor performance: http://localhost:3000/api/performance/dashboard'));
  console.log(chalk.blue('• Review logs for any issues'));
  console.log(chalk.blue('• Proceed to Phase 2: Enhanced Security Implementation\n'));
}

/**
 * Main setup function
 */
async function runSetup() {
  printHeader();
  
  const results = {
    mongodb: false,
    redis: false,
    indexes: false,
    config: false,
    performance: false
  };
  
  try {
    // Validate configuration first
    results.config = validateConfiguration();
    if (!results.config) {
      console.log(chalk.red('\n❌ Configuration validation failed. Please check your .env file.'));
      process.exit(1);
    }
    
    // Test MongoDB connection
    results.mongodb = await testMongoConnection();
    if (!results.mongodb) {
      console.log(chalk.red('\n❌ MongoDB connection failed. Please check your database configuration.'));
      process.exit(1);
    }
    
    // Create database indexes
    results.indexes = await setupDatabaseIndexes();
    
    // Test Redis connection (optional)
    results.redis = await testRedisConnection();
    
    // Run performance checks
    results.performance = await runPerformanceChecks();
    
  } catch (error) {
    console.log(chalk.red('\n❌ Setup failed with error:'), error.message);
    process.exit(1);
  } finally {
    // Close database connection
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
  
  printSummary(results);
  
  // Exit with appropriate code
  const criticalChecks = [results.mongodb, results.config];
  const allCriticalPassed = criticalChecks.every(check => check);
  
  process.exit(allCriticalPassed ? 0 : 1);
}

// Run setup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runSetup().catch(error => {
    console.error(chalk.red('Setup script failed:'), error);
    process.exit(1);
  });
}

export { runSetup };
export default runSetup;
