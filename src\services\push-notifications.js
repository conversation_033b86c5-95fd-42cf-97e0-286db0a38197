/**
 * Push Notifications Module for Trading Signals App
 *
 * This file contains functionality for registering and sending push notifications
 * for important trading signals and market events.
 */

// Check if browser supports notifications
const notificationsSupported = 'Notification' in window;

// When the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize push notifications
    initializePushNotifications();

    // Add event listener for notification settings
    const notificationToggle = document.getElementById('notificationToggle');
    if (notificationToggle) {
        notificationToggle.addEventListener('change', function() {
            if (this.checked) {
                requestNotificationPermission();
            } else {
                disableNotifications();
            }
        });
    }
});

// Function to initialize push notifications
function initializePushNotifications() {
    // Check if notifications are supported
    if (!notificationsSupported) {
        console.warn('Push notifications are not supported by this browser');
        disableNotificationUI();
        return;
    }

    // Check if permission is already granted
    if (Notification.permission === 'granted') {
        enableNotificationUI();
    } else if (Notification.permission === 'denied') {
        disableNotificationUI();
    } else {
        // Default state (permission not asked yet)
        const notificationToggle = document.getElementById('notificationToggle');
        if (notificationToggle) {
            notificationToggle.checked = false;
        }
    }
}

// Function to request notification permission
function requestNotificationPermission() {
    if (!notificationsSupported) return;

    Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
            console.log('Notification permission granted');
            enableNotificationUI();

            // Send a test notification
            sendTestNotification();
        } else {
            console.warn('Notification permission denied');
            disableNotificationUI();
        }
    });
}

// Function to enable notification UI
function enableNotificationUI() {
    const notificationToggle = document.getElementById('notificationToggle');
    if (notificationToggle) {
        notificationToggle.checked = true;
    }

    const notificationStatus = document.getElementById('notificationStatus');
    if (notificationStatus) {
        notificationStatus.textContent = 'Enabled';
        notificationStatus.className = 'text-success';
    }

    // Save preference to localStorage
    localStorage.setItem('notifications_enabled', 'true');
}

// Function to disable notification UI
function disableNotificationUI() {
    const notificationToggle = document.getElementById('notificationToggle');
    if (notificationToggle) {
        notificationToggle.checked = false;
    }

    const notificationStatus = document.getElementById('notificationStatus');
    if (notificationStatus) {
        if (!notificationsSupported) {
            notificationStatus.textContent = 'Not Supported';
            notificationStatus.className = 'text-muted';
        } else {
            notificationStatus.textContent = 'Disabled';
            notificationStatus.className = 'text-danger';
        }
    }

    // Save preference to localStorage
    localStorage.setItem('notifications_enabled', 'false');
}

// Function to disable notifications
function disableNotifications() {
    disableNotificationUI();
}

// Function to send a test notification
function sendTestNotification() {
    if (Notification.permission === 'granted') {
        const notification = new Notification('Trading Signals App', {
            body: 'Notifications are now enabled. You will receive alerts for important trading signals.',
            icon: 'favicon.ico'
        });

        notification.onclick = function() {
            window.focus();
            notification.close();
        };
    }
}

/**
 * Enhanced Signal Notification Service
 */
class SignalNotificationService {
  constructor() {
    this.notificationHistory = [];
    this.userPreferences = {
      enabled: true,
      minConfidence: 60,
      symbols: [],
      signalTypes: ['BUY', 'SELL'],
      soundEnabled: true,
      vibrationEnabled: true,
      showOnlyHighConfidence: false
    };
    this.rateLimiter = new Map(); // Symbol -> last notification time
    this.minNotificationInterval = 300000; // 5 minutes between notifications for same symbol
  }

  /**
   * Process a trading signal for notification
   * @param {Object} signal - Trading signal object
   * @param {Object} userPreferences - User notification preferences
   */
  async processSignal(signal, userPreferences = null) {
    try {
      if (!signal || !this.shouldNotify(signal, userPreferences)) {
        return false;
      }

      // Check rate limiting
      if (this.isRateLimited(signal.symbol)) {
        console.log(`Notification rate limited for ${signal.symbol}`);
        return false;
      }

      // Send notification
      const notificationSent = await this.sendSignalNotification(signal);

      if (notificationSent) {
        // Update rate limiter
        this.rateLimiter.set(signal.symbol, Date.now());

        // Add to history
        this.addToHistory(signal);

        console.log(`Notification sent for ${signal.type} signal on ${signal.symbol}`);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error processing signal notification:', error);
      return false;
    }
  }

  /**
   * Check if signal should trigger notification
   * @param {Object} signal - Trading signal
   * @param {Object} userPreferences - User preferences
   * @returns {boolean} Should notify
   */
  shouldNotify(signal, userPreferences = null) {
    const prefs = userPreferences || this.userPreferences;

    // Check if notifications are enabled
    if (!prefs.enabled) return false;

    // Check notification permission
    if (Notification.permission !== 'granted') return false;

    // Check confidence threshold
    if (signal.confidence < prefs.minConfidence) return false;

    // Check signal type filter
    if (!prefs.signalTypes.includes(signal.type)) return false;

    // Check symbol filter (if specified)
    if (prefs.symbols.length > 0 && !prefs.symbols.includes(signal.symbol)) return false;

    // Check high confidence only filter
    if (prefs.showOnlyHighConfidence && signal.confidence < 80) return false;

    return true;
  }

  /**
   * Check if signal is rate limited
   * @param {string} symbol - Trading symbol
   * @returns {boolean} Is rate limited
   */
  isRateLimited(symbol) {
    const lastNotification = this.rateLimiter.get(symbol);
    if (!lastNotification) return false;

    return (Date.now() - lastNotification) < this.minNotificationInterval;
  }

  /**
   * Send signal notification
   * @param {Object} signal - Trading signal
   * @returns {Promise<boolean>} Notification sent successfully
   */
  async sendSignalNotification(signal) {
    try {
      const title = this.formatNotificationTitle(signal);
      const body = this.formatNotificationBody(signal);
      const options = this.getNotificationOptions(signal);

      const notification = new Notification(title, {
        body,
        ...options
      });

      // Set up click handler
      notification.onclick = () => {
        window.focus();
        notification.close();
        this.handleNotificationClick(signal);
      };

      // Auto-close for low confidence signals
      if (signal.confidence < 80) {
        setTimeout(() => {
          notification.close();
        }, 10000);
      }

      // Play sound if enabled
      if (this.userPreferences.soundEnabled) {
        this.playNotificationSound(signal);
      }

      return true;
    } catch (error) {
      console.error('Error sending notification:', error);
      return false;
    }
  }

  /**
   * Format notification title
   * @param {Object} signal - Trading signal
   * @returns {string} Formatted title
   */
  formatNotificationTitle(signal) {
    const confidenceEmoji = signal.confidence >= 80 ? '🔥' : signal.confidence >= 60 ? '⚡' : '📊';
    return `${confidenceEmoji} ${signal.type} Signal: ${signal.symbol}`;
  }

  /**
   * Format notification body
   * @param {Object} signal - Trading signal
   * @returns {string} Formatted body
   */
  formatNotificationBody(signal) {
    let body = `Confidence: ${signal.confidence}%`;

    if (signal.entryPrice) {
      body += `\nEntry: ${signal.entryPrice}`;
    }

    if (signal.stopLoss && signal.takeProfit) {
      body += `\nSL: ${signal.stopLoss} | TP: ${signal.takeProfit}`;
    }

    if (signal.reasoning && signal.reasoning.length < 100) {
      body += `\n${signal.reasoning}`;
    }

    return body;
  }

  /**
   * Get notification options
   * @param {Object} signal - Trading signal
   * @returns {Object} Notification options
   */
  getNotificationOptions(signal) {
    return {
      icon: this.getSignalIcon(signal),
      badge: '/icons/notification-badge.png',
      tag: `signal-${signal.symbol}-${signal.type}`,
      requireInteraction: signal.confidence >= 80,
      vibrate: this.userPreferences.vibrationEnabled ? [200, 100, 200] : [],
      actions: [
        {
          action: 'view',
          title: 'View Details',
          icon: '/icons/view-icon.png'
        },
        {
          action: 'dismiss',
          title: 'Dismiss',
          icon: '/icons/dismiss-icon.png'
        }
      ],
      data: {
        signalId: signal.id,
        symbol: signal.symbol,
        type: signal.type,
        timestamp: signal.timestamp
      }
    };
  }

  /**
   * Get signal icon based on type and confidence
   * @param {Object} signal - Trading signal
   * @returns {string} Icon path
   */
  getSignalIcon(signal) {
    const confidence = signal.confidence || 50;

    if (signal.type === 'BUY') {
      return confidence >= 80 ? '/icons/buy-strong.png' : '/icons/buy-signal.png';
    } else if (signal.type === 'SELL') {
      return confidence >= 80 ? '/icons/sell-strong.png' : '/icons/sell-signal.png';
    }

    return '/icons/hold-signal.png';
  }

  /**
   * Handle notification click
   * @param {Object} signal - Trading signal
   */
  handleNotificationClick(signal) {
    // Scroll to signals section
    const signalsSection = document.getElementById('signalsCollapse') ||
                          document.getElementById('signals-container');

    if (signalsSection) {
      signalsSection.scrollIntoView({ behavior: 'smooth' });
    }

    // Highlight the specific signal if possible
    const signalElement = document.querySelector(`[data-signal-id="${signal.id}"]`);
    if (signalElement) {
      signalElement.classList.add('highlight');
      setTimeout(() => signalElement.classList.remove('highlight'), 3000);
    }

    // Dispatch custom event for other components
    window.dispatchEvent(new CustomEvent('signal-notification-clicked', {
      detail: { signal }
    }));
  }

  /**
   * Play notification sound
   * @param {Object} signal - Trading signal
   */
  playNotificationSound(signal) {
    try {
      const audio = new Audio();

      if (signal.confidence >= 80) {
        audio.src = '/sounds/high-confidence-signal.mp3';
      } else if (signal.type === 'BUY') {
        audio.src = '/sounds/buy-signal.mp3';
      } else {
        audio.src = '/sounds/sell-signal.mp3';
      }

      audio.volume = 0.3;
      audio.play().catch(e => console.log('Could not play notification sound:', e));
    } catch (error) {
      console.log('Error playing notification sound:', error);
    }
  }

  /**
   * Add notification to history
   * @param {Object} signal - Trading signal
   */
  addToHistory(signal) {
    this.notificationHistory.unshift({
      signal,
      timestamp: new Date().toISOString(),
      id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    });

    // Keep only last 100 notifications
    if (this.notificationHistory.length > 100) {
      this.notificationHistory = this.notificationHistory.slice(0, 100);
    }
  }

  /**
   * Update user preferences
   * @param {Object} preferences - New preferences
   */
  updatePreferences(preferences) {
    this.userPreferences = { ...this.userPreferences, ...preferences };

    // Save to localStorage
    try {
      localStorage.setItem('signalNotificationPreferences', JSON.stringify(this.userPreferences));
    } catch (error) {
      console.error('Error saving notification preferences:', error);
    }
  }

  /**
   * Load user preferences from localStorage
   */
  loadPreferences() {
    try {
      const saved = localStorage.getItem('signalNotificationPreferences');
      if (saved) {
        this.userPreferences = { ...this.userPreferences, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('Error loading notification preferences:', error);
    }
  }

  /**
   * Get notification history
   * @param {number} limit - Maximum number of notifications to return
   * @returns {Array} Notification history
   */
  getHistory(limit = 50) {
    return this.notificationHistory.slice(0, limit);
  }

  /**
   * Clear notification history
   */
  clearHistory() {
    this.notificationHistory = [];
  }
}

// Create global instance
const signalNotificationService = new SignalNotificationService();

// Load preferences on initialization
signalNotificationService.loadPreferences();

// Make service globally available
window.signalNotificationService = signalNotificationService;

// Function to send a trading signal notification (legacy compatibility)
function sendTradingSignalNotification(signal) {
  return signalNotificationService.processSignal(signal);
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    SignalNotificationService,
    signalNotificationService,
    sendTradingSignalNotification
  };
}

// Function to send a price alert notification
function sendPriceAlertNotification(symbol, price, condition) {
    if (Notification.permission !== 'granted') return;

    const title = `Price Alert: ${symbol}`;
    const body = `Price ${condition} ${price}`;

    const notification = new Notification(title, {
        body: body,
        icon: 'price-alert-icon.png',
        badge: 'notification-badge.png',
        vibrate: [200, 100, 200]
    });

    notification.onclick = function() {
        window.focus();
        notification.close();

        // Scroll to chart section
        const chartSection = document.getElementById('chartCollapse');
        if (chartSection) {
            chartSection.scrollIntoView({ behavior: 'smooth' });
        }
    };
}

// Function to send an economic event notification
function sendEconomicEventNotification(event) {
    if (Notification.permission !== 'granted' || !event) return;

    const title = `Economic Event: ${event.currency}`;
    const body = `${event.event} in ${getTimeUntilEvent(event.time)}`;

    const notification = new Notification(title, {
        body: body,
        icon: 'economic-event-icon.png',
        badge: 'notification-badge.png',
        vibrate: [200, 100, 200]
    });

    notification.onclick = function() {
        window.focus();
        notification.close();

        // Scroll to economic calendar section
        const calendarSection = document.getElementById('economicCalendarCollapse');
        if (calendarSection) {
            calendarSection.scrollIntoView({ behavior: 'smooth' });
        }
    };
}

// Helper function to get time until event
function getTimeUntilEvent(eventTime) {
    const now = new Date();
    const [hours, minutes] = eventTime.split(':').map(Number);

    const eventDate = new Date(now);
    eventDate.setHours(hours, minutes, 0, 0);

    // If event time is in the past for today, assume it's for tomorrow
    if (eventDate < now) {
        eventDate.setDate(eventDate.getDate() + 1);
    }

    const diffMs = eventDate - now;
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 60) {
        return `${diffMins} minutes`;
    } else {
        const hours = Math.floor(diffMins / 60);
        const mins = diffMins % 60;
        return `${hours} hour${hours > 1 ? 's' : ''} ${mins > 0 ? `and ${mins} minute${mins > 1 ? 's' : ''}` : ''}`;
    }
}

// Function to check for upcoming economic events and send notifications
function checkUpcomingEconomicEvents() {
    if (Notification.permission !== 'granted') return;

    // Get upcoming high-impact events
    const upcomingEvents = window.economicCalendar?.getUpcomingHighImpactEvents() || [];

    // Check if there are any events in the next 30 minutes
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    upcomingEvents.forEach(event => {
        const [hour, minute] = event.time.split(':').map(Number);

        // Calculate minutes until event
        let minutesUntil = (hour - currentHour) * 60 + (minute - currentMinute);
        if (minutesUntil < 0) {
            minutesUntil += 24 * 60; // Event is tomorrow
        }

        // Notify if event is within 30 minutes
        if (minutesUntil <= 30 && minutesUntil > 0) {
            sendEconomicEventNotification(event);
        }
    });
}

// Set up periodic checks for economic events
setInterval(checkUpcomingEconomicEvents, 5 * 60 * 1000); // Check every 5 minutes

// Export functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initializePushNotifications,
        sendTradingSignalNotification,
        sendPriceAlertNotification,
        sendEconomicEventNotification
    };
} else {
    // For browser environment
    window.pushNotifications = {
        initializePushNotifications,
        sendTradingSignalNotification,
        sendPriceAlertNotification,
        sendEconomicEventNotification
    };
}
