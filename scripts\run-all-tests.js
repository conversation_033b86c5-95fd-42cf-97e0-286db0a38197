#!/usr/bin/env node

/**
 * Comprehensive Test Runner for Trading Signals App
 * 
 * Executes all test types in sequence with detailed reporting:
 * - Unit tests with coverage
 * - Integration tests
 * - Performance tests
 * - End-to-end tests
 * 
 * Generates consolidated reports and performance metrics.
 */

const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');

class TestRunner {
  constructor() {
    this.results = {
      unit: null,
      integration: null,
      performance: null,
      e2e: null,
      overall: {
        startTime: null,
        endTime: null,
        totalDuration: 0,
        passed: 0,
        failed: 0,
        coverage: null
      }
    };
    
    this.config = {
      timeout: 300000, // 5 minutes per test suite
      retries: 2,
      parallel: false,
      generateReports: true,
      coverageThreshold: 85
    };
  }

  /**
   * Run all test suites
   */
  async runAllTests() {
    console.log('🚀 Starting comprehensive test suite for Trading Signals App\n');
    this.results.overall.startTime = new Date();

    try {
      // Setup test environment
      await this.setupTestEnvironment();

      // Run test suites in sequence
      await this.runUnitTests();
      await this.runIntegrationTests();
      await this.runPerformanceTests();
      await this.runE2ETests();

      // Generate reports
      if (this.config.generateReports) {
        await this.generateConsolidatedReport();
      }

      // Display summary
      this.displaySummary();

      // Exit with appropriate code
      const hasFailures = this.results.overall.failed > 0;
      process.exit(hasFailures ? 1 : 0);

    } catch (error) {
      console.error('❌ Test runner failed:', error);
      process.exit(1);
    }
  }

  /**
   * Setup test environment
   */
  async setupTestEnvironment() {
    console.log('🔧 Setting up test environment...');
    
    // Create test directories
    await this.ensureDirectory('test-results');
    await this.ensureDirectory('coverage');
    
    // Set environment variables
    process.env.NODE_ENV = 'test';
    process.env.LOG_LEVEL = 'error';
    
    console.log('✅ Test environment ready\n');
  }

  /**
   * Run unit tests
   */
  async runUnitTests() {
    console.log('🧪 Running unit tests...');
    
    try {
      const result = await this.runCommand('npm', ['run', 'test:unit'], {
        timeout: this.config.timeout
      });
      
      this.results.unit = {
        passed: result.success,
        output: result.output,
        coverage: await this.extractCoverage(),
        duration: result.duration
      };
      
      if (result.success) {
        console.log('✅ Unit tests passed');
      } else {
        console.log('❌ Unit tests failed');
        this.results.overall.failed++;
      }
      
    } catch (error) {
      console.log('❌ Unit tests failed with error:', error.message);
      this.results.unit = { passed: false, error: error.message };
      this.results.overall.failed++;
    }
    
    console.log('');
  }

  /**
   * Run integration tests
   */
  async runIntegrationTests() {
    console.log('🔗 Running integration tests...');
    
    try {
      const result = await this.runCommand('npm', ['run', 'test:integration'], {
        timeout: this.config.timeout
      });
      
      this.results.integration = {
        passed: result.success,
        output: result.output,
        duration: result.duration
      };
      
      if (result.success) {
        console.log('✅ Integration tests passed');
        this.results.overall.passed++;
      } else {
        console.log('❌ Integration tests failed');
        this.results.overall.failed++;
      }
      
    } catch (error) {
      console.log('❌ Integration tests failed with error:', error.message);
      this.results.integration = { passed: false, error: error.message };
      this.results.overall.failed++;
    }
    
    console.log('');
  }

  /**
   * Run performance tests
   */
  async runPerformanceTests() {
    console.log('⚡ Running performance tests...');
    
    try {
      const result = await this.runCommand('npm', ['run', 'test:performance'], {
        timeout: this.config.timeout * 2 // Performance tests may take longer
      });
      
      this.results.performance = {
        passed: result.success,
        output: result.output,
        duration: result.duration
      };
      
      if (result.success) {
        console.log('✅ Performance tests passed');
        this.results.overall.passed++;
      } else {
        console.log('❌ Performance tests failed');
        this.results.overall.failed++;
      }
      
    } catch (error) {
      console.log('❌ Performance tests failed with error:', error.message);
      this.results.performance = { passed: false, error: error.message };
      this.results.overall.failed++;
    }
    
    console.log('');
  }

  /**
   * Run end-to-end tests
   */
  async runE2ETests() {
    console.log('🌐 Running end-to-end tests...');
    
    try {
      const result = await this.runCommand('npm', ['run', 'test:e2e'], {
        timeout: this.config.timeout * 3 // E2E tests take the longest
      });
      
      this.results.e2e = {
        passed: result.success,
        output: result.output,
        duration: result.duration
      };
      
      if (result.success) {
        console.log('✅ End-to-end tests passed');
        this.results.overall.passed++;
      } else {
        console.log('❌ End-to-end tests failed');
        this.results.overall.failed++;
      }
      
    } catch (error) {
      console.log('❌ End-to-end tests failed with error:', error.message);
      this.results.e2e = { passed: false, error: error.message };
      this.results.overall.failed++;
    }
    
    console.log('');
  }

  /**
   * Run a command and return results
   */
  async runCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const child = spawn(command, args, {
        stdio: 'pipe',
        shell: true,
        ...options
      });

      let output = '';
      let errorOutput = '';

      child.stdout.on('data', (data) => {
        output += data.toString();
      });

      child.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      const timeout = setTimeout(() => {
        child.kill();
        reject(new Error(`Command timed out after ${options.timeout}ms`));
      }, options.timeout || 60000);

      child.on('close', (code) => {
        clearTimeout(timeout);
        const duration = Date.now() - startTime;
        
        resolve({
          success: code === 0,
          output: output + errorOutput,
          duration,
          exitCode: code
        });
      });

      child.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  /**
   * Extract coverage information
   */
  async extractCoverage() {
    try {
      const coveragePath = path.join(process.cwd(), 'coverage', 'coverage-summary.json');
      const coverageData = await fs.readFile(coveragePath, 'utf8');
      const coverage = JSON.parse(coverageData);
      
      return {
        lines: coverage.total.lines.pct,
        functions: coverage.total.functions.pct,
        branches: coverage.total.branches.pct,
        statements: coverage.total.statements.pct
      };
    } catch (error) {
      console.warn('⚠️ Could not extract coverage data:', error.message);
      return null;
    }
  }

  /**
   * Generate consolidated test report
   */
  async generateConsolidatedReport() {
    console.log('📊 Generating consolidated test report...');
    
    this.results.overall.endTime = new Date();
    this.results.overall.totalDuration = this.results.overall.endTime - this.results.overall.startTime;
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: this.results.overall,
      testSuites: {
        unit: this.results.unit,
        integration: this.results.integration,
        performance: this.results.performance,
        e2e: this.results.e2e
      },
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        ci: !!process.env.CI
      }
    };
    
    // Save JSON report
    const reportPath = path.join(process.cwd(), 'test-results', 'consolidated-report.json');
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    
    // Generate HTML report
    await this.generateHTMLReport(report);
    
    console.log(`✅ Reports generated in test-results/`);
  }

  /**
   * Generate HTML report
   */
  async generateHTMLReport(report) {
    const html = `
<!DOCTYPE html>
<html>
<head>
    <title>Trading Signals App - Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e9ecef; padding: 15px; border-radius: 5px; flex: 1; }
        .passed { background: #d4edda; color: #155724; }
        .failed { background: #f8d7da; color: #721c24; }
        .test-suite { margin: 20px 0; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; }
        .coverage { background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Trading Signals App - Test Report</h1>
        <p>Generated: ${report.timestamp}</p>
        <p>Total Duration: ${Math.round(report.summary.totalDuration / 1000)}s</p>
    </div>
    
    <div class="summary">
        <div class="metric ${report.summary.passed > 0 ? 'passed' : ''}">
            <h3>Passed</h3>
            <p>${report.summary.passed}</p>
        </div>
        <div class="metric ${report.summary.failed > 0 ? 'failed' : ''}">
            <h3>Failed</h3>
            <p>${report.summary.failed}</p>
        </div>
    </div>
    
    ${Object.entries(report.testSuites).map(([name, suite]) => `
        <div class="test-suite ${suite?.passed ? 'passed' : 'failed'}">
            <h3>${name.charAt(0).toUpperCase() + name.slice(1)} Tests</h3>
            <p>Status: ${suite?.passed ? '✅ Passed' : '❌ Failed'}</p>
            ${suite?.duration ? `<p>Duration: ${Math.round(suite.duration / 1000)}s</p>` : ''}
            ${suite?.error ? `<p>Error: ${suite.error}</p>` : ''}
        </div>
    `).join('')}
    
    ${report.testSuites.unit?.coverage ? `
        <div class="coverage">
            <h3>Code Coverage</h3>
            <p>Lines: ${report.testSuites.unit.coverage.lines}%</p>
            <p>Functions: ${report.testSuites.unit.coverage.functions}%</p>
            <p>Branches: ${report.testSuites.unit.coverage.branches}%</p>
            <p>Statements: ${report.testSuites.unit.coverage.statements}%</p>
        </div>
    ` : ''}
</body>
</html>`;
    
    const htmlPath = path.join(process.cwd(), 'test-results', 'report.html');
    await fs.writeFile(htmlPath, html);
  }

  /**
   * Display test summary
   */
  displaySummary() {
    console.log('\n📋 Test Summary');
    console.log('================');
    console.log(`Total Duration: ${Math.round(this.results.overall.totalDuration / 1000)}s`);
    console.log(`Passed Suites: ${this.results.overall.passed}`);
    console.log(`Failed Suites: ${this.results.overall.failed}`);
    
    if (this.results.unit?.coverage) {
      console.log('\n📊 Coverage Summary');
      console.log('==================');
      console.log(`Lines: ${this.results.unit.coverage.lines}%`);
      console.log(`Functions: ${this.results.unit.coverage.functions}%`);
      console.log(`Branches: ${this.results.unit.coverage.branches}%`);
      console.log(`Statements: ${this.results.unit.coverage.statements}%`);
      
      const avgCoverage = (
        this.results.unit.coverage.lines +
        this.results.unit.coverage.functions +
        this.results.unit.coverage.branches +
        this.results.unit.coverage.statements
      ) / 4;
      
      if (avgCoverage >= this.config.coverageThreshold) {
        console.log(`✅ Coverage target met (${avgCoverage.toFixed(1)}% >= ${this.config.coverageThreshold}%)`);
      } else {
        console.log(`❌ Coverage target not met (${avgCoverage.toFixed(1)}% < ${this.config.coverageThreshold}%)`);
      }
    }
    
    console.log('\n' + (this.results.overall.failed === 0 ? '🎉 All tests passed!' : '❌ Some tests failed'));
  }

  /**
   * Ensure directory exists
   */
  async ensureDirectory(dir) {
    try {
      await fs.access(dir);
    } catch {
      await fs.mkdir(dir, { recursive: true });
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const runner = new TestRunner();
  runner.runAllTests().catch(console.error);
}

module.exports = TestRunner;
