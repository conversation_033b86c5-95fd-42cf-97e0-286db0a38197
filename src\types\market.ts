/**
 * Market Data Types and Interfaces
 * 
 * Comprehensive type definitions for market data, quotes, technical indicators,
 * and financial instruments across different asset classes.
 * 
 * @version 1.0.0
 */

import {
  MarketType,
  Timeframe,
  Timestamp,
  Price,
  Volume,
  Percentage
} from './common';

// ============================================================================
// CORE MARKET DATA INTERFACES
// ============================================================================

/**
 * Unified Market Data Interface
 * 
 * Standard market data structure used across all data sources
 * (Alpha Vantage, Polygon, FMP, etc.)
 */
export interface MarketData {
  // Core identification
  symbol: string;
  timeframe: Timeframe;
  timestamp: Timestamp;
  
  // OHLCV data
  open: Price;
  high: Price;
  low: Price;
  close: Price;
  volume: Volume;
  
  // Additional data
  adjustedClose?: Price;
  dividendAmount?: number;
  splitCoefficient?: number;
  
  // Metadata
  source: string;
  lastUpdated: Timestamp;
  marketType: MarketType;
}

/**
 * Real-time Quote Data
 */
export interface Quote {
  symbol: string;
  price: Price;
  bid?: Price;
  ask?: Price;
  bidSize?: Volume;
  askSize?: Volume;
  spread?: Price;
  change: Price;
  changePercent: Percentage;
  volume: Volume;
  averageVolume?: Volume;
  marketCap?: number;
  timestamp: Timestamp;
  source: string;
  marketType: MarketType;
  marketStatus: MarketStatus;
}

export enum MarketStatus {
  OPEN = 'open',
  CLOSED = 'closed',
  PRE_MARKET = 'pre_market',
  AFTER_HOURS = 'after_hours',
  HOLIDAY = 'holiday'
}

// ============================================================================
// TECHNICAL INDICATORS
// ============================================================================

export interface TechnicalIndicatorData {
  symbol: string;
  timeframe: Timeframe;
  timestamp: Timestamp;
  
  // Moving Averages
  sma?: MovingAverageIndicator;
  ema?: MovingAverageIndicator;
  wma?: MovingAverageIndicator;
  
  // Oscillators
  rsi?: RSIIndicator;
  macd?: MACDIndicator;
  stochastic?: StochasticIndicator;
  williams?: WilliamsRIndicator;
  cci?: CCIIndicator;
  
  // Trend Indicators
  bollinger?: BollingerBandsIndicator;
  ichimoku?: IchimokuIndicator;
  parabolicSAR?: ParabolicSARIndicator;
  
  // Volume Indicators
  obv?: OBVIndicator;
  volumeProfile?: VolumeProfileIndicator;
  
  // Volatility Indicators
  atr?: ATRIndicator;
  volatility?: VolatilityIndicator;
  
  // Support/Resistance
  pivotPoints?: PivotPointsIndicator;
  fibonacciRetracements?: FibonacciIndicator;
}

// ============================================================================
// SPECIFIC INDICATOR INTERFACES
// ============================================================================

export interface MovingAverageIndicator {
  period: number;
  value: number;
  trend: 'up' | 'down' | 'sideways';
  slope?: number;
  crossover?: {
    type: 'golden_cross' | 'death_cross' | 'price_cross';
    direction: 'bullish' | 'bearish';
    timestamp: Timestamp;
  };
}

export interface RSIIndicator {
  period: number;
  value: number;
  overbought: boolean;
  oversold: boolean;
  divergence?: 'bullish' | 'bearish' | 'hidden_bullish' | 'hidden_bearish';
  trend: 'up' | 'down' | 'sideways';
}

export interface MACDIndicator {
  fastPeriod: number;
  slowPeriod: number;
  signalPeriod: number;
  macd: number;
  signal: number;
  histogram: number;
  crossover?: {
    type: 'signal_line' | 'zero_line';
    direction: 'bullish' | 'bearish';
    timestamp: Timestamp;
  };
}

export interface StochasticIndicator {
  kPeriod: number;
  dPeriod: number;
  k: number;
  d: number;
  overbought: boolean;
  oversold: boolean;
  crossover?: {
    direction: 'bullish' | 'bearish';
    timestamp: Timestamp;
  };
}

export interface WilliamsRIndicator {
  period: number;
  value: number;
  overbought: boolean;
  oversold: boolean;
}

export interface CCIIndicator {
  period: number;
  value: number;
  overbought: boolean;
  oversold: boolean;
  trend: 'up' | 'down' | 'sideways';
}

export interface BollingerBandsIndicator {
  period: number;
  standardDeviations: number;
  upper: number;
  middle: number;
  lower: number;
  bandwidth: number;
  percentB: number;
  squeeze: boolean;
  position: 'above_upper' | 'above_middle' | 'below_middle' | 'below_lower' | 'between';
}

export interface IchimokuIndicator {
  tenkanSen: number;
  kijunSen: number;
  senkouSpanA: number;
  senkouSpanB: number;
  chikouSpan: number;
  signal: 'bullish' | 'bearish' | 'neutral';
  cloudColor: 'green' | 'red';
  pricePosition: 'above_cloud' | 'in_cloud' | 'below_cloud';
}

export interface ParabolicSARIndicator {
  value: number;
  trend: 'up' | 'down';
  acceleration: number;
  reversal?: {
    timestamp: Timestamp;
    price: Price;
  };
}

export interface OBVIndicator {
  value: number;
  trend: 'up' | 'down' | 'sideways';
  divergence?: 'bullish' | 'bearish';
}

export interface VolumeProfileIndicator {
  valueAreaHigh: Price;
  valueAreaLow: Price;
  pointOfControl: Price;
  volumeAtPrice: Array<{
    price: Price;
    volume: Volume;
    percentage: Percentage;
  }>;
}

export interface ATRIndicator {
  period: number;
  value: number;
  percentOfPrice: Percentage;
  trend: 'increasing' | 'decreasing' | 'stable';
}

export interface VolatilityIndicator {
  value: Percentage;
  period: number;
  trend: 'increasing' | 'decreasing' | 'stable';
  percentile: Percentage;
}

export interface PivotPointsIndicator {
  pivot: Price;
  resistance1: Price;
  resistance2: Price;
  resistance3: Price;
  support1: Price;
  support2: Price;
  support3: Price;
  type: 'standard' | 'fibonacci' | 'woodie' | 'camarilla';
}

export interface FibonacciIndicator {
  high: Price;
  low: Price;
  levels: Array<{
    level: Percentage;
    price: Price;
    type: 'retracement' | 'extension';
  }>;
}

// ============================================================================
// MARKET ANALYSIS TYPES
// ============================================================================

export interface MarketAnalysis {
  symbol: string;
  timeframe: Timeframe;
  timestamp: Timestamp;
  
  // Overall assessment
  sentiment: 'bullish' | 'bearish' | 'neutral';
  confidence: Percentage;
  strength: 'weak' | 'moderate' | 'strong';
  
  // Technical analysis
  trend: {
    short: 'up' | 'down' | 'sideways';
    medium: 'up' | 'down' | 'sideways';
    long: 'up' | 'down' | 'sideways';
    overall: 'up' | 'down' | 'sideways';
  };
  
  // Key levels
  support: Price[];
  resistance: Price[];
  keyLevels: Array<{
    price: Price;
    type: 'support' | 'resistance' | 'pivot';
    strength: 'weak' | 'moderate' | 'strong';
  }>;
  
  // Pattern recognition
  patterns: Array<{
    name: string;
    type: 'bullish' | 'bearish' | 'neutral';
    confidence: Percentage;
    target?: Price;
    invalidation?: Price;
  }>;
  
  // Risk assessment
  volatility: 'low' | 'medium' | 'high';
  riskLevel: 'low' | 'medium' | 'high';
  
  // Recommendations
  recommendation: 'strong_buy' | 'buy' | 'hold' | 'sell' | 'strong_sell';
  targetPrice?: Price;
  stopLoss?: Price;
  
  // Metadata
  source: string;
  model?: string;
  processingTime?: number;
}

// ============================================================================
// MARKET DATA REQUESTS
// ============================================================================

export interface MarketDataRequest {
  symbol: string;
  timeframe: Timeframe;
  from?: Timestamp;
  to?: Timestamp;
  limit?: number;
  includeIndicators?: boolean;
  indicators?: string[];
}

export interface QuoteRequest {
  symbols: string[];
  includeExtendedHours?: boolean;
}

export interface TechnicalIndicatorRequest {
  symbol: string;
  timeframe: Timeframe;
  indicators: string[];
  periods?: Record<string, number>;
  from?: Timestamp;
  to?: Timestamp;
}

// ============================================================================
// MARKET DATA RESPONSES
// ============================================================================

export interface MarketDataResponse {
  symbol: string;
  timeframe: Timeframe;
  data: MarketData[];
  indicators?: TechnicalIndicatorData;
  analysis?: MarketAnalysis;
  metadata: {
    source: string;
    lastUpdated: Timestamp;
    dataPoints: number;
    fromCache: boolean;
    processingTime: number;
  };
}

export interface QuoteResponse {
  quotes: Quote[];
  metadata: {
    source: string;
    timestamp: Timestamp;
    marketStatus: MarketStatus;
  };
}

// ============================================================================
// SYMBOL AND INSTRUMENT TYPES
// ============================================================================

export interface Symbol {
  symbol: string;
  name: string;
  marketType: MarketType;
  exchange: string;
  currency: string;
  country: string;
  sector?: string;
  industry?: string;
  active: boolean;
  tradable: boolean;
  minPriceIncrement?: number;
  lotSize?: number;
  marginRequirement?: Percentage;
}

export interface Exchange {
  code: string;
  name: string;
  country: string;
  timezone: string;
  marketHours: {
    open: string;
    close: string;
    preMarket?: string;
    afterHours?: string;
  };
  holidays: string[];
  active: boolean;
}

// ============================================================================
// TYPE GUARDS AND VALIDATORS
// ============================================================================

export function isMarketData(obj: any): obj is MarketData {
  return (
    obj &&
    typeof obj.symbol === 'string' &&
    typeof obj.open === 'number' &&
    typeof obj.high === 'number' &&
    typeof obj.low === 'number' &&
    typeof obj.close === 'number' &&
    typeof obj.volume === 'number' &&
    typeof obj.timestamp === 'string'
  );
}

export function isQuote(obj: any): obj is Quote {
  return (
    obj &&
    typeof obj.symbol === 'string' &&
    typeof obj.price === 'number' &&
    typeof obj.change === 'number' &&
    typeof obj.changePercent === 'number' &&
    typeof obj.timestamp === 'string'
  );
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export function calculateChange(current: Price, previous: Price): { change: Price; changePercent: Percentage } {
  const change = current - previous;
  const changePercent = previous !== 0 ? (change / previous) * 100 : 0;
  
  return { change, changePercent };
}

export function isMarketOpen(exchange: Exchange, timestamp: Timestamp = new Date().toISOString()): boolean {
  const date = new Date(timestamp);
  const timeString = date.toTimeString().substring(0, 5);
  
  // Check if it's a holiday
  const dateString = date.toISOString().substring(0, 10);
  if (exchange.holidays.includes(dateString)) {
    return false;
  }
  
  // Check if it's within market hours
  return timeString >= exchange.marketHours.open && timeString <= exchange.marketHours.close;
}

export function formatPrice(price: Price, decimals: number = 2): string {
  return price.toFixed(decimals);
}

export function formatVolume(volume: Volume): string {
  if (volume >= 1000000) {
    return `${(volume / 1000000).toFixed(1)}M`;
  } else if (volume >= 1000) {
    return `${(volume / 1000).toFixed(1)}K`;
  }
  return volume.toString();
}
