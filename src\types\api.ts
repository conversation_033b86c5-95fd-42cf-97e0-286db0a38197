/**
 * API Types and Interfaces
 * 
 * Standardized API request/response types ensuring consistency
 * across all endpoints and client-server communication.
 * 
 * @version 1.0.0
 */

import {
  Timestamp,
  ObjectId,
  ValidationError,
  ResponseMetadata,
  PaginationMetadata,
  ErrorInfo
} from './common';
import { UnifiedSignal, SignalFilter, SignalQueryOptions, SignalAnalytics } from './signals';
import { User, UserPreferences } from './users';
import { MarketData, TechnicalIndicatorData } from './market';

// ============================================================================
// STANDARD API RESPONSE WRAPPER
// ============================================================================

/**
 * Standard API Response Format
 * 
 * All API endpoints must return responses in this format to ensure
 * consistency across the application.
 */
export interface APIResponse<T = any> {
  status: 'success' | 'error';
  data?: T;
  message?: string;
  errors?: ValidationError[];
  meta: ResponseMetadata;
}

/**
 * Paginated API Response
 * 
 * Used for endpoints that return lists of items with pagination.
 */
export interface PaginatedAPIResponse<T = any> extends APIResponse<T[]> {
  meta: ResponseMetadata & {
    pagination: PaginationMetadata;
  };
}

// ============================================================================
// AUTHENTICATION API TYPES
// ============================================================================

export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresAt: Timestamp;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  acceptTerms: boolean;
  referralCode?: string;
}

export interface RegisterResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresAt: Timestamp;
  emailVerificationRequired: boolean;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
  expiresAt: Timestamp;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// ============================================================================
// SIGNAL API TYPES
// ============================================================================

export interface CreateSignalRequest {
  symbol: string;
  type: 'BUY' | 'SELL' | 'HOLD';
  entryPrice: number;
  stopLoss?: number;
  takeProfit?: number;
  timeframe: string;
  analysis?: string;
  confidence?: number;
  source?: string;
}

export interface UpdateSignalRequest {
  id: string;
  status?: 'ACTIVE' | 'CLOSED' | 'EXPIRED' | 'CANCELLED';
  stopLoss?: number;
  takeProfit?: number;
  analysis?: string;
  confidence?: number;
}

export interface GetSignalsRequest {
  filter?: SignalFilter;
  sort?: {
    field: string;
    direction: 'asc' | 'desc';
  };
  page?: number;
  limit?: number;
}

export interface GetSignalsResponse {
  signals: UnifiedSignal[];
  total: number;
  analytics?: SignalAnalytics;
}

export interface GenerateSignalRequest {
  symbol: string;
  timeframe: string;
  marketData?: any;
  indicators?: any;
  options?: {
    source?: string;
    minConfidence?: number;
    enableAI?: boolean;
    model?: string;
  };
}

export interface GenerateSignalResponse {
  signals: UnifiedSignal[];
  metadata: {
    processingTime: number;
    signalsGenerated: number;
    source: string;
    model?: string;
  };
}

// ============================================================================
// MARKET DATA API TYPES
// ============================================================================

export interface GetMarketDataRequest {
  symbol: string;
  timeframe: string;
  from?: Timestamp;
  to?: Timestamp;
  limit?: number;
}

export interface GetMarketDataResponse {
  symbol: string;
  timeframe: string;
  data: MarketData[];
  lastUpdated: Timestamp;
  source: string;
}

export interface GetQuoteRequest {
  symbol: string;
}

export interface GetQuoteResponse {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  timestamp: Timestamp;
  source: string;
}

export interface GetTechnicalIndicatorsRequest {
  symbol: string;
  timeframe: string;
  indicators: string[];
  period?: number;
}

export interface GetTechnicalIndicatorsResponse {
  symbol: string;
  timeframe: string;
  indicators: TechnicalIndicatorData;
  calculatedAt: Timestamp;
}

// ============================================================================
// USER API TYPES
// ============================================================================

export interface GetUserProfileResponse {
  user: User;
  preferences: UserPreferences;
  subscription: {
    plan: string;
    status: string;
    features: string[];
    limits: any;
  };
}

export interface UpdateUserProfileRequest {
  firstName?: string;
  lastName?: string;
  avatar?: string;
  preferences?: Partial<UserPreferences>;
}

export interface UpdateUserPreferencesRequest {
  preferences: Partial<UserPreferences>;
}

// ============================================================================
// AI API TYPES
// ============================================================================

export interface AIAnalysisRequest {
  symbol: string;
  timeframe: string;
  marketData: any;
  indicators?: any;
  options?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
  };
}

export interface AIAnalysisResponse {
  analysis: {
    summary: string;
    sentiment: 'bullish' | 'bearish' | 'neutral';
    confidence: number;
    keyPoints: string[];
    risks: string[];
    opportunities: string[];
  };
  signals?: UnifiedSignal[];
  metadata: {
    model: string;
    processingTime: number;
    tokens: number;
    cost?: number;
  };
}

export interface AITradingSignalRequest {
  symbol: string;
  marketData: {
    currentPrice: number;
    volume: number;
    priceHistory?: number[];
  };
  indicators: any;
  options?: {
    model?: string;
    temperature?: number;
    enableFallback?: boolean;
  };
}

export interface AITradingSignalResponse {
  signal: UnifiedSignal;
  reasoning: string;
  confidence: number;
  metadata: {
    model: string;
    processingTime: number;
    fallbackUsed?: boolean;
  };
}

// ============================================================================
// WEBSOCKET API TYPES
// ============================================================================

export interface WebSocketSubscribeRequest {
  type: 'subscribe';
  channels: string[];
  symbols?: string[];
  userId?: string;
}

export interface WebSocketUnsubscribeRequest {
  type: 'unsubscribe';
  channels: string[];
  symbols?: string[];
}

export interface WebSocketSignalUpdate {
  type: 'signal_update';
  data: UnifiedSignal;
  timestamp: Timestamp;
}

export interface WebSocketMarketUpdate {
  type: 'market_update';
  data: {
    symbol: string;
    price: number;
    change: number;
    volume: number;
    timestamp: Timestamp;
  };
}

export interface WebSocketNotification {
  type: 'notification';
  data: {
    id: string;
    title: string;
    message: string;
    level: 'info' | 'warning' | 'error' | 'success';
    timestamp: Timestamp;
    userId?: string;
  };
}

export interface WebSocketConnectionStatus {
  type: 'connection_status';
  data: {
    connected: boolean;
    clientId: string;
    timestamp: Timestamp;
  };
}

export type WebSocketMessage = 
  | WebSocketSubscribeRequest
  | WebSocketUnsubscribeRequest
  | WebSocketSignalUpdate
  | WebSocketMarketUpdate
  | WebSocketNotification
  | WebSocketConnectionStatus;

// ============================================================================
// ERROR API TYPES
// ============================================================================

export interface APIErrorResponse {
  status: 'error';
  error: {
    code: string;
    message: string;
    details?: any;
    suggestion?: string;
  };
  meta: ResponseMetadata;
}

export interface ValidationErrorResponse extends APIErrorResponse {
  errors: ValidationError[];
}

// ============================================================================
// HEALTH CHECK API TYPES
// ============================================================================

export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: Timestamp;
  version: string;
  services: {
    database: 'connected' | 'disconnected';
    redis: 'connected' | 'disconnected';
    ai: 'available' | 'unavailable';
    websocket: 'active' | 'inactive';
  };
  metrics: {
    uptime: number;
    memoryUsage: number;
    cpuUsage: number;
    activeConnections: number;
  };
}

// ============================================================================
// ANALYTICS API TYPES
// ============================================================================

export interface GetAnalyticsRequest {
  type: 'signals' | 'performance' | 'usage';
  timeframe: string;
  from?: Timestamp;
  to?: Timestamp;
  groupBy?: 'day' | 'week' | 'month';
}

export interface GetAnalyticsResponse {
  type: string;
  timeframe: string;
  data: any[];
  summary: {
    total: number;
    average: number;
    trend: 'up' | 'down' | 'stable';
  };
  generatedAt: Timestamp;
}

// ============================================================================
// TYPE GUARDS
// ============================================================================

export function isAPIResponse<T>(obj: any): obj is APIResponse<T> {
  return (
    obj &&
    typeof obj === 'object' &&
    (obj.status === 'success' || obj.status === 'error') &&
    obj.meta &&
    typeof obj.meta.timestamp === 'string'
  );
}

export function isAPIError(obj: any): obj is APIErrorResponse {
  return (
    isAPIResponse(obj) &&
    obj.status === 'error' &&
    obj.error &&
    typeof obj.error.code === 'string' &&
    typeof obj.error.message === 'string'
  );
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export function createSuccessResponse<T>(
  data: T,
  message?: string,
  meta?: Partial<ResponseMetadata>
): APIResponse<T> {
  return {
    status: 'success',
    data,
    message,
    meta: {
      timestamp: new Date().toISOString(),
      requestId: `req-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
      version: '1.0.0',
      ...meta
    }
  };
}

export function createErrorResponse(
  code: string,
  message: string,
  details?: any,
  suggestion?: string
): APIErrorResponse {
  return {
    status: 'error',
    error: {
      code,
      message,
      details,
      suggestion
    },
    meta: {
      timestamp: new Date().toISOString(),
      requestId: `req-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
      version: '1.0.0'
    }
  };
}
