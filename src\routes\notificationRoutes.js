import express from 'express';
import { protect } from '../middleware/auth.js';
import {
  getNotifications,
  createNotification,
  markAsRead,
  markAllAsRead,
  deleteNotification,
} from '../controllers/notificationController.js';

const router = express.Router();

// Protect all notification routes
router.use(protect);

// Get all notifications for the user
router.get('/', getNotifications);

// Create a new notification
router.post('/', createNotification);

// Mark a notification as read
router.patch('/:id/read', markAsRead);

// Mark all notifications as read
router.patch('/read-all', markAllAsRead);

// Delete a notification
router.delete('/:id', deleteNotification);

export default router; 