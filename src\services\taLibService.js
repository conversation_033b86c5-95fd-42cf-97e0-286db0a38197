import talib from 'talib';

/**
 * Calculate a technical indicator using TA-Lib
 * @param {string} name - Indicator name (e.g., 'SMA', 'RSI', 'MACD')
 * @param {object} params - Parameters for the indicator
 * @returns {Promise<object>} - Result from TA-Lib
 */
export function calculateIndicator(name, params) {
  return new Promise((resolve, reject) => {
    talib.execute({
      name,
      ...params,
    }, (err, result) => {
      if (err) return reject(err);
      resolve(result);
    });
  });
}

// Example usage:
// calculateIndicator('SMA', { startIdx: 0, endIdx: close.length - 1, inReal: close, optInTimePeriod: 14 })
//   .then(res => console.log(res))
//   .catch(console.error); 