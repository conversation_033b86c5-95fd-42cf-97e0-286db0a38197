/**
 * Swagger Setup for Express App
 * 
 * This file sets up Swagger UI for the Express app.
 */

const swaggerUi = require('swagger-ui-express');
const swaggerSpec = require('./swagger');
const logger = require('../utils/logger');

/**
 * Set up Swagger UI for Express app
 * @param {Express} app - Express app instance
 */
function setupSwagger(app) {
  // Serve Swagger UI
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Trading Signals App API Documentation',
    customfavIcon: '/favicon.ico'
  }));
  
  // Serve Swagger spec as JSON
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerSpec);
  });
  
  logger.info('Swagger UI set up at /api-docs');
}

module.exports = setupSwagger;
