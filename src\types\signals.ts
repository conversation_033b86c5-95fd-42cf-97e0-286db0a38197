/**
 * Signal Types and Interfaces
 * 
 * Comprehensive type definitions for trading signals, ensuring consistency
 * across frontend, backend, database, and API layers.
 * 
 * @version 1.0.0
 */

import {
  SignalType,
  SignalSource,
  SignalStatus,
  Timeframe,
  Timestamp,
  ObjectId,
  Price,
  Percentage,
  SignalMetadata,
  SignalPerformance,
  ValidationResult
} from './common';

// ============================================================================
// CORE SIGNAL INTERFACES
// ============================================================================

/**
 * Unified Signal Interface
 * 
 * This is the master interface that all signal objects must conform to
 * across the entire application. It ensures consistency between:
 * - Database documents
 * - API responses
 * - Frontend components
 * - WebSocket messages
 */
export interface UnifiedSignal {
  // Core identification
  id: string;
  _id?: ObjectId; // MongoDB ObjectId (optional for new signals)
  
  // Basic signal information
  symbol: string;
  type: SignalType;
  source: SignalSource;
  subSource?: string; // e.g., 'openai', 'rsi', 'macd'
  
  // Price information
  entryPrice: Price;
  stopLoss?: Price;
  takeProfit?: Price;
  currentPrice?: Price;
  
  // Signal quality and confidence
  confidence: Percentage; // 0-100
  strength?: Percentage; // Legacy support, use confidence instead
  qualityScore?: Percentage;
  
  // Timing and context
  timeframe: Timeframe;
  expiresAt?: Timestamp;
  
  // Analysis and reasoning
  analysis?: string;
  reasoning?: string;
  technicalIndicators?: TechnicalIndicators;
  
  // Risk management
  riskReward?: number;
  positionSize?: number;
  maxRisk?: Percentage;
  
  // Status and lifecycle
  status: SignalStatus;
  
  // User and system information
  userId?: ObjectId;
  generatedBy?: string; // 'system', 'user', 'ai'
  
  // Performance tracking
  performance?: SignalPerformance;
  
  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
  
  // Metadata
  metadata: SignalMetadata;
}

// ============================================================================
// TECHNICAL ANALYSIS TYPES
// ============================================================================

export interface TechnicalIndicators {
  // Moving Averages
  sma?: MovingAverageData;
  ema?: MovingAverageData;
  
  // Oscillators
  rsi?: RSIData;
  macd?: MACDData;
  stochastic?: StochasticData;
  
  // Trend Indicators
  bollinger?: BollingerBandsData;
  ichimoku?: IchimokuData;
  
  // Volume Indicators
  volume?: VolumeData;
  obv?: number; // On-Balance Volume
  
  // Support/Resistance
  supportLevels?: Price[];
  resistanceLevels?: Price[];
  
  // Pattern Recognition
  patterns?: PatternData[];
}

export interface MovingAverageData {
  period: number;
  value: number;
  trend: 'up' | 'down' | 'sideways';
  crossover?: 'bullish' | 'bearish';
}

export interface RSIData {
  value: number;
  period: number;
  overbought: boolean;
  oversold: boolean;
  divergence?: 'bullish' | 'bearish';
}

export interface MACDData {
  macd: number;
  signal: number;
  histogram: number;
  crossover?: 'bullish' | 'bearish';
}

export interface StochasticData {
  k: number;
  d: number;
  overbought: boolean;
  oversold: boolean;
}

export interface BollingerBandsData {
  upper: number;
  middle: number;
  lower: number;
  squeeze: boolean;
  position: 'above' | 'below' | 'middle';
}

export interface IchimokuData {
  tenkanSen: number;
  kijunSen: number;
  senkouSpanA: number;
  senkouSpanB: number;
  chikouSpan: number;
  signal: 'bullish' | 'bearish' | 'neutral';
}

export interface VolumeData {
  current: number;
  average: number;
  trend: 'increasing' | 'decreasing' | 'stable';
  spike: boolean;
}

export interface PatternData {
  name: string;
  type: 'bullish' | 'bearish' | 'neutral';
  confidence: Percentage;
  timeframe: Timeframe;
  description?: string;
}

// ============================================================================
// SIGNAL GENERATION TYPES
// ============================================================================

export interface SignalGenerationRequest {
  symbol: string;
  timeframe: Timeframe;
  marketData: MarketDataPoint[];
  indicators?: TechnicalIndicators;
  options?: SignalGenerationOptions;
}

export interface SignalGenerationOptions {
  source?: SignalSource;
  minConfidence?: Percentage;
  maxSignals?: number;
  riskLevel?: 'LOW' | 'MEDIUM' | 'HIGH';
  enableAI?: boolean;
  enableTraditional?: boolean;
  model?: string;
  temperature?: number;
}

export interface SignalGenerationResult {
  success: boolean;
  signals: UnifiedSignal[];
  errors?: string[];
  metadata: {
    processingTime: number;
    signalsGenerated: number;
    source: SignalSource;
    timestamp: Timestamp;
  };
}

export interface MarketDataPoint {
  timestamp: Timestamp;
  open: Price;
  high: Price;
  low: Price;
  close: Price;
  volume: number;
  symbol: string;
}

// ============================================================================
// SIGNAL FILTERING AND SORTING
// ============================================================================

export interface SignalFilter {
  symbols?: string[];
  types?: SignalType[];
  sources?: SignalSource[];
  timeframes?: Timeframe[];
  minConfidence?: Percentage;
  maxConfidence?: Percentage;
  status?: SignalStatus[];
  dateFrom?: Timestamp;
  dateTo?: Timestamp;
  userId?: ObjectId;
}

export interface SignalSortOptions {
  field: 'createdAt' | 'confidence' | 'symbol' | 'type' | 'performance';
  direction: 'asc' | 'desc';
}

export interface SignalQueryOptions {
  filter?: SignalFilter;
  sort?: SignalSortOptions;
  limit?: number;
  offset?: number;
  includePerformance?: boolean;
  includeMetadata?: boolean;
}

// ============================================================================
// SIGNAL VALIDATION
// ============================================================================

export interface SignalValidationRules {
  requiredFields: (keyof UnifiedSignal)[];
  priceValidation: {
    minPrice: Price;
    maxPrice: Price;
    stopLossRequired: boolean;
    takeProfitRequired: boolean;
  };
  confidenceValidation: {
    minConfidence: Percentage;
    maxConfidence: Percentage;
  };
  timeframeValidation: {
    allowedTimeframes: Timeframe[];
  };
}

// ============================================================================
// SIGNAL ANALYTICS
// ============================================================================

export interface SignalAnalytics {
  totalSignals: number;
  signalsByType: Record<SignalType, number>;
  signalsBySource: Record<SignalSource, number>;
  signalsByTimeframe: Record<Timeframe, number>;
  averageConfidence: Percentage;
  performanceMetrics: {
    winRate: Percentage;
    averageReturn: Percentage;
    totalPnL: number;
  };
  timeRange: {
    from: Timestamp;
    to: Timestamp;
  };
}

// ============================================================================
// SIGNAL SUBSCRIPTION TYPES
// ============================================================================

export interface SignalSubscription {
  id: string;
  userId: ObjectId;
  symbols: string[];
  timeframes: Timeframe[];
  minConfidence: Percentage;
  sources: SignalSource[];
  active: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// ============================================================================
// TYPE GUARDS AND VALIDATORS
// ============================================================================

export function isUnifiedSignal(obj: any): obj is UnifiedSignal {
  return (
    obj &&
    typeof obj.id === 'string' &&
    typeof obj.symbol === 'string' &&
    Object.values(SignalType).includes(obj.type) &&
    Object.values(SignalSource).includes(obj.source) &&
    typeof obj.entryPrice === 'number' &&
    typeof obj.confidence === 'number' &&
    Object.values(Timeframe).includes(obj.timeframe) &&
    Object.values(SignalStatus).includes(obj.status) &&
    typeof obj.createdAt === 'string' &&
    typeof obj.updatedAt === 'string'
  );
}

export function validateSignal(signal: Partial<UnifiedSignal>): ValidationResult {
  const errors: any[] = [];
  
  // Required field validation
  if (!signal.symbol) errors.push({ field: 'symbol', message: 'Symbol is required', code: 'REQUIRED' });
  if (!signal.type) errors.push({ field: 'type', message: 'Signal type is required', code: 'REQUIRED' });
  if (!signal.entryPrice) errors.push({ field: 'entryPrice', message: 'Entry price is required', code: 'REQUIRED' });
  
  // Price validation
  if (signal.entryPrice && signal.entryPrice <= 0) {
    errors.push({ field: 'entryPrice', message: 'Entry price must be positive', code: 'INVALID_PRICE' });
  }
  
  // Confidence validation
  if (signal.confidence !== undefined && (signal.confidence < 0 || signal.confidence > 100)) {
    errors.push({ field: 'confidence', message: 'Confidence must be between 0 and 100', code: 'INVALID_RANGE' });
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export function createSignalId(): string {
  return `signal-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
}

export function calculateRiskReward(entryPrice: Price, stopLoss?: Price, takeProfit?: Price): number | undefined {
  if (!stopLoss || !takeProfit) return undefined;
  
  const risk = Math.abs(entryPrice - stopLoss);
  const reward = Math.abs(takeProfit - entryPrice);
  
  return risk > 0 ? reward / risk : undefined;
}

export function isSignalExpired(signal: UnifiedSignal): boolean {
  if (!signal.expiresAt) return false;
  return new Date(signal.expiresAt) < new Date();
}
