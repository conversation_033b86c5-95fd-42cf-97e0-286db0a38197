import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import rateLimit from 'express-rate-limit';
import Jo<PERSON> from 'joi';
import mongoose from 'mongoose';
import { redisCacheService } from './redisCacheService.js';
import logger from '../utils/logger.js';

/**
 * Enhanced Security Service - Phase 2 Implementation
 *
 * Comprehensive security solution providing:
 * - JWT refresh token mechanism with httpOnly cookies
 * - RBAC system (Admin/Premium/Basic roles)
 * - Rate limiting with user-based limits
 * - Input validation using Joi schemas
 * - Session management with Redis
 * - Password security with bcrypt
 *
 * @class EnhancedSecurityService
 * @version 2.0.0
 */
export class EnhancedSecurityService {
  constructor() {
    // JWT Configuration
    this.jwtConfig = {
      accessSecret: process.env.JWT_SECRET || 'your-access-secret',
      refreshSecret: process.env.JWT_REFRESH_SECRET || 'your-refresh-secret',
      accessExpiry: process.env.JWT_ACCESS_EXPIRY || '15m',
      refreshExpiry: process.env.JWT_REFRESH_EXPIRY || '7d'
    };

    // RBAC Configuration
    this.roles = {
      ADMIN: {
        name: 'admin',
        permissions: ['*'], // All permissions
        rateLimit: { requests: 0, window: 0 }, // No limits
        features: ['ai_advanced', 'user_management', 'system_config', 'analytics']
      },
      PREMIUM: {
        name: 'premium',
        permissions: ['ai_analysis', 'historical_data', 'advanced_charts', 'export_data'],
        rateLimit: { requests: 1000, window: 3600000 }, // 1000/hour
        features: ['ai_advanced', 'historical_data', 'unlimited_api']
      },
      BASIC: {
        name: 'basic',
        permissions: ['basic_analysis', 'current_data'],
        rateLimit: { requests: 100, window: 3600000 }, // 100/hour
        features: ['basic_charts', 'limited_api']
      }
    };

    // Rate Limiting Configuration
    this.rateLimitConfig = {
      basic: {
        windowMs: parseInt(process.env.RATE_LIMIT_BASIC_WINDOW || '3600000', 10),
        max: parseInt(process.env.RATE_LIMIT_BASIC_REQUESTS || '100', 10),
        message: 'Too many requests from this IP, please try again later.'
      },
      premium: {
        windowMs: parseInt(process.env.RATE_LIMIT_PREMIUM_WINDOW || '3600000', 10),
        max: parseInt(process.env.RATE_LIMIT_PREMIUM_REQUESTS || '1000', 10),
        message: 'Rate limit exceeded for premium user.'
      },
      ai: {
        windowMs: parseInt(process.env.RATE_LIMIT_AI_WINDOW || '60000', 10),
        max: parseInt(process.env.RATE_LIMIT_AI_REQUESTS || '10', 10),
        message: 'AI service rate limit exceeded.'
      }
    };

    // Session Configuration
    this.sessionConfig = {
      timeout: parseInt(process.env.SESSION_TIMEOUT || '1800000', 10), // 30 minutes
      maxConcurrent: parseInt(process.env.MAX_CONCURRENT_SESSIONS || '3', 10),
      secret: process.env.SESSION_SECRET || 'your-session-secret'
    };

    // Password Configuration
    this.passwordConfig = {
      saltRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
      minLength: parseInt(process.env.PASSWORD_MIN_LENGTH || '8', 10),
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true
    };

    // Validation Schemas
    this.validationSchemas = this.initializeValidationSchemas();

    // Rate limiters
    this.rateLimiters = this.initializeRateLimiters();

    // Active sessions tracking
    this.activeSessions = new Map();

    logger.info('Enhanced Security Service initialized');
  }

  /**
   * Initialize Joi validation schemas
   *
   * @private
   * @returns {Object} Validation schemas
   */
  initializeValidationSchemas() {
    return {
      // User registration schema
      userRegistration: Joi.object({
        email: Joi.string().email().required().max(255),
        password: Joi.string().min(this.passwordConfig.minLength).required()
          .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]'))
          .message('Password must contain uppercase, lowercase, number, and special character'),
        firstName: Joi.string().min(2).max(50).required(),
        lastName: Joi.string().min(2).max(50).required(),
        role: Joi.string().valid('basic', 'premium').default('basic')
      }),

      // User login schema
      userLogin: Joi.object({
        email: Joi.string().email().required(),
        password: Joi.string().required(),
        rememberMe: Joi.boolean().default(false)
      }),

      // Market data query schema
      marketDataQuery: Joi.object({
        symbol: Joi.string().uppercase().min(1).max(10).required(),
        timeframe: Joi.string().valid('1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w').default('1d'),
        limit: Joi.number().integer().min(1).max(1000).default(100),
        startDate: Joi.date().iso(),
        endDate: Joi.date().iso().greater(Joi.ref('startDate'))
      }),

      // AI analysis request schema
      aiAnalysisRequest: Joi.object({
        symbol: Joi.string().uppercase().min(1).max(10).required(),
        timeframe: Joi.string().valid('1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w').required(),
        analysisType: Joi.string().valid('market_analysis', 'trading_signal', 'sentiment_analysis', 'risk_assessment').required(),
        model: Joi.string().valid('gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo-preview').default('gpt-3.5-turbo'),
        options: Joi.object({
          temperature: Joi.number().min(0).max(1).default(0.3),
          enableFallback: Joi.boolean().default(true)
        }).default({})
      }),

      // Trading signal schema
      tradingSignal: Joi.object({
        symbol: Joi.string().uppercase().required(),
        signal: Joi.string().valid('BUY', 'SELL', 'HOLD').required(),
        confidence: Joi.number().min(0).max(100).required(),
        price: Joi.number().positive().required(),
        stopLoss: Joi.number().positive(),
        takeProfit: Joi.number().positive(),
        timeframe: Joi.string().required(),
        reasoning: Joi.string().max(1000)
      }),

      // User profile update schema
      userProfileUpdate: Joi.object({
        firstName: Joi.string().min(2).max(50),
        lastName: Joi.string().min(2).max(50),
        preferences: Joi.object({
          defaultTimeframe: Joi.string().valid('1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'),
          notifications: Joi.boolean(),
          theme: Joi.string().valid('light', 'dark')
        })
      }),

      // Pagination schema
      pagination: Joi.object({
        page: Joi.number().integer().min(1).default(1),
        limit: Joi.number().integer().min(1).max(100).default(20),
        sortBy: Joi.string().default('createdAt'),
        sortOrder: Joi.string().valid('asc', 'desc').default('desc')
      })
    };
  }

  /**
   * Initialize rate limiters
   *
   * @private
   * @returns {Object} Rate limiter instances
   */
  initializeRateLimiters() {
    return {
      // Basic IP-based rate limiter
      basic: rateLimit({
        windowMs: this.rateLimitConfig.basic.windowMs,
        max: this.rateLimitConfig.basic.max,
        message: { error: this.rateLimitConfig.basic.message },
        standardHeaders: true,
        legacyHeaders: false,
        keyGenerator: (req) => req.ip
      }),

      // User-based rate limiter for authenticated requests
      authenticated: rateLimit({
        windowMs: this.rateLimitConfig.premium.windowMs,
        max: (req) => {
          const userRole = req.user?.role || 'basic';
          return this.roles[userRole.toUpperCase()]?.rateLimit.requests || this.rateLimitConfig.basic.max;
        },
        message: (req) => ({
          error: `Rate limit exceeded for ${req.user?.role || 'basic'} user`,
          retryAfter: Math.ceil(this.rateLimitConfig.premium.windowMs / 1000)
        }),
        keyGenerator: (req) => req.user?.id || req.ip,
        skip: (req) => req.user?.role === 'admin'
      }),

      // AI endpoints specific rate limiter
      ai: rateLimit({
        windowMs: this.rateLimitConfig.ai.windowMs,
        max: this.rateLimitConfig.ai.max,
        message: { error: this.rateLimitConfig.ai.message },
        keyGenerator: (req) => req.user?.id || req.ip,
        skip: (req) => req.user?.role === 'admin'
      })
    };
  }

  /**
   * Generate JWT access token
   *
   * @param {Object} payload - Token payload
   * @returns {string} JWT access token
   */
  generateAccessToken(payload) {
    return jwt.sign(payload, this.jwtConfig.accessSecret, {
      expiresIn: this.jwtConfig.accessExpiry,
      issuer: 'trading-signals-app',
      audience: 'trading-signals-users'
    });
  }

  /**
   * Generate JWT refresh token
   *
   * @param {Object} payload - Token payload
   * @returns {string} JWT refresh token
   */
  generateRefreshToken(payload) {
    return jwt.sign(payload, this.jwtConfig.refreshSecret, {
      expiresIn: this.jwtConfig.refreshExpiry,
      issuer: 'trading-signals-app',
      audience: 'trading-signals-users'
    });
  }

  /**
   * Verify JWT access token
   *
   * @param {string} token - JWT token
   * @returns {Object} Decoded token payload
   */
  verifyAccessToken(token) {
    try {
      return jwt.verify(token, this.jwtConfig.accessSecret, {
        issuer: 'trading-signals-app',
        audience: 'trading-signals-users'
      });
    } catch (error) {
      throw new Error(`Invalid access token: ${error.message}`);
    }
  }

  /**
   * Verify JWT refresh token
   *
   * @param {string} token - JWT refresh token
   * @returns {Object} Decoded token payload
   */
  verifyRefreshToken(token) {
    try {
      return jwt.verify(token, this.jwtConfig.refreshSecret, {
        issuer: 'trading-signals-app',
        audience: 'trading-signals-users'
      });
    } catch (error) {
      throw new Error(`Invalid refresh token: ${error.message}`);
    }
  }

  /**
   * Hash password using bcrypt
   *
   * @param {string} password - Plain text password
   * @returns {Promise<string>} Hashed password
   */
  async hashPassword(password) {
    try {
      return await bcrypt.hash(password, this.passwordConfig.saltRounds);
    } catch (error) {
      logger.error('Error hashing password:', error);
      throw new Error('Password hashing failed');
    }
  }

  /**
   * Verify password against hash
   *
   * @param {string} password - Plain text password
   * @param {string} hash - Hashed password
   * @returns {Promise<boolean>} Password match result
   */
  async verifyPassword(password, hash) {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      logger.error('Error verifying password:', error);
      throw new Error('Password verification failed');
    }
  }

  /**
   * Create user session in Redis
   *
   * @param {string} userId - User ID
   * @param {Object} sessionData - Session data
   * @returns {Promise<string>} Session ID
   */
  async createSession(userId, sessionData = {}) {
    try {
      const sessionId = `session:${userId}:${Date.now()}:${Math.random().toString(36).substr(2, 9)}`;

      // Check concurrent session limit
      await this.enforceSessionLimit(userId);

      const session = {
        userId,
        sessionId,
        createdAt: new Date().toISOString(),
        lastAccessedAt: new Date().toISOString(),
        isActive: true,
        ipAddress: sessionData.ipAddress,
        userAgent: sessionData.userAgent,
        ...sessionData
      };

      // Store session in Redis
      await redisCacheService.cacheUserSession(sessionId, session);

      // Track active session
      this.activeSessions.set(sessionId, { userId, createdAt: Date.now() });

      logger.info(`Session created for user ${userId}: ${sessionId}`);
      return sessionId;
    } catch (error) {
      logger.error('Error creating session:', error);
      throw new Error('Session creation failed');
    }
  }

  /**
   * Get user session from Redis
   *
   * @param {string} sessionId - Session ID
   * @returns {Promise<Object|null>} Session data or null
   */
  async getSession(sessionId) {
    try {
      const session = await redisCacheService.getUserSession(sessionId);

      if (session && session.isActive) {
        // Update last accessed time
        session.lastAccessedAt = new Date().toISOString();
        await redisCacheService.cacheUserSession(sessionId, session);

        return session;
      }

      return null;
    } catch (error) {
      logger.error('Error getting session:', error);
      return null;
    }
  }

  /**
   * Invalidate user session
   *
   * @param {string} sessionId - Session ID
   * @returns {Promise<boolean>} Success status
   */
  async invalidateSession(sessionId) {
    try {
      const session = await this.getSession(sessionId);

      if (session) {
        session.isActive = false;
        session.invalidatedAt = new Date().toISOString();

        await redisCacheService.cacheUserSession(sessionId, session);
        this.activeSessions.delete(sessionId);

        logger.info(`Session invalidated: ${sessionId}`);
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Error invalidating session:', error);
      return false;
    }
  }

  /**
   * Enforce concurrent session limit
   *
   * @private
   * @param {string} userId - User ID
   */
  async enforceSessionLimit(userId) {
    try {
      const userSessions = Array.from(this.activeSessions.entries())
        .filter(([_, session]) => session.userId === userId)
        .sort((a, b) => a[1].createdAt - b[1].createdAt);

      if (userSessions.length >= this.sessionConfig.maxConcurrent) {
        // Remove oldest sessions
        const sessionsToRemove = userSessions.slice(0, userSessions.length - this.sessionConfig.maxConcurrent + 1);

        for (const [sessionId] of sessionsToRemove) {
          await this.invalidateSession(sessionId);
        }
      }
    } catch (error) {
      logger.error('Error enforcing session limit:', error);
    }
  }

  /**
   * Check if user has permission
   *
   * @param {string} userRole - User role
   * @param {string} permission - Required permission
   * @returns {boolean} Permission check result
   */
  hasPermission(userRole, permission) {
    const role = this.roles[userRole?.toUpperCase()];
    if (!role) return false;

    // Admin has all permissions
    if (role.permissions.includes('*')) return true;

    return role.permissions.includes(permission);
  }

  /**
   * Check if user has access to feature
   *
   * @param {string} userRole - User role
   * @param {string} feature - Required feature
   * @returns {boolean} Feature access result
   */
  hasFeatureAccess(userRole, feature) {
    const role = this.roles[userRole?.toUpperCase()];
    if (!role) return false;

    return role.features.includes(feature);
  }

  /**
   * Validate request data using Joi schema
   *
   * @param {Object} data - Data to validate
   * @param {string} schemaName - Schema name
   * @returns {Object} Validation result
   */
  validateInput(data, schemaName) {
    const schema = this.validationSchemas[schemaName];
    if (!schema) {
      throw new Error(`Validation schema '${schemaName}' not found`);
    }

    const { error, value } = schema.validate(data, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      return {
        isValid: false,
        errors: validationErrors,
        data: null
      };
    }

    return {
      isValid: true,
      errors: [],
      data: value
    };
  }

  /**
   * Authentication middleware
   *
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async authenticateToken(req, res, next) {
    try {
      const authHeader = req.headers.authorization;
      const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

      if (!token) {
        return res.status(401).json({
          error: 'Access token required',
          code: 'TOKEN_MISSING'
        });
      }

      const decoded = this.verifyAccessToken(token);

      // Check if session is still valid
      if (decoded.sessionId) {
        const session = await this.getSession(decoded.sessionId);
        if (!session || !session.isActive) {
          return res.status(401).json({
            error: 'Session expired or invalid',
            code: 'SESSION_INVALID'
          });
        }
      }

      req.user = decoded;
      next();
    } catch (error) {
      logger.error('Authentication error:', error);

      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          error: 'Access token expired',
          code: 'TOKEN_EXPIRED'
        });
      }

      return res.status(401).json({
        error: 'Invalid access token',
        code: 'TOKEN_INVALID'
      });
    }
  }

  /**
   * Authorization middleware
   *
   * @param {string|Array} requiredPermissions - Required permissions
   * @returns {Function} Express middleware function
   */
  requirePermissions(requiredPermissions) {
    const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];

    return (req, res, next) => {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const userRole = req.user.role;
      const hasAllPermissions = permissions.every(permission =>
        this.hasPermission(userRole, permission)
      );

      if (!hasAllPermissions) {
        return res.status(403).json({
          error: 'Insufficient permissions',
          code: 'INSUFFICIENT_PERMISSIONS',
          required: permissions,
          userRole
        });
      }

      next();
    };
  }

  /**
   * Feature access middleware
   *
   * @param {string} requiredFeature - Required feature
   * @returns {Function} Express middleware function
   */
  requireFeature(requiredFeature) {
    return (req, res, next) => {
      if (!req.user) {
        return res.status(401).json({
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const userRole = req.user.role;
      const hasFeature = this.hasFeatureAccess(userRole, requiredFeature);

      if (!hasFeature) {
        return res.status(403).json({
          error: 'Feature not available for your subscription',
          code: 'FEATURE_RESTRICTED',
          feature: requiredFeature,
          userRole
        });
      }

      next();
    };
  }

  /**
   * Input validation middleware
   *
   * @param {string} schemaName - Validation schema name
   * @param {string} source - Data source ('body', 'query', 'params')
   * @returns {Function} Express middleware function
   */
  validateInput(schemaName, source = 'body') {
    return (req, res, next) => {
      const data = req[source];
      const validation = this.validateInput(data, schemaName);

      if (!validation.isValid) {
        return res.status(400).json({
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: validation.errors
        });
      }

      req.validated = req.validated || {};
      req.validated[source] = validation.data;
      next();
    };
  }

  /**
   * Get security metrics
   *
   * @returns {Object} Security metrics
   */
  getSecurityMetrics() {
    const activeSessions = this.activeSessions.size;
    const sessionsByRole = {};

    for (const [sessionId, session] of this.activeSessions.entries()) {
      const role = session.role || 'unknown';
      sessionsByRole[role] = (sessionsByRole[role] || 0) + 1;
    }

    return {
      activeSessions,
      sessionsByRole,
      maxConcurrentSessions: this.sessionConfig.maxConcurrent,
      sessionTimeout: this.sessionConfig.timeout,
      rateLimitConfig: this.rateLimitConfig,
      passwordPolicy: {
        minLength: this.passwordConfig.minLength,
        requireUppercase: this.passwordConfig.requireUppercase,
        requireLowercase: this.passwordConfig.requireLowercase,
        requireNumbers: this.passwordConfig.requireNumbers,
        requireSpecialChars: this.passwordConfig.requireSpecialChars
      },
      roles: Object.keys(this.roles),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions() {
    try {
      const now = Date.now();
      const expiredSessions = [];

      for (const [sessionId, session] of this.activeSessions.entries()) {
        if (now - session.createdAt > this.sessionConfig.timeout) {
          expiredSessions.push(sessionId);
        }
      }

      for (const sessionId of expiredSessions) {
        await this.invalidateSession(sessionId);
      }

      if (expiredSessions.length > 0) {
        logger.info(`Cleaned up ${expiredSessions.length} expired sessions`);
      }
    } catch (error) {
      logger.error('Error cleaning up expired sessions:', error);
    }
  }
}

// Create and export singleton instance
export const enhancedSecurityService = new EnhancedSecurityService();

// Start session cleanup interval
setInterval(() => {
  enhancedSecurityService.cleanupExpiredSessions();
}, 300000); // Every 5 minutes

export default enhancedSecurityService;
