import journalService from '../services/journalService.js';
import { APIError } from '../../../middleware/errorHandler.js';

export const createTrade = async (req, res, next) => {
  try {
    const trade = await journalService.createTrade({ ...req.body, user: req.user.id });
    res.status(201).json({ status: 'success', data: trade });
  } catch (error) {
    next(error);
  }
};

export const getTrades = async (req, res, next) => {
  try {
    const trades = await journalService.getTradesByUser(req.user.id);
    res.json({ status: 'success', data: trades });
  } catch (error) {
    next(error);
  }
};

export const getTradeById = async (req, res, next) => {
  try {
    const trade = await journalService.getTradeById(req.params.id, req.user.id);
    if (!trade) return next(new APIError(404, 'Trade not found'));
    res.json({ status: 'success', data: trade });
  } catch (error) {
    next(error);
  }
};

export const deleteTrade = async (req, res, next) => {
  try {
    const trade = await journalService.deleteTrade(req.params.id, req.user.id);
    if (!trade) return next(new APIError(404, 'Trade not found'));
    res.json({ status: 'success', message: 'Trade deleted' });
  } catch (error) {
    next(error);
  }
};

export const getTradeAnalytics = async (req, res, next) => {
  try {
    const analytics = await journalService.getTradeAnalytics(req.user.id);
    res.json({ status: 'success', data: analytics });
  } catch (error) {
    next(error);
  }
};

export default {
  createTrade,
  getTrades,
  getTradeById,
  deleteTrade,
  getTradeAnalytics,
}; 