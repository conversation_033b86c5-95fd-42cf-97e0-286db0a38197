/**
 * Chart Fix Script
 * This script ensures the chart is properly initialized and populated with data
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Chart fix script loaded');
    
    // Initialize chart after a short delay to ensure all dependencies are loaded
    setTimeout(initializeFixedChart, 500);
    
    // Add event listener for the analyze button
    const analyzeBtn = document.getElementById('analyzeBtn');
    if (analyzeBtn) {
        analyzeBtn.addEventListener('click', function() {
            console.log('Analyze button clicked');
            loadChartData();
        });
    }
    
    // Add event listeners for chart type buttons
    setupChartTypeButtons();
});

/**
 * Initialize the fixed chart
 */
function initializeFixedChart() {
    console.log('Initializing fixed chart');
    
    // Get the chart canvas
    const chartCanvas = document.getElementById('priceChart');
    if (!chartCanvas) {
        console.error('Chart canvas not found');
        return;
    }
    
    // Check if Chart.js is loaded
    if (typeof Chart === 'undefined') {
        console.error('Chart.js is not loaded');
        return;
    }
    
    // Check if the financial chart plugin is loaded
    const hasFinancialPlugin = typeof Chart.controllers !== 'undefined' && 
                              typeof Chart.controllers.candlestick !== 'undefined';
    
    // Default to line chart if financial plugin is not available
    const chartType = hasFinancialPlugin ? 'candlestick' : 'line';
    console.log(`Using chart type: ${chartType}`);
    
    // Generate mock data
    const mockData = generateMockChartData();
    
    // Create chart configuration
    const config = {
        type: chartType,
        data: {
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'day',
                        displayFormats: {
                            day: 'MMM d'
                        }
                    },
                    ticks: {
                        source: 'auto'
                    }
                },
                y: {
                    type: 'linear',
                    position: 'right'
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const point = context.raw;
                            if (chartType === 'candlestick') {
                                return [
                                    `Open: ${point.o}`,
                                    `High: ${point.h}`,
                                    `Low: ${point.l}`,
                                    `Close: ${point.c}`
                                ];
                            } else {
                                return `Price: ${point.y}`;
                            }
                        }
                    }
                }
            }
        }
    };
    
    // Add dataset based on chart type
    if (chartType === 'candlestick') {
        config.data.datasets.push({
            label: 'XAUUSD',
            data: mockData.ohlc,
            color: {
                up: 'rgba(75, 192, 192, 1)',
                down: 'rgba(255, 99, 132, 1)',
                unchanged: 'rgba(90, 90, 90, 1)'
            }
        });
    } else {
        config.data.datasets.push({
            label: 'XAUUSD',
            data: mockData.line,
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            pointRadius: 2,
            pointHoverRadius: 5
        });
    }
    
    // Create chart instance
    window.fixedChartInstance = new Chart(chartCanvas, config);
    
    console.log('Fixed chart initialized');
}

/**
 * Generate mock chart data
 */
function generateMockChartData() {
    console.log('Generating mock chart data');
    
    const ohlcData = [];
    const lineData = [];
    const dates = [];
    
    // Base price for Gold (XAU/USD)
    let basePrice = 2340.5;
    
    // Generate 30 days of data
    const now = new Date();
    for (let i = 30; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        dates.push(date);
        
        // Random price movement (-0.5% to +0.5%)
        const change = (Math.random() - 0.5) * 0.01;
        basePrice = basePrice * (1 + change);
        
        // Generate OHLC data
        const open = basePrice * (1 - Math.random() * 0.002);
        const high = basePrice * (1 + Math.random() * 0.004);
        const low = basePrice * (1 - Math.random() * 0.004);
        const close = basePrice;
        
        // Add to OHLC data array
        ohlcData.push({
            x: date,
            o: open.toFixed(2),
            h: high.toFixed(2),
            l: low.toFixed(2),
            c: close.toFixed(2)
        });
        
        // Add to line data array
        lineData.push({
            x: date,
            y: close.toFixed(2)
        });
    }
    
    return {
        ohlc: ohlcData,
        line: lineData,
        dates: dates
    };
}

/**
 * Load chart data (simulates API call)
 */
function loadChartData() {
    console.log('Loading chart data');
    
    // Show loading indicator if available
    if (typeof NProgress !== 'undefined') {
        NProgress.start();
    }
    
    // Simulate API delay
    setTimeout(function() {
        // Get selected values
        const symbol = document.getElementById('symbol')?.value || 'XAUUSD';
        const timeframe = document.getElementById('timeframe')?.value || 'H1';
        
        // Generate new mock data
        const mockData = generateMockChartData();
        
        // Update chart
        updateFixedChart(mockData, symbol, timeframe);
        
        // Hide loading indicator
        if (typeof NProgress !== 'undefined') {
            NProgress.done();
        }
    }, 1000);
}

/**
 * Update the fixed chart with new data
 */
function updateFixedChart(data, symbol, timeframe) {
    console.log(`Updating chart with ${symbol} data (${timeframe})`);
    
    if (!window.fixedChartInstance) {
        console.error('Chart instance not found');
        return;
    }
    
    // Update chart data
    const chartType = window.fixedChartInstance.config.type;
    
    if (chartType === 'candlestick') {
        window.fixedChartInstance.data.datasets[0].label = symbol;
        window.fixedChartInstance.data.datasets[0].data = data.ohlc;
    } else {
        window.fixedChartInstance.data.datasets[0].label = symbol;
        window.fixedChartInstance.data.datasets[0].data = data.line;
    }
    
    // Update chart
    window.fixedChartInstance.update();
    
    console.log('Chart updated successfully');
}

/**
 * Set up chart type buttons
 */
function setupChartTypeButtons() {
    const candlestickBtn = document.getElementById('candlestickBtn');
    const lineChartBtn = document.getElementById('lineChartBtn');
    const areaChartBtn = document.getElementById('areaChartBtn');
    
    if (candlestickBtn) {
        candlestickBtn.addEventListener('click', function() {
            changeChartType('candlestick');
        });
    }
    
    if (lineChartBtn) {
        lineChartBtn.addEventListener('click', function() {
            changeChartType('line');
        });
    }
    
    if (areaChartBtn) {
        areaChartBtn.addEventListener('click', function() {
            changeChartType('area');
        });
    }
}

/**
 * Change chart type
 */
function changeChartType(type) {
    console.log(`Changing chart type to ${type}`);
    
    if (!window.fixedChartInstance) {
        console.error('Chart instance not found');
        return;
    }
    
    // Destroy current chart
    window.fixedChartInstance.destroy();
    
    // Reinitialize with new type
    initializeFixedChart();
}
