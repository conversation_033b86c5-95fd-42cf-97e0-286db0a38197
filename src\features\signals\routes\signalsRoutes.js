import express from 'express';
import { protect } from '../../../middleware/auth.js';
import {
  getAllSignals,
  createSignal,
  getSignalById,
  deleteSignal
} from '../controllers/signalsController.js';

const router = express.Router();

router.get('/', getAllSignals);
router.post('/', protect, createSignal);
router.get('/:id', getSignalById);
router.delete('/:id', protect, deleteSignal);

export default router;
