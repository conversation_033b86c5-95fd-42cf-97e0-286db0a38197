/**
 * Enhanced Features CSS
 * Styles for WebSocket status, notifications, and signal overlays
 */

/* WebSocket Status Indicator */
.websocket-status {
  position: fixed;
  top: 10px;
  right: 10px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  color: white;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.websocket-status.connected {
  background-color: #4CAF50;
}

.websocket-status.connecting {
  background-color: #FF9800;
  animation: pulse 1.5s infinite;
}

.websocket-status.disconnected {
  background-color: #F44336;
}

.websocket-status.error {
  background-color: #9C27B0;
}

.websocket-status.failed {
  background-color: #424242;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Notification Container */
.notification-container {
  position: fixed;
  top: 60px;
  right: 10px;
  width: 350px;
  z-index: 1001;
  pointer-events: none;
}

.notification {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-bottom: 10px;
  overflow: hidden;
  transform: translateX(100%);
  animation: slideIn 0.3s ease forwards;
  pointer-events: auto;
}

.notification-content {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notification-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
  margin-left: 10px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-close:hover {
  color: #333;
}

/* Notification Types */
.notification-success {
  border-left: 4px solid #4CAF50;
}

.notification-warning {
  border-left: 4px solid #FF9800;
}

.notification-error {
  border-left: 4px solid #F44336;
}

.notification-info {
  border-left: 4px solid #2196F3;
}

@keyframes slideIn {
  to {
    transform: translateX(0);
  }
}

/* Signal Cards Enhancement */
.signal-card {
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.signal-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.signal-card.highlight {
  animation: highlight 3s ease;
}

@keyframes highlight {
  0%, 100% { background-color: transparent; }
  50% { background-color: rgba(255, 235, 59, 0.3); }
}

.signal-card.buy {
  border-left: 4px solid #4CAF50;
}

.signal-card.sell {
  border-left: 4px solid #F44336;
}

.signal-card.hold {
  border-left: 4px solid #FF9800;
}

/* Signal Confidence Badge */
.signal-confidence {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  color: white;
}

.signal-confidence.high {
  background-color: #4CAF50;
}

.signal-confidence.medium {
  background-color: #FF9800;
}

.signal-confidence.low {
  background-color: #F44336;
}

/* Signal Source Badge */
.signal-source {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
  margin-left: 8px;
}

.signal-source.ai {
  background-color: #9C27B0;
  color: white;
}

.signal-source.traditional {
  background-color: #607D8B;
  color: white;
}

.signal-source.unified {
  background-color: #3F51B5;
  color: white;
}

/* Chart Signal Markers */
.chart-container {
  position: relative;
}

.signal-marker {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: white;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
}

.signal-marker:hover {
  transform: scale(1.2);
}

.signal-marker.buy {
  background-color: #4CAF50;
}

.signal-marker.sell {
  background-color: #F44336;
}

.signal-marker.hold {
  background-color: #FF9800;
}

/* Signal Details Tooltip */
.signal-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.signal-tooltip.visible {
  opacity: 1;
}

/* Loading States */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Button States */
.btn-generate-signals {
  position: relative;
  overflow: hidden;
}

.btn-generate-signals.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-container {
    width: calc(100vw - 20px);
    right: 10px;
    left: 10px;
  }
  
  .websocket-status {
    top: 5px;
    right: 5px;
    font-size: 11px;
    padding: 6px 10px;
  }
  
  .signal-card {
    margin-bottom: 12px;
    padding: 12px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .notification {
    background: #2d2d2d;
    color: #ffffff;
  }
  
  .notification-close {
    color: #cccccc;
  }
  
  .notification-close:hover {
    color: #ffffff;
  }
  
  .signal-card {
    background: #2d2d2d;
    color: #ffffff;
  }
}
