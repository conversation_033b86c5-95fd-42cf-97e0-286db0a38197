import React, { useState } from 'react';
import alertsService, { ALERT_TYPES, ALERT_PRIORITIES } from '../../../services/alertsService';
import { getPatternsByType, getPatternTypes } from '../../../utils/candlestickPatterns';

/**
 * PatternAlertForm Component
 * 
 * Form for creating pattern alerts
 */
const PatternAlertForm = ({ symbol, timeframe, onAlertCreated, onCancel }) => {
  // State for form data
  const [formData, setFormData] = useState({
    alertType: 'specific', // 'specific' or 'type'
    pattern: '',
    patternType: 'bullish',
    minSignificance: 7,
    message: '',
    priority: ALERT_PRIORITIES.MEDIUM,
    notifyBrowser: true,
    notifyApp: true,
    expiresAt: ''
  });
  
  // State for form errors
  const [errors, setErrors] = useState({});
  
  // State for submission status
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitResult, setSubmitResult] = useState({ success: false, message: '' });
  
  // Get available patterns
  const patternTypes = getPatternTypes();
  const patterns = formData.patternType ? 
    getPatternsByType(formData.patternType) : 
    [];
  
  // Handle input change
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };
  
  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    if (formData.alertType === 'specific' && !formData.pattern) {
      newErrors.pattern = 'Please select a pattern';
    }
    
    if (formData.alertType === 'type' && !formData.patternType) {
      newErrors.patternType = 'Please select a pattern type';
    }
    
    if (!formData.notifyBrowser && !formData.notifyApp) {
      newErrors.notification = 'Please select at least one notification method';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    setSubmitResult({ success: false, message: '' });
    
    try {
      // Prepare notification methods
      const notificationMethods = [];
      if (formData.notifyApp) notificationMethods.push('app');
      if (formData.notifyBrowser) notificationMethods.push('browser');
      
      // Prepare alert conditions
      const conditions = {
        minSignificance: parseInt(formData.minSignificance)
      };
      
      if (formData.alertType === 'specific') {
        conditions.pattern = formData.pattern;
      } else {
        conditions.patternType = formData.patternType;
      }
      
      // Create alert
      const alert = alertsService.createAlert({
        type: ALERT_TYPES.PATTERN,
        symbol,
        timeframe,
        conditions,
        message: formData.message || undefined,
        priority: formData.priority,
        notificationMethods,
        expiresAt: formData.expiresAt ? new Date(formData.expiresAt).getTime() : 0
      });
      
      setSubmitResult({
        success: true,
        message: 'Pattern alert created successfully!'
      });
      
      // Notify parent component
      if (onAlertCreated) {
        onAlertCreated(alert);
      }
      
      // Reset form after successful submission
      setFormData({
        alertType: 'specific',
        pattern: '',
        patternType: 'bullish',
        minSignificance: 7,
        message: '',
        priority: ALERT_PRIORITIES.MEDIUM,
        notifyBrowser: true,
        notifyApp: true,
        expiresAt: ''
      });
      
      // Close form after 2 seconds
      setTimeout(() => {
        if (onCancel) {
          onCancel();
        }
      }, 2000);
    } catch (error) {
      console.error('Error creating alert:', error);
      setSubmitResult({
        success: false,
        message: 'Failed to create alert. Please try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="pattern-alert-form bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
      <h2 className="text-lg font-semibold mb-4">Create Pattern Alert</h2>
      
      <form onSubmit={handleSubmit}>
        {/* Alert Type */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Alert Type
          </label>
          <div className="flex space-x-4">
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="alertType"
                value="specific"
                checked={formData.alertType === 'specific'}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Specific Pattern</span>
            </label>
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="alertType"
                value="type"
                checked={formData.alertType === 'type'}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Pattern Type</span>
            </label>
          </div>
        </div>
        
        {/* Pattern Type (for type alerts) */}
        {formData.alertType === 'type' && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Pattern Type
            </label>
            <select
              name="patternType"
              value={formData.patternType}
              onChange={handleInputChange}
              className={`w-full p-2 border rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                errors.patternType ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
            >
              {patternTypes.map(type => (
                <option key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </option>
              ))}
            </select>
            {errors.patternType && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.patternType}</p>
            )}
          </div>
        )}
        
        {/* Specific Pattern (for specific alerts) */}
        {formData.alertType === 'specific' && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Pattern
            </label>
            <div className="flex space-x-4">
              <div className="flex-1">
                <select
                  name="patternType"
                  value={formData.patternType}
                  onChange={handleInputChange}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  {patternTypes.map(type => (
                    <option key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex-1">
                <select
                  name="pattern"
                  value={formData.pattern}
                  onChange={handleInputChange}
                  className={`w-full p-2 border rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 ${
                    errors.pattern ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                >
                  <option value="">Select Pattern</option>
                  {patterns.map(pattern => (
                    <option key={pattern.id} value={pattern.id}>
                      {pattern.name}
                    </option>
                  ))}
                </select>
                {errors.pattern && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.pattern}</p>
                )}
              </div>
            </div>
          </div>
        )}
        
        {/* Minimum Significance */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Minimum Significance (1-10)
          </label>
          <input
            type="range"
            name="minSignificance"
            min="1"
            max="10"
            value={formData.minSignificance}
            onChange={handleInputChange}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
          />
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
            <span>Low (1)</span>
            <span>Medium (5)</span>
            <span>High (10)</span>
          </div>
          <div className="text-center mt-1 text-sm font-medium">
            {formData.minSignificance}
          </div>
        </div>
        
        {/* Custom Message */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Custom Message (Optional)
          </label>
          <input
            type="text"
            name="message"
            value={formData.message}
            onChange={handleInputChange}
            placeholder="Custom alert message"
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          />
        </div>
        
        {/* Priority */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Priority
          </label>
          <select
            name="priority"
            value={formData.priority}
            onChange={handleInputChange}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            <option value={ALERT_PRIORITIES.LOW}>Low</option>
            <option value={ALERT_PRIORITIES.MEDIUM}>Medium</option>
            <option value={ALERT_PRIORITIES.HIGH}>High</option>
          </select>
        </div>
        
        {/* Notification Methods */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Notification Methods
          </label>
          <div className="space-y-2">
            <label className="inline-flex items-center">
              <input
                type="checkbox"
                name="notifyApp"
                checked={formData.notifyApp}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">In-App Notification</span>
            </label>
            <label className="inline-flex items-center">
              <input
                type="checkbox"
                name="notifyBrowser"
                checked={formData.notifyBrowser}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Browser Notification</span>
            </label>
            {errors.notification && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.notification}</p>
            )}
          </div>
        </div>
        
        {/* Expiration Date */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Expiration Date (Optional)
          </label>
          <input
            type="datetime-local"
            name="expiresAt"
            value={formData.expiresAt}
            onChange={handleInputChange}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          />
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Leave empty for no expiration
          </p>
        </div>
        
        {/* Submit Button */}
        <div className="flex justify-between mt-6">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md shadow"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Creating...' : 'Create Alert'}
          </button>
        </div>
        
        {/* Result Message */}
        {submitResult.message && (
          <div className={`mt-4 p-3 rounded ${
            submitResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {submitResult.message}
          </div>
        )}
      </form>
    </div>
  );
};

export default PatternAlertForm;
