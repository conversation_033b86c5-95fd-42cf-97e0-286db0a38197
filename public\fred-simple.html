<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FRED API Simple Example</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>FRED API Simple Example</h1>
        <p>This is a simple example of using the FRED API.</p>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>Economic Calendar</h5>
            </div>
            <div class="card-body">
                <button id="fetchBtn" class="btn btn-primary mb-3">Fetch Economic Calendar Data</button>
                <div id="loading" class="d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-2">Loading...</span>
                </div>
                <div id="error" class="alert alert-danger d-none"></div>
                <pre id="result" class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;"></pre>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fetchBtn = document.getElementById('fetchBtn');
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const result = document.getElementById('result');

            fetchBtn.addEventListener('click', async function() {
                // Show loading
                loading.classList.remove('d-none');
                error.classList.add('d-none');
                result.textContent = '';

                try {
                    // Fetch economic calendar data
                    const response = await fetch('/api/economic-calendar');
                    
                    if (!response.ok) {
                        throw new Error(`API error! status: ${response.status}`);
                    }
                    
                    const data = await response.json();
                    
                    // Display result
                    result.textContent = JSON.stringify(data, null, 2);
                    
                } catch (err) {
                    // Show error
                    error.textContent = `Error: ${err.message}`;
                    error.classList.remove('d-none');
                } finally {
                    // Hide loading
                    loading.classList.add('d-none');
                }
            });
        });
    </script>
</body>
</html>
