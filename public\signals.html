<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Trading Signals - Trading Signals App</title>
  <link rel="stylesheet" href="css/styles.css">
  <style>
    /* Additional styles for trading signals page */
    .signals-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 1.5rem;
      margin-top: 2rem;
    }
    
    .signal-card {
      background-color: #f8f9fa;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    
    .signal-card.buy {
      border-top: 4px solid #198754; /* Green for buy signals */
    }
    
    .signal-card.sell {
      border-top: 4px solid #dc3545; /* Red for sell signals */
    }
    
    .signal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: #e9ecef;
    }
    
    .signal-header h3 {
      margin: 0;
      font-size: 1.25rem;
    }
    
    .signal-timeframe {
      font-size: 0.875rem;
      font-weight: 500;
      padding: 0.25rem 0.5rem;
      background-color: #6c757d;
      color: white;
      border-radius: 4px;
    }
    
    .signal-body {
      padding: 1rem;
    }
    
    .signal-prices {
      margin-bottom: 1rem;
    }
    
    .signal-meta {
      font-size: 0.875rem;
      color: #6c757d;
    }
    
    .signal-analysis {
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #dee2e6;
    }
    
    .signal-actions {
      display: flex;
      justify-content: flex-end;
      gap: 0.5rem;
      padding: 1rem;
      background-color: #f1f3f5;
    }
    
    .btn-sm {
      padding: 0.375rem 0.75rem;
      font-size: 0.875rem;
    }
    
    .btn-edit {
      background-color: #6c757d;
      color: white;
      border: none;
    }
    
    .btn-delete {
      background-color: #dc3545;
      color: white;
      border: none;
    }
    
    .form-container {
      max-width: 800px;
      margin: 2rem auto;
      padding: 2rem;
      background-color: #f8f9fa;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .form-row {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1rem;
    }
    
    .form-group {
      flex: 1;
      min-width: 200px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #ced4da;
      border-radius: 4px;
      font-size: 1rem;
    }
    
    .form-group textarea {
      min-height: 100px;
    }
    
    .filter-container {
      margin-bottom: 2rem;
      padding: 1rem;
      background-color: #e9ecef;
      border-radius: 8px;
    }
    
    .message {
      margin: 1rem 0;
      padding: 0.75rem;
      border-radius: 4px;
    }
    
    .message.success {
      background-color: #d1e7dd;
      color: #0f5132;
    }
    
    .message.error {
      background-color: #f8d7da;
      color: #842029;
    }
  </style>
</head>
<body>
  <header>
    <nav class="main-nav">
      <div class="logo">
        <a href="/">Trading Signals App</a>
      </div>
      <div class="nav-links">
        <a href="/">Home</a>
        <a href="/signals.html" class="active">Signals</a>
        <a href="/dashboard.html">Dashboard</a>
      </div>
      <div id="auth-nav" class="auth-nav">
        <!-- Auth navigation will be inserted here by JavaScript -->
      </div>
    </nav>
  </header>
  
  <main>
    <div class="container">
      <h1>Trading Signals</h1>
      
      <div id="message-container" class="message"></div>
      
      <div class="form-container">
        <h2>Create New Signal</h2>
        <form id="signal-form" data-mode="create">
          <div class="form-row">
            <div class="form-group">
              <label for="signal-symbol">Symbol</label>
              <input type="text" id="signal-symbol" name="symbol" required placeholder="e.g., EURUSD, BTCUSD">
            </div>
            <div class="form-group">
              <label for="signal-type">Type</label>
              <select id="signal-type" name="type" required>
                <option value="BUY">BUY</option>
                <option value="SELL">SELL</option>
              </select>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="signal-entry-price">Entry Price</label>
              <input type="number" id="signal-entry-price" name="entryPrice" required step="0.00001">
            </div>
            <div class="form-group">
              <label for="signal-stop-loss">Stop Loss</label>
              <input type="number" id="signal-stop-loss" name="stopLoss" required step="0.00001">
            </div>
            <div class="form-group">
              <label for="signal-take-profit">Take Profit</label>
              <input type="number" id="signal-take-profit" name="takeProfit" required step="0.00001">
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="signal-timeframe">Timeframe</label>
              <select id="signal-timeframe" name="timeframe" required>
                <option value="M5">5 Minutes (M5)</option>
                <option value="M15">15 Minutes (M15)</option>
                <option value="M30">30 Minutes (M30)</option>
                <option value="H1">1 Hour (H1)</option>
                <option value="H4">4 Hours (H4)</option>
                <option value="D1">Daily (D1)</option>
                <option value="W1">Weekly (W1)</option>
              </select>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="signal-analysis">Analysis (Optional)</label>
              <textarea id="signal-analysis" name="analysis" placeholder="Enter your analysis or notes about this signal..."></textarea>
            </div>
          </div>
          
          <div class="form-row">
            <button type="submit" class="btn btn-primary">Create Signal</button>
            <button type="button" class="btn btn-outline" onclick="window.TradingSignals.resetSignalForm()">Reset</button>
          </div>
        </form>
      </div>
      
      <div class="filter-container">
        <h3>Filter Signals</h3>
        <form id="filter-form">
          <div class="form-row">
            <div class="form-group">
              <label for="filter-symbol">Symbol</label>
              <input type="text" id="filter-symbol" name="symbol" placeholder="e.g., EURUSD">
            </div>
            <div class="form-group">
              <label for="filter-type">Type</label>
              <select id="filter-type" name="type">
                <option value="">All</option>
                <option value="BUY">BUY</option>
                <option value="SELL">SELL</option>
              </select>
            </div>
            <div class="form-group">
              <label for="filter-timeframe">Timeframe</label>
              <select id="filter-timeframe" name="timeframe">
                <option value="">All</option>
                <option value="M5">5 Minutes (M5)</option>
                <option value="M15">15 Minutes (M15)</option>
                <option value="M30">30 Minutes (M30)</option>
                <option value="H1">1 Hour (H1)</option>
                <option value="H4">4 Hours (H4)</option>
                <option value="D1">Daily (D1)</option>
                <option value="W1">Weekly (W1)</option>
              </select>
            </div>
          </div>
          
          <div class="form-row">
            <button type="submit" class="btn btn-primary">Apply Filters</button>
            <button type="reset" class="btn btn-outline">Reset Filters</button>
          </div>
        </form>
      </div>
      
      <div id="signals-container" class="signals-container">
        <!-- Trading signals will be inserted here by JavaScript -->
        <p>Loading signals...</p>
      </div>
    </div>
  </main>
  
  <footer>
    <p>&copy; 2023 Trading Signals App. All rights reserved.</p>
  </footer>
  
  <script src="js/auth.js"></script>
  <script src="js/trading-signals.js"></script>
</body>
</html>
