/**
 * WebSocket Service for Trading Signals App
 * 
 * This service provides real-time data streaming via WebSockets.
 * It allows clients to subscribe to market data, trading signals,
 * and other real-time events.
 */

const WebSocket = require('ws');
const http = require('http');
const url = require('url');
const logger = require('../utils/logger');
const { verifyJWT } = require('../middleware/auth');

class WebSocketService {
  /**
   * Create a new WebSocketService
   * @param {Object} server - HTTP server instance
   * @param {Object} options - Configuration options
   */
  constructor(server, options = {}) {
    this.options = {
      path: '/ws',
      pingInterval: 30000, // 30 seconds
      pongTimeout: 10000, // 10 seconds
      ...options
    };
    
    // Create WebSocket server
    this.wss = new WebSocket.Server({
      server,
      path: this.options.path,
      clientTracking: true
    });
    
    // Initialize client tracking
    this.clients = new Map();
    this.subscriptions = new Map();
    this.stats = {
      totalConnections: 0,
      activeConnections: 0,
      messagesSent: 0,
      messagesReceived: 0,
      errors: 0
    };
    
    // Set up event handlers
    this.setupEventHandlers();
    
    // Start ping interval
    this.startPingInterval();
    
    logger.info(`WebSocket server initialized on path: ${this.options.path}`);
  }
  
  /**
   * Set up WebSocket event handlers
   */
  setupEventHandlers() {
    this.wss.on('connection', (ws, req) => this.handleConnection(ws, req));
    this.wss.on('error', (error) => {
      logger.error('WebSocket server error:', error);
      this.stats.errors++;
    });
  }
  
  /**
   * Handle new WebSocket connection
   * @param {WebSocket} ws - WebSocket connection
   * @param {http.IncomingMessage} req - HTTP request
   */
  async handleConnection(ws, req) {
    try {
      // Parse URL and query parameters
      const parsedUrl = url.parse(req.url, true);
      const { token, clientId } = parsedUrl.query;
      
      // Generate client ID if not provided
      const id = clientId || `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Verify token if authentication is required
      let user = null;
      if (token) {
        try {
          user = await verifyJWT(token);
        } catch (error) {
          logger.warn(`Invalid token from client ${id}:`, error);
          ws.close(4001, 'Authentication failed');
          return;
        }
      }
      
      // Store client information
      this.clients.set(ws, {
        id,
        user,
        ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        userAgent: req.headers['user-agent'],
        connectedAt: new Date(),
        subscriptions: new Set(),
        isAlive: true
      });
      
      // Update stats
      this.stats.totalConnections++;
      this.stats.activeConnections++;
      
      logger.info(`WebSocket client connected: ${id}`);
      
      // Set up client event handlers
      ws.on('message', (message) => this.handleMessage(ws, message));
      ws.on('close', () => this.handleClose(ws));
      ws.on('error', (error) => this.handleError(ws, error));
      ws.on('pong', () => this.handlePong(ws));
      
      // Send welcome message
      this.sendToClient(ws, {
        type: 'connection',
        status: 'connected',
        clientId: id,
        authenticated: !!user,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Error handling WebSocket connection:', error);
      ws.close(1011, 'Internal server error');
    }
  }
  
  /**
   * Handle WebSocket message
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} message - Message data
   */
  handleMessage(ws, message) {
    try {
      // Parse message
      const data = JSON.parse(message);
      
      // Update stats
      this.stats.messagesReceived++;
      
      // Get client info
      const client = this.clients.get(ws);
      if (!client) {
        logger.warn('Message received from unknown client');
        return;
      }
      
      logger.debug(`WebSocket message from ${client.id}:`, data);
      
      // Handle message based on type
      switch (data.type) {
        case 'subscribe':
          this.handleSubscribe(ws, data);
          break;
        case 'unsubscribe':
          this.handleUnsubscribe(ws, data);
          break;
        case 'ping':
          this.sendToClient(ws, { type: 'pong', timestamp: new Date().toISOString() });
          break;
        default:
          logger.warn(`Unknown message type from client ${client.id}:`, data.type);
      }
    } catch (error) {
      logger.error('Error handling WebSocket message:', error);
      this.stats.errors++;
    }
  }
  
  /**
   * Handle subscription request
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} data - Subscription data
   */
  handleSubscribe(ws, data) {
    try {
      const client = this.clients.get(ws);
      if (!client) return;
      
      const { channel, params = {} } = data;
      
      // Validate channel
      if (!channel) {
        this.sendToClient(ws, {
          type: 'error',
          error: 'Missing channel parameter',
          originalRequest: data
        });
        return;
      }
      
      // Create subscription key
      const subscriptionKey = this.getSubscriptionKey(channel, params);
      
      // Add to client subscriptions
      client.subscriptions.add(subscriptionKey);
      
      // Add to global subscriptions
      if (!this.subscriptions.has(subscriptionKey)) {
        this.subscriptions.set(subscriptionKey, new Set());
      }
      this.subscriptions.get(subscriptionKey).add(ws);
      
      logger.info(`Client ${client.id} subscribed to ${subscriptionKey}`);
      
      // Send confirmation
      this.sendToClient(ws, {
        type: 'subscribed',
        channel,
        params,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Error handling subscription:', error);
      this.stats.errors++;
    }
  }
  
  /**
   * Handle unsubscribe request
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} data - Unsubscription data
   */
  handleUnsubscribe(ws, data) {
    try {
      const client = this.clients.get(ws);
      if (!client) return;
      
      const { channel, params = {} } = data;
      
      // Validate channel
      if (!channel) {
        this.sendToClient(ws, {
          type: 'error',
          error: 'Missing channel parameter',
          originalRequest: data
        });
        return;
      }
      
      // Create subscription key
      const subscriptionKey = this.getSubscriptionKey(channel, params);
      
      // Remove from client subscriptions
      client.subscriptions.delete(subscriptionKey);
      
      // Remove from global subscriptions
      if (this.subscriptions.has(subscriptionKey)) {
        this.subscriptions.get(subscriptionKey).delete(ws);
        
        // Clean up empty subscription sets
        if (this.subscriptions.get(subscriptionKey).size === 0) {
          this.subscriptions.delete(subscriptionKey);
        }
      }
      
      logger.info(`Client ${client.id} unsubscribed from ${subscriptionKey}`);
      
      // Send confirmation
      this.sendToClient(ws, {
        type: 'unsubscribed',
        channel,
        params,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Error handling unsubscription:', error);
      this.stats.errors++;
    }
  }
  
  /**
   * Handle WebSocket close
   * @param {WebSocket} ws - WebSocket connection
   */
  handleClose(ws) {
    try {
      const client = this.clients.get(ws);
      if (!client) return;
      
      logger.info(`WebSocket client disconnected: ${client.id}`);
      
      // Clean up subscriptions
      for (const subscriptionKey of client.subscriptions) {
        if (this.subscriptions.has(subscriptionKey)) {
          this.subscriptions.get(subscriptionKey).delete(ws);
          
          // Clean up empty subscription sets
          if (this.subscriptions.get(subscriptionKey).size === 0) {
            this.subscriptions.delete(subscriptionKey);
          }
        }
      }
      
      // Remove client
      this.clients.delete(ws);
      
      // Update stats
      this.stats.activeConnections--;
    } catch (error) {
      logger.error('Error handling WebSocket close:', error);
      this.stats.errors++;
    }
  }
  
  /**
   * Handle WebSocket error
   * @param {WebSocket} ws - WebSocket connection
   * @param {Error} error - Error object
   */
  handleError(ws, error) {
    try {
      const client = this.clients.get(ws);
      logger.error(`WebSocket error for client ${client ? client.id : 'unknown'}:`, error);
      this.stats.errors++;
    } catch (error) {
      logger.error('Error handling WebSocket error:', error);
    }
  }
  
  /**
   * Handle pong response
   * @param {WebSocket} ws - WebSocket connection
   */
  handlePong(ws) {
    const client = this.clients.get(ws);
    if (client) {
      client.isAlive = true;
    }
  }
  
  /**
   * Start ping interval
   */
  startPingInterval() {
    this.pingInterval = setInterval(() => {
      this.wss.clients.forEach((ws) => {
        const client = this.clients.get(ws);
        if (!client) return;
        
        if (client.isAlive === false) {
          logger.warn(`Client ${client.id} timed out`);
          return ws.terminate();
        }
        
        client.isAlive = false;
        ws.ping();
      });
    }, this.options.pingInterval);
  }
  
  /**
   * Send data to a specific client
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} data - Data to send
   */
  sendToClient(ws, data) {
    try {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(data));
        this.stats.messagesSent++;
      }
    } catch (error) {
      logger.error('Error sending to client:', error);
      this.stats.errors++;
    }
  }
  
  /**
   * Publish data to a channel
   * @param {string} channel - Channel name
   * @param {Object} data - Data to publish
   * @param {Object} params - Channel parameters
   */
  publish(channel, data, params = {}) {
    try {
      const subscriptionKey = this.getSubscriptionKey(channel, params);
      
      if (!this.subscriptions.has(subscriptionKey)) {
        // No subscribers for this channel
        return 0;
      }
      
      const subscribers = this.subscriptions.get(subscriptionKey);
      let sentCount = 0;
      
      // Prepare message
      const message = JSON.stringify({
        type: 'update',
        channel,
        params,
        data,
        timestamp: new Date().toISOString()
      });
      
      // Send to all subscribers
      for (const ws of subscribers) {
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(message);
          sentCount++;
        }
      }
      
      this.stats.messagesSent += sentCount;
      return sentCount;
    } catch (error) {
      logger.error('Error publishing to channel:', error);
      this.stats.errors++;
      return 0;
    }
  }
  
  /**
   * Get subscription key
   * @param {string} channel - Channel name
   * @param {Object} params - Channel parameters
   * @returns {string} Subscription key
   */
  getSubscriptionKey(channel, params = {}) {
    return `${channel}:${JSON.stringify(params)}`;
  }
  
  /**
   * Get service statistics
   * @returns {Object} Service statistics
   */
  getStats() {
    return {
      ...this.stats,
      subscriptionCount: this.subscriptions.size,
      subscriptions: Array.from(this.subscriptions.keys()),
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * Close the WebSocket server
   */
  close() {
    clearInterval(this.pingInterval);
    
    this.wss.clients.forEach((ws) => {
      ws.close(1001, 'Server shutting down');
    });
    
    this.wss.close();
    logger.info('WebSocket server closed');
  }
}

module.exports = WebSocketService;
