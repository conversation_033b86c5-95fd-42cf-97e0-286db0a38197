<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Trading Signals App</title>

    <!-- Favicon -->
    <link rel="icon" href="/icon-192.png" type="image/png">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#2E3D63">
    <link rel="manifest" href="/manifest.json">
    <link rel="apple-touch-icon" href="/icon-192.png">

    <!-- OpenGraph Meta Tags -->
    <meta property="og:title" content="Trading Signals App - Offline">
    <meta property="og:description" content="Trading Signals App - Please check your internet connection">
    <meta property="og:image" content="/icon-512.png">
    <meta property="og:url" content="https://trading-signals-app.example.com/offline.html">
    <meta property="og:type" content="website">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Trading Signals App - Offline">
    <meta name="twitter:description" content="Trading Signals App - Please check your internet connection">
    <meta name="twitter:image" content="/icon-512.png">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        :root {
            --primary-color: #2E3D63;
            --secondary-color: #2DBBB7;
            --accent-color: #FAC42D;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --body-bg: #0d1117;
            --body-color: #f8f9fa;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            --hover-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        }

        [data-theme="light"] {
            --primary-color: #2E3D63;
            --secondary-color: #2DBBB7;
            --accent-color: #FAC42D;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --body-bg: #ffffff;
            --body-color: #212529;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            --hover-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        }

        body {
            background-color: var(--body-bg);
            color: var(--body-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .offline-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 2rem;
        }

        .offline-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #0d6efd;
        }

        .offline-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            max-width: 600px;
        }

        .retry-button {
            padding: 0.75rem 2rem;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            background-color: #0d6efd;
            border: none;
        }

        .retry-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
            background-color: #0b5ed7;
        }

        .retry-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .available-section {
            margin-top: 3rem;
            width: 100%;
            max-width: 600px;
        }

        .available-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .available-links {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .available-link {
            display: block;
            padding: 1rem;
            background-color: rgba(13, 110, 253, 0.1);
            border-radius: 8px;
            color: var(--body-color);
            text-decoration: none;
            transition: all 0.3s ease;
            text-align: left;
            border-left: 4px solid #0d6efd;
        }

        .available-link:hover {
            background-color: rgba(13, 110, 253, 0.2);
            transform: translateX(5px);
        }

        .footer {
            margin-top: 2rem;
            padding: 1rem;
            text-align: center;
            font-size: 0.9rem;
            color: var(--secondary-color);
        }

        /* Dark mode toggle */
        .theme-toggle {
            position: absolute;
            top: 1rem;
            right: 1rem;
            z-index: 1000;
        }

        .theme-toggle-checkbox {
            opacity: 0;
            position: absolute;
        }

        .theme-toggle-label {
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            background-color: var(--light-color);
            color: var(--dark-color);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            transition: all 0.3s ease;
        }

        [data-theme="light"] .theme-toggle-label {
            background-color: var(--dark-color);
            color: var(--light-color);
        }

        .theme-toggle-label:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <!-- Dark Mode Toggle -->
    <div class="theme-toggle">
        <input type="checkbox" id="darkModeToggle" class="theme-toggle-checkbox">
        <label for="darkModeToggle" class="theme-toggle-label">
            <i class="fas fa-sun"></i>
        </label>
    </div>

    <div class="offline-container">
        <h1 class="offline-title">You're Offline</h1>

        <p class="offline-message">
            It looks like you've lost your internet connection.
            Some features of the Trading Signals App may not
            work properly until you're back online.
        </p>

        <button id="retryButton" class="btn retry-button">
            <i class="fas fa-sync-alt me-2"></i> Try Again
        </button>

        <div class="available-section">
            <h2 class="available-title">Available Offline</h2>
            <ul class="available-links">
                <li><a href="/" class="available-link">Home</a></li>
                <li><a href="/login" class="available-link">Login</a></li>
            </ul>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <p>Trading Signals App &copy; 2023 - All Rights Reserved</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Check if service worker is registered
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(registration => {
                console.log('Service Worker is ready');
            });
        }

        // Retry button
        document.getElementById('retryButton').addEventListener('click', () => {
            window.location.href = '/';
        });

        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');

        // Initialize theme from localStorage (default is dark in this case)
        const savedTheme = localStorage.getItem('theme_preference');
        if (savedTheme) {
            document.documentElement.setAttribute('data-theme', savedTheme);
            darkModeToggle.checked = savedTheme === 'light';
        } else {
            // Default to dark theme
            document.documentElement.setAttribute('data-theme', 'dark');
            darkModeToggle.checked = false;
        }

        // Toggle theme
        darkModeToggle.addEventListener('change', function() {
            const theme = this.checked ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('theme_preference', theme);
        });
    </script>
</body>
</html>
