/**
 * Trading Signals App - Main Application Script
 *
 * This script handles the main functionality of the Trading Signals App,
 * including fetching and displaying real-time market data, updating charts,
 * and generating trading signals.
 */

// Application state
const appState = {
    // Market data
    market: {
        currentSymbol: 'BTCUSD',
        currentTimeframe: '1h',
        currentAssetType: 'crypto',
        data: null,
        isLoading: false,
        hasError: false,
        errorMessage: '',
        lastUpdated: null,
        updateInterval: null
    },

    // UI state
    ui: {
        isDarkMode: false,
        isMenuOpen: false,
        activeTab: 'market'
    },

    // Chart state
    chart: {
        instance: null,
        type: 'candlestick',
        indicators: ['sma', 'volume']
    },
    
    // Economic calendar
    calendar: {
        events: [],
        isLoading: false,
        hasError: false,
        errorMessage: '',
        lastUpdated: null
    }
};

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', initApp);

/**
 * Initialize the application
 */
function initApp() {
    console.log('Initializing Trading Signals App...');

    // Start progress indicator
    if (typeof NProgress !== 'undefined') {
        NProgress.start();
    }

    // Set up event listeners
    setupEventListeners();

    // Initialize UI components
    initializeUI();

    // Load initial market data
    loadMarketData();
    
    // Load economic calendar
    loadEconomicCalendar();

    // Set up auto-refresh
    setupAutoRefresh();

    console.log('App initialization complete');

    // Complete progress indicator
    if (typeof NProgress !== 'undefined') {
        setTimeout(() => {
            NProgress.done();
        }, 500);
    }
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
    // Symbol selector
    const symbolSelector = document.getElementById('symbol');
    if (symbolSelector) {
        symbolSelector.addEventListener('change', function() {
            appState.market.currentSymbol = this.value;
            loadMarketData();
        });
    }

    // Timeframe selector
    const timeframeSelector = document.getElementById('timeframe');
    if (timeframeSelector) {
        timeframeSelector.addEventListener('change', function() {
            appState.market.currentTimeframe = this.value;
            loadMarketData();
        });
    }

    // Asset type selector
    const assetTypeSelector = document.getElementById('assetType');
    if (assetTypeSelector) {
        assetTypeSelector.addEventListener('change', function() {
            appState.market.currentAssetType = this.value;
            loadMarketData();
        });
    }

    // Refresh button
    const refreshButton = document.getElementById('refreshData');
    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            loadMarketData();
        });
    }
    
    // Economic calendar refresh button
    const refreshCalendarBtn = document.getElementById('refreshCalendarBtn');
    if (refreshCalendarBtn) {
        refreshCalendarBtn.addEventListener('click', function() {
            loadEconomicCalendar();
        });
    }

    // Dark mode toggle
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('change', function() {
            toggleDarkMode(this.checked);
        });
    }

    // Menu toggle
    const menuToggle = document.getElementById('menuToggle');
    if (menuToggle) {
        menuToggle.addEventListener('click', function() {
            toggleMenu();
        });
    }

    // Tab buttons
    const tabButtons = document.querySelectorAll('[data-tab]');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tab = this.getAttribute('data-tab');
            switchTab(tab);
        });
    });
    
    console.log('Event listeners set up');
}

/**
 * Initialize UI components
 */
function initializeUI() {
    // Set initial values for selects
    const symbolSelector = document.getElementById('symbol');
    if (symbolSelector) {
        symbolSelector.value = appState.market.currentSymbol;
    }

    const timeframeSelector = document.getElementById('timeframe');
    if (timeframeSelector) {
        timeframeSelector.value = appState.market.currentTimeframe;
    }

    const assetTypeSelector = document.getElementById('assetType');
    if (assetTypeSelector) {
        assetTypeSelector.value = appState.market.currentAssetType;
    }

    // Set initial dark mode
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle) {
        const isDarkMode = localStorage.getItem('darkMode') === 'true';
        darkModeToggle.checked = isDarkMode;
        toggleDarkMode(isDarkMode);
    }

    // Set initial active tab
    const activeTab = localStorage.getItem('activeTab') || 'market';
    switchTab(activeTab);
    
    console.log('UI initialized');
}

/**
 * Load market data
 * @param {boolean} silent - Whether to show loading indicators
 */
async function loadMarketData(silent = false) {
    try {
        // Update loading state
        if (!silent) {
            updateLoadingState(true);
        }

        // Fetch market data using the market data service
        const data = await window.marketDataService.getMarketData(
            appState.market.currentSymbol,
            appState.market.currentTimeframe,
            appState.market.currentAssetType
        );

        // Update app state with new data
        appState.market.data = data;
        appState.market.lastUpdated = new Date();
        appState.market.isLoading = false;
        appState.market.hasError = false;
        appState.market.errorMessage = '';

        // Update UI
        updateMarketUI();
        updateChart();
        generateTradingSignals();

        return data;
    } catch (error) {
        // Update error state
        console.error('Error loading market data:', error);
        appState.market.isLoading = false;
        appState.market.hasError = true;
        appState.market.errorMessage = error.message || 'Failed to load market data';
        
        // Update UI to show error
        updateErrorState(appState.market.errorMessage);
        
        return null;
    }
}

/**
 * Load economic calendar data
 */
async function loadEconomicCalendar() {
    try {
        // Update loading state
        appState.calendar.isLoading = true;
        
        // Update UI
        const calendarStatus = document.getElementById('calendarStatus');
        if (calendarStatus) {
            calendarStatus.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
        }
        
        // Get today's date and 7 days from now
        const today = new Date();
        const nextWeek = new Date(today);
        nextWeek.setDate(today.getDate() + 7);
        
        // Format dates as YYYY-MM-DD
        const startDate = today.toISOString().split('T')[0];
        const endDate = nextWeek.toISOString().split('T')[0];
        
        // Fetch economic calendar data
        const events = await window.economicCalendarService.getEconomicCalendar({
            startDate,
            endDate,
            importance: 'all',
            country: 'all'
        });
        
        // Update app state
        appState.calendar.events = events;
        appState.calendar.lastUpdated = new Date();
        appState.calendar.isLoading = false;
        appState.calendar.hasError = false;
        appState.calendar.errorMessage = '';
        
        // Update UI
        updateCalendarUI();
        
        return events;
    } catch (error) {
        // Update error state
        console.error('Error loading economic calendar:', error);
        appState.calendar.isLoading = false;
        appState.calendar.hasError = true;
        appState.calendar.errorMessage = error.message || 'Failed to load economic calendar';
        
        // Update UI to show error
        const calendarStatus = document.getElementById('calendarStatus');
        if (calendarStatus) {
            calendarStatus.innerHTML = `<div class="alert alert-danger">Error: ${appState.calendar.errorMessage}</div>`;
        }
        
        return null;
    }
}

/**
 * Update market UI with current data
 */
function updateMarketUI() {
    // Update last updated time
    const lastUpdatedElement = document.getElementById('lastUpdated');
    if (lastUpdatedElement && appState.market.lastUpdated) {
        lastUpdatedElement.textContent = appState.market.lastUpdated.toLocaleTimeString();
    }

    // Update current price
    const currentPriceElement = document.getElementById('currentPrice');
    if (currentPriceElement && appState.market.data && appState.market.data.closes.length > 0) {
        const currentPrice = appState.market.data.closes[appState.market.data.closes.length - 1];
        currentPriceElement.textContent = currentPrice.toFixed(2);
    }

    // Update price change
    const priceChangeElement = document.getElementById('priceChange');
    if (priceChangeElement && appState.market.data && appState.market.data.closes.length > 1) {
        const currentPrice = appState.market.data.closes[appState.market.data.closes.length - 1];
        const previousPrice = appState.market.data.closes[appState.market.data.closes.length - 2];
        const change = currentPrice - previousPrice;
        const changePercent = (change / previousPrice) * 100;
        
        priceChangeElement.textContent = `${change.toFixed(2)} (${changePercent.toFixed(2)}%)`;
        
        if (change > 0) {
            priceChangeElement.classList.remove('text-danger');
            priceChangeElement.classList.add('text-success');
        } else {
            priceChangeElement.classList.remove('text-success');
            priceChangeElement.classList.add('text-danger');
        }
    }

    // Update loading state
    updateLoadingState(false);
}

/**
 * Update economic calendar UI
 */
function updateCalendarUI() {
    const calendarBody = document.getElementById('calendarBody');
    const calendarStatus = document.getElementById('calendarStatus');
    
    if (!calendarBody) return;
    
    // Clear loading indicator
    if (calendarStatus) {
        calendarStatus.innerHTML = '';
    }
    
    // Clear existing events
    calendarBody.innerHTML = '';
    
    // Check if there are events
    if (!appState.calendar.events || appState.calendar.events.length === 0) {
        calendarBody.innerHTML = '<tr><td colspan="5" class="text-center">No economic events found</td></tr>';
        return;
    }
    
    // Group events by date
    const eventsByDate = {};
    appState.calendar.events.forEach(event => {
        if (!eventsByDate[event.date]) {
            eventsByDate[event.date] = [];
        }
        eventsByDate[event.date].push(event);
    });
    
    // Sort dates
    const sortedDates = Object.keys(eventsByDate).sort();
    
    // Create HTML for each date group
    sortedDates.forEach(date => {
        // Add date header
        const dateHeader = document.createElement('tr');
        dateHeader.className = 'bg-light';
        dateHeader.innerHTML = `<th colspan="5" class="text-center">${formatDate(date)}</th>`;
        calendarBody.appendChild(dateHeader);
        
        // Add events for this date
        eventsByDate[date].forEach(event => {
            const row = document.createElement('tr');
            row.setAttribute('data-importance', event.importance);
            
            // Set row class based on importance
            if (event.importance === 'high') {
                row.className = 'table-danger';
            } else if (event.importance === 'medium') {
                row.className = 'table-warning';
            }
            
            row.innerHTML = `
                <td>${event.time}</td>
                <td>${event.country}</td>
                <td>${event.title}</td>
                <td>${event.forecast || '-'}</td>
                <td>${event.previous || '-'}</td>
            `;
            
            calendarBody.appendChild(row);
        });
    });
}

/**
 * Format date as a readable string
 * @param {string} dateStr - Date string in YYYY-MM-DD format
 * @returns {string} - Formatted date string
 */
function formatDate(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleDateString(undefined, { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
}

/**
 * Update chart with current data
 */
function updateChart() {
    console.log('Updating chart with new data...');

    // Check if we have market data
    if (!appState.market.data) {
        console.warn('No market data available for chart update');
        return;
    }

    try {
        // If chart.js is loaded and updateChart function is available, call it
        if (window.updateChart && typeof window.updateChart === 'function') {
            window.updateChart(appState.market.data);
        } else if (window.chartIntegration && typeof window.chartIntegration.updateChart === 'function') {
            window.chartIntegration.updateChart();
        } else {
            console.error('Chart update function not found. Make sure chart.js is properly loaded.');
        }
    } catch (error) {
        console.error('Error updating chart:', error);
    }
}

/**
 * Generate trading signals based on current data
 */
function generateTradingSignals() {
    console.log('Generating trading signals...');

    // Check if we have market data
    if (!appState.market.data) {
        console.warn('No market data available for signal generation');
        return;
    }

    try {
        // If technical analysis module is loaded, use it to generate signals
        if (window.technicalAnalysis && typeof window.technicalAnalysis.generateSignals === 'function') {
            const signals = window.technicalAnalysis.generateSignals(appState.market.data);
            updateSignalsUI(signals);
        } else {
            console.warn('Technical analysis module not found. Cannot generate signals.');
        }
    } catch (error) {
        console.error('Error generating trading signals:', error);
    }
}

/**
 * Update signals UI with generated signals
 * @param {Array} signals - Trading signals
 */
function updateSignalsUI(signals) {
    const signalsContainer = document.getElementById('tradingSignals');
    if (!signalsContainer) return;

    // Clear existing signals
    signalsContainer.innerHTML = '';

    // Check if we have signals
    if (!signals || signals.length === 0) {
        signalsContainer.innerHTML = '<div class="alert alert-info">No trading signals available</div>';
        return;
    }

    // Create signals list
    const signalsList = document.createElement('div');
    signalsList.className = 'list-group';

    signals.forEach(signal => {
        const signalItem = document.createElement('div');
        signalItem.className = `list-group-item list-group-item-${signal.type === 'buy' ? 'success' : 'danger'}`;

        signalItem.innerHTML = `
            <div class="d-flex w-100 justify-content-between">
                <h5 class="mb-1">${signal.type.toUpperCase()} ${appState.market.currentSymbol}</h5>
                <small>${new Date(signal.timestamp).toLocaleTimeString()}</small>
            </div>
            <p class="mb-1">${signal.reason}</p>
            <small>Confidence: ${signal.confidence}%</small>
        `;

        signalsList.appendChild(signalItem);
    });

    signalsContainer.appendChild(signalsList);
}

/**
 * Update loading state
 * @param {boolean} isLoading - Whether data is loading
 */
function updateLoadingState(isLoading) {
    appState.market.isLoading = isLoading;

    // Update loading spinner
    const loadingSpinner = document.getElementById('loadingSpinner');
    if (loadingSpinner) {
        if (isLoading) {
            loadingSpinner.style.display = 'inline-block';
        } else {
            loadingSpinner.style.display = 'none';
        }
    }

    // Update refresh button
    const refreshButton = document.getElementById('refreshData');
    if (refreshButton) {
        refreshButton.disabled = isLoading;
    }
}

/**
 * Update error state
 * @param {string} errorMessage - Error message
 */
function updateErrorState(errorMessage) {
    // Update error message
    const errorElement = document.getElementById('errorMessage');
    if (errorElement) {
        errorElement.textContent = errorMessage;
        errorElement.style.display = errorMessage ? 'block' : 'none';
    }

    // Update loading state
    updateLoadingState(false);
}

/**
 * Toggle dark mode
 * @param {boolean} isDark - Whether to enable dark mode
 */
function toggleDarkMode(isDark) {
    appState.ui.isDarkMode = isDark;

    // Update body class
    if (isDark) {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
    } else {
        document.body.classList.remove('dark-mode');
        document.documentElement.setAttribute('data-theme', 'light');
    }

    // Save preference
    localStorage.setItem('darkMode', isDark);

    // Dispatch theme change event
    window.dispatchEvent(new CustomEvent('themechange', { detail: { theme: isDark ? 'dark' : 'light' } }));
}

/**
 * Toggle menu
 */
function toggleMenu() {
    appState.ui.isMenuOpen = !appState.ui.isMenuOpen;

    // Update menu class
    const menu = document.getElementById('sideMenu');
    if (menu) {
        if (appState.ui.isMenuOpen) {
            menu.classList.add('show');
        } else {
            menu.classList.remove('show');
        }
    }
}

/**
 * Switch active tab
 * @param {string} tab - Tab ID
 */
function switchTab(tab) {
    appState.ui.activeTab = tab;

    // Update tab buttons
    const tabButtons = document.querySelectorAll('[data-tab]');
    tabButtons.forEach(button => {
        if (button.getAttribute('data-tab') === tab) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }
    });

    // Update tab content
    const tabContents = document.querySelectorAll('[data-tab-content]');
    tabContents.forEach(content => {
        if (content.getAttribute('data-tab-content') === tab) {
            content.style.display = 'block';
        } else {
            content.style.display = 'none';
        }
    });

    // Save preference
    localStorage.setItem('activeTab', tab);
}

/**
 * Set up auto-refresh
 */
function setupAutoRefresh() {
    // Clear existing interval
    if (appState.market.updateInterval) {
        clearInterval(appState.market.updateInterval);
    }

    // Set up new interval
    appState.market.updateInterval = setInterval(() => {
        loadMarketData(true); // Silent update
    }, 60000); // Update every minute
}

// Export functions for external use
window.app = {
    loadMarketData,
    loadEconomicCalendar,
    updateChart,
    generateTradingSignals,
    toggleDarkMode,
    toggleMenu,
    switchTab
};
