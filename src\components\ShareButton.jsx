import React from 'react';

export default function ShareButton({ text, url, className }) {
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Trading Signals App',
        text: text || 'Check out this trading signal!',
        url: url || window.location.href,
      });
    } else {
      window.open(
        `https://twitter.com/intent/tweet?text=${encodeURIComponent(text || 'Check out this trading signal!')}&url=${encodeURIComponent(url || window.location.href)}`,
        '_blank'
      );
    }
  };

  return (
    <button
      className={`bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded ${className || ''}`}
      onClick={handleShare}
      aria-label="Share"
      type="button"
    >
      <span role="img" aria-label="Share" className="mr-1">🔗</span> Share
    </button>
  );
} 