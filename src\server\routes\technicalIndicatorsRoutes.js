/**
 * Technical Indicators Routes for Trading Signals App
 */

const axios = require('axios');
const logger = require('../utils/logger');

module.exports = function(app, apiCache, API_CONFIG) {
  // Get technical indicators for a symbol
  app.get('/api/technical-indicators/:symbol', (req, res, next) => {
    const symbol = req.params.symbol;
    
    // Create cache key
    const cacheKey = `technical_indicators_${symbol}`;
    
    // Check cache first
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      return res.json(cachedData);
    }
    
    // Mock technical indicators data
    const indicators = {
      rsi: { value: 65.3, signal: 'buy' },
      macd: { value: 0.0023, signal: 'buy' },
      ma50: { value: 1.0840, signal: 'buy' },
      ma200: { value: 1.0820, signal: 'buy' },
      bollinger: { upper: 1.0910, middle: 1.0865, lower: 1.0820, signal: 'neutral' }
    };
    
    // Cache the response
    apiCache.set(cacheKey, indicators, 300); // 5 minutes TTL
    
    return res.json(indicators);
  });
  
  // Get specific technical indicator for a symbol
  app.get('/api/technical/:indicator/:symbol', async (req, res) => {
    try {
      const { indicator, symbol } = req.params;
      const { 
        interval = '1d', 
        time_period = 14, 
        series_type = 'close' 
      } = req.query;
      
      // Create cache key
      const cacheKey = `technical_${indicator}_${symbol}_${interval}_${time_period}_${series_type}`;
      
      // Check cache first
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        return res.json(cachedData);
      }
      
      // Get technical indicator from Alpha Vantage
      try {
        const response = await axios.get(API_CONFIG.ALPHA_VANTAGE.BASE_URL, {
          params: {
            function: indicator,
            symbol,
            interval,
            time_period,
            series_type,
            apikey: API_CONFIG.ALPHA_VANTAGE.API_KEY
          }
        });
        
        // Cache the response
        apiCache.set(cacheKey, response.data, 300); // 5 minutes TTL
        
        return res.json(response.data);
      } catch (error) {
        logger.warn(`Failed to get technical indicator data from Alpha Vantage: ${error.message}`);
        
        // Return mock data
        const mockData = getMockTechnicalIndicator(indicator, symbol, interval, time_period);
        
        // Cache the mock data (shorter TTL)
        apiCache.set(cacheKey, mockData, 60); // 1 minute TTL for mock data
        
        return res.json(mockData);
      }
    } catch (error) {
      logger.error('Technical indicator API error:', error.message);
      res.status(500).json({ error: 'Failed to fetch technical indicator data' });
    }
  });
  
  // Helper function to get mock technical indicator data
  function getMockTechnicalIndicator(indicator, symbol, interval, time_period) {
    const now = new Date();
    const data = {
      'Meta Data': {
        '1: Symbol': symbol,
        '2: Indicator': indicator.toUpperCase(),
        '3: Last Refreshed': now.toISOString(),
        '4: Interval': interval,
        '5: Time Period': time_period,
        '6: Series Type': 'close'
      }
    };
    
    // Generate mock indicator data
    const technicalData = {};
    for (let i = 30; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(now.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      switch (indicator.toUpperCase()) {
        case 'RSI':
          technicalData[dateStr] = {
            'RSI': 50 + Math.sin(i / 5) * 20 + (Math.random() * 10 - 5)
          };
          break;
        case 'MACD':
          technicalData[dateStr] = {
            'MACD': Math.sin(i / 10) * 0.01 + (Math.random() * 0.002 - 0.001),
            'MACD_Signal': Math.sin((i + 2) / 10) * 0.01 + (Math.random() * 0.002 - 0.001),
            'MACD_Hist': Math.sin(i / 5) * 0.005 + (Math.random() * 0.001 - 0.0005)
          };
          break;
        case 'SMA':
        case 'EMA':
          technicalData[dateStr] = {
            [indicator]: 100 + Math.sin(i / 15) * 10 + (Math.random() * 2 - 1)
          };
          break;
        case 'BBANDS':
          const middle = 100 + Math.sin(i / 15) * 10;
          technicalData[dateStr] = {
            'Real Upper Band': middle + 2 * (1 + Math.random() * 0.5),
            'Real Middle Band': middle,
            'Real Lower Band': middle - 2 * (1 + Math.random() * 0.5)
          };
          break;
        default:
          technicalData[dateStr] = {
            [indicator]: 100 + Math.sin(i / 10) * 10 + (Math.random() * 5 - 2.5)
          };
      }
    }
    
    // Add the technical data to the response
    data[`Technical Analysis: ${indicator.toUpperCase()}`] = technicalData;
    
    return data;
  }
};
