/**
 * Swagger/OpenAPI Documentation Service for Trading Signals App
 * 
 * Provides comprehensive API documentation with request/response schemas,
 * authentication details, and interactive testing interface.
 */

const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

/**
 * Swagger configuration options
 */
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Trading Signals App API',
      version: '2.0.0',
      description: `
        Comprehensive API for the Trading Signals Application providing:
        - Real-time market data access
        - Technical analysis and indicators
        - AI-powered trading signals
        - User authentication and management
        - Economic calendar integration
        - Performance analytics
      `,
      contact: {
        name: 'Trading Signals App Team',
        email: '<EMAIL>',
        url: 'https://tradingsignals.app'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:3000',
        description: 'Development server'
      },
      {
        url: 'https://api.tradingsignals.app',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token obtained from /api/auth/login endpoint'
        },
        apiKey: {
          type: 'apiKey',
          in: 'header',
          name: 'X-API-Key',
          description: 'API key for external integrations'
        }
      },
      schemas: {
        User: {
          type: 'object',
          required: ['email', 'username'],
          properties: {
            _id: {
              type: 'string',
              description: 'User unique identifier',
              example: '507f1f77bcf86cd799439011'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address',
              example: '<EMAIL>'
            },
            username: {
              type: 'string',
              description: 'Unique username',
              example: 'trader123'
            },
            role: {
              type: 'string',
              enum: ['user', 'premium', 'admin'],
              description: 'User role',
              example: 'user'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Account creation timestamp'
            },
            preferences: {
              type: 'object',
              properties: {
                theme: {
                  type: 'string',
                  enum: ['light', 'dark'],
                  example: 'dark'
                },
                notifications: {
                  type: 'boolean',
                  example: true
                }
              }
            }
          }
        },
        MarketData: {
          type: 'object',
          required: ['symbol', 'timestamp', 'open', 'high', 'low', 'close'],
          properties: {
            symbol: {
              type: 'string',
              description: 'Trading pair symbol',
              example: 'EURUSD'
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
              description: 'Data timestamp'
            },
            timeframe: {
              type: 'string',
              enum: ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1'],
              description: 'Chart timeframe',
              example: 'H1'
            },
            open: {
              type: 'number',
              format: 'float',
              description: 'Opening price',
              example: 1.0850
            },
            high: {
              type: 'number',
              format: 'float',
              description: 'Highest price',
              example: 1.0870
            },
            low: {
              type: 'number',
              format: 'float',
              description: 'Lowest price',
              example: 1.0840
            },
            close: {
              type: 'number',
              format: 'float',
              description: 'Closing price',
              example: 1.0860
            },
            volume: {
              type: 'integer',
              description: 'Trading volume',
              example: 150000
            }
          }
        },
        TradingSignal: {
          type: 'object',
          required: ['symbol', 'type', 'strength'],
          properties: {
            _id: {
              type: 'string',
              description: 'Signal unique identifier'
            },
            symbol: {
              type: 'string',
              description: 'Trading pair symbol',
              example: 'EURUSD'
            },
            type: {
              type: 'string',
              enum: ['buy', 'sell'],
              description: 'Signal type',
              example: 'buy'
            },
            strength: {
              type: 'integer',
              minimum: 0,
              maximum: 100,
              description: 'Signal strength percentage',
              example: 85
            },
            timeframe: {
              type: 'string',
              enum: ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1'],
              description: 'Recommended timeframe',
              example: 'H1'
            },
            entryPrice: {
              type: 'number',
              format: 'float',
              description: 'Recommended entry price',
              example: 1.0850
            },
            stopLoss: {
              type: 'number',
              format: 'float',
              description: 'Stop loss price',
              example: 1.0820
            },
            takeProfit: {
              type: 'number',
              format: 'float',
              description: 'Take profit price',
              example: 1.0920
            },
            analysis: {
              type: 'string',
              description: 'Signal analysis and reasoning',
              example: 'Strong bullish momentum with RSI oversold recovery'
            },
            generatedBy: {
              type: 'string',
              enum: ['system', 'ai', 'manual'],
              description: 'Signal generation method',
              example: 'ai'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Signal creation timestamp'
            }
          }
        },
        TechnicalIndicators: {
          type: 'object',
          properties: {
            symbol: {
              type: 'string',
              example: 'EURUSD'
            },
            timeframe: {
              type: 'string',
              example: 'H1'
            },
            rsi: {
              type: 'number',
              format: 'float',
              minimum: 0,
              maximum: 100,
              description: 'Relative Strength Index',
              example: 65.5
            },
            macd: {
              type: 'object',
              properties: {
                macd: {
                  type: 'number',
                  format: 'float',
                  example: 0.0015
                },
                signal: {
                  type: 'number',
                  format: 'float',
                  example: 0.0010
                },
                histogram: {
                  type: 'number',
                  format: 'float',
                  example: 0.0005
                }
              }
            },
            ema: {
              type: 'number',
              format: 'float',
              description: 'Exponential Moving Average',
              example: 1.0845
            },
            bollingerBands: {
              type: 'object',
              properties: {
                upper: {
                  type: 'number',
                  format: 'float',
                  example: 1.0890
                },
                middle: {
                  type: 'number',
                  format: 'float',
                  example: 1.0850
                },
                lower: {
                  type: 'number',
                  format: 'float',
                  example: 1.0810
                }
              }
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
              description: 'Calculation timestamp'
            }
          }
        },
        Error: {
          type: 'object',
          required: ['status', 'message'],
          properties: {
            status: {
              type: 'string',
              enum: ['error'],
              example: 'error'
            },
            message: {
              type: 'string',
              description: 'Error message',
              example: 'Invalid request parameters'
            },
            code: {
              type: 'string',
              description: 'Error code',
              example: 'INVALID_PARAMS'
            },
            details: {
              type: 'object',
              description: 'Additional error details'
            }
          }
        },
        SuccessResponse: {
          type: 'object',
          required: ['status'],
          properties: {
            status: {
              type: 'string',
              enum: ['success'],
              example: 'success'
            },
            message: {
              type: 'string',
              description: 'Success message',
              example: 'Operation completed successfully'
            },
            data: {
              type: 'object',
              description: 'Response data'
            }
          }
        }
      },
      responses: {
        UnauthorizedError: {
          description: 'Authentication required',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                status: 'error',
                message: 'Authentication required',
                code: 'UNAUTHORIZED'
              }
            }
          }
        },
        ForbiddenError: {
          description: 'Insufficient permissions',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                status: 'error',
                message: 'Insufficient permissions',
                code: 'FORBIDDEN'
              }
            }
          }
        },
        NotFoundError: {
          description: 'Resource not found',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                status: 'error',
                message: 'Resource not found',
                code: 'NOT_FOUND'
              }
            }
          }
        },
        ValidationError: {
          description: 'Validation error',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              },
              example: {
                status: 'error',
                message: 'Validation failed',
                code: 'VALIDATION_ERROR',
                details: {
                  field: 'email',
                  message: 'Invalid email format'
                }
              }
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: [
    './src/routes/*.js',
    './src/controllers/*.js',
    './server.js'
  ]
};

/**
 * Generate Swagger specification
 */
const swaggerSpec = swaggerJsdoc(swaggerOptions);

/**
 * Swagger UI options
 */
const swaggerUiOptions = {
  explorer: true,
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    filter: true,
    showExtensions: true,
    showCommonExtensions: true,
    docExpansion: 'none'
  },
  customCss: `
    .swagger-ui .topbar { display: none }
    .swagger-ui .info .title { color: #1f2937 }
    .swagger-ui .scheme-container { background: #f9fafb; padding: 15px; border-radius: 5px; }
  `,
  customSiteTitle: 'Trading Signals API Documentation'
};

module.exports = {
  swaggerSpec,
  swaggerUi,
  swaggerUiOptions
};
