/**
 * Economic Calendar Routes for Trading Signals App
 */

const axios = require('axios');
const logger = require('../utils/logger');

module.exports = function(app, apiCache, API_CONFIG, getFormattedDate, transformFredCalendarToEvents) {
  // Get economic calendar events
  app.get('/api/economic-calendar', async (req, res) => {
    try {
      // Try to get real data from FRED API
      const cacheKey = 'economic_calendar';

      // Check if data is in cache
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        logger.info('Returning cached economic calendar data');
        return res.json(cachedData);
      }

      try {
        // Construct the API URL
        const apiUrl = `${API_CONFIG.FRED.BASE_URL}${API_CONFIG.FRED.ENDPOINTS.CALENDAR}?include_release_dates_with_no_data=true&start_date=${getFormattedDate(0)}&end_date=${getFormattedDate(14)}&api_key=${API_CONFIG.FRED.API_KEY}&file_type=json`;

        // Log the API request (hiding the API key)
        logger.info(`Making FRED API request: ${apiUrl.replace(/api_key=([^&]*)/, 'api_key=HIDDEN')}`);

        // Add retry logic for FRED API
        const maxRetries = 2;
        let retries = 0;
        let fredResponse;

        while (retries <= maxRetries) {
          try {
            // Make the API request with a longer timeout
            fredResponse = await axios.get(apiUrl, {
              timeout: 15000, // 15 second timeout
              headers: {
                'User-Agent': 'Trading Signals App/1.0',
                'Accept': 'application/json'
              }
            });

            // If we got here, the request was successful
            break;
          } catch (retryError) {
            if (retries < maxRetries) {
              // Wait before retrying (exponential backoff)
              const delay = Math.pow(2, retries) * 1000;
              logger.warn(`FRED API request failed, retrying after ${delay}ms (attempt ${retries + 1}/${maxRetries}): ${retryError.message}`);
              await new Promise(resolve => setTimeout(resolve, delay));
              retries++;
            } else {
              // Max retries reached, rethrow the error
              throw retryError;
            }
          }
        }

        // Log successful response
        logger.info(`FRED API response status: ${fredResponse.status}`);

        // Validate the response data
        if (!fredResponse || !fredResponse.data) {
          throw new Error('Empty response from FRED API');
        }

        // Transform FRED data to economic events format
        const events = transformFredCalendarToEvents(fredResponse.data);

        // Cache the response
        apiCache.set(cacheKey, { events }, 900); // 15 minutes TTL

        return res.json({ events });
      } catch (error) {
        logger.warn('Failed to get economic calendar data from FRED:', error.message);
        logger.error({
          message: 'FRED API error details',
          error: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString()
        });

        // Fall back to mock data
        const events = [
          { time: '08:30', currency: 'USD', event: 'Non-Farm Payrolls', importance: 'high', forecast: '200K' },
          { time: '10:00', currency: 'EUR', event: 'ECB Interest Rate Decision', importance: 'high', forecast: '4.50%' },
          { time: '12:30', currency: 'GBP', event: 'Manufacturing PMI', importance: 'medium', forecast: '52.3' },
          { time: '14:00', currency: 'USD', event: 'ISM Manufacturing PMI', importance: 'medium', forecast: '49.8' },
          { time: '15:30', currency: 'USD', event: 'Crude Oil Inventories', importance: 'medium', forecast: '-2.1M' },
          { time: '18:00', currency: 'USD', event: 'FOMC Meeting Minutes', importance: 'high', forecast: 'N/A' }
        ];

        // Cache the mock data (shorter TTL)
        apiCache.set(cacheKey, { events }, 300); // 5 minutes TTL for mock data

        return res.json({ events });
      }
    } catch (error) {
      logger.error('Economic calendar API error:', error.message);
      res.status(500).json({ error: 'Failed to fetch economic calendar data' });
    }
  });

  // Get FRED data
  app.get('/api/fred-data', async (req, res) => {
    try {
      const releaseType = req.query.type || 'calendar'; // calendar, releases, or series
      const seriesId = req.query.series || 'GDP'; // Default to GDP if not specified
      const startDate = req.query.start_date || getFormattedDate(-30); // Default to 30 days ago
      const endDate = req.query.end_date || getFormattedDate(0); // Default to today

      const cacheKey = `fred_${releaseType}_${seriesId}_${startDate}_${endDate}`;

      // Check if data is in cache
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        logger.info(`Returning cached FRED data for ${releaseType}`);
        return res.json(cachedData);
      }

      // Prepare API request
      const config = API_CONFIG.FRED;
      let endpoint, params;

      switch (releaseType) {
        case 'series':
          endpoint = config.ENDPOINTS.SERIES;
          params = `series_id=${seriesId}&observation_start=${startDate}&observation_end=${endDate}&api_key=${config.API_KEY}&file_type=json`;
          break;
        case 'releases':
          endpoint = config.ENDPOINTS.RELEASES;
          params = `api_key=${config.API_KEY}&file_type=json`;
          break;
        case 'calendar':
        default:
          endpoint = config.ENDPOINTS.CALENDAR;
          params = `include_release_dates_with_no_data=true&start_date=${startDate}&end_date=${endDate}&api_key=${config.API_KEY}&file_type=json`;
          break;
      }

      // Construct the API URL
      const apiUrl = `${config.BASE_URL}${endpoint}?${params}`;

      // Log the API request (hiding the API key)
      logger.info(`Making FRED API request: ${apiUrl.replace(/api_key=([^&]*)/, 'api_key=HIDDEN')}`);

      // Add retry logic for FRED API
      const maxRetries = 2;
      let retries = 0;
      let response;

      while (retries <= maxRetries) {
        try {
          // Make the API request with a longer timeout
          response = await axios.get(apiUrl, {
            timeout: 15000, // 15 second timeout
            headers: {
              'User-Agent': 'Trading Signals App/1.0',
              'Accept': 'application/json'
            }
          });

          // If we got here, the request was successful
          break;
        } catch (retryError) {
          if (retries < maxRetries) {
            // Wait before retrying (exponential backoff)
            const delay = Math.pow(2, retries) * 1000;
            logger.warn(`FRED API request failed, retrying after ${delay}ms (attempt ${retries + 1}/${maxRetries}): ${retryError.message}`);
            await new Promise(resolve => setTimeout(resolve, delay));
            retries++;
          } else {
            // Max retries reached, rethrow the error
            throw retryError;
          }
        }
      }

      // Log successful response
      logger.info(`FRED API response status: ${response.status}`);

      // Validate the response data
      if (!response || !response.data) {
        throw new Error('Empty response from FRED API');
      }

      // Process the data based on the type
      let processedData;

      if (releaseType === 'calendar') {
        // Transform calendar data into economic events format
        processedData = {
          events: transformFredCalendarToEvents(response.data)
        };
      } else {
        // Return raw data for other types
        processedData = response.data;
      }

      // Cache the response (15 minutes TTL for FRED data)
      apiCache.set(cacheKey, processedData, 900);

      return res.json(processedData);
    } catch (error) {
      logger.error('FRED API error:', error.message);
      res.status(500).json({
        error: 'Failed to fetch data from FRED API',
        message: error.message
      });
    }
  });
};
