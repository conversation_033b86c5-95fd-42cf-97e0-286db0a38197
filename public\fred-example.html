<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FRED API Example - Trading Signals App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #0d3b66;
            color: white;
        }
        .table-responsive {
            max-height: 400px;
            overflow-y: auto;
        }
        .btn-primary {
            background-color: #0d3b66;
            border-color: #0d3b66;
        }
        .btn-primary:hover {
            background-color: #0a2a4a;
            border-color: #0a2a4a;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
        .high-importance {
            background-color: rgba(220, 53, 69, 0.1);
        }
        .medium-importance {
            background-color: rgba(255, 193, 7, 0.1);
        }
        .low-importance {
            background-color: rgba(108, 117, 125, 0.1);
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
        }
        .nav-tabs .nav-link.active {
            background-color: #0d3b66;
            color: white;
        }
        .nav-tabs .nav-link {
            color: #0d3b66;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row mb-4">
            <div class="col-md-12">
                <h1 class="text-center">FRED API Example</h1>
                <p class="text-center text-muted">Fetch and display economic data from the Federal Reserve Economic Data (FRED) API</p>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <ul class="nav nav-tabs" id="fredTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="calendar-tab" data-bs-toggle="tab" data-bs-target="#calendar" type="button" role="tab" aria-controls="calendar" aria-selected="true">Economic Calendar</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="series-tab" data-bs-toggle="tab" data-bs-target="#series" type="button" role="tab" aria-controls="series" aria-selected="false">Economic Series</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="releases-tab" data-bs-toggle="tab" data-bs-target="#releases" type="button" role="tab" aria-controls="releases" aria-selected="false">All Releases</button>
                    </li>
                </ul>
                <div class="tab-content" id="fredTabsContent">
                    <!-- Calendar Tab -->
                    <div class="tab-pane fade show active" id="calendar" role="tabpanel" aria-labelledby="calendar-tab">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Economic Calendar</h5>
                                <button id="refreshCalendarBtn" class="btn btn-sm btn-light">
                                    <i class="fas fa-sync-alt me-1"></i>Refresh
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">Start Date</span>
                                            <input type="date" id="calendarStartDate" class="form-control">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">End Date</span>
                                            <input type="date" id="calendarEndDate" class="form-control">
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <select id="importanceFilter" class="form-select">
                                            <option value="all">All Importance Levels</option>
                                            <option value="high">High Importance Only</option>
                                            <option value="medium">Medium Importance Only</option>
                                            <option value="low">Low Importance Only</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button id="fetchCalendarBtn" class="btn btn-primary">
                                            <i class="fas fa-search me-1"></i>Fetch Calendar Data
                                        </button>
                                    </div>
                                </div>
                                <div id="calendarLoading" class="loading d-none">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                                <div id="calendarError" class="alert alert-danger d-none"></div>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Time</th>
                                                <th>Currency</th>
                                                <th>Event</th>
                                                <th>Importance</th>
                                                <th>Value</th>
                                            </tr>
                                        </thead>
                                        <tbody id="calendarTableBody">
                                            <!-- Calendar data will be inserted here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Series Tab -->
                    <div class="tab-pane fade" id="series" role="tabpanel" aria-labelledby="series-tab">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Economic Series Data</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">Series ID</span>
                                            <input type="text" id="seriesId" class="form-control" value="GDP" placeholder="e.g., GDP, UNRATE, CPIAUCSL">
                                        </div>
                                        <div class="form-text">Common series: GDP (Gross Domestic Product), UNRATE (Unemployment Rate), CPIAUCSL (Consumer Price Index)</div>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button id="fetchSeriesBtn" class="btn btn-primary">
                                            <i class="fas fa-search me-1"></i>Fetch Series Data
                                        </button>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">Start Date</span>
                                            <input type="date" id="seriesStartDate" class="form-control">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">End Date</span>
                                            <input type="date" id="seriesEndDate" class="form-control">
                                        </div>
                                    </div>
                                </div>
                                <div id="seriesLoading" class="loading d-none">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                                <div id="seriesError" class="alert alert-danger d-none"></div>
                                <div id="seriesChart" style="height: 400px;"></div>
                                <div id="seriesData" class="mt-3">
                                    <h6>Raw Series Data</h6>
                                    <pre id="seriesRawData">Select a series and click "Fetch Series Data" to view the data.</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Releases Tab -->
                    <div class="tab-pane fade" id="releases" role="tabpanel" aria-labelledby="releases-tab">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">All Economic Releases</h5>
                                <button id="fetchReleasesBtn" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Fetch Releases
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="releasesLoading" class="loading d-none">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                                <div id="releasesError" class="alert alert-danger d-none"></div>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Press Release</th>
                                                <th>Link</th>
                                            </tr>
                                        </thead>
                                        <tbody id="releasesTableBody">
                                            <!-- Releases data will be inserted here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set default dates
            setDefaultDates();
            
            // Add event listeners
            document.getElementById('fetchCalendarBtn').addEventListener('click', fetchCalendarData);
            document.getElementById('refreshCalendarBtn').addEventListener('click', fetchCalendarData);
            document.getElementById('importanceFilter').addEventListener('change', filterCalendarByImportance);
            document.getElementById('fetchSeriesBtn').addEventListener('click', fetchSeriesData);
            document.getElementById('fetchReleasesBtn').addEventListener('click', fetchReleasesData);
            
            // Fetch calendar data on load
            fetchCalendarData();
        });
        
        // Set default dates (current month)
        function setDefaultDates() {
            const today = new Date();
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            
            document.getElementById('calendarStartDate').valueAsDate = startOfMonth;
            document.getElementById('calendarEndDate').valueAsDate = endOfMonth;
            
            // For series, use last year
            const oneYearAgo = new Date();
            oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
            document.getElementById('seriesStartDate').valueAsDate = oneYearAgo;
            document.getElementById('seriesEndDate').valueAsDate = today;
        }
        
        // Fetch calendar data
        async function fetchCalendarData() {
            const startDate = document.getElementById('calendarStartDate').value;
            const endDate = document.getElementById('calendarEndDate').value;
            
            if (!startDate || !endDate) {
                showError('calendarError', 'Please select start and end dates');
                return;
            }
            
            // Show loading
            document.getElementById('calendarLoading').classList.remove('d-none');
            document.getElementById('calendarError').classList.add('d-none');
            document.getElementById('calendarTableBody').innerHTML = '';
            
            try {
                const response = await fetch(`/api/fred-data?type=calendar&start_date=${startDate}&end_date=${endDate}`);
                
                if (!response.ok) {
                    throw new Error(`API error: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Display calendar data
                displayCalendarData(data.events);
            } catch (error) {
                showError('calendarError', `Error fetching calendar data: ${error.message}`);
            } finally {
                document.getElementById('calendarLoading').classList.add('d-none');
            }
        }
        
        // Display calendar data
        function displayCalendarData(events) {
            const tableBody = document.getElementById('calendarTableBody');
            tableBody.innerHTML = '';
            
            if (!events || events.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center">No economic events found for the selected period</td></tr>';
                return;
            }
            
            // Sort events by date and time
            events.sort((a, b) => {
                const dateA = new Date(`1970/01/01 ${a.time}`);
                const dateB = new Date(`1970/01/01 ${b.time}`);
                return dateA - dateB;
            });
            
            // Add events to table
            events.forEach(event => {
                const row = document.createElement('tr');
                
                // Set row class based on importance
                if (event.importance === 'high') {
                    row.classList.add('high-importance');
                } else if (event.importance === 'medium') {
                    row.classList.add('medium-importance');
                } else {
                    row.classList.add('low-importance');
                }
                
                // Set data attribute for filtering
                row.setAttribute('data-importance', event.importance);
                
                // Extract date from time (if available)
                let date = 'N/A';
                let time = event.time || 'N/A';
                
                if (time.includes('T')) {
                    const parts = time.split('T');
                    date = parts[0];
                    time = parts[1].substring(0, 5); // HH:MM
                }
                
                // Create cells
                row.innerHTML = `
                    <td>${date}</td>
                    <td>${time}</td>
                    <td>${event.currency || 'USD'}</td>
                    <td>${event.event}</td>
                    <td>
                        <span class="badge ${event.importance === 'high' ? 'bg-danger' : (event.importance === 'medium' ? 'bg-warning text-dark' : 'bg-secondary')}">
                            ${event.importance.charAt(0).toUpperCase() + event.importance.slice(1)}
                        </span>
                    </td>
                    <td>${event.forecast || 'N/A'}</td>
                `;
                
                tableBody.appendChild(row);
            });
            
            // Apply current filter
            filterCalendarByImportance();
        }
        
        // Filter calendar by importance
        function filterCalendarByImportance() {
            const filter = document.getElementById('importanceFilter').value;
            const rows = document.querySelectorAll('#calendarTableBody tr');
            
            rows.forEach(row => {
                const importance = row.getAttribute('data-importance');
                
                if (filter === 'all' || filter === importance) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        // Fetch series data
        async function fetchSeriesData() {
            const seriesId = document.getElementById('seriesId').value.trim();
            const startDate = document.getElementById('seriesStartDate').value;
            const endDate = document.getElementById('seriesEndDate').value;
            
            if (!seriesId) {
                showError('seriesError', 'Please enter a series ID');
                return;
            }
            
            if (!startDate || !endDate) {
                showError('seriesError', 'Please select start and end dates');
                return;
            }
            
            // Show loading
            document.getElementById('seriesLoading').classList.remove('d-none');
            document.getElementById('seriesError').classList.add('d-none');
            document.getElementById('seriesRawData').textContent = 'Loading...';
            
            try {
                const response = await fetch(`/api/fred-data?type=series&series=${seriesId}&start_date=${startDate}&end_date=${endDate}`);
                
                if (!response.ok) {
                    throw new Error(`API error: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Display raw data
                document.getElementById('seriesRawData').textContent = JSON.stringify(data, null, 2);
                
                // Display chart if observations are available
                if (data.observations && data.observations.length > 0) {
                    displaySeriesChart(data.observations, seriesId);
                } else {
                    document.getElementById('seriesChart').innerHTML = '<div class="alert alert-warning">No data available for the selected series and date range</div>';
                }
            } catch (error) {
                showError('seriesError', `Error fetching series data: ${error.message}`);
                document.getElementById('seriesRawData').textContent = 'Error fetching data';
            } finally {
                document.getElementById('seriesLoading').classList.add('d-none');
            }
        }
        
        // Display series chart
        function displaySeriesChart(observations, seriesId) {
            const chartContainer = document.getElementById('seriesChart');
            chartContainer.innerHTML = '<canvas id="seriesChartCanvas"></canvas>';
            
            const dates = [];
            const values = [];
            
            observations.forEach(obs => {
                if (obs.date && obs.value) {
                    dates.push(obs.date);
                    values.push(parseFloat(obs.value));
                }
            });
            
            const ctx = document.getElementById('seriesChartCanvas').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [{
                        label: seriesId,
                        data: values,
                        borderColor: '#0d3b66',
                        backgroundColor: 'rgba(13, 59, 102, 0.1)',
                        borderWidth: 2,
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `FRED Series: ${seriesId}`,
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Value'
                            }
                        }
                    }
                }
            });
        }
        
        // Fetch releases data
        async function fetchReleasesData() {
            // Show loading
            document.getElementById('releasesLoading').classList.remove('d-none');
            document.getElementById('releasesError').classList.add('d-none');
            document.getElementById('releasesTableBody').innerHTML = '';
            
            try {
                const response = await fetch('/api/fred-data?type=releases');
                
                if (!response.ok) {
                    throw new Error(`API error: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Display releases data
                displayReleasesData(data.releases);
            } catch (error) {
                showError('releasesError', `Error fetching releases data: ${error.message}`);
            } finally {
                document.getElementById('releasesLoading').classList.add('d-none');
            }
        }
        
        // Display releases data
        function displayReleasesData(releases) {
            const tableBody = document.getElementById('releasesTableBody');
            tableBody.innerHTML = '';
            
            if (!releases || releases.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="4" class="text-center">No releases found</td></tr>';
                return;
            }
            
            // Sort releases by name
            releases.sort((a, b) => a.name.localeCompare(b.name));
            
            // Add releases to table
            releases.forEach(release => {
                const row = document.createElement('tr');
                
                row.innerHTML = `
                    <td>${release.id}</td>
                    <td>${release.name}</td>
                    <td>${release.press_release ? 'Yes' : 'No'}</td>
                    <td>
                        ${release.link ? `<a href="${release.link}" target="_blank" class="btn btn-sm btn-outline-primary">View</a>` : 'N/A'}
                    </td>
                `;
                
                tableBody.appendChild(row);
            });
        }
        
        // Show error message
        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            errorElement.textContent = message;
            errorElement.classList.remove('d-none');
        }
    </script>
</body>
</html>
