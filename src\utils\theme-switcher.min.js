/**
 * Theme Switcher for Trading Signals App (Minified)
 */
!function(){const e="darkMode",t="data-theme";function a(){return localStorage.getItem(e)}function n(a){a?(document.documentElement.setAttribute(t,"dark"),document.body.classList.add("dark-mode")):(document.documentElement.setAttribute(t,"light"),document.body.classList.remove("dark-mode")),localStorage.setItem(e,a),window.dispatchEvent(new CustomEvent("themechange",{detail:{theme:a?"dark":"light"}}))}function o(){const e=window.matchMedia("(prefers-color-scheme: dark)");return e.matches}function r(){const e=a();null===e?n(o()):n("true"===e)}document.addEventListener("DOMContentLoaded",function(){r();const t=document.getElementById("darkModeToggle");t&&(t.checked="true"===a()||null===a()&&o(),t.addEventListener("change",function(){n(this.checked)}));const c=document.querySelectorAll("[data-toggle-theme]");c.forEach(e=>{e.addEventListener("click",function(){const e="true"===a();n(!e);const t=document.getElementById("darkModeToggle");t&&(t.checked=!e)})})}),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",e=>{null===a()&&n(e.matches)}),window.themeSwitcher={isDarkMode:()=>"true"===a(),toggleTheme:()=>{n("true"!==a())},setTheme:n,getTheme:a,detectPreference:o,initialize:r}}();
