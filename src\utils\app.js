/**
 * Trading Signals App - Main Application Logic
 * 
 * This file contains the core functionality for the Trading Signals App,
 * including market data handling, chart initialization, and signal generation.
 */

// Application state
const marketData = {
    currentSymbol: 'EURUSD',
    currentTimeframe: 'H1',
    currentMarketType: 'forex',
    prices: [],
    dates: [],
    volumes: [],
    opens: [],
    highs: [],
    lows: [],
    closes: [],
    lastUpdated: null,
    isLoading: false,
    hasError: false,
    errorMessage: ''
};

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', initApp);

/**
 * Initialize the application
 */
function initApp() {
    console.log('Initializing Trading Signals App...');
    
    // Start progress indicator if available
    if (typeof NProgress !== 'undefined') {
        NProgress.start();
    }
    
    // Set up event listeners
    setupEventListeners();
    
    // Initialize UI components
    initializeUI();
    
    // Load initial market data
    loadMarketData();
    
    // Register service worker if available
    registerServiceWorker();
    
    console.log('App initialization complete');
    
    // Complete progress indicator
    if (typeof NProgress !== 'undefined') {
        setTimeout(() => {
            NProgress.done();
        }, 500);
    }
}

/**
 * Set up event listeners for user interactions
 */
function setupEventListeners() {
    // Market selection controls
    const marketTypeSelect = document.getElementById('marketType');
    const symbolSelect = document.getElementById('symbol');
    const timeframeSelect = document.getElementById('timeframe');
    const analyzeBtn = document.getElementById('analyzeBtn');
    
    if (marketTypeSelect) {
        marketTypeSelect.addEventListener('change', handleMarketTypeChange);
    }
    
    if (symbolSelect) {
        symbolSelect.addEventListener('change', handleSymbolChange);
    }
    
    if (timeframeSelect) {
        timeframeSelect.addEventListener('change', handleTimeframeChange);
    }
    
    if (analyzeBtn) {
        analyzeBtn.addEventListener('click', analyzeMarket);
    }
    
    // Calendar filter
    const calendarFilter = document.getElementById('calendarFilter');
    if (calendarFilter) {
        calendarFilter.addEventListener('change', filterCalendarEvents);
    }
    
    // Calendar refresh button
    const refreshCalendarBtn = document.getElementById('refreshCalendarBtn');
    if (refreshCalendarBtn) {
        refreshCalendarBtn.addEventListener('click', loadEconomicCalendar);
    }
    
    console.log('Event listeners set up');
}

/**
 * Initialize UI components
 */
function initializeUI() {
    // Set initial values for selects
    const symbolSelect = document.getElementById('symbol');
    const timeframeSelect = document.getElementById('timeframe');
    const marketTypeSelect = document.getElementById('marketType');
    
    if (symbolSelect) {
        symbolSelect.value = marketData.currentSymbol;
    }
    
    if (timeframeSelect) {
        timeframeSelect.value = marketData.currentTimeframe;
    }
    
    if (marketTypeSelect) {
        marketTypeSelect.value = marketData.currentMarketType;
    }
    
    // Update market overview
    updateMarketOverview();
    
    // Load economic calendar
    loadEconomicCalendar();
    
    // Initialize theme from localStorage
    initializeTheme();
    
    console.log('UI initialized');
}

/**
 * Initialize theme from localStorage
 */
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme_preference');
    if (savedTheme) {
        document.documentElement.setAttribute('data-theme', savedTheme);
        
        // Update theme toggle if it exists
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            darkModeToggle.checked = savedTheme === 'dark';
        }
    }
}

/**
 * Handle market type change
 */
function handleMarketTypeChange(event) {
    marketData.currentMarketType = event.target.value;
    
    // Update symbol options based on market type
    updateSymbolOptions(marketData.currentMarketType);
    
    console.log(`Market type changed to: ${marketData.currentMarketType}`);
}

/**
 * Update symbol options based on market type
 */
function updateSymbolOptions(marketType) {
    const symbolSelect = document.getElementById('symbol');
    if (!symbolSelect) return;
    
    // Clear current options
    symbolSelect.innerHTML = '';
    
    // Add new options based on market type
    let options = [];
    
    switch (marketType) {
        case 'forex':
            options = [
                { value: 'EURUSD', text: 'يورو/دولار (EUR/USD)' },
                { value: 'GBPUSD', text: 'جنيه/دولار (GBP/USD)' },
                { value: 'USDJPY', text: 'دولار/ين (USD/JPY)' },
                { value: 'AUDUSD', text: 'دولار استرالي/دولار (AUD/USD)' },
                { value: 'USDCAD', text: 'دولار/دولار كندي (USD/CAD)' }
            ];
            break;
        case 'commodities':
            options = [
                { value: 'XAUUSD', text: 'الذهب (XAU/USD)' },
                { value: 'XAGUSD', text: 'الفضة (XAG/USD)' },
                { value: 'USOIL', text: 'النفط الأمريكي (WTI)' },
                { value: 'UKOIL', text: 'النفط البريطاني (Brent)' }
            ];
            break;
        case 'indices':
            options = [
                { value: 'US30', text: 'داو جونز (US30)' },
                { value: 'SPX500', text: 'إس آند بي 500 (SPX500)' },
                { value: 'NASDAQ', text: 'ناسداك (NASDAQ)' },
                { value: 'GER30', text: 'داكس الألماني (GER30)' }
            ];
            break;
        case 'crypto':
            options = [
                { value: 'BTCUSD', text: 'بيتكوين/دولار (BTC/USD)' },
                { value: 'ETHUSD', text: 'إيثريوم/دولار (ETH/USD)' },
                { value: 'LTCUSD', text: 'لايتكوين/دولار (LTC/USD)' }
            ];
            break;
    }
    
    // Add options to select
    options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option.value;
        optionElement.textContent = option.text;
        symbolSelect.appendChild(optionElement);
    });
    
    // Set first option as selected
    if (options.length > 0) {
        marketData.currentSymbol = options[0].value;
        symbolSelect.value = marketData.currentSymbol;
    }
}

/**
 * Handle symbol change
 */
function handleSymbolChange(event) {
    marketData.currentSymbol = event.target.value;
    console.log(`Symbol changed to: ${marketData.currentSymbol}`);
    
    // Load market data for new symbol
    loadMarketData();
}

/**
 * Handle timeframe change
 */
function handleTimeframeChange(event) {
    marketData.currentTimeframe = event.target.value;
    console.log(`Timeframe changed to: ${marketData.currentTimeframe}`);
    
    // Load market data for new timeframe
    loadMarketData();
}

/**
 * Load market data from API
 */
function loadMarketData() {
    // Show loading state
    marketData.isLoading = true;
    marketData.hasError = false;
    updateMarketOverview();
    
    console.log(`Loading market data for ${marketData.currentSymbol} (${marketData.currentTimeframe})`);
    
    // Fetch data from API
    fetch(`/api/market-data/${marketData.currentSymbol}?interval=${marketData.currentTimeframe}&assetType=${marketData.currentMarketType}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`API error: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Update market data
            marketData.prices = data.prices || [];
            marketData.dates = data.dates || [];
            marketData.volumes = data.volumes || [];
            marketData.opens = data.opens || [];
            marketData.highs = data.highs || [];
            marketData.lows = data.lows || [];
            marketData.closes = data.closes || [];
            marketData.currentPrice = data.currentPrice;
            marketData.dailyChange = data.dailyChange;
            marketData.sentiment = data.sentiment;
            marketData.lastUpdated = new Date().toISOString();
            marketData.isLoading = false;
            
            console.log('Market data loaded successfully');
            
            // Update UI
            updateMarketOverview();
            
            // Update chart if available
            if (typeof updateChart === 'function') {
                updateChart();
            }
            
            // Generate trading signals
            generateTradingSignals();
        })
        .catch(error => {
            console.error('Error loading market data:', error);
            marketData.isLoading = false;
            marketData.hasError = true;
            marketData.errorMessage = error.message;
            
            // Update UI with error state
            updateMarketOverview();
        });
}

/**
 * Update market overview section
 */
function updateMarketOverview() {
    const currentSymbolElement = document.getElementById('currentSymbol');
    const currentPriceElement = document.getElementById('currentPrice');
    const dailyChangeElement = document.getElementById('dailyChange');
    const progressBar = document.querySelector('.progress-bar');
    
    if (currentSymbolElement) {
        currentSymbolElement.textContent = getSymbolDisplayName(marketData.currentSymbol);
    }
    
    if (currentPriceElement) {
        if (marketData.isLoading) {
            currentPriceElement.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
        } else if (marketData.hasError) {
            currentPriceElement.innerHTML = `<span class="text-danger"><i class="fas fa-exclamation-triangle"></i> Error: ${marketData.errorMessage}</span>`;
        } else {
            currentPriceElement.textContent = marketData.currentPrice ? marketData.currentPrice.toFixed(5) : 'N/A';
        }
    }
    
    if (dailyChangeElement) {
        if (marketData.dailyChange) {
            const isPositive = marketData.dailyChange >= 0;
            dailyChangeElement.textContent = `${isPositive ? '+' : ''}${marketData.dailyChange.toFixed(2)}%`;
            dailyChangeElement.className = `badge ${isPositive ? 'bg-success' : 'bg-danger'}`;
        } else {
            dailyChangeElement.textContent = 'N/A';
            dailyChangeElement.className = 'badge bg-secondary';
        }
    }
    
    if (progressBar) {
        if (marketData.sentiment) {
            progressBar.style.width = `${marketData.sentiment}%`;
            progressBar.setAttribute('aria-valuenow', marketData.sentiment);
            
            // Update color based on sentiment
            if (marketData.sentiment > 60) {
                progressBar.className = 'progress-bar bg-success';
                progressBar.textContent = `${marketData.sentiment}% صاعد`;
            } else if (marketData.sentiment < 40) {
                progressBar.className = 'progress-bar bg-danger';
                progressBar.textContent = `${marketData.sentiment}% هابط`;
            } else {
                progressBar.className = 'progress-bar bg-warning';
                progressBar.textContent = `${marketData.sentiment}% محايد`;
            }
        } else {
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', 0);
            progressBar.className = 'progress-bar bg-secondary';
            progressBar.textContent = 'N/A';
        }
    }
}

/**
 * Get display name for symbol
 */
function getSymbolDisplayName(symbol) {
    const symbolMap = {
        'EURUSD': 'يورو/دولار',
        'GBPUSD': 'جنيه/دولار',
        'USDJPY': 'دولار/ين',
        'AUDUSD': 'دولار استرالي/دولار',
        'USDCAD': 'دولار/دولار كندي',
        'XAUUSD': 'الذهب',
        'XAGUSD': 'الفضة',
        'USOIL': 'النفط الأمريكي',
        'UKOIL': 'النفط البريطاني',
        'US30': 'داو جونز',
        'SPX500': 'إس آند بي 500',
        'NASDAQ': 'ناسداك',
        'GER30': 'داكس الألماني',
        'BTCUSD': 'بيتكوين/دولار',
        'ETHUSD': 'إيثريوم/دولار',
        'LTCUSD': 'لايتكوين/دولار'
    };
    
    return symbolMap[symbol] || symbol;
}

/**
 * Analyze market and generate trading signals
 */
function analyzeMarket() {
    console.log('Analyzing market...');
    
    // Show loading state
    const signalsContainer = document.getElementById('signalsContainer');
    if (signalsContainer) {
        signalsContainer.innerHTML = '<div class="text-center"><span class="spinner-border" role="status" aria-hidden="true"></span><p>جاري تحليل السوق...</p></div>';
    }
    
    // Call API to analyze market
    fetch(`/analyze/${marketData.currentSymbol}/${marketData.currentTimeframe}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`API error: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Analysis complete:', data);
            
            // Update signals container
            updateTradingSignals(data);
            
            // Update technical indicators if available
            if (typeof updateTechnicalIndicators === 'function') {
                updateTechnicalIndicators(data);
            }
        })
        .catch(error => {
            console.error('Error analyzing market:', error);
            
            // Show error in signals container
            if (signalsContainer) {
                signalsContainer.innerHTML = `<div class="alert alert-danger"><h4>خطأ في التحليل</h4><p>${error.message}</p></div>`;
            }
        });
}

/**
 * Update trading signals display
 */
function updateTradingSignals(analysisData) {
    const signalsContainer = document.getElementById('signalsContainer');
    if (!signalsContainer) return;
    
    // Clear container
    signalsContainer.innerHTML = '';
    
    // Create signal elements based on analysis
    if (analysisData.sentiment === 'Bullish' || analysisData.confidence > 0.6) {
        // Buy signal
        const buySignal = document.createElement('div');
        buySignal.className = 'alert alert-success';
        buySignal.innerHTML = `
            <h4>إشارة شراء</h4>
            <p><strong>نقطة الدخول:</strong> ${analysisData.entry_price}</p>
            <p><strong>وقف الخسارة:</strong> ${analysisData.stop_loss}</p>
            <p><strong>جني الأرباح:</strong> ${analysisData.target_price}</p>
            <p><strong>التحليل:</strong> مؤشر القوة النسبية يظهر زخمًا صاعدًا. السعر فوق المتوسط المتحرك 50 و 200.</p>
            <p><strong>قوة الإشارة:</strong> <span class="badge bg-success">${Math.round(analysisData.confidence * 100)}%</span></p>
        `;
        signalsContainer.appendChild(buySignal);
    } else if (analysisData.sentiment === 'Bearish' || analysisData.confidence < 0.4) {
        // Sell signal
        const sellSignal = document.createElement('div');
        sellSignal.className = 'alert alert-danger';
        sellSignal.innerHTML = `
            <h4>إشارة بيع</h4>
            <p><strong>نقطة الدخول:</strong> ${analysisData.entry_price}</p>
            <p><strong>وقف الخسارة:</strong> ${analysisData.stop_loss}</p>
            <p><strong>جني الأرباح:</strong> ${analysisData.target_price}</p>
            <p><strong>التحليل:</strong> مؤشر MACD يظهر تقاطعًا هبوطيًا. السعر تحت المتوسط المتحرك 50.</p>
            <p><strong>قوة الإشارة:</strong> <span class="badge bg-danger">${Math.round((1 - analysisData.confidence) * 100)}%</span></p>
        `;
        signalsContainer.appendChild(sellSignal);
    } else {
        // Neutral signal
        const neutralSignal = document.createElement('div');
        neutralSignal.className = 'alert alert-warning';
        neutralSignal.innerHTML = `
            <h4>إشارة محايدة</h4>
            <p><strong>السعر الحالي:</strong> ${analysisData.entry_price}</p>
            <p><strong>التحليل:</strong> المؤشرات الفنية متضاربة. يفضل الانتظار حتى ظهور إشارة أوضح.</p>
            <p><strong>الاتجاه:</strong> <span class="badge bg-warning text-dark">${analysisData.sentiment}</span></p>
        `;
        signalsContainer.appendChild(neutralSignal);
    }
}

/**
 * Load economic calendar data
 */
function loadEconomicCalendar() {
    console.log('Loading economic calendar...');
    
    // Show loading state
    const calendarStatus = document.getElementById('calendarStatus');
    const calendarBody = document.getElementById('calendarBody');
    
    if (calendarStatus) {
        calendarStatus.innerHTML = '<div class="alert alert-info">جاري تحميل بيانات التقويم الاقتصادي...</div>';
    }
    
    // Fetch calendar data from API
    fetch('/api/economic-calendar')
        .then(response => {
            if (!response.ok) {
                throw new Error(`API error: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Economic calendar loaded:', data);
            
            // Clear loading state
            if (calendarStatus) {
                calendarStatus.innerHTML = '';
            }
            
            // Update calendar table
            if (calendarBody && data.events) {
                updateCalendarTable(data.events);
            }
        })
        .catch(error => {
            console.error('Error loading economic calendar:', error);
            
            // Show error in calendar status
            if (calendarStatus) {
                calendarStatus.innerHTML = `<div class="alert alert-danger">خطأ في تحميل التقويم الاقتصادي: ${error.message}</div>`;
            }
        });
}

/**
 * Update economic calendar table
 */
function updateCalendarTable(events) {
    const calendarBody = document.getElementById('calendarBody');
    if (!calendarBody) return;
    
    // Clear table
    calendarBody.innerHTML = '';
    
    // Add events to table
    events.forEach(event => {
        const row = document.createElement('tr');
        
        // Set row class based on importance
        if (event.importance === 'high') {
            row.className = 'table-danger';
        } else if (event.importance === 'medium') {
            row.className = 'table-warning';
        } else {
            row.className = 'table-light';
        }
        
        // Set data attribute for filtering
        row.setAttribute('data-importance', event.importance);
        
        // Create row content
        row.innerHTML = `
            <td>${event.time}</td>
            <td>${event.currency}</td>
            <td>${event.event}</td>
            <td><span class="badge ${getBadgeClass(event.importance)}">${getImportanceText(event.importance)}</span></td>
            <td>${event.forecast}</td>
        `;
        
        calendarBody.appendChild(row);
    });
    
    // Apply current filter
    filterCalendarEvents();
}

/**
 * Filter economic calendar events
 */
function filterCalendarEvents() {
    const calendarFilter = document.getElementById('calendarFilter');
    const calendarRows = document.querySelectorAll('#calendarBody tr');
    
    if (!calendarFilter || !calendarRows.length) return;
    
    const filterValue = calendarFilter.value;
    
    calendarRows.forEach(row => {
        const importance = row.getAttribute('data-importance');
        
        if (filterValue === 'all' || importance === filterValue) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

/**
 * Get badge class for importance level
 */
function getBadgeClass(importance) {
    switch (importance) {
        case 'high':
            return 'bg-danger';
        case 'medium':
            return 'bg-warning text-dark';
        case 'low':
            return 'bg-info text-dark';
        default:
            return 'bg-secondary';
    }
}

/**
 * Get importance text in Arabic
 */
function getImportanceText(importance) {
    switch (importance) {
        case 'high':
            return 'عالي';
        case 'medium':
            return 'متوسط';
        case 'low':
            return 'منخفض';
        default:
            return 'غير معروف';
    }
}

/**
 * Register service worker for PWA functionality
 */
function registerServiceWorker() {
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('/service-worker.js')
                .then(registration => {
                    console.log('Service Worker registered with scope:', registration.scope);
                })
                .catch(error => {
                    console.error('Service Worker registration failed:', error);
                });
        });
    }
}

/**
 * Generate trading signals based on technical indicators
 */
function generateTradingSignals() {
    // This function will be implemented to generate trading signals
    // based on technical indicators and market data
    console.log('Generating trading signals...');
    
    // If technical-analysis.js is loaded, use its generateTradingSignal function
    if (window.technicalAnalysis && typeof window.technicalAnalysis.generateTradingSignal === 'function') {
        const data = {
            symbol: marketData.currentSymbol,
            timeframe: marketData.currentTimeframe,
            price: marketData.currentPrice,
            opens: marketData.opens,
            highs: marketData.highs,
            lows: marketData.lows,
            closes: marketData.closes,
            volumes: marketData.volumes
        };
        
        const signal = window.technicalAnalysis.generateTradingSignal(data);
        console.log('Generated trading signal:', signal);
    }
}

// Export functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { initApp, marketData };
} else if (typeof window !== 'undefined') {
    window.initApp = initApp;
    window.marketData = marketData;
}
