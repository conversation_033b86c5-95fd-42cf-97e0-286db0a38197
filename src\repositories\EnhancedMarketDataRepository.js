/**
 * Enhanced Market Data Repository for MongoDB
 * 
 * This repository extends the base MarketDataRepository with:
 * - Time series data storage and retrieval
 * - Efficient data aggregation
 * - Advanced querying capabilities
 * - TTL indexes for automatic data expiration
 */

const MarketDataRepository = require('./MarketDataRepository');
const logger = require('../utils/logger');

class EnhancedMarketDataRepository extends MarketDataRepository {
  /**
   * Create a new EnhancedMarketDataRepository instance
   * @param {Object} db - MongoDB database instance
   */
  constructor(db) {
    super(db);
    this.setupIndexes();
    logger.debug('EnhancedMarketDataRepository initialized');
  }

  /**
   * Set up additional indexes for better performance
   */
  async setupIndexes() {
    try {
      // Create compound index for symbol, timeframe, and timestamp
      await this.collection.createIndex(
        { symbol: 1, timeframe: 1, timestamp: -1 },
        { name: 'idx_symbol_timeframe_timestamp' }
      );

      // Create TTL index for automatic data expiration
      // This will automatically delete documents older than the specified TTL
      await this.collection.createIndex(
        { createdAt: 1 },
        { 
          name: 'idx_ttl_created_at',
          expireAfterSeconds: 30 * 24 * 60 * 60 // 30 days
        }
      );

      // Create index for source field
      await this.collection.createIndex(
        { source: 1 },
        { name: 'idx_source' }
      );

      logger.info('EnhancedMarketDataRepository indexes created');
    } catch (error) {
      logger.error('Error creating EnhancedMarketDataRepository indexes:', error);
    }
  }

  /**
   * Find market data by date range
   * @param {string} symbol - Trading symbol (e.g., 'EURUSD')
   * @param {string} timeframe - Timeframe (e.g., 'M5', 'H1', 'D1')
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @param {Object} options - Query options (sort, limit, skip)
   * @returns {Promise<Array>} Array of market data points
   */
  async findByDateRange(symbol, timeframe, startDate, endDate, options = {}) {
    try {
      return await this.find(
        {
          symbol,
          timeframe,
          timestamp: {
            $gte: startDate instanceof Date ? startDate : new Date(startDate),
            $lte: endDate instanceof Date ? endDate : new Date(endDate)
          }
        },
        {
          ...options,
          sort: options.sort || { timestamp: 1 } // Chronological order by default
        }
      );
    } catch (error) {
      logger.error('Error finding market data by date range:', error);
      throw error;
    }
  }

  /**
   * Get aggregated OHLC data with specified interval
   * @param {string} symbol - Trading symbol (e.g., 'EURUSD')
   * @param {string} baseTimeframe - Base timeframe of stored data (e.g., 'M1', 'M5')
   * @param {string} aggregateTimeframe - Target timeframe for aggregation (e.g., 'H1', 'D1')
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Promise<Array>} Array of aggregated OHLC data
   */
  async getAggregatedOHLC(symbol, baseTimeframe, aggregateTimeframe, startDate, endDate) {
    try {
      // Convert timeframes to milliseconds for aggregation
      const timeframeMap = {
        'M1': 60 * 1000,
        'M5': 5 * 60 * 1000,
        'M15': 15 * 60 * 1000,
        'M30': 30 * 60 * 1000,
        'H1': 60 * 60 * 1000,
        'H4': 4 * 60 * 60 * 1000,
        'D1': 24 * 60 * 60 * 1000,
        'W1': 7 * 24 * 60 * 60 * 1000
      };

      // Validate timeframes
      if (!timeframeMap[baseTimeframe] || !timeframeMap[aggregateTimeframe]) {
        throw new Error('Invalid timeframe');
      }

      // Ensure target timeframe is larger than base timeframe
      if (timeframeMap[aggregateTimeframe] <= timeframeMap[baseTimeframe]) {
        // If requesting same or smaller timeframe, just return the data
        return await this.findByDateRange(symbol, baseTimeframe, startDate, endDate);
      }

      // Calculate aggregation window
      const aggregationWindow = timeframeMap[aggregateTimeframe];

      // Prepare date objects
      const start = startDate instanceof Date ? startDate : new Date(startDate);
      const end = endDate instanceof Date ? endDate : new Date(endDate);

      // Use MongoDB aggregation pipeline
      const pipeline = [
        // Match documents for the symbol, timeframe, and date range
        {
          $match: {
            symbol,
            timeframe: baseTimeframe,
            timestamp: { $gte: start, $lte: end }
          }
        },
        // Add a field for the aggregation window
        {
          $addFields: {
            windowStart: {
              $subtract: [
                { $toLong: "$timestamp" },
                { $mod: [{ $toLong: "$timestamp" }, aggregationWindow] }
              ]
            }
          }
        },
        // Group by the window start time
        {
          $group: {
            _id: "$windowStart",
            timestamp: { $first: { $toDate: "$windowStart" } },
            open: { $first: "$open" },
            high: { $max: "$high" },
            low: { $min: "$low" },
            close: { $last: "$close" },
            volume: { $sum: "$volume" },
            count: { $sum: 1 }
          }
        },
        // Sort by timestamp
        {
          $sort: { timestamp: 1 }
        },
        // Project the final fields
        {
          $project: {
            _id: 0,
            timestamp: 1,
            open: 1,
            high: 1,
            low: 1,
            close: 1,
            volume: 1,
            count: 1
          }
        }
      ];

      return await this.collection.aggregate(pipeline).toArray();
    } catch (error) {
      logger.error('Error getting aggregated OHLC data:', error);
      throw error;
    }
  }

  /**
   * Upsert market data (insert if not exists, update if exists)
   * @param {Object} marketData - Market data object
   * @returns {Promise<Object>} Upserted market data
   */
  async upsertMarketData(marketData) {
    try {
      // Validate required fields
      if (!marketData.symbol || !marketData.timeframe || !marketData.timestamp) {
        throw new Error('Symbol, timeframe, and timestamp are required');
      }

      // Ensure timestamp is a Date object
      if (!(marketData.timestamp instanceof Date)) {
        marketData.timestamp = new Date(marketData.timestamp);
      }

      // Add metadata
      const now = new Date();
      const dataWithMetadata = {
        ...marketData,
        updatedAt: now,
        createdAt: marketData.createdAt || now
      };

      // Upsert the document
      const result = await this.collection.updateOne(
        {
          symbol: marketData.symbol,
          timeframe: marketData.timeframe,
          timestamp: marketData.timestamp
        },
        { $set: dataWithMetadata },
        { upsert: true }
      );

      return {
        ...dataWithMetadata,
        _id: result.upsertedId || marketData._id
      };
    } catch (error) {
      logger.error('Error upserting market data:', error);
      throw error;
    }
  }

  /**
   * Bulk upsert market data
   * @param {Array} dataPoints - Array of market data points
   * @returns {Promise<Object>} BulkWrite result
   */
  async bulkUpsertMarketData(dataPoints) {
    try {
      if (!Array.isArray(dataPoints) || dataPoints.length === 0) {
        throw new Error('Data points must be a non-empty array');
      }

      // Prepare bulk operations
      const now = new Date();
      const operations = dataPoints.map(data => {
        // Validate required fields
        if (!data.symbol || !data.timeframe || !data.timestamp) {
          throw new Error('Symbol, timeframe, and timestamp are required for all data points');
        }

        // Ensure timestamp is a Date object
        const timestamp = data.timestamp instanceof Date ? data.timestamp : new Date(data.timestamp);

        return {
          updateOne: {
            filter: {
              symbol: data.symbol,
              timeframe: data.timeframe,
              timestamp: timestamp
            },
            update: {
              $set: {
                ...data,
                timestamp,
                updatedAt: now,
                createdAt: data.createdAt || now
              }
            },
            upsert: true
          }
        };
      });

      // Execute bulk write
      return await this.collection.bulkWrite(operations);
    } catch (error) {
      logger.error('Error bulk upserting market data:', error);
      throw error;
    }
  }

  /**
   * Get available symbols
   * @returns {Promise<Array>} Array of unique symbols
   */
  async getAvailableSymbols() {
    try {
      return await this.collection.distinct('symbol');
    } catch (error) {
      logger.error('Error getting available symbols:', error);
      throw error;
    }
  }

  /**
   * Get available timeframes for a symbol
   * @param {string} symbol - Trading symbol
   * @returns {Promise<Array>} Array of unique timeframes
   */
  async getAvailableTimeframes(symbol) {
    try {
      return await this.collection.distinct('timeframe', { symbol });
    } catch (error) {
      logger.error('Error getting available timeframes:', error);
      throw error;
    }
  }

  /**
   * Get data statistics for a symbol and timeframe
   * @param {string} symbol - Trading symbol
   * @param {string} timeframe - Timeframe
   * @returns {Promise<Object>} Statistics object
   */
  async getDataStatistics(symbol, timeframe) {
    try {
      const pipeline = [
        {
          $match: { symbol, timeframe }
        },
        {
          $group: {
            _id: null,
            count: { $sum: 1 },
            oldestTimestamp: { $min: '$timestamp' },
            newestTimestamp: { $max: '$timestamp' },
            avgVolume: { $avg: '$volume' },
            maxHigh: { $max: '$high' },
            minLow: { $min: '$low' }
          }
        }
      ];

      const result = await this.collection.aggregate(pipeline).toArray();
      return result.length > 0 ? result[0] : { count: 0 };
    } catch (error) {
      logger.error('Error getting data statistics:', error);
      throw error;
    }
  }
}

module.exports = EnhancedMarketDataRepository;
