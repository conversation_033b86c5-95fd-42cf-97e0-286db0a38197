import React, { useState, useEffect } from 'react';
import patternRecognitionService from '../../../services/patternRecognitionService';
import { getPatternInfo } from '../../../utils/candlestickPatterns';
import PatternAlertForm from './PatternAlertForm';

/**
 * PatternRecognition Component
 *
 * Displays detected patterns in market data and provides detailed information
 */
const PatternRecognition = ({ marketData, timeframe, symbol }) => {
  // State for detected patterns
  const [patterns, setPatterns] = useState({
    candlestick: [],
    chart: [],
    support: [],
    resistance: []
  });

  // State for selected pattern
  const [selectedPattern, setSelectedPattern] = useState(null);

  // State for filter
  const [filter, setFilter] = useState('all'); // 'all', 'bullish', 'bearish'

  // State for alert form
  const [showAlertForm, setShowAlertForm] = useState(false);

  // State for alert creation success
  const [alertCreated, setAlertCreated] = useState(false);

  // Detect patterns when market data changes
  useEffect(() => {
    if (!marketData || marketData.length === 0) return;

    // Detect candlestick patterns
    const candlestickPatterns = patternRecognitionService.detectCandlestickPatterns(marketData);

    // Detect chart patterns
    const chartPatterns = patternRecognitionService.detectChartPatterns(marketData);

    // Detect support and resistance levels
    const { support, resistance } = patternRecognitionService.detectSupportResistance(marketData);

    setPatterns({
      candlestick: candlestickPatterns,
      chart: chartPatterns,
      support,
      resistance
    });
  }, [marketData]);

  // Filter patterns based on selected filter
  const filteredPatterns = {
    candlestick: filter === 'all'
      ? patterns.candlestick
      : patterns.candlestick.filter(p => p.type === filter),
    chart: filter === 'all'
      ? patterns.chart
      : patterns.chart.filter(p => p.type === filter)
  };

  // Handle pattern selection
  const handlePatternSelect = (pattern) => {
    setSelectedPattern(pattern);
  };

  // Close pattern details
  const closePatternDetails = () => {
    setSelectedPattern(null);
  };

  // Get pattern information
  const getPatternDetails = (pattern) => {
    return getPatternInfo(pattern.pattern) || {
      name: pattern.pattern.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      description: pattern.description || 'No description available.',
      interpretation: 'No interpretation available.',
      reliability: 'Unknown'
    };
  };

  // Render pattern badge with appropriate color
  const renderPatternBadge = (pattern) => {
    const colorClass = pattern.type === 'bullish'
      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      : pattern.type === 'bearish'
        ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
        : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';

    return (
      <span className={`px-2 py-1 rounded text-xs font-medium ${colorClass}`}>
        {pattern.pattern.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </span>
    );
  };

  // Render significance stars
  const renderSignificance = (significance) => {
    const stars = [];
    const maxStars = 10;

    for (let i = 0; i < maxStars; i++) {
      stars.push(
        <span
          key={i}
          className={`text-sm ${i < significance ? 'text-yellow-500' : 'text-gray-300 dark:text-gray-600'}`}
        >
          ★
        </span>
      );
    }

    return (
      <div className="flex">
        {stars}
        <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
          ({significance}/10)
        </span>
      </div>
    );
  };

  return (
    <div className="pattern-recognition bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
      <h2 className="text-lg font-semibold mb-4">Pattern Recognition</h2>

      {/* Filter and Alert Controls */}
      <div className="mb-4 flex justify-between items-center">
        <div className="flex space-x-2">
          <button
            className={`px-3 py-1 rounded-md ${
              filter === 'all'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => setFilter('all')}
          >
            All
          </button>
          <button
            className={`px-3 py-1 rounded-md ${
              filter === 'bullish'
                ? 'bg-green-600 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => setFilter('bullish')}
          >
            Bullish
          </button>
          <button
            className={`px-3 py-1 rounded-md ${
              filter === 'bearish'
                ? 'bg-red-600 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => setFilter('bearish')}
          >
            Bearish
          </button>
        </div>

        <button
          className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow flex items-center"
          onClick={() => setShowAlertForm(true)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
          </svg>
          Create Alert
        </button>
      </div>

      {/* Alert Created Success Message */}
      {alertCreated && (
        <div className="mb-4 p-3 bg-green-100 text-green-800 rounded-md flex justify-between items-center">
          <span>Pattern alert created successfully!</span>
          <button
            onClick={() => setAlertCreated(false)}
            className="text-green-800 hover:text-green-900"
          >
            &times;
          </button>
        </div>
      )}

      {/* No Patterns Message */}
      {filteredPatterns.candlestick.length === 0 && filteredPatterns.chart.length === 0 && (
        <div className="text-center py-4 text-gray-500 dark:text-gray-400">
          No patterns detected. Try adjusting the timeframe or symbol.
        </div>
      )}

      {/* Candlestick Patterns */}
      {filteredPatterns.candlestick.length > 0 && (
        <div className="mb-4">
          <h3 className="text-md font-medium mb-2">Candlestick Patterns</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {filteredPatterns.candlestick.map((pattern, index) => (
              <div
                key={`${pattern.pattern}-${index}`}
                className="border border-gray-200 dark:border-gray-700 rounded-md p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750"
                onClick={() => handlePatternSelect(pattern)}
              >
                <div className="flex justify-between items-start">
                  <div>
                    {renderPatternBadge(pattern)}
                    <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                      {new Date(pattern.candle.x).toLocaleString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      {pattern.candle.c.toFixed(5)}
                    </p>
                    {renderSignificance(pattern.significance)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Chart Patterns */}
      {filteredPatterns.chart.length > 0 && (
        <div className="mb-4">
          <h3 className="text-md font-medium mb-2">Chart Patterns</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {filteredPatterns.chart.map((pattern, index) => (
              <div
                key={`${pattern.pattern}-${index}`}
                className="border border-gray-200 dark:border-gray-700 rounded-md p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-750"
                onClick={() => handlePatternSelect(pattern)}
              >
                <div className="flex justify-between items-start">
                  <div>
                    {renderPatternBadge(pattern)}
                    <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                      {pattern.startIndex && pattern.endIndex
                        ? `${new Date(marketData[pattern.startIndex].x).toLocaleDateString()} - ${new Date(marketData[pattern.endIndex].x).toLocaleDateString()}`
                        : 'Date range not available'}
                    </p>
                  </div>
                  <div className="text-right">
                    {renderSignificance(pattern.significance)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Support and Resistance Levels */}
      {(patterns.support.length > 0 || patterns.resistance.length > 0) && (
        <div>
          <h3 className="text-md font-medium mb-2">Support & Resistance Levels</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Support Levels */}
            <div>
              <h4 className="text-sm font-medium text-green-600 dark:text-green-400 mb-1">Support Levels</h4>
              {patterns.support.length === 0 ? (
                <p className="text-sm text-gray-500 dark:text-gray-400">No support levels detected</p>
              ) : (
                <ul className="space-y-1">
                  {patterns.support.map((level, index) => (
                    <li key={`support-${index}`} className="flex justify-between text-sm">
                      <span>{level.price.toFixed(5)}</span>
                      <span className="text-gray-500 dark:text-gray-400">
                        Strength: {level.strength}/10
                      </span>
                    </li>
                  ))}
                </ul>
              )}
            </div>

            {/* Resistance Levels */}
            <div>
              <h4 className="text-sm font-medium text-red-600 dark:text-red-400 mb-1">Resistance Levels</h4>
              {patterns.resistance.length === 0 ? (
                <p className="text-sm text-gray-500 dark:text-gray-400">No resistance levels detected</p>
              ) : (
                <ul className="space-y-1">
                  {patterns.resistance.map((level, index) => (
                    <li key={`resistance-${index}`} className="flex justify-between text-sm">
                      <span>{level.price.toFixed(5)}</span>
                      <span className="text-gray-500 dark:text-gray-400">
                        Strength: {level.strength}/10
                      </span>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Pattern Details Modal */}
      {selectedPattern && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className={`p-4 text-white ${
              selectedPattern.type === 'bullish'
                ? 'bg-green-600'
                : selectedPattern.type === 'bearish'
                  ? 'bg-red-600'
                  : 'bg-blue-600'
            }`}>
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-bold">
                  {getPatternDetails(selectedPattern).name}
                </h3>
                <button
                  onClick={closePatternDetails}
                  className="text-white hover:text-gray-200"
                >
                  &times;
                </button>
              </div>
            </div>

            <div className="p-4">
              <div className="mb-4">
                <h4 className="text-sm text-gray-500 dark:text-gray-400 mb-1">Type</h4>
                <p className="font-medium">
                  {selectedPattern.type.charAt(0).toUpperCase() + selectedPattern.type.slice(1)}
                </p>
              </div>

              <div className="mb-4">
                <h4 className="text-sm text-gray-500 dark:text-gray-400 mb-1">Significance</h4>
                {renderSignificance(selectedPattern.significance)}
              </div>

              <div className="mb-4">
                <h4 className="text-sm text-gray-500 dark:text-gray-400 mb-1">Description</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  {getPatternDetails(selectedPattern).description}
                </p>
              </div>

              <div className="mb-4">
                <h4 className="text-sm text-gray-500 dark:text-gray-400 mb-1">Interpretation</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  {getPatternDetails(selectedPattern).interpretation}
                </p>
              </div>

              <div className="mb-4">
                <h4 className="text-sm text-gray-500 dark:text-gray-400 mb-1">Reliability</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  {getPatternDetails(selectedPattern).reliability}
                </p>
              </div>

              <div className="flex justify-between">
                <button
                  onClick={() => {
                    closePatternDetails();
                    setShowAlertForm(true);
                  }}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                  </svg>
                  Create Alert
                </button>

                <button
                  onClick={closePatternDetails}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Pattern Alert Form Modal */}
      {showAlertForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-lg w-full max-h-[90vh] overflow-y-auto">
            <PatternAlertForm
              symbol={symbol}
              timeframe={timeframe}
              onAlertCreated={(alert) => {
                setShowAlertForm(false);
                setAlertCreated(true);

                // Hide success message after 5 seconds
                setTimeout(() => {
                  setAlertCreated(false);
                }, 5000);
              }}
              onCancel={() => setShowAlertForm(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default PatternRecognition;
