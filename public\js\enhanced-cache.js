/**
 * Enhanced Cache System for Trading Signals App
 * 
 * Features:
 * - Configurable TTL (Time-To-Live) based on data type
 * - Persistent caching with localStorage
 * - Memory-based LRU cache for performance
 * - Cache statistics and monitoring
 * - Automatic cache invalidation
 */

class EnhancedCache {
    /**
     * Create a new EnhancedCache instance
     * @param {Object} options - Cache options
     * @param {number} options.defaultTTL - Default TTL in milliseconds (default: 5 minutes)
     * @param {number} options.maxSize - Maximum number of items in memory cache (default: 100)
     * @param {boolean} options.useLocalStorage - Whether to use localStorage for persistence (default: true)
     * @param {string} options.storagePrefix - Prefix for localStorage keys (default: 'tsapp_cache_')
     * @param {Object} options.ttlByType - TTL configuration by data type
     */
    constructor(options = {}) {
        this.options = {
            defaultTTL: 5 * 60 * 1000, // 5 minutes
            maxSize: 100,
            useLocalStorage: true,
            storagePrefix: 'tsapp_cache_',
            ttlByType: {
                // Default TTL values by data type
                marketData: 1 * 60 * 1000,     // 1 minute for market data
                historicalData: 60 * 60 * 1000, // 1 hour for historical data
                economicCalendar: 30 * 60 * 1000, // 30 minutes for economic calendar
                technicalIndicators: 5 * 60 * 1000, // 5 minutes for technical indicators
                news: 15 * 60 * 1000,          // 15 minutes for news
                metadata: 24 * 60 * 60 * 1000  // 24 hours for metadata
            },
            ...options
        };

        // In-memory cache
        this.cache = new Map();
        
        // Cache statistics
        this.stats = {
            hits: 0,
            misses: 0,
            writes: 0,
            localStorage: {
                hits: 0,
                misses: 0,
                writes: 0
            }
        };

        // Initialize
        this._init();
    }

    /**
     * Initialize the cache
     * @private
     */
    _init() {
        // Load persistent cache from localStorage if enabled
        if (this.options.useLocalStorage) {
            try {
                this._loadFromLocalStorage();
            } catch (error) {
                console.error('Error loading cache from localStorage:', error);
                // Clear potentially corrupted cache
                this._clearLocalStorage();
            }
        }

        // Set up automatic cache cleanup interval
        this._setupCleanupInterval();
    }

    /**
     * Load cache from localStorage
     * @private
     */
    _loadFromLocalStorage() {
        if (!window.localStorage) return;

        // Get all cache keys from localStorage
        const cacheKeys = Object.keys(localStorage).filter(key => 
            key.startsWith(this.options.storagePrefix)
        );

        // Load each cache item
        for (const key of cacheKeys) {
            try {
                const rawData = localStorage.getItem(key);
                if (!rawData) continue;

                const data = JSON.parse(rawData);
                
                // Check if the item is expired
                if (data.expiry && data.expiry < Date.now()) {
                    // Remove expired item
                    localStorage.removeItem(key);
                    continue;
                }

                // Add to in-memory cache (without localStorage persistence to avoid circular saving)
                const cacheKey = key.substring(this.options.storagePrefix.length);
                this.cache.set(cacheKey, data);
            } catch (error) {
                console.warn(`Error parsing cache item ${key}:`, error);
                // Remove corrupted item
                localStorage.removeItem(key);
            }
        }

        console.log(`Loaded ${this.cache.size} items from localStorage cache`);
    }

    /**
     * Set up automatic cache cleanup interval
     * @private
     */
    _setupCleanupInterval() {
        // Clean up expired items every 5 minutes
        setInterval(() => {
            this._cleanupExpiredItems();
        }, 5 * 60 * 1000);
    }

    /**
     * Clean up expired items from cache
     * @private
     */
    _cleanupExpiredItems() {
        const now = Date.now();
        let expiredCount = 0;

        // Clean up in-memory cache
        for (const [key, value] of this.cache.entries()) {
            if (value.expiry && value.expiry < now) {
                this.cache.delete(key);
                expiredCount++;

                // Also remove from localStorage if enabled
                if (this.options.useLocalStorage && window.localStorage) {
                    localStorage.removeItem(`${this.options.storagePrefix}${key}`);
                }
            }
        }

        if (expiredCount > 0) {
            console.log(`Cleaned up ${expiredCount} expired cache items`);
        }
    }

    /**
     * Clear localStorage cache
     * @private
     */
    _clearLocalStorage() {
        if (!window.localStorage) return;

        // Get all cache keys from localStorage
        const cacheKeys = Object.keys(localStorage).filter(key => 
            key.startsWith(this.options.storagePrefix)
        );

        // Remove each cache item
        for (const key of cacheKeys) {
            localStorage.removeItem(key);
        }

        console.log(`Cleared ${cacheKeys.length} items from localStorage cache`);
    }

    /**
     * Get TTL for a specific data type
     * @param {string} type - Data type
     * @returns {number} - TTL in milliseconds
     * @private
     */
    _getTTL(type) {
        return this.options.ttlByType[type] || this.options.defaultTTL;
    }

    /**
     * Get item from cache
     * @param {string} key - Cache key
     * @param {string} type - Data type (for TTL calculation)
     * @returns {*} - Cached value or undefined if not found or expired
     */
    get(key, type = 'default') {
        // Try to get from in-memory cache first
        if (this.cache.has(key)) {
            const item = this.cache.get(key);
            
            // Check if the item is expired
            if (item.expiry && item.expiry < Date.now()) {
                // Remove expired item
                this.cache.delete(key);
                
                // Also remove from localStorage if enabled
                if (this.options.useLocalStorage && window.localStorage) {
                    localStorage.removeItem(`${this.options.storagePrefix}${key}`);
                }
                
                this.stats.misses++;
                return undefined;
            }
            
            // Update access time for LRU
            item.lastAccessed = Date.now();
            this.cache.set(key, item);
            
            this.stats.hits++;
            return item.data;
        }
        
        // If not in memory cache, try localStorage if enabled
        if (this.options.useLocalStorage && window.localStorage) {
            const storageKey = `${this.options.storagePrefix}${key}`;
            const rawData = localStorage.getItem(storageKey);
            
            if (rawData) {
                try {
                    const item = JSON.parse(rawData);
                    
                    // Check if the item is expired
                    if (item.expiry && item.expiry < Date.now()) {
                        // Remove expired item
                        localStorage.removeItem(storageKey);
                        this.stats.localStorage.misses++;
                        this.stats.misses++;
                        return undefined;
                    }
                    
                    // Add to in-memory cache
                    this.cache.set(key, item);
                    
                    this.stats.localStorage.hits++;
                    this.stats.hits++;
                    return item.data;
                } catch (error) {
                    console.warn(`Error parsing cache item ${key}:`, error);
                    localStorage.removeItem(storageKey);
                }
            }
        }
        
        this.stats.misses++;
        return undefined;
    }

    /**
     * Set item in cache
     * @param {string} key - Cache key
     * @param {*} data - Data to cache
     * @param {string} type - Data type (for TTL calculation)
     * @param {number} ttl - Custom TTL in milliseconds (overrides type-based TTL)
     */
    set(key, data, type = 'default', ttl = null) {
        // Calculate expiry time
        const ttlValue = ttl || this._getTTL(type);
        const expiry = Date.now() + ttlValue;
        
        // Create cache item
        const item = {
            data,
            expiry,
            type,
            lastAccessed: Date.now(),
            created: Date.now()
        };
        
        // Add to in-memory cache
        this.cache.set(key, item);
        
        // Add to localStorage if enabled
        if (this.options.useLocalStorage && window.localStorage) {
            try {
                const storageKey = `${this.options.storagePrefix}${key}`;
                localStorage.setItem(storageKey, JSON.stringify(item));
                this.stats.localStorage.writes++;
            } catch (error) {
                console.warn('Error saving to localStorage:', error);
                // If localStorage is full, clear it and try again
                if (error.name === 'QuotaExceededError') {
                    this._clearLocalStorage();
                    try {
                        const storageKey = `${this.options.storagePrefix}${key}`;
                        localStorage.setItem(storageKey, JSON.stringify(item));
                        this.stats.localStorage.writes++;
                    } catch (retryError) {
                        console.error('Failed to save to localStorage after clearing:', retryError);
                    }
                }
            }
        }
        
        // Enforce maximum cache size
        this._enforceCacheSize();
        
        this.stats.writes++;
    }

    /**
     * Enforce maximum cache size using LRU policy
     * @private
     */
    _enforceCacheSize() {
        if (this.cache.size <= this.options.maxSize) return;
        
        // Convert to array for sorting
        const entries = Array.from(this.cache.entries());
        
        // Sort by last accessed time (oldest first)
        entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
        
        // Remove oldest entries until we're under the limit
        const entriesToRemove = entries.slice(0, this.cache.size - this.options.maxSize);
        
        for (const [key] of entriesToRemove) {
            this.cache.delete(key);
            
            // Also remove from localStorage if enabled
            if (this.options.useLocalStorage && window.localStorage) {
                localStorage.removeItem(`${this.options.storagePrefix}${key}`);
            }
        }
        
        console.log(`Removed ${entriesToRemove.length} items from cache due to size limit`);
    }

    /**
     * Delete item from cache
     * @param {string} key - Cache key
     */
    delete(key) {
        // Remove from in-memory cache
        this.cache.delete(key);
        
        // Remove from localStorage if enabled
        if (this.options.useLocalStorage && window.localStorage) {
            localStorage.removeItem(`${this.options.storagePrefix}${key}`);
        }
    }

    /**
     * Clear all cache
     */
    clear() {
        // Clear in-memory cache
        this.cache.clear();
        
        // Clear localStorage if enabled
        if (this.options.useLocalStorage) {
            this._clearLocalStorage();
        }
        
        // Reset statistics
        this.resetStats();
        
        console.log('Cache cleared');
    }

    /**
     * Reset cache statistics
     */
    resetStats() {
        this.stats = {
            hits: 0,
            misses: 0,
            writes: 0,
            localStorage: {
                hits: 0,
                misses: 0,
                writes: 0
            }
        };
    }

    /**
     * Get cache statistics
     * @returns {Object} - Cache statistics
     */
    getStats() {
        const hitRatio = this.stats.hits + this.stats.misses > 0 
            ? this.stats.hits / (this.stats.hits + this.stats.misses) 
            : 0;
            
        return {
            ...this.stats,
            size: this.cache.size,
            hitRatio: hitRatio.toFixed(2),
            timestamp: new Date().toISOString()
        };
    }
}

// Create global instance
window.enhancedCache = new EnhancedCache();

console.log('Enhanced cache system initialized');
