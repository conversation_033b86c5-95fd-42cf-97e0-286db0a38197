/**
 * Validation Middleware for API Requests
 * 
 * This middleware validates request data against schemas.
 */

const validationService = require('../services/validationService');
const logger = require('../utils/logger');

/**
 * Validate request body against schema
 * @param {Object} schema - Joi schema
 * @returns {Function} Express middleware
 */
function validateBody(schema) {
  return (req, res, next) => {
    try {
      const result = validationService.validate(req.body, schema);
      
      if (!result.valid) {
        logger.warn('Request body validation failed', { 
          path: req.path, 
          errors: result.errors 
        });
        
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: result.errors
        });
      }
      
      // Replace request body with validated data
      req.body = result.value;
      next();
    } catch (error) {
      logger.error('Error in validation middleware', error);
      return res.status(500).json({
        status: 'error',
        message: 'Internal server error during validation'
      });
    }
  };
}

/**
 * Validate request query against schema
 * @param {Object} schema - Joi schema
 * @returns {Function} Express middleware
 */
function validateQuery(schema) {
  return (req, res, next) => {
    try {
      const result = validationService.validate(req.query, schema);
      
      if (!result.valid) {
        logger.warn('Request query validation failed', { 
          path: req.path, 
          errors: result.errors 
        });
        
        return res.status(400).json({
          status: 'error',
          message: 'Query validation failed',
          errors: result.errors
        });
      }
      
      // Replace request query with validated data
      req.query = result.value;
      next();
    } catch (error) {
      logger.error('Error in query validation middleware', error);
      return res.status(500).json({
        status: 'error',
        message: 'Internal server error during validation'
      });
    }
  };
}

/**
 * Validate request params against schema
 * @param {Object} schema - Joi schema
 * @returns {Function} Express middleware
 */
function validateParams(schema) {
  return (req, res, next) => {
    try {
      const result = validationService.validate(req.params, schema);
      
      if (!result.valid) {
        logger.warn('Request params validation failed', { 
          path: req.path, 
          errors: result.errors 
        });
        
        return res.status(400).json({
          status: 'error',
          message: 'Path parameters validation failed',
          errors: result.errors
        });
      }
      
      // Replace request params with validated data
      req.params = result.value;
      next();
    } catch (error) {
      logger.error('Error in params validation middleware', error);
      return res.status(500).json({
        status: 'error',
        message: 'Internal server error during validation'
      });
    }
  };
}

module.exports = {
  validateBody,
  validateQuery,
  validateParams
};
