/**
 * Common Types and Utilities
 * 
 * Shared type definitions used across the trading signals application.
 * These types ensure consistency between frontend and backend components.
 * 
 * @version 1.0.0
 */

// ============================================================================
// ENUMS AND CONSTANTS
// ============================================================================

export enum SignalType {
  BUY = 'BUY',
  SELL = 'SELL',
  HOLD = 'HOLD'
}

export enum SignalSource {
  AI = 'AI',
  TRADITIONAL = 'TRADITIONAL',
  UNIFIED = 'UNIFIED',
  MANUAL = 'MANUAL'
}

export enum SignalStatus {
  ACTIVE = 'ACTIVE',
  CLOSED = 'CLOSED',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED'
}

export enum Timeframe {
  M1 = 'M1',
  M5 = 'M5',
  M15 = 'M15',
  M30 = 'M30',
  H1 = 'H1',
  H4 = 'H4',
  D1 = 'D1',
  W1 = 'W1',
  MN = 'MN'
}

export enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  PREMIUM = 'premium'
}

export enum MarketType {
  STOCKS = 'stocks',
  FOREX = 'forex',
  CRYPTO = 'crypto',
  COMMODITIES = 'commodities',
  ECONOMICS = 'economics'
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type Timestamp = string; // ISO 8601 format
export type ObjectId = string;
export type Percentage = number; // 0-100
export type Price = number;
export type Volume = number;

// ============================================================================
// VALIDATION TYPES
// ============================================================================

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// ============================================================================
// METADATA TYPES
// ============================================================================

export interface ResponseMetadata {
  timestamp: Timestamp;
  requestId: string;
  version: string;
  processingTime?: number;
  source?: string;
  pagination?: PaginationMetadata;
}

export interface PaginationMetadata {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface SignalMetadata {
  model?: string;
  processingTime?: number;
  tokens?: number;
  version: string;
  tags?: string[];
  confidence?: Percentage;
  riskLevel?: 'LOW' | 'MEDIUM' | 'HIGH';
}

export interface UserMetadata {
  lastLogin?: Timestamp;
  loginCount?: number;
  preferences?: UserPreferences;
  subscription?: SubscriptionInfo;
}

// ============================================================================
// PREFERENCE TYPES
// ============================================================================

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  notifications: NotificationPreferences;
  trading: TradingPreferences;
  display: DisplayPreferences;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  signalAlerts: boolean;
  marketUpdates: boolean;
  systemNotifications: boolean;
}

export interface TradingPreferences {
  defaultTimeframe: Timeframe;
  riskTolerance: 'LOW' | 'MEDIUM' | 'HIGH';
  preferredMarkets: MarketType[];
  autoTrading: boolean;
  maxPositionSize: number;
}

export interface DisplayPreferences {
  chartType: 'candlestick' | 'line' | 'bar';
  indicators: string[];
  layout: 'compact' | 'standard' | 'expanded';
  currency: string;
}

// ============================================================================
// SUBSCRIPTION TYPES
// ============================================================================

export interface SubscriptionInfo {
  plan: 'free' | 'basic' | 'premium' | 'enterprise';
  status: 'active' | 'inactive' | 'cancelled' | 'expired';
  startDate: Timestamp;
  endDate?: Timestamp;
  features: string[];
  limits: SubscriptionLimits;
}

export interface SubscriptionLimits {
  signalsPerDay: number;
  aiAnalysisPerDay: number;
  historicalDataMonths: number;
  realTimeUpdates: boolean;
  advancedIndicators: boolean;
}

// ============================================================================
// PERFORMANCE TYPES
// ============================================================================

export interface PerformanceMetrics {
  accuracy: Percentage;
  profitLoss: number;
  winRate: Percentage;
  averageReturn: Percentage;
  maxDrawdown: Percentage;
  sharpeRatio?: number;
  totalTrades: number;
  successfulTrades: number;
}

export interface SignalPerformance {
  actualEntry?: Price;
  actualExit?: Price;
  pnl?: number;
  pnlPercentage?: Percentage;
  accuracy?: Percentage;
  duration?: number; // in milliseconds
  status: 'open' | 'closed' | 'expired';
}

// ============================================================================
// ERROR TYPES
// ============================================================================

export interface ErrorInfo {
  code: string;
  message: string;
  details?: any;
  timestamp: Timestamp;
  requestId?: string;
  stack?: string;
}

export interface APIError extends ErrorInfo {
  statusCode: number;
  isOperational: boolean;
  suggestion?: string;
}

// ============================================================================
// WEBSOCKET TYPES
// ============================================================================

export interface WebSocketMessage<T = any> {
  type: string;
  data: T;
  timestamp: Timestamp;
  id?: string;
}

export interface WebSocketConnectionState {
  connected: boolean;
  connecting: boolean;
  error?: string;
  lastConnected?: Timestamp;
  reconnectAttempts: number;
}

// ============================================================================
// TYPE GUARDS
// ============================================================================

export function isSignalType(value: any): value is SignalType {
  return Object.values(SignalType).includes(value);
}

export function isSignalSource(value: any): value is SignalSource {
  return Object.values(SignalSource).includes(value);
}

export function isTimeframe(value: any): value is Timeframe {
  return Object.values(Timeframe).includes(value);
}

export function isValidPrice(value: any): value is Price {
  return typeof value === 'number' && value > 0 && isFinite(value);
}

export function isValidPercentage(value: any): value is Percentage {
  return typeof value === 'number' && value >= 0 && value <= 100;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export function generateRequestId(): string {
  return `req-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
}

export function formatTimestamp(date: Date = new Date()): Timestamp {
  return date.toISOString();
}

export function parseTimestamp(timestamp: Timestamp): Date {
  return new Date(timestamp);
}

export function calculatePercentageChange(oldValue: number, newValue: number): number {
  if (oldValue === 0) return 0;
  return ((newValue - oldValue) / oldValue) * 100;
}
