/**
 * Pattern Recognition Service
 *
 * Provides functions to detect various candlestick patterns and chart formations
 * in market data for technical analysis.
 */

import logger from '../utils/logger.js';
import { areLevelsSimilar, isSeparated, findPeaks, findTroughs, calculateTrendLine } from './patternUtils.js';

/**
 * Detect candlestick patterns in the provided OHLC data
 *
 * @param {Array} data - Array of OHLC data objects
 * @param {Object} options - Configuration options for pattern detection
 * @returns {Array} - Array of detected patterns with their locations
 */
export const detectCandlestickPatterns = (data, options = {}) => {
  if (!data || !Array.isArray(data) || data.length < 5) {
    logger.warn('Insufficient data for pattern recognition');
    return [];
  }

  const patterns = [];

  // Process each candle for potential patterns
  for (let i = 4; i < data.length; i++) {
    // We need at least 5 candles for most patterns (current + 4 previous)
    const candles = data.slice(i - 4, i + 1);

    // Check for various patterns
    const detectedPatterns = [
      checkDoji(candles),
      checkHammer(candles),
      checkEngulfing(candles),
      checkMorningStar(candles),
      checkEveningStar(candles),
      checkThreeWhiteSoldiers(candles),
      checkThreeBlackCrows(candles),
      checkPiercingLine(candles),
      checkDarkCloudCover(candles),
      checkHarami(candles)
    ].filter(Boolean); // Remove null/undefined values

    // Add detected patterns to the result
    patterns.push(...detectedPatterns.map(pattern => ({
      ...pattern,
      index: i,
      candle: data[i]
    })));
  }

  return patterns;
};

/**
 * Detect chart patterns in the provided OHLC data
 *
 * @param {Array} data - Array of OHLC data objects
 * @param {Object} options - Configuration options for pattern detection
 * @returns {Array} - Array of detected patterns with their locations
 */
export const detectChartPatterns = (data, options = {}) => {
  if (!data || !Array.isArray(data) || data.length < 20) {
    logger.warn('Insufficient data for chart pattern recognition');
    return [];
  }

  const patterns = [];

  // Check for various chart patterns
  const headAndShoulders = checkHeadAndShoulders(data);
  if (headAndShoulders) patterns.push(headAndShoulders);

  const doubleTop = checkDoubleTop(data);
  if (doubleTop) patterns.push(doubleTop);

  const doubleBottom = checkDoubleBottom(data);
  if (doubleBottom) patterns.push(doubleBottom);

  const triangles = checkTriangles(data);
  patterns.push(...triangles);

  const channels = checkChannels(data);
  patterns.push(...channels);

  const flags = checkFlags(data);
  patterns.push(...flags);

  const pennants = checkPennants(data);
  patterns.push(...pennants);

  const cupAndHandle = checkCupAndHandle(data);
  if (cupAndHandle) patterns.push(cupAndHandle);

  return patterns;
};

/**
 * Detect support and resistance levels in the provided OHLC data
 *
 * @param {Array} data - Array of OHLC data objects
 * @param {Object} options - Configuration options
 * @returns {Object} - Object containing support and resistance levels
 */
export const detectSupportResistance = (data, options = {}) => {
  if (!data || !Array.isArray(data) || data.length < 10) {
    logger.warn('Insufficient data for support/resistance detection');
    return { support: [], resistance: [] };
  }

  const { sensitivity = 3, lookback = 50 } = options;

  // Use only the most recent data for analysis
  const recentData = data.slice(-Math.min(lookback, data.length));

  // Find local minima (support) and maxima (resistance)
  const support = [];
  const resistance = [];

  // Skip the first and last few candles
  for (let i = sensitivity; i < recentData.length - sensitivity; i++) {
    const current = recentData[i];

    // Check for local minimum (support)
    let isSupport = true;
    for (let j = i - sensitivity; j < i; j++) {
      if (recentData[j].low <= current.low) {
        isSupport = false;
        break;
      }
    }

    for (let j = i + 1; j <= i + sensitivity; j++) {
      if (recentData[j].low <= current.low) {
        isSupport = false;
        break;
      }
    }

    if (isSupport) {
      support.push({
        price: current.low,
        index: data.length - recentData.length + i,
        strength: calculateLevelStrength(recentData, i, 'support')
      });
    }

    // Check for local maximum (resistance)
    let isResistance = true;
    for (let j = i - sensitivity; j < i; j++) {
      if (recentData[j].high >= current.high) {
        isResistance = false;
        break;
      }
    }

    for (let j = i + 1; j <= i + sensitivity; j++) {
      if (recentData[j].high >= current.high) {
        isResistance = false;
        break;
      }
    }

    if (isResistance) {
      resistance.push({
        price: current.high,
        index: data.length - recentData.length + i,
        strength: calculateLevelStrength(recentData, i, 'resistance')
      });
    }
  }

  // Cluster similar levels
  const clusteredSupport = clusterLevels(support, options);
  const clusteredResistance = clusterLevels(resistance, options);

  return {
    support: clusteredSupport,
    resistance: clusteredResistance
  };
};

/**
 * Calculate the strength of a support/resistance level
 *
 * @param {Array} data - OHLC data
 * @param {number} index - Index of the level
 * @param {string} type - 'support' or 'resistance'
 * @returns {number} - Strength value (0-10)
 */
const calculateLevelStrength = (data, index, type) => {
  // Base strength
  let strength = 5;

  // Factors that increase strength:
  // 1. Volume at the level
  const volume = data[index].volume || 0;
  const avgVolume = data.reduce((sum, candle) => sum + (candle.volume || 0), 0) / data.length;
  if (volume > avgVolume * 1.5) strength += 1;
  if (volume > avgVolume * 2) strength += 1;

  // 2. Multiple touches of the level
  const price = type === 'support' ? data[index].low : data[index].high;
  const priceTolerance = price * 0.001; // 0.1% tolerance

  let touches = 0;
  for (const candle of data) {
    const testPrice = type === 'support' ? candle.low : candle.high;
    if (Math.abs(testPrice - price) <= priceTolerance) {
      touches++;
    }
  }

  if (touches >= 3) strength += 1;
  if (touches >= 5) strength += 1;

  // 3. Recent level (more recent = stronger)
  const recency = (index / data.length);
  strength += recency * 2;

  // Ensure strength is between 1-10
  return Math.max(1, Math.min(10, Math.round(strength)));
};

/**
 * Cluster similar price levels to avoid duplicates
 *
 * @param {Array} levels - Array of price levels
 * @param {Object} options - Configuration options
 * @returns {Array} - Clustered levels
 */
const clusterLevels = (levels, options = {}) => {
  if (levels.length <= 1) return levels;

  const { clusterTolerance = 0.002 } = options; // 0.2% default tolerance

  // Sort levels by price
  const sortedLevels = [...levels].sort((a, b) => a.price - b.price);

  const clusters = [];
  let currentCluster = [sortedLevels[0]];

  for (let i = 1; i < sortedLevels.length; i++) {
    const currentLevel = sortedLevels[i];
    const prevLevel = currentCluster[currentCluster.length - 1];

    // Check if current level is within tolerance of previous level
    if ((currentLevel.price - prevLevel.price) / prevLevel.price <= clusterTolerance) {
      // Add to current cluster
      currentCluster.push(currentLevel);
    } else {
      // Create a new cluster
      clusters.push(averageCluster(currentCluster));
      currentCluster = [currentLevel];
    }
  }

  // Add the last cluster
  if (currentCluster.length > 0) {
    clusters.push(averageCluster(currentCluster));
  }

  return clusters;
};

/**
 * Calculate the average values for a cluster of levels
 *
 * @param {Array} cluster - Array of levels in a cluster
 * @returns {Object} - Averaged level
 */
const averageCluster = (cluster) => {
  if (cluster.length === 1) return cluster[0];

  const avgPrice = cluster.reduce((sum, level) => sum + level.price, 0) / cluster.length;
  const avgIndex = Math.round(cluster.reduce((sum, level) => sum + level.index, 0) / cluster.length);
  const maxStrength = Math.max(...cluster.map(level => level.strength));

  return {
    price: avgPrice,
    index: avgIndex,
    strength: maxStrength,
    clusterSize: cluster.length
  };
};

// ===== Candlestick Pattern Detection Functions =====

/**
 * Check for Doji pattern
 *
 * @param {Array} candles - Array of candle data
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkDoji = (candles) => {
  const current = candles[candles.length - 1];

  // A doji has open and close prices that are very close
  const bodySize = Math.abs(current.close - current.open);
  const totalRange = current.high - current.low;

  // Body should be very small compared to the total range
  if (totalRange > 0 && bodySize / totalRange < 0.1) {
    return {
      pattern: 'doji',
      type: 'reversal',
      significance: 6,
      description: 'A Doji represents indecision in the market. The open and close prices are very close, indicating a balance between buyers and sellers.'
    };
  }

  return null;
};

/**
 * Check for Hammer pattern
 *
 * @param {Array} candles - Array of candle data
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkHammer = (candles) => {
  const current = candles[candles.length - 1];
  const prev = candles[candles.length - 2];

  // Check for downtrend
  const isDowntrend = prev.close < prev.open && prev.close < candles[candles.length - 3].close;

  if (!isDowntrend) return null;

  const bodySize = Math.abs(current.close - current.open);
  const totalRange = current.high - current.low;

  // Body should be in the upper third of the candle
  const upperShadow = current.high - Math.max(current.open, current.close);
  const lowerShadow = Math.min(current.open, current.close) - current.low;

  // Hammer has a small upper shadow, small body, and long lower shadow
  if (upperShadow < bodySize * 0.5 && lowerShadow > bodySize * 2 && bodySize / totalRange < 0.3) {
    return {
      pattern: 'hammer',
      type: 'bullish',
      significance: 7,
      description: 'A Hammer is a bullish reversal pattern that forms during a downtrend. It has a small body at the upper end of the trading range with a long lower shadow.'
    };
  }

  return null;
};

/**
 * Check for Engulfing pattern
 *
 * @param {Array} candles - Array of candle data
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkEngulfing = (candles) => {
  const current = candles[candles.length - 1];
  const prev = candles[candles.length - 2];

  // Bullish engulfing
  if (prev.close < prev.open && // Previous candle is bearish
      current.close > current.open && // Current candle is bullish
      current.open < prev.close && // Current open is below previous close
      current.close > prev.open) { // Current close is above previous open

    return {
      pattern: 'bullish_engulfing',
      type: 'bullish',
      significance: 8,
      description: 'A Bullish Engulfing pattern forms when a small bearish candle is followed by a large bullish candle that completely engulfs the previous candle.'
    };
  }

  // Bearish engulfing
  if (prev.close > prev.open && // Previous candle is bullish
      current.close < current.open && // Current candle is bearish
      current.open > prev.close && // Current open is above previous close
      current.close < prev.open) { // Current close is below previous open

    return {
      pattern: 'bearish_engulfing',
      type: 'bearish',
      significance: 8,
      description: 'A Bearish Engulfing pattern forms when a small bullish candle is followed by a large bearish candle that completely engulfs the previous candle.'
    };
  }

  return null;
};

/**
 * Check for Morning Star pattern
 *
 * @param {Array} candles - Array of candle data
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkMorningStar = (candles) => {
  const first = candles[candles.length - 3]; // First day
  const second = candles[candles.length - 2]; // Second day
  const third = candles[candles.length - 1]; // Third day

  // First day: bearish candle
  const isFirstBearish = first.close < first.open;

  // Second day: small body with gap down
  const isSecondSmall = Math.abs(second.close - second.open) < Math.abs(first.close - first.open) * 0.5;
  const hasGapDown = Math.max(second.open, second.close) < first.close;

  // Third day: bullish candle that closes above the midpoint of the first day
  const isThirdBullish = third.close > third.open;
  const closesAboveMidpoint = third.close > (first.open + first.close) / 2;

  if (isFirstBearish && isSecondSmall && hasGapDown && isThirdBullish && closesAboveMidpoint) {
    return {
      pattern: 'morning_star',
      type: 'bullish',
      significance: 9,
      description: 'A Morning Star is a bullish reversal pattern consisting of three candles: a large bearish candle, a small-bodied candle, and a large bullish candle.'
    };
  }

  return null;
};

/**
 * Check for Evening Star pattern
 *
 * @param {Array} candles - Array of candle data
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkEveningStar = (candles) => {
  const first = candles[candles.length - 3]; // First day
  const second = candles[candles.length - 2]; // Second day
  const third = candles[candles.length - 1]; // Third day

  // First day: bullish candle
  const isFirstBullish = first.close > first.open;

  // Second day: small body with gap up
  const isSecondSmall = Math.abs(second.close - second.open) < Math.abs(first.close - first.open) * 0.5;
  const hasGapUp = Math.min(second.open, second.close) > first.close;

  // Third day: bearish candle that closes below the midpoint of the first day
  const isThirdBearish = third.close < third.open;
  const closesBelowMidpoint = third.close < (first.open + first.close) / 2;

  if (isFirstBullish && isSecondSmall && hasGapUp && isThirdBearish && closesBelowMidpoint) {
    return {
      pattern: 'evening_star',
      type: 'bearish',
      significance: 9,
      description: 'An Evening Star is a bearish reversal pattern consisting of three candles: a large bullish candle, a small-bodied candle, and a large bearish candle.'
    };
  }

  return null;
};

/**
 * Check for Three White Soldiers pattern
 *
 * @param {Array} candles - Array of candle data
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkThreeWhiteSoldiers = (candles) => {
  const first = candles[candles.length - 3];
  const second = candles[candles.length - 2];
  const third = candles[candles.length - 1];

  // All three candles should be bullish
  const allBullish = first.close > first.open && second.close > second.open && third.close > third.open;

  // Each candle should open within the previous candle's body
  const properOpens = second.open > first.open && second.open < first.close &&
                      third.open > second.open && third.open < second.close;

  // Each candle should close higher than the previous
  const higherCloses = second.close > first.close && third.close > second.close;

  // Small upper shadows
  const smallUpperShadows = (first.high - first.close) < (first.close - first.open) * 0.3 &&
                            (second.high - second.close) < (second.close - second.open) * 0.3 &&
                            (third.high - third.close) < (third.close - third.open) * 0.3;

  if (allBullish && properOpens && higherCloses && smallUpperShadows) {
    return {
      pattern: 'three_white_soldiers',
      type: 'bullish',
      significance: 9,
      description: 'Three White Soldiers is a bullish reversal pattern consisting of three consecutive bullish candles, each closing higher than the previous and opening within the previous candle\'s body.'
    };
  }

  return null;
};

/**
 * Check for Three Black Crows pattern
 *
 * @param {Array} candles - Array of candle data
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkThreeBlackCrows = (candles) => {
  const first = candles[candles.length - 3];
  const second = candles[candles.length - 2];
  const third = candles[candles.length - 1];

  // All three candles should be bearish
  const allBearish = first.close < first.open && second.close < second.open && third.close < third.open;

  // Each candle should open within the previous candle's body
  const properOpens = second.open < first.open && second.open > first.close &&
                      third.open < second.open && third.open > second.close;

  // Each candle should close lower than the previous
  const lowerCloses = second.close < first.close && third.close < second.close;

  // Small lower shadows
  const smallLowerShadows = (first.close - first.low) < (first.open - first.close) * 0.3 &&
                            (second.close - second.low) < (second.open - second.close) * 0.3 &&
                            (third.close - third.low) < (third.open - third.close) * 0.3;

  if (allBearish && properOpens && lowerCloses && smallLowerShadows) {
    return {
      pattern: 'three_black_crows',
      type: 'bearish',
      significance: 9,
      description: 'Three Black Crows is a bearish reversal pattern consisting of three consecutive bearish candles, each closing lower than the previous and opening within the previous candle\'s body.'
    };
  }

  return null;
};

/**
 * Check for Piercing Line pattern
 *
 * @param {Array} candles - Array of candle data
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkPiercingLine = (candles) => {
  const first = candles[candles.length - 2];
  const second = candles[candles.length - 1];

  // First candle is bearish
  const isFirstBearish = first.close < first.open;

  // Second candle is bullish
  const isSecondBullish = second.close > second.open;

  // Second candle opens below first candle's low
  const opensBelow = second.open < first.low;

  // Second candle closes above the midpoint of the first candle
  const closesAboveMidpoint = second.close > (first.open + first.close) / 2;

  // Second candle closes below the first candle's open
  const closesBelowOpen = second.close < first.open;

  if (isFirstBearish && isSecondBullish && opensBelow && closesAboveMidpoint && closesBelowOpen) {
    return {
      pattern: 'piercing_line',
      type: 'bullish',
      significance: 7,
      description: 'A Piercing Line is a bullish reversal pattern where a bearish candle is followed by a bullish candle that opens below the previous low and closes above the midpoint of the previous candle.'
    };
  }

  return null;
};

/**
 * Check for Dark Cloud Cover pattern
 *
 * @param {Array} candles - Array of candle data
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkDarkCloudCover = (candles) => {
  const first = candles[candles.length - 2];
  const second = candles[candles.length - 1];

  // First candle is bullish
  const isFirstBullish = first.close > first.open;

  // Second candle is bearish
  const isSecondBearish = second.close < second.open;

  // Second candle opens above first candle's high
  const opensAbove = second.open > first.high;

  // Second candle closes below the midpoint of the first candle
  const closesBelowMidpoint = second.close < (first.open + first.close) / 2;

  // Second candle closes above the first candle's open
  const closesAboveOpen = second.close > first.open;

  if (isFirstBullish && isSecondBearish && opensAbove && closesBelowMidpoint && closesAboveOpen) {
    return {
      pattern: 'dark_cloud_cover',
      type: 'bearish',
      significance: 7,
      description: 'A Dark Cloud Cover is a bearish reversal pattern where a bullish candle is followed by a bearish candle that opens above the previous high and closes below the midpoint of the previous candle.'
    };
  }

  return null;
};

/**
 * Check for Harami pattern
 *
 * @param {Array} candles - Array of candle data
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkHarami = (candles) => {
  const first = candles[candles.length - 2];
  const second = candles[candles.length - 1];

  const firstBodySize = Math.abs(first.close - first.open);
  const secondBodySize = Math.abs(second.close - second.open);

  // Second candle's body should be smaller than the first
  const isSecondSmaller = secondBodySize < firstBodySize * 0.6;

  // Second candle's body should be contained within the first candle's body
  const isContained = Math.max(second.open, second.close) < Math.max(first.open, first.close) &&
                      Math.min(second.open, second.close) > Math.min(first.open, first.close);

  if (isSecondSmaller && isContained) {
    // Bullish Harami
    if (first.close < first.open && second.close > second.open) {
      return {
        pattern: 'bullish_harami',
        type: 'bullish',
        significance: 6,
        description: 'A Bullish Harami is a reversal pattern where a large bearish candle is followed by a smaller bullish candle that is completely contained within the body of the previous candle.'
      };
    }

    // Bearish Harami
    if (first.close > first.open && second.close < second.open) {
      return {
        pattern: 'bearish_harami',
        type: 'bearish',
        significance: 6,
        description: 'A Bearish Harami is a reversal pattern where a large bullish candle is followed by a smaller bearish candle that is completely contained within the body of the previous candle.'
      };
    }
  }

  return null;
};

// ===== Chart Pattern Detection Functions =====

/**
 * Find local peaks in price data
 *
 * @param {Array} data - OHLC data
 * @param {string} type - 'high' or 'low'
 * @param {number} sensitivity - Number of candles to check on each side
 * @returns {Array} - Array of peak indices
 */
const findPeaks = (data, type = 'high', sensitivity = 3) => {
  const peaks = [];
  const priceKey = type === 'high' ? 'high' : 'low';

  // Skip the first and last few candles based on sensitivity
  for (let i = sensitivity; i < data.length - sensitivity; i++) {
    const current = data[i][priceKey];

    let isPeak = true;

    // Check if this is a peak by comparing with surrounding candles
    for (let j = i - sensitivity; j < i; j++) {
      if ((type === 'high' && data[j][priceKey] >= current) ||
          (type === 'low' && data[j][priceKey] <= current)) {
        isPeak = false;
        break;
      }
    }

    if (isPeak) {
      for (let j = i + 1; j <= i + sensitivity; j++) {
        if ((type === 'high' && data[j][priceKey] >= current) ||
            (type === 'low' && data[j][priceKey] <= current)) {
          isPeak = false;
          break;
        }
      }
    }

    if (isPeak) {
      peaks.push(i);
    }
  }

  return peaks;
};

/**
 * Check for Head and Shoulders pattern
 *
 * @param {Array} data - OHLC data
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkHeadAndShoulders = (data) => {
  // Need at least 30 candles for a reliable pattern
  if (data.length < 30) return null;

  // Find peaks (potential shoulders and head)
  const peaks = findPeaks(data, 'high', 5);

  // Need at least 3 peaks for H&S
  if (peaks.length < 3) return null;

  // Check each possible combination of 3 consecutive peaks
  for (let i = 0; i < peaks.length - 2; i++) {
    const leftShoulder = peaks[i];
    const head = peaks[i + 1];
    const rightShoulder = peaks[i + 2];

    // Check if the head is higher than both shoulders
    if (data[head].high > data[leftShoulder].high &&
        data[head].high > data[rightShoulder].high) {

      // Check if shoulders are at roughly the same level (within 5%)
      const shoulderDiff = Math.abs(data[leftShoulder].high - data[rightShoulder].high);
      const shoulderAvg = (data[leftShoulder].high + data[rightShoulder].high) / 2;

      if (shoulderDiff / shoulderAvg < 0.05) {
        // Check for neckline (support line connecting the troughs between peaks)
        const leftTrough = findLowestBetween(data, leftShoulder, head);
        const rightTrough = findLowestBetween(data, head, rightShoulder);

        // Calculate neckline
        const necklineSlope = (data[rightTrough].low - data[leftTrough].low) / (rightTrough - leftTrough);
        const necklineIntercept = data[leftTrough].low - necklineSlope * leftTrough;

        // Check if the pattern is complete (price has broken below neckline)
        const patternComplete = rightShoulder < data.length - 1 &&
                               data[rightShoulder + 1].close < (necklineSlope * (rightShoulder + 1) + necklineIntercept);

        return {
          pattern: 'head_and_shoulders',
          type: 'bearish',
          significance: 9,
          description: 'Head and Shoulders pattern detected',
          points: {
            leftShoulder,
            head,
            rightShoulder,
            leftTrough,
            rightTrough
          },
          neckline: {
            slope: necklineSlope,
            intercept: necklineIntercept
          },
          complete: patternComplete
        };
      }
    }
  }

  return null;
};

/**
 * Find the lowest point between two indices
 *
 * @param {Array} data - OHLC data
 * @param {number} start - Start index
 * @param {number} end - End index
 * @returns {number} - Index of the lowest point
 */
const findLowestBetween = (data, start, end) => {
  let lowestIdx = start;
  let lowestVal = data[start].low;

  for (let i = start + 1; i < end; i++) {
    if (data[i].low < lowestVal) {
      lowestVal = data[i].low;
      lowestIdx = i;
    }
  }

  return lowestIdx;
};

/**
 * Find the highest point between two indices
 *
 * @param {Array} data - OHLC data
 * @param {number} start - Start index
 * @param {number} end - End index
 * @returns {number} - Index of the highest point
 */
const findHighestBetween = (data, start, end) => {
  let highestIdx = start;
  let highestVal = data[start].high;

  for (let i = start + 1; i < end; i++) {
    if (data[i].high > highestVal) {
      highestVal = data[i].high;
      highestIdx = i;
    }
  }

  return highestIdx;
};

/**
 * Check for Double Top pattern
 *
 * @param {Array} data - OHLC data
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkDoubleTop = (data) => {
  // Need at least 20 candles for a reliable pattern
  if (data.length < 20) return null;

  // Find peaks
  const peaks = findPeaks(data, 'high', 4);

  // Need at least 2 peaks for Double Top
  if (peaks.length < 2) return null;

  // Check each possible pair of peaks
  for (let i = 0; i < peaks.length - 1; i++) {
    const firstPeak = peaks[i];
    const secondPeak = peaks[i + 1];

    // Peaks should be separated by at least 5 candles
    if (secondPeak - firstPeak < 5) continue;

    // Peaks should be at roughly the same level (within 3%)
    const peakDiff = Math.abs(data[firstPeak].high - data[secondPeak].high);
    const peakAvg = (data[firstPeak].high + data[secondPeak].high) / 2;

    if (peakDiff / peakAvg < 0.03) {
      // Find the trough between the peaks
      const trough = findLowestBetween(data, firstPeak, secondPeak);

      // Trough should be significantly lower than the peaks (at least 3%)
      const troughDepth = (peakAvg - data[trough].low) / peakAvg;

      if (troughDepth > 0.03) {
        // Check if the pattern is complete (price has broken below the trough)
        const patternComplete = secondPeak < data.length - 1 &&
                               data[secondPeak + 1].close < data[trough].low;

        return {
          pattern: 'double_top',
          type: 'bearish',
          significance: 8,
          description: 'Double Top pattern detected',
          points: {
            firstPeak,
            secondPeak,
            trough
          },
          complete: patternComplete
        };
      }
    }
  }

  return null;
};

/**
 * Check for Double Bottom pattern
 *
 * @param {Array} data - OHLC data
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkDoubleBottom = (data) => {
  // Need at least 20 candles for a reliable pattern
  if (data.length < 20) return null;

  // Find troughs
  const troughs = findPeaks(data, 'low', 4);

  // Need at least 2 troughs for Double Bottom
  if (troughs.length < 2) return null;

  // Check each possible pair of troughs
  for (let i = 0; i < troughs.length - 1; i++) {
    const firstTrough = troughs[i];
    const secondTrough = troughs[i + 1];

    // Troughs should be separated by at least 5 candles
    if (secondTrough - firstTrough < 5) continue;

    // Troughs should be at roughly the same level (within 3%)
    const troughDiff = Math.abs(data[firstTrough].low - data[secondTrough].low);
    const troughAvg = (data[firstTrough].low + data[secondTrough].low) / 2;

    if (troughDiff / troughAvg < 0.03) {
      // Find the peak between the troughs
      const peak = findHighestBetween(data, firstTrough, secondTrough);

      // Peak should be significantly higher than the troughs (at least 3%)
      const peakHeight = (data[peak].high - troughAvg) / troughAvg;

      if (peakHeight > 0.03) {
        // Check if the pattern is complete (price has broken above the peak)
        const patternComplete = secondTrough < data.length - 1 &&
                               data[secondTrough + 1].close > data[peak].high;

        return {
          pattern: 'double_bottom',
          type: 'bullish',
          significance: 8,
          description: 'Double Bottom pattern detected',
          points: {
            firstTrough,
            secondTrough,
            peak
          },
          complete: patternComplete
        };
      }
    }
  }

  return null;
};

/**
 * Check for Triangle patterns (Ascending, Descending, Symmetrical)
 *
 * @param {Array} data - OHLC data
 * @returns {Array} - Array of detected triangle patterns
 */
const checkTriangles = (data) => {
  // Need at least 20 candles for a reliable pattern
  if (data.length < 20) return [];

  const patterns = [];

  // Find peaks and troughs
  const peaks = findPeaks(data, 'high', 3);
  const troughs = findPeaks(data, 'low', 3);

  // Need at least 2 peaks and 2 troughs for triangles
  if (peaks.length < 2 || troughs.length < 2) return [];

  // Check for ascending triangle (flat top, rising bottom)
  const ascendingTriangle = checkAscendingTriangle(data, peaks, troughs);
  if (ascendingTriangle) patterns.push(ascendingTriangle);

  // Check for descending triangle (flat bottom, falling top)
  const descendingTriangle = checkDescendingTriangle(data, peaks, troughs);
  if (descendingTriangle) patterns.push(descendingTriangle);

  // Check for symmetrical triangle (converging trend lines)
  const symmetricalTriangle = checkSymmetricalTriangle(data, peaks, troughs);
  if (symmetricalTriangle) patterns.push(symmetricalTriangle);

  return patterns;
};

/**
 * Check for Ascending Triangle pattern
 *
 * @param {Array} data - OHLC data
 * @param {Array} peaks - Array of peak indices
 * @param {Array} troughs - Array of trough indices
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkAscendingTriangle = (data, peaks, troughs) => {
  // Need at least 2 peaks and 2 troughs
  if (peaks.length < 2 || troughs.length < 2) return null;

  // Find potential resistance line (flat top)
  for (let i = 0; i < peaks.length - 1; i++) {
    const firstPeak = peaks[i];
    const secondPeak = peaks[i + 1];

    // Peaks should be separated by at least 5 candles
    if (secondPeak - firstPeak < 5) continue;

    // Peaks should be at roughly the same level (within 2%)
    const peakDiff = Math.abs(data[firstPeak].high - data[secondPeak].high);
    const peakAvg = (data[firstPeak].high + data[secondPeak].high) / 2;

    if (peakDiff / peakAvg < 0.02) {
      // Find troughs between and after these peaks
    const relevantTroughs = troughs.filter(t => t > firstPeak && t < secondPeak + 10);

    if (relevantTroughs.length >= 2) {
        // Check if troughs are forming an ascending line
      const firstTrough = relevantTroughs[0];
      const lastTrough = relevantTroughs[relevantTroughs.length - 1];

      if (data[lastTrough].low > data[firstTrough].low) {
          // Calculate support line (connecting troughs)
          const supportSlope = (data[lastTrough].low - data[firstTrough].low) / (lastTrough - firstTrough);
          const supportIntercept = data[firstTrough].low - supportSlope * firstTrough;

          // Calculate resistance line (flat)
          const resistanceLevel = peakAvg;

          // Check if the pattern is valid (support slope is positive)
        if (supportSlope > 0) {
            // Check if the pattern is complete (price has broken above resistance)
          const lastIndex = Math.max(secondPeak, lastTrough);
            const patternComplete = lastIndex < data.length - 1 &&
                                   data[lastIndex + 1].close > resistanceLevel;

          return {
            pattern: 'ascending_triangle',
            type: 'bullish',
            significance: 7,
            description: 'Ascending Triangle pattern detected',
              points: {
                firstPeak,
                secondPeak,
                firstTrough,
                lastTrough
              },
              support: {
                slope: supportSlope,
                intercept: supportIntercept
              },
            resistance: resistanceLevel,
            complete: patternComplete
          };
        }
      }
    }
  }
  }

  return null;
};

/**
 * Check for Descending Triangle pattern
 *
 * @param {Array} data - OHLC data
 * @param {Array} peaks - Array of peak indices
 * @param {Array} troughs - Array of trough indices
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkDescendingTriangle = (data, peaks, troughs) => {
  // Need at least 2 peaks and 2 troughs
  if (peaks.length < 2 || troughs.length < 2) return null;

  // Find potential support line (flat bottom)
  for (let i = 0; i < troughs.length - 1; i++) {
    const firstTrough = troughs[i];
    const secondTrough = troughs[i + 1];

    // Troughs should be separated by at least 5 candles
    if (secondTrough - firstTrough < 5) continue;

    // Troughs should be at roughly the same level (within 2%)
    const troughDiff = Math.abs(data[firstTrough].low - data[secondTrough].low);
    const troughAvg = (data[firstTrough].low + data[secondTrough].low) / 2;

    if (troughDiff / troughAvg < 0.02) {
      // Find peaks between and after these troughs
    const relevantPeaks = peaks.filter(p => p > firstTrough && p < secondTrough + 10);

    if (relevantPeaks.length >= 2) {
        // Check if peaks are forming a descending line
      const firstPeak = relevantPeaks[0];
      const lastPeak = relevantPeaks[relevantPeaks.length - 1];

      if (data[lastPeak].high < data[firstPeak].high) {
          // Calculate resistance line (connecting peaks)
          const resistanceSlope = (data[lastPeak].high - data[firstPeak].high) / (lastPeak - firstPeak);
          const resistanceIntercept = data[firstPeak].high - resistanceSlope * firstPeak;

          // Calculate support line (flat)
          const supportLevel = troughAvg;

          // Check if the pattern is valid (resistance slope is negative)
        if (resistanceSlope < 0) {
            // Check if the pattern is complete (price has broken below support)
          const lastIndex = Math.max(secondTrough, lastPeak);
            const patternComplete = lastIndex < data.length - 1 &&
                                   data[lastIndex + 1].close < supportLevel;

          return {
            pattern: 'descending_triangle',
            type: 'bearish',
            significance: 7,
            description: 'Descending Triangle pattern detected',
              points: {
                firstTrough,
                secondTrough,
                firstPeak,
                lastPeak
              },
            support: supportLevel,
              resistance: {
                slope: resistanceSlope,
                intercept: resistanceIntercept
              },
            complete: patternComplete
          };
        }
      }
    }
  }
  }

  return null;
};

/**
 * Check for Symmetrical Triangle pattern
 *
 * @param {Array} data - OHLC data
 * @param {Array} peaks - Array of peak indices
 * @param {Array} troughs - Array of trough indices
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkSymmetricalTriangle = (data, peaks, troughs) => {
  // Need at least 2 peaks and 2 troughs
  if (peaks.length < 2 || troughs.length < 2) return null;

  // Check for converging trend lines
  for (let i = 0; i < peaks.length - 1; i++) {
    const firstPeak = peaks[i];
    const secondPeak = peaks[i + 1];

    // Peaks should be separated by at least 5 candles
    if (secondPeak - firstPeak < 5) continue;

    // Second peak should be lower than first peak
    if (data[secondPeak].high >= data[firstPeak].high) continue;

    // Find troughs that could form the lower trend line
    const relevantTroughs = troughs.filter(t => t > firstPeak - 5 && t < secondPeak + 5);

    if (relevantTroughs.length >= 2) {
      const firstTrough = relevantTroughs[0];
      const lastTrough = relevantTroughs[relevantTroughs.length - 1];

      // Second trough should be higher than first trough
      if (data[lastTrough].low <= data[firstTrough].low) continue;

      // Calculate upper trend line (connecting peaks)
      const upperSlope = (data[secondPeak].high - data[firstPeak].high) / (secondPeak - firstPeak);
      const upperIntercept = data[firstPeak].high - upperSlope * firstPeak;

      // Calculate lower trend line (connecting troughs)
      const lowerSlope = (data[lastTrough].low - data[firstTrough].low) / (lastTrough - firstTrough);
      const lowerIntercept = data[firstTrough].low - lowerSlope * firstTrough;

      // Check if the pattern is valid (upper slope is negative, lower slope is positive)
      if (upperSlope < 0 && lowerSlope > 0) {
        // Calculate the convergence point
        const convergenceX = (lowerIntercept - upperIntercept) / (upperSlope - lowerSlope);

        // Check if convergence point is within a reasonable range
        if (convergenceX > secondPeak && convergenceX < secondPeak + 20) {
          // Determine if the pattern is bullish or bearish based on the preceding trend
          const precedingTrend = determineTrend(data, firstPeak - 10, firstPeak);

          // Check if the pattern is complete (price has broken out of the triangle)
          const lastIndex = Math.max(secondPeak, lastTrough);
          const upperLineAtLastIndex = upperSlope * lastIndex + upperIntercept;
          const lowerLineAtLastIndex = lowerSlope * lastIndex + lowerIntercept;

          const breakoutUp = lastIndex < data.length - 1 && data[lastIndex + 1].close > upperLineAtLastIndex;
          const breakoutDown = lastIndex < data.length - 1 && data[lastIndex + 1].close < lowerLineAtLastIndex;

          return {
            pattern: 'symmetrical_triangle',
            type: precedingTrend === 'uptrend' ? 'bullish' : 'bearish',
            significance: 6,
            description: 'Symmetrical Triangle pattern detected',
            points: {
              firstPeak,
              secondPeak,
              firstTrough,
              lastTrough
            },
            upper: {
              slope: upperSlope,
              intercept: upperIntercept
            },
            lower: {
              slope: lowerSlope,
              intercept: lowerIntercept
            },
            convergencePoint: convergenceX,
            complete: breakoutUp || breakoutDown,
            breakoutDirection: breakoutUp ? 'up' : breakoutDown ? 'down' : null
          };
        }
      }
    }
  }

  return null;
};

/**
 * Determine the trend direction in a given range of data
 *
 * @param {Array} data - OHLC data
 * @param {number} start - Start index
 * @param {number} end - End index
 * @returns {string} - 'uptrend', 'downtrend', or 'sideways'
 */
const determineTrend = (data, start, end) => {
  if (start < 0) start = 0;
  if (end >= data.length) end = data.length - 1;

  // Simple linear regression to determine trend
  let sumX = 0;
  let sumY = 0;
  let sumXY = 0;
  let sumX2 = 0;
  let n = end - start + 1;

  for (let i = start; i <= end; i++) {
    const x = i - start;
    const y = data[i].close;

    sumX += x;
    sumY += y;
    sumXY += x * y;
    sumX2 += x * x;
  }

  const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);

  if (slope > 0.001) return 'uptrend';
  if (slope < -0.001) return 'downtrend';
  return 'sideways';
};

/**
 * Check for Channel patterns (Ascending, Descending, Horizontal)
 *
 * @param {Array} data - OHLC data
 * @returns {Array} - Array of detected channel patterns
 */
const checkChannels = (data) => {
  // Need at least 20 candles for a reliable pattern
  if (data.length < 20) return [];

  const patterns = [];

  // Find peaks and troughs
  const peaks = findPeaks(data, 'high', 3);
  const troughs = findPeaks(data, 'low', 3);

  // Need at least 2 peaks and 2 troughs for channels
  if (peaks.length < 2 || troughs.length < 2) return [];

  // Check for ascending channel
  const ascendingChannel = checkAscendingChannel(data, peaks, troughs);
  if (ascendingChannel) patterns.push(ascendingChannel);

  // Check for descending channel
  const descendingChannel = checkDescendingChannel(data, peaks, troughs);
  if (descendingChannel) patterns.push(descendingChannel);

  // Check for horizontal channel
  const horizontalChannel = checkHorizontalChannel(data, peaks, troughs);
  if (horizontalChannel) patterns.push(horizontalChannel);

  return patterns;
};

/**
 * Check for Ascending Channel pattern
 *
 * @param {Array} data - OHLC data
 * @param {Array} peaks - Array of peak indices
 * @param {Array} troughs - Array of trough indices
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkAscendingChannel = (data, peaks, troughs) => {
  // Need at least 2 peaks and 2 troughs
  if (peaks.length < 2 || troughs.length < 2) return null;

  // Check for parallel ascending lines
  for (let i = 0; i < peaks.length - 1; i++) {
    const firstPeak = peaks[i];
    const secondPeak = peaks[i + 1];

    // Peaks should be separated by at least 5 candles
    if (secondPeak - firstPeak < 5) continue;

    // Second peak should be higher than first peak
    if (data[secondPeak].high <= data[firstPeak].high) continue;

    // Calculate upper trend line (connecting peaks)
    const upperSlope = (data[secondPeak].high - data[firstPeak].high) / (secondPeak - firstPeak);
    const upperIntercept = data[firstPeak].high - upperSlope * firstPeak;

    // Find troughs that could form the lower trend line
    const relevantTroughs = troughs.filter(t => t >= firstPeak - 5 && t <= secondPeak + 5);

    if (relevantTroughs.length >= 2) {
      // Find the best pair of troughs that form a parallel line
      let bestParallelism = Infinity;
      let bestTroughPair = null;

      for (let j = 0; j < relevantTroughs.length - 1; j++) {
        for (let k = j + 1; k < relevantTroughs.length; k++) {
          const firstTrough = relevantTroughs[j];
          const secondTrough = relevantTroughs[k];

          // Calculate lower trend line (connecting troughs)
          const lowerSlope = (data[secondTrough].low - data[firstTrough].low) / (secondTrough - firstTrough);

          // Check if slopes are similar (parallel lines)
          const slopeDifference = Math.abs(upperSlope - lowerSlope);

          if (slopeDifference < bestParallelism && slopeDifference < 0.0005) {
            bestParallelism = slopeDifference;
            bestTroughPair = [firstTrough, secondTrough];
          }
        }
      }

      if (bestTroughPair) {
        const [firstTrough, secondTrough] = bestTroughPair;

        // Calculate lower trend line
        const lowerSlope = (data[secondTrough].low - data[firstTrough].low) / (secondTrough - firstTrough);
        const lowerIntercept = data[firstTrough].low - lowerSlope * firstTrough;

        // Check if the pattern is valid (both slopes are positive)
        if (upperSlope > 0 && lowerSlope > 0) {
          // Check if the pattern is complete (price has broken out of the channel)
          const lastIndex = Math.max(secondPeak, secondTrough);
          const upperLineAtLastIndex = upperSlope * lastIndex + upperIntercept;
          const lowerLineAtLastIndex = lowerSlope * lastIndex + lowerIntercept;

          const breakoutUp = lastIndex < data.length - 1 && data[lastIndex + 1].close > upperLineAtLastIndex;
          const breakoutDown = lastIndex < data.length - 1 && data[lastIndex + 1].close < lowerLineAtLastIndex;

          return {
            pattern: 'ascending_channel',
            type: 'bullish',
            significance: 7,
            description: 'Ascending Channel pattern detected',
            points: {
              firstPeak,
              secondPeak,
              firstTrough,
              secondTrough
            },
            upper: {
              slope: upperSlope,
              intercept: upperIntercept
            },
            lower: {
              slope: lowerSlope,
              intercept: lowerIntercept
            },
            complete: breakoutUp || breakoutDown,
            breakoutDirection: breakoutUp ? 'up' : breakoutDown ? 'down' : null
          };
        }
      }
    }
  }

  return null;
};

/**
 * Check for Descending Channel pattern
 *
 * @param {Array} data - OHLC data
 * @param {Array} peaks - Array of peak indices
 * @param {Array} troughs - Array of trough indices
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkDescendingChannel = (data, peaks, troughs) => {
  // Need at least 2 peaks and 2 troughs
  if (peaks.length < 2 || troughs.length < 2) return null;

  // Check for parallel descending lines
  for (let i = 0; i < peaks.length - 1; i++) {
    const firstPeak = peaks[i];
    const secondPeak = peaks[i + 1];

    // Peaks should be separated by at least 5 candles
    if (secondPeak - firstPeak < 5) continue;

    // Second peak should be lower than first peak
    if (data[secondPeak].high >= data[firstPeak].high) continue;

    // Calculate upper trend line (connecting peaks)
    const upperSlope = (data[secondPeak].high - data[firstPeak].high) / (secondPeak - firstPeak);
    const upperIntercept = data[firstPeak].high - upperSlope * firstPeak;

    // Find troughs that could form the lower trend line
    const relevantTroughs = troughs.filter(t => t >= firstPeak - 5 && t <= secondPeak + 5);

    if (relevantTroughs.length >= 2) {
      // Find the best pair of troughs that form a parallel line
      let bestParallelism = Infinity;
      let bestTroughPair = null;

      for (let j = 0; j < relevantTroughs.length - 1; j++) {
        for (let k = j + 1; k < relevantTroughs.length; k++) {
          const firstTrough = relevantTroughs[j];
          const secondTrough = relevantTroughs[k];

          // Calculate lower trend line (connecting troughs)
          const lowerSlope = (data[secondTrough].low - data[firstTrough].low) / (secondTrough - firstTrough);

          // Check if slopes are similar (parallel lines)
          const slopeDifference = Math.abs(upperSlope - lowerSlope);

          if (slopeDifference < bestParallelism && slopeDifference < 0.0005) {
            bestParallelism = slopeDifference;
            bestTroughPair = [firstTrough, secondTrough];
          }
        }
      }

      if (bestTroughPair) {
        const [firstTrough, secondTrough] = bestTroughPair;

        // Calculate lower trend line
        const lowerSlope = (data[secondTrough].low - data[firstTrough].low) / (secondTrough - firstTrough);
        const lowerIntercept = data[firstTrough].low - lowerSlope * firstTrough;

        // Check if the pattern is valid (both slopes are negative)
        if (upperSlope < 0 && lowerSlope < 0) {
          // Check if the pattern is complete (price has broken out of the channel)
          const lastIndex = Math.max(secondPeak, secondTrough);
          const upperLineAtLastIndex = upperSlope * lastIndex + upperIntercept;
          const lowerLineAtLastIndex = lowerSlope * lastIndex + lowerIntercept;

          const breakoutUp = lastIndex < data.length - 1 && data[lastIndex + 1].close > upperLineAtLastIndex;
          const breakoutDown = lastIndex < data.length - 1 && data[lastIndex + 1].close < lowerLineAtLastIndex;

          return {
            pattern: 'descending_channel',
            type: 'bearish',
            significance: 7,
            description: 'Descending Channel pattern detected',
            points: {
              firstPeak,
              secondPeak,
              firstTrough,
              secondTrough
            },
            upper: {
              slope: upperSlope,
              intercept: upperIntercept
            },
            lower: {
              slope: lowerSlope,
              intercept: lowerIntercept
            },
            complete: breakoutUp || breakoutDown,
            breakoutDirection: breakoutUp ? 'up' : breakoutDown ? 'down' : null
          };
        }
      }
    }
  }

  return null;
};

/**
 * Check for Horizontal Channel pattern
 *
 * @param {Array} data - OHLC data
 * @param {Array} peaks - Array of peak indices
 * @param {Array} troughs - Array of trough indices
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkHorizontalChannel = (data, peaks, troughs) => {
  // Need at least 2 peaks and 2 troughs
  if (peaks.length < 2 || troughs.length < 2) return null;

  // Find potential resistance line (horizontal top)
  for (let i = 0; i < peaks.length - 1; i++) {
    const firstPeak = peaks[i];
    const secondPeak = peaks[i + 1];

    // Peaks should be separated by at least 5 candles
    if (secondPeak - firstPeak < 5) continue;

    // Peaks should be at roughly the same level (within 2%)
    const peakDiff = Math.abs(data[firstPeak].high - data[secondPeak].high);
    const peakAvg = (data[firstPeak].high + data[secondPeak].high) / 2;

    if (peakDiff / peakAvg < 0.02) {
      // Find potential support line (horizontal bottom)
      for (let j = 0; j < troughs.length - 1; j++) {
        const firstTrough = troughs[j];
        const secondTrough = troughs[j + 1];

        // Troughs should be separated by at least 5 candles
        if (secondTrough - firstTrough < 5) continue;

        // Troughs should be at roughly the same level (within 2%)
        const troughDiff = Math.abs(data[firstTrough].low - data[secondTrough].low);
        const troughAvg = (data[firstTrough].low + data[secondTrough].low) / 2;

        if (troughDiff / troughAvg < 0.02) {
          // Check if the channel width is significant (at least 3%)
          const channelWidth = (peakAvg - troughAvg) / troughAvg;

          if (channelWidth > 0.03) {
            // Check if the pattern is complete (price has broken out of the channel)
            const lastIndex = Math.max(secondPeak, secondTrough);

            const breakoutUp = lastIndex < data.length - 1 && data[lastIndex + 1].close > peakAvg;
            const breakoutDown = lastIndex < data.length - 1 && data[lastIndex + 1].close < troughAvg;

            return {
              pattern: 'horizontal_channel',
              type: 'neutral',
              significance: 6,
              description: 'Horizontal Channel pattern detected',
              points: {
                firstPeak,
                secondPeak,
                firstTrough,
                secondTrough
              },
              resistance: peakAvg,
              support: troughAvg,
              complete: breakoutUp || breakoutDown,
              breakoutDirection: breakoutUp ? 'up' : breakoutDown ? 'down' : null
            };
          }
        }
      }
    }
  }

  return null;
};

/**
 * Check for Flag patterns (Bullish, Bearish)
 *
 * @param {Array} data - OHLC data
 * @returns {Array} - Array of detected flag patterns
 */
const checkFlags = (data) => {
  // Need at least 20 candles for a reliable pattern
  if (data.length < 20) return [];

  const patterns = [];

  // Find peaks and troughs
  const peaks = findPeaks(data, 'high', 3);
  const troughs = findPeaks(data, 'low', 3);

  // Check for bullish flag
  const bullishFlag = checkBullishFlag(data, peaks, troughs);
  if (bullishFlag) patterns.push(bullishFlag);

  // Check for bearish flag
  const bearishFlag = checkBearishFlag(data, peaks, troughs);
  if (bearishFlag) patterns.push(bearishFlag);

  return patterns;
};

/**
 * Check for Bullish Flag pattern
 *
 * @param {Array} data - OHLC data
 * @param {Array} peaks - Array of peak indices
 * @param {Array} troughs - Array of trough indices
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkBullishFlag = (data, peaks, troughs) => {
  // Need at least 2 peaks and 2 troughs
  if (peaks.length < 2 || troughs.length < 2) return null;

  // Look for a strong uptrend (pole) followed by a consolidation (flag)
  for (let i = 0; i < data.length - 10; i++) {
    // Check for a strong uptrend (pole)
    const poleStart = i;
    let poleEnd = -1;

    // Find the end of the pole (a significant high)
    for (let j = i + 5; j < Math.min(i + 15, data.length); j++) {
      if (peaks.includes(j)) {
        poleEnd = j;
        break;
      }
    }

    if (poleEnd === -1) continue;

    // Calculate pole height
    const poleHeight = data[poleEnd].high - data[poleStart].low;
    const polePercentage = poleHeight / data[poleStart].low;

    // Pole should be a significant move (at least 3%)
    if (polePercentage < 0.03) continue;

    // Look for a consolidation pattern (flag) after the pole
    const flagStart = poleEnd;
    let flagEnd = -1;

    // Flag should be a period of consolidation (between 5-15 candles)
    for (let j = flagStart + 5; j < Math.min(flagStart + 15, data.length); j++) {
      // Check if price is breaking out of the consolidation
      if (data[j].close > data[flagStart].high) {
        flagEnd = j;
        break;
      }
    }

    if (flagEnd === -1) continue;

    // Check if the flag forms a channel (parallel lines)
    const flagHighs = [];
    const flagLows = [];

    for (let j = flagStart; j <= flagEnd; j++) {
      flagHighs.push(data[j].high);
      flagLows.push(data[j].low);
    }

    // Calculate the slope of the upper and lower trendlines
    const upperSlope = calculateSlope(flagHighs);
    const lowerSlope = calculateSlope(flagLows);

    // Flag should have slightly downward or flat slope
    if (upperSlope > 0.001) continue;

    // Upper and lower trendlines should be roughly parallel
    if (Math.abs(upperSlope - lowerSlope) > 0.001) continue;

    // Calculate flag height (should be smaller than pole)
    const flagHeight = Math.max(...flagHighs) - Math.min(...flagLows);

    // Flag height should be less than 50% of pole height
    if (flagHeight > poleHeight * 0.5) continue;

    // Check if the pattern is complete (price has broken above the flag)
    const breakout = flagEnd < data.length - 1 && data[flagEnd + 1].close > Math.max(...flagHighs);

    return {
      pattern: 'bullish_flag',
      type: 'continuation',
      significance: 8,
      description: 'Bullish Flag pattern detected',
      points: {
        poleStart,
        poleEnd,
        flagStart,
        flagEnd
      },
      pole: {
        height: poleHeight,
        percentage: polePercentage
      },
      flag: {
        upperSlope,
        lowerSlope,
        height: flagHeight
      },
      complete: breakout
    };
  }

  return null;
};

/**
 * Check for Bearish Flag pattern
 *
 * @param {Array} data - OHLC data
 * @param {Array} peaks - Array of peak indices
 * @param {Array} troughs - Array of trough indices
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkBearishFlag = (data, peaks, troughs) => {
  // Need at least 2 peaks and 2 troughs
  if (peaks.length < 2 || troughs.length < 2) return null;

  // Look for a strong downtrend (pole) followed by a consolidation (flag)
  for (let i = 0; i < data.length - 10; i++) {
    // Check for a strong downtrend (pole)
    const poleStart = i;
    let poleEnd = -1;

    // Find the end of the pole (a significant low)
    for (let j = i + 5; j < Math.min(i + 15, data.length); j++) {
      if (troughs.includes(j)) {
        poleEnd = j;
        break;
      }
    }

    if (poleEnd === -1) continue;

    // Calculate pole height
    const poleHeight = data[poleStart].high - data[poleEnd].low;
    const polePercentage = poleHeight / data[poleStart].high;

    // Pole should be a significant move (at least 3%)
    if (polePercentage < 0.03) continue;

    // Look for a consolidation pattern (flag) after the pole
    const flagStart = poleEnd;
    let flagEnd = -1;

    // Flag should be a period of consolidation (between 5-15 candles)
    for (let j = flagStart + 5; j < Math.min(flagStart + 15, data.length); j++) {
      // Check if price is breaking out of the consolidation
      if (data[j].close < data[flagStart].low) {
        flagEnd = j;
        break;
      }
    }

    if (flagEnd === -1) continue;

    // Check if the flag forms a channel (parallel lines)
    const flagHighs = [];
    const flagLows = [];

    for (let j = flagStart; j <= flagEnd; j++) {
      flagHighs.push(data[j].high);
      flagLows.push(data[j].low);
    }

    // Calculate the slope of the upper and lower trendlines
    const upperSlope = calculateSlope(flagHighs);
    const lowerSlope = calculateSlope(flagLows);

    // Flag should have slightly upward or flat slope
    if (upperSlope < -0.001) continue;

    // Upper and lower trendlines should be roughly parallel
    if (Math.abs(upperSlope - lowerSlope) > 0.001) continue;

    // Calculate flag height (should be smaller than pole)
    const flagHeight = Math.max(...flagHighs) - Math.min(...flagLows);

    // Flag height should be less than 50% of pole height
    if (flagHeight > poleHeight * 0.5) continue;

    // Check if the pattern is complete (price has broken below the flag)
    const breakout = flagEnd < data.length - 1 && data[flagEnd + 1].close < Math.min(...flagLows);

    return {
      pattern: 'bearish_flag',
      type: 'continuation',
      significance: 8,
      description: 'Bearish Flag pattern detected',
      points: {
        poleStart,
        poleEnd,
        flagStart,
        flagEnd
      },
      pole: {
        height: poleHeight,
        percentage: polePercentage
      },
      flag: {
        upperSlope,
        lowerSlope,
        height: flagHeight
      },
      complete: breakout
    };
  }

  return null;
};

/**
 * Calculate the slope of a line using linear regression
 *
 * @param {Array} values - Array of values
 * @returns {number} - Slope of the line
 */
const calculateSlope = (values) => {
  const n = values.length;

  if (n <= 1) return 0;

  let sumX = 0;
  let sumY = 0;
  let sumXY = 0;
  let sumX2 = 0;

  for (let i = 0; i < n; i++) {
    sumX += i;
    sumY += values[i];
    sumXY += i * values[i];
    sumX2 += i * i;
  }

  return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
};

/**
 * Check for Pennant patterns (Bullish, Bearish)
 *
 * @param {Array} data - OHLC data
 * @returns {Array} - Array of detected pennant patterns
 */
const checkPennants = (data) => {
  // Need at least 20 candles for a reliable pattern
  if (data.length < 20) return [];

  const patterns = [];

  // Find peaks and troughs
  const peaks = findPeaks(data, 'high', 3);
  const troughs = findPeaks(data, 'low', 3);

  // Check for bullish pennant
  const bullishPennant = checkBullishPennant(data, peaks, troughs);
  if (bullishPennant) patterns.push(bullishPennant);

  // Check for bearish pennant
  const bearishPennant = checkBearishPennant(data, peaks, troughs);
  if (bearishPennant) patterns.push(bearishPennant);

  return patterns;
};

/**
 * Check for Bullish Pennant pattern
 *
 * @param {Array} data - OHLC data
 * @param {Array} peaks - Array of peak indices
 * @param {Array} troughs - Array of trough indices
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkBullishPennant = (data, peaks, troughs) => {
  // Need at least 2 peaks and 2 troughs
  if (peaks.length < 2 || troughs.length < 2) return null;

  // Look for a strong uptrend (pole) followed by a consolidation (pennant)
  for (let i = 0; i < data.length - 10; i++) {
    // Check for a strong uptrend (pole)
    const poleStart = i;
    let poleEnd = -1;

    // Find the end of the pole (a significant high)
    for (let j = i + 5; j < Math.min(i + 15, data.length); j++) {
      if (peaks.includes(j)) {
        poleEnd = j;
        break;
      }
    }

    if (poleEnd === -1) continue;

    // Calculate pole height
    const poleHeight = data[poleEnd].high - data[poleStart].low;
    const polePercentage = poleHeight / data[poleStart].low;

    // Pole should be a significant move (at least 3%)
    if (polePercentage < 0.03) continue;

    // Look for a consolidation pattern (pennant) after the pole
    const pennantStart = poleEnd;
    let pennantEnd = -1;

    // Pennant should be a period of consolidation (between 5-15 candles)
    for (let j = pennantStart + 5; j < Math.min(pennantStart + 15, data.length); j++) {
      // Check if price is breaking out of the consolidation
      if (data[j].close > data[pennantStart].high) {
        pennantEnd = j;
        break;
      }
    }

    if (pennantEnd === -1) continue;

    // Check if the pennant forms a symmetrical triangle
    const pennantHighs = [];
    const pennantLows = [];

    for (let j = pennantStart; j <= pennantEnd; j++) {
      pennantHighs.push(data[j].high);
      pennantLows.push(data[j].low);
    }

    // Calculate the slope of the upper and lower trendlines
    const upperSlope = calculateSlope(pennantHighs);
    const lowerSlope = calculateSlope(pennantLows);

    // Pennant should have converging trendlines (upper slope negative, lower slope positive)
    if (upperSlope >= 0 || lowerSlope <= 0) continue;

    // Calculate pennant height (should be smaller than pole)
    const pennantHeight = Math.max(...pennantHighs) - Math.min(...pennantLows);

    // Pennant height should be less than 50% of pole height
    if (pennantHeight > poleHeight * 0.5) continue;

    // Check if the pattern is complete (price has broken above the pennant)
    const breakout = pennantEnd < data.length - 1 && data[pennantEnd + 1].close > Math.max(...pennantHighs);

    return {
      pattern: 'bullish_pennant',
      type: 'continuation',
      significance: 8,
      description: 'Bullish Pennant pattern detected',
      points: {
        poleStart,
        poleEnd,
        pennantStart,
        pennantEnd
      },
      pole: {
        height: poleHeight,
        percentage: polePercentage
      },
      pennant: {
        upperSlope,
        lowerSlope,
        height: pennantHeight
      },
      complete: breakout
    };
  }

  return null;
};

/**
 * Check for Bearish Pennant pattern
 *
 * @param {Array} data - OHLC data
 * @param {Array} peaks - Array of peak indices
 * @param {Array} troughs - Array of trough indices
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkBearishPennant = (data, peaks, troughs) => {
  // Need at least 2 peaks and 2 troughs
  if (peaks.length < 2 || troughs.length < 2) return null;

  // Look for a strong downtrend (pole) followed by a consolidation (pennant)
  for (let i = 0; i < data.length - 10; i++) {
    // Check for a strong downtrend (pole)
    const poleStart = i;
    let poleEnd = -1;

    // Find the end of the pole (a significant low)
    for (let j = i + 5; j < Math.min(i + 15, data.length); j++) {
      if (troughs.includes(j)) {
        poleEnd = j;
        break;
      }
    }

    if (poleEnd === -1) continue;

    // Calculate pole height
    const poleHeight = data[poleStart].high - data[poleEnd].low;
    const polePercentage = poleHeight / data[poleStart].high;

    // Pole should be a significant move (at least 3%)
    if (polePercentage < 0.03) continue;

    // Look for a consolidation pattern (pennant) after the pole
    const pennantStart = poleEnd;
    let pennantEnd = -1;

    // Pennant should be a period of consolidation (between 5-15 candles)
    for (let j = pennantStart + 5; j < Math.min(pennantStart + 15, data.length); j++) {
      // Check if price is breaking out of the consolidation
      if (data[j].close < data[pennantStart].low) {
        pennantEnd = j;
        break;
      }
    }

    if (pennantEnd === -1) continue;

    // Check if the pennant forms a symmetrical triangle
    const pennantHighs = [];
    const pennantLows = [];

    for (let j = pennantStart; j <= pennantEnd; j++) {
      pennantHighs.push(data[j].high);
      pennantLows.push(data[j].low);
    }

    // Calculate the slope of the upper and lower trendlines
    const upperSlope = calculateSlope(pennantHighs);
    const lowerSlope = calculateSlope(pennantLows);

    // Pennant should have converging trendlines (upper slope negative, lower slope positive)
    if (upperSlope >= 0 || lowerSlope <= 0) continue;

    // Calculate pennant height (should be smaller than pole)
    const pennantHeight = Math.max(...pennantHighs) - Math.min(...pennantLows);

    // Pennant height should be less than 50% of pole height
    if (pennantHeight > poleHeight * 0.5) continue;

    // Check if the pattern is complete (price has broken below the pennant)
    const breakout = pennantEnd < data.length - 1 && data[pennantEnd + 1].close < Math.min(...pennantLows);

    return {
      pattern: 'bearish_pennant',
      type: 'continuation',
      significance: 8,
      description: 'Bearish Pennant pattern detected',
      points: {
        poleStart,
        poleEnd,
        pennantStart,
        pennantEnd
      },
      pole: {
        height: poleHeight,
        percentage: polePercentage
      },
      pennant: {
        upperSlope,
        lowerSlope,
        height: pennantHeight
      },
      complete: breakout
    };
  }

  return null;
};

/**
 * Check for Cup and Handle pattern
 *
 * @param {Array} data - OHLC data
 * @returns {Object|null} - Pattern object or null if not found
 */
const checkCupAndHandle = (data) => {
  // Need at least 30 candles for a reliable pattern
  if (data.length < 30) return null;

  // Find peaks and troughs
  const peaks = findPeaks(data, 'high', 5);
  const troughs = findPeaks(data, 'low', 5);

  // Need at least 2 peaks and 1 trough
  if (peaks.length < 2 || troughs.length < 1) return null;

  // Look for a cup and handle pattern
  for (let i = 0; i < peaks.length - 1; i++) {
    const leftPeak = peaks[i];

    // Find a trough after the left peak
    let cupBottom = -1;
    for (let j = 0; j < troughs.length; j++) {
      if (troughs[j] > leftPeak) {
        cupBottom = troughs[j];
        break;
      }
    }

    if (cupBottom === -1) continue;

    // Find a right peak after the cup bottom
    let rightPeak = -1;
    for (let j = i + 1; j < peaks.length; j++) {
      if (peaks[j] > cupBottom) {
        rightPeak = peaks[j];
        break;
      }
    }

    if (rightPeak === -1) continue;

    // Check if the cup is U-shaped (not V-shaped)
    const cupWidth = rightPeak - leftPeak;
    const cupDepth = (data[leftPeak].high + data[rightPeak].high) / 2 - data[cupBottom].low;

    // Cup should be wide enough (at least 15 candles)
    if (cupWidth < 15) continue;

    // Cup depth should be significant (at least 3%)
    const cupDepthPercentage = cupDepth / data[cupBottom].low;
    if (cupDepthPercentage < 0.03) continue;

    // Check if the right peak is at a similar level as the left peak
    const peakDiff = Math.abs(data[rightPeak].high - data[leftPeak].high);
    const peakAvg = (data[rightPeak].high + data[leftPeak].high) / 2;

    if (peakDiff / peakAvg > 0.03) continue;

    // Look for a handle after the right peak
    let handleBottom = -1;
    for (let j = 0; j < troughs.length; j++) {
      if (troughs[j] > rightPeak) {
        handleBottom = troughs[j];
        break;
      }
    }

    if (handleBottom === -1) continue;

    // Handle should be shallower than the cup
    const handleDepth = data[rightPeak].high - data[handleBottom].low;
    if (handleDepth > cupDepth * 0.7) continue;

    // Handle should not be too long (less than half the cup width)
    const handleWidth = handleBottom - rightPeak;
    if (handleWidth > cupWidth * 0.5) continue;

    // Check if the pattern is complete (price has broken above the right peak)
    const breakout = handleBottom < data.length - 1 && data[handleBottom + 1].close > data[rightPeak].high;

    return {
      pattern: 'cup_and_handle',
      type: 'continuation',
      significance: 9,
      description: 'Cup and Handle pattern detected',
      points: {
        leftPeak,
        cupBottom,
        rightPeak,
        handleBottom
      },
      cup: {
        width: cupWidth,
        depth: cupDepth,
        depthPercentage: cupDepthPercentage
      },
      handle: {
        width: handleWidth,
        depth: handleDepth
      },
      complete: breakout
    };
  }

  return null;
};

export default {
  detectCandlestickPatterns,
  detectChartPatterns,
  detectSupportResistance
};
