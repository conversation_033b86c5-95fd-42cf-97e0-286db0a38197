/**
 * Webhook Routes
 * 
 * This file contains routes for handling webhooks from external services
 * like Stripe, etc.
 */

const express = require('express');
const logger = require('../../utils/logger');
const { captureException } = require('../../utils/sentry');

// Import Stripe
const Stripe = require('stripe');
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

/**
 * Set up webhook routes
 * @param {Object} app - Express app
 */
module.exports = (app) => {
  const router = express.Router();

  /**
   * Stripe webhook endpoint
   * 
   * This endpoint receives webhook events from Stripe and processes them.
   * It verifies the webhook signature to ensure the request is from <PERSON><PERSON>.
   * 
   * @route POST /api/webhooks/stripe
   * @group Webhooks - Webhook endpoints for external services
   * @param {Object} req.body - Stripe event payload
   * @returns {Object} 200 - Success response
   * @returns {Error} 400 - Invalid signature
   * @returns {Error} 500 - Server error
   */
  router.post('/stripe', express.raw({ type: 'application/json' }), async (req, res) => {
    try {
      const signature = req.headers['stripe-signature'];
      
      if (!signature) {
        logger.warn('Stripe webhook received without signature');
        return res.status(400).json({ error: 'Missing stripe-signature header' });
      }
      
      // Verify the webhook signature
      const event = stripe.webhooks.constructEvent(
        req.body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET
      );
      
      // Process the webhook event
      const result = await handleStripeWebhookEvent(event);
      
      // Return a 200 response to acknowledge receipt of the event
      res.status(200).json(result);
    } catch (error) {
      logger.error('Error processing Stripe webhook:', error);
      captureException(error, { source: 'webhookRoutes.js', context: 'stripe webhook' });
      
      // Return a 400 error if the signature verification fails
      if (error.type === 'StripeSignatureVerificationError') {
        return res.status(400).json({ error: 'Invalid signature' });
      }
      
      // Return a 500 error for all other errors
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  // Mount the router
  app.use('/api/webhooks', router);
  
  logger.info('Webhook routes initialized');
};

/**
 * Handle Stripe webhook events
 * @param {Object} event - Stripe event object
 * @returns {Promise<Object>} - Response object
 */
async function handleStripeWebhookEvent(event) {
  const { type, data } = event;
  
  logger.info(`Processing Stripe webhook event: ${type}`);
  
  try {
    switch (type) {
      case 'payment_intent.succeeded':
        return await handlePaymentIntentSucceeded(data.object);
        
      case 'payment_intent.payment_failed':
        return await handlePaymentIntentFailed(data.object);
        
      case 'customer.subscription.created':
        return await handleSubscriptionCreated(data.object);
        
      case 'customer.subscription.updated':
        return await handleSubscriptionUpdated(data.object);
        
      case 'customer.subscription.deleted':
        return await handleSubscriptionDeleted(data.object);
        
      case 'invoice.payment_succeeded':
        return await handleInvoicePaymentSucceeded(data.object);
        
      case 'invoice.payment_failed':
        return await handleInvoicePaymentFailed(data.object);
        
      default:
        logger.info(`Unhandled webhook event type: ${type}`);
        return { status: 'ignored', message: `Unhandled event type: ${type}` };
    }
  } catch (error) {
    logger.error(`Error handling webhook event ${type}:`, error);
    throw error;
  }
}

// Event handlers for specific webhook events

async function handlePaymentIntentSucceeded(paymentIntent) {
  // Update order status, send confirmation email, etc.
  logger.info(`Payment succeeded: ${paymentIntent.id}`);
  return { status: 'success', message: 'Payment processed successfully' };
}

async function handlePaymentIntentFailed(paymentIntent) {
  // Update order status, notify customer, etc.
  logger.info(`Payment failed: ${paymentIntent.id}`);
  return { status: 'failed', message: 'Payment failed' };
}

async function handleSubscriptionCreated(subscription) {
  // Provision access, update user status, etc.
  logger.info(`Subscription created: ${subscription.id}`);
  return { status: 'success', message: 'Subscription created' };
}

async function handleSubscriptionUpdated(subscription) {
  // Update user access, etc.
  logger.info(`Subscription updated: ${subscription.id}`);
  return { status: 'success', message: 'Subscription updated' };
}

async function handleSubscriptionDeleted(subscription) {
  // Remove user access, update status, etc.
  logger.info(`Subscription deleted: ${subscription.id}`);
  return { status: 'success', message: 'Subscription deleted' };
}

async function handleInvoicePaymentSucceeded(invoice) {
  // Update subscription status, etc.
  logger.info(`Invoice payment succeeded: ${invoice.id}`);
  return { status: 'success', message: 'Invoice payment succeeded' };
}

async function handleInvoicePaymentFailed(invoice) {
  // Notify customer, update subscription status, etc.
  logger.info(`Invoice payment failed: ${invoice.id}`);
  return { status: 'failed', message: 'Invoice payment failed' };
}
