/**
 * Technical Indicators for Trading Signals App
 * 
 * This file provides functionality for calculating and displaying technical indicators.
 */

// Technical indicator constants
const INDICATORS = {
  SMA: {
    name: 'Simple Moving Average',
    periods: [5, 10, 20, 50, 100, 200],
    color: '#3a7bd5',
    description: 'Average price over a specific number of periods'
  },
  EMA: {
    name: 'Exponential Moving Average',
    periods: [5, 10, 20, 50, 100, 200],
    color: '#00d2d3',
    description: 'Weighted average price with more weight given to recent prices'
  },
  RSI: {
    name: 'Relative Strength Index',
    periods: [7, 14, 21],
    color: '#ffd700',
    description: 'Momentum oscillator that measures the speed and change of price movements'
  },
  MACD: {
    name: 'Moving Average Convergence Divergence',
    periods: [12, 26, 9], // Fast, Slow, Signal
    color: '#ff7675',
    description: 'Trend-following momentum indicator showing the relationship between two moving averages'
  },
  BB: {
    name: 'Bollinger Bands',
    periods: [20],
    color: '#a29bfe',
    description: 'Volatility bands placed above and below a moving average'
  }
};

// Initialize technical indicators
function initTechnicalIndicators() {
  console.log('Initializing technical indicators');
  
  // Set up indicator selectors
  setupIndicatorSelectors();
  
  // Set up period selectors
  setupPeriodSelectors();
}

// Set up indicator selectors
function setupIndicatorSelectors() {
  const indicatorSelectors = document.querySelectorAll('.indicator-selector');
  
  indicatorSelectors.forEach(selector => {
    // Clear existing options
    selector.innerHTML = '';
    
    // Add options for each indicator
    Object.keys(INDICATORS).forEach(key => {
      const option = document.createElement('option');
      option.value = key;
      option.textContent = INDICATORS[key].name;
      selector.appendChild(option);
    });
    
    // Add change event listener
    selector.addEventListener('change', function() {
      const indicator = this.value;
      const periodSelector = this.closest('.indicator-control').querySelector('.period-selector');
      
      if (periodSelector) {
        updatePeriodOptions(periodSelector, indicator);
      }
      
      // Update indicator on chart if available
      if (typeof updateChartIndicator === 'function') {
        updateChartIndicator(indicator, periodSelector ? periodSelector.value : null);
      }
    });
  });
}

// Set up period selectors
function setupPeriodSelectors() {
  const periodSelectors = document.querySelectorAll('.period-selector');
  
  periodSelectors.forEach(selector => {
    // Get the associated indicator
    const indicatorSelector = selector.closest('.indicator-control').querySelector('.indicator-selector');
    const indicator = indicatorSelector ? indicatorSelector.value : 'SMA';
    
    // Update period options
    updatePeriodOptions(selector, indicator);
    
    // Add change event listener
    selector.addEventListener('change', function() {
      const period = this.value;
      const indicator = this.closest('.indicator-control').querySelector('.indicator-selector').value;
      
      // Update indicator on chart if available
      if (typeof updateChartIndicator === 'function') {
        updateChartIndicator(indicator, period);
      }
    });
  });
}

// Update period options based on selected indicator
function updatePeriodOptions(selector, indicator) {
  // Clear existing options
  selector.innerHTML = '';
  
  // Add options for the selected indicator
  const periods = INDICATORS[indicator]?.periods || [14];
  
  periods.forEach(period => {
    const option = document.createElement('option');
    option.value = period;
    option.textContent = period;
    selector.appendChild(option);
  });
}

// Calculate Simple Moving Average
function calculateSMA(data, period) {
  const result = [];
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      result.push(null);
    } else {
      let sum = 0;
      for (let j = 0; j < period; j++) {
        sum += data[i - j];
      }
      result.push(sum / period);
    }
  }
  
  return result;
}

// Calculate Exponential Moving Average
function calculateEMA(data, period) {
  const result = [];
  const multiplier = 2 / (period + 1);
  
  // First EMA is SMA
  let ema = data.slice(0, period).reduce((sum, price) => sum + price, 0) / period;
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      result.push(null);
    } else if (i === period - 1) {
      result.push(ema);
    } else {
      ema = (data[i] - ema) * multiplier + ema;
      result.push(ema);
    }
  }
  
  return result;
}

// Calculate Relative Strength Index
function calculateRSI(data, period) {
  const result = [];
  const gains = [];
  const losses = [];
  
  // Calculate gains and losses
  for (let i = 1; i < data.length; i++) {
    const change = data[i] - data[i - 1];
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? Math.abs(change) : 0);
  }
  
  // Calculate RSI
  for (let i = 0; i < data.length; i++) {
    if (i < period) {
      result.push(null);
    } else {
      const avgGain = gains.slice(i - period, i).reduce((sum, gain) => sum + gain, 0) / period;
      const avgLoss = losses.slice(i - period, i).reduce((sum, loss) => sum + loss, 0) / period;
      
      if (avgLoss === 0) {
        result.push(100);
      } else {
        const rs = avgGain / avgLoss;
        result.push(100 - (100 / (1 + rs)));
      }
    }
  }
  
  return result;
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initTechnicalIndicators);

// Export functions for external use
window.TechnicalIndicators = {
  INDICATORS,
  calculateSMA,
  calculateEMA,
  calculateRSI,
  updateIndicator: function(chartInstance, indicator, period) {
    console.log(`Updating ${indicator} with period ${period}`);
    // Implementation depends on the charting library being used
  }
};

console.log('Technical indicators loaded');
