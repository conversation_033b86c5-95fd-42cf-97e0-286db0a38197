/**
 * @swagger
 * tags:
 *   name: Analytics
 *   description: Trading signals analytics
 */

/**
 * @swagger
 * /api/analytics/signal-performance:
 *   get:
 *     summary: Get signal performance statistics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Signal performance retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 50
 *                     active:
 *                       type: object
 *                       properties:
 *                         count:
 *                           type: integer
 *                           example: 20
 *                         avgStrength:
 *                           type: number
 *                           example: 75.5
 *                     executed:
 *                       type: object
 *                       properties:
 *                         count:
 *                           type: integer
 *                           example: 25
 *                         avgStrength:
 *                           type: number
 *                           example: 80.2
 *                     expired:
 *                       type: object
 *                       properties:
 *                         count:
 *                           type: integer
 *                           example: 5
 *                         avgStrength:
 *                           type: number
 *                           example: 65.8
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/analytics/signal-distribution/symbol:
 *   get:
 *     summary: Get signal distribution by symbol
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Signal distribution by symbol retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       symbol:
 *                         type: string
 *                         example: EURUSD
 *                       count:
 *                         type: integer
 *                         example: 25
 *                       buyCount:
 *                         type: integer
 *                         example: 15
 *                       sellCount:
 *                         type: integer
 *                         example: 10
 *                       buyPercentage:
 *                         type: number
 *                         example: 60
 *                       sellPercentage:
 *                         type: number
 *                         example: 40
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/analytics/signal-distribution/timeframe:
 *   get:
 *     summary: Get signal distribution by timeframe
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Signal distribution by timeframe retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       timeframe:
 *                         type: string
 *                         example: H1
 *                       count:
 *                         type: integer
 *                         example: 20
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/analytics/signal-trend:
 *   get:
 *     summary: Get signal trend over time
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 365
 *           default: 30
 *         description: Number of days to look back
 *     responses:
 *       200:
 *         description: Signal trend retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         format: date-time
 *                       count:
 *                         type: integer
 *                         example: 5
 *                       buyCount:
 *                         type: integer
 *                         example: 3
 *                       sellCount:
 *                         type: integer
 *                         example: 2
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/analytics/market-data-stats:
 *   get:
 *     summary: Get market data statistics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: symbol
 *         schema:
 *           type: string
 *         required: true
 *         description: Trading symbol
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [M1, M5, M15, M30, H1, H4, D1, W1, MN]
 *         required: true
 *         description: Chart timeframe
 *     responses:
 *       200:
 *         description: Market data statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     count:
 *                       type: integer
 *                       example: 100
 *                     avgVolume:
 *                       type: number
 *                       example: 1250.75
 *                     maxHigh:
 *                       type: number
 *                       example: 1.0925
 *                     minLow:
 *                       type: number
 *                       example: 1.0850
 *                     avgClose:
 *                       type: number
 *                       example: 1.0880
 *                     latestClose:
 *                       type: number
 *                       example: 1.0890
 *                     latestTimestamp:
 *                       type: string
 *                       format: date-time
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
