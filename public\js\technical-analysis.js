/**
 * Technical Analysis Module for Trading Signals App
 *
 * This module provides functions for analyzing market data and generating trading signals.
 */

// Technical indicator weights for signal generation
const technicalIndicators = {
    signalWeights: {
        rsi: 1.5,
        macd: 1.8,
        ema: 1.2,
        supertrend: 2.0,
        bollinger: 1.3,
        atr: 0.8
    }
};

/**
 * Generate RSI (Relative Strength Index)
 * @param {Array<number>} prices - Array of closing prices
 * @param {number} period - RSI period (default: 14)
 * @returns {Array<number>} - Array of RSI values
 */
function calculateRSI(prices, period = 14) {
    if (!prices || prices.length < period + 1) {
        return null;
    }

    const gains = [];
    const losses = [];

    // Calculate price changes
    for (let i = 1; i < prices.length; i++) {
        const change = prices[i] - prices[i - 1];
        gains.push(change >= 0 ? change : 0);
        losses.push(change < 0 ? Math.abs(change) : 0);
    }

    // Calculate average gains and losses
    let avgGain = gains.slice(0, period).reduce((sum, gain) => sum + gain, 0) / period;
    let avgLoss = losses.slice(0, period).reduce((sum, loss) => sum + loss, 0) / period;

    const rsiValues = [];

    // First RSI value
    let rs = avgGain / (avgLoss === 0 ? 0.001 : avgLoss); // Avoid division by zero
    let rsi = 100 - (100 / (1 + rs));
    rsiValues.push(rsi);

    // Calculate remaining RSI values
    for (let i = period; i < gains.length; i++) {
        avgGain = ((avgGain * (period - 1)) + gains[i]) / period;
        avgLoss = ((avgLoss * (period - 1)) + losses[i]) / period;

        rs = avgGain / (avgLoss === 0 ? 0.001 : avgLoss);
        rsi = 100 - (100 / (1 + rs));
        rsiValues.push(rsi);
    }

    return rsiValues;
}

/**
 * Get RSI signal based on value
 * @param {number} rsi - RSI value
 * @returns {Object} - Signal object with type and strength
 */
function getRSISignal(rsi) {
    if (rsi === null) {
        return { type: 'neutral', strength: 0 };
    }

    if (rsi < 30) {
        // Oversold - bullish signal
        const strength = Math.min((30 - rsi) / 10, 1);
        return { type: 'buy', strength };
    } else if (rsi > 70) {
        // Overbought - bearish signal
        const strength = Math.min((rsi - 70) / 10, 1);
        return { type: 'sell', strength };
    } else {
        // Neutral zone
        return { type: 'neutral', strength: 0 };
    }
}

/**
 * Calculate EMA (Exponential Moving Average)
 * @param {Array<number>} prices - Array of closing prices
 * @param {number} period - EMA period
 * @returns {number} - EMA value
 */
function calculateEMA(prices, period) {
    if (!prices || prices.length < period) {
        return null;
    }

    const multiplier = 2 / (period + 1);

    // Calculate SMA for first EMA value
    let ema = prices.slice(0, period).reduce((sum, price) => sum + price, 0) / period;

    // Calculate EMA for remaining prices
    for (let i = period; i < prices.length; i++) {
        ema = (prices[i] - ema) * multiplier + ema;
    }

    return ema;
}

/**
 * Generate a trading signal based on market data - simplified version
 * @param {Object} data - Market data object
 * @returns {Object} - Trading signal object
 */
function generateTradingSignal(data) {
    try {
        if (!data || !data.closes || data.closes.length < 10) {
            return createNeutralSignal(data);
        }

        // Get basic data
        const closes = data.closes;
        const currentPrice = data.currentPrice || closes[closes.length - 1];

        // Simple signal generation based on last few prices
        const lastPrice = closes[closes.length - 1];
        const prevPrice = closes[closes.length - 2];

        // Determine signal type based on simple price movement
        let signalType = 'neutral';
        let confidence = 0.5;

        if (lastPrice > prevPrice * 1.005) {
            signalType = 'buy';
            confidence = 0.7;
        } else if (lastPrice < prevPrice * 0.995) {
            signalType = 'sell';
            confidence = 0.7;
        }

        // Simple entry, stop loss, and take profit levels
        const entryPrice = currentPrice;
        const stopLoss = signalType === 'buy' ? entryPrice * 0.99 : entryPrice * 1.01;
        const takeProfit = signalType === 'buy' ? entryPrice * 1.015 : entryPrice * 0.985;

        // Simple risk/reward ratio
        const risk = Math.abs(entryPrice - stopLoss);
        const reward = Math.abs(entryPrice - takeProfit);
        const riskRewardRatio = reward / risk;

        return {
            type: signalType,
            entryPrice: entryPrice,
            stopLoss: stopLoss,
            takeProfit: takeProfit,
            riskRewardRatio: riskRewardRatio,
            confidence: confidence,
            indicators: {
                price: currentPrice
            },
            timestamp: new Date().toISOString()
        };
    } catch (error) {
        return createNeutralSignal(data);
    }
}

/**
 * Get trend based on EMA values - simplified version
 * @param {number} price - Current price
 * @param {number} ema20 - 20-period EMA
 * @returns {Object} - Trend object with type and strength
 */
function getEMATrend(price, ema20) {
    if (price === null || ema20 === null) {
        return { type: 'neutral', strength: 0 };
    }

    if (price > ema20 * 1.01) {
        return { type: 'buy', strength: 0.7 };
    } else if (price < ema20 * 0.99) {
        return { type: 'sell', strength: 0.7 };
    } else {
        return { type: 'neutral', strength: 0 };
    }
}

/**
 * Calculate ATR (Average True Range) - simplified version
 * @param {Array<number>} highs - Array of high prices
 * @param {Array<number>} lows - Array of low prices
 * @returns {number} - ATR value
 */
function calculateATR(highs, lows) {
    if (!highs || !lows || highs.length < 5 || lows.length < 5) {
        return null;
    }

    // Simple average range calculation
    let sum = 0;
    const count = Math.min(highs.length, lows.length, 10); // Use last 10 bars at most

    for (let i = 0; i < count; i++) {
        sum += (highs[i] - lows[i]);
    }

    return sum / count;
}

/**
 * Create a neutral signal - simplified version
 * @param {Object} data - Market data
 * @returns {Object} - Neutral signal object
 */
function createNeutralSignal(data) {
    const price = data?.currentPrice || 100;

    return {
        type: 'neutral',
        entryPrice: price,
        stopLoss: price * 0.99,
        takeProfit: price * 1.01,
        riskRewardRatio: 1,
        confidence: 0.5,
        timestamp: new Date().toISOString()
    };
}

// Export functions for external use
window.technicalAnalysis = {
    generateTradingSignal
};
