import React, { useState, useEffect } from 'react';
import AlertNotification from './AlertNotification';
import alertsService, { ALERT_STATUSES } from '../../services/alertsService';

/**
 * AlertsManager Component
 * 
 * Manages and displays alert notifications
 */
const AlertsManager = () => {
  // State for active alerts
  const [activeAlerts, setActiveAlerts] = useState([]);
  
  // State for notification queue
  const [notificationQueue, setNotificationQueue] = useState([]);
  
  // Maximum number of notifications to show at once
  const maxNotifications = 3;
  
  // Check for triggered alerts
  useEffect(() => {
    // Get all active alerts
    const fetchAlerts = () => {
      const alerts = alertsService.getAlerts({ status: ALERT_STATUSES.TRIGGERED });
      setActiveAlerts(alerts);
      
      // Add newly triggered alerts to notification queue
      const newAlerts = alerts.filter(alert => 
        !notificationQueue.some(notification => notification.id === alert.id)
      );
      
      if (newAlerts.length > 0) {
        setNotificationQueue(prev => [...prev, ...newAlerts]);
      }
    };
    
    // Fetch alerts initially
    fetchAlerts();
    
    // Set up interval to check for new alerts
    const interval = setInterval(fetchAlerts, 30000); // Check every 30 seconds
    
    return () => clearInterval(interval);
  }, [notificationQueue]);
  
  // Handle notification close
  const handleNotificationClose = (alertId) => {
    // Remove from notification queue
    setNotificationQueue(prev => prev.filter(alert => alert.id !== alertId));
    
    // Mark alert as active (not triggered)
    const alert = activeAlerts.find(alert => alert.id === alertId);
    if (alert) {
      alertsService.updateAlert(alertId, { status: ALERT_STATUSES.ACTIVE });
    }
  };
  
  // Get notifications to display (limited by maxNotifications)
  const visibleNotifications = notificationQueue.slice(0, maxNotifications);
  
  // Request permission for browser notifications
  const requestNotificationPermission = async () => {
    if (!('Notification' in window)) {
      console.log('This browser does not support desktop notifications');
      return;
    }
    
    if (Notification.permission !== 'granted' && Notification.permission !== 'denied') {
      await Notification.requestPermission();
    }
  };
  
  // Show browser notification
  const showBrowserNotification = (alert) => {
    if (!('Notification' in window) || Notification.permission !== 'granted') {
      return;
    }
    
    // Format alert message
    const title = `${alert.type.charAt(0).toUpperCase() + alert.type.slice(1)} Alert`;
    const options = {
      body: alert.message || `Alert for ${alert.symbol}`,
      icon: '/logo192.png', // Assuming you have this icon
      tag: alert.id,
      requireInteraction: alert.priority === 'high'
    };
    
    // Create and show notification
    const notification = new Notification(title, options);
    
    // Handle notification click
    notification.onclick = () => {
      window.focus();
      notification.close();
    };
  };
  
  // Show notifications for new alerts
  useEffect(() => {
    // Request notification permission
    requestNotificationPermission();
    
    // Show browser notifications for new alerts
    activeAlerts.forEach(alert => {
      if (alert.notificationMethods && alert.notificationMethods.includes('browser')) {
        showBrowserNotification(alert);
      }
    });
  }, [activeAlerts]);

  return (
    <div className="alerts-manager">
      {/* Render visible notifications */}
      {visibleNotifications.map((alert, index) => (
        <div 
          key={alert.id}
          className="mb-4"
          style={{ 
            position: 'fixed',
            bottom: `${(index * 120) + 16}px`,
            right: '16px',
            zIndex: 1000 - index
          }}
        >
          <AlertNotification 
            alert={alert}
            onClose={() => handleNotificationClose(alert.id)}
            autoClose={true}
            autoCloseDelay={5000 + (index * 1000)} // Stagger auto-close times
          />
        </div>
      ))}
      
      {/* Notification counter badge (if there are more notifications than we can show) */}
      {notificationQueue.length > maxNotifications && (
        <div className="fixed bottom-4 left-4 bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center shadow-lg z-50">
          +{notificationQueue.length - maxNotifications}
        </div>
      )}
    </div>
  );
};

export default AlertsManager;
