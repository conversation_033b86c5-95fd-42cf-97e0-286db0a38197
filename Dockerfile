# Use official Node.js LTS image
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Install dependencies (including devDependencies for build)
COPY package*.json ./
RUN npm install

# Copy the rest of the app
COPY . .

# Build frontend
RUN npm run build

# Remove devDependencies for smaller image
RUN npm prune --production

# Expose the port your app runs on
EXPOSE 3000

# Start the server
CMD ["npm", "run", "start"] 