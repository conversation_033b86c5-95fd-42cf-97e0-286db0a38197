import React from 'react';
import Modal from './ui/Modal.jsx';
import Button from './ui/Button.jsx';

const FAQ = [
  { q: 'How do I create a trading signal?', a: 'Go to the Signals section and click the "Create Signal" button.' },
  { q: 'How do I change my profile or settings?', a: 'Click the user icon in the top bar to open your profile and settings.' },
  { q: 'How do I view notifications?', a: 'Click the bell icon in the top bar to see your notifications.' },
];

export default function HelpModal({ open, onClose, onStartTour }) {
  if (!open) return null;
  return (
    <Modal open={open} onClose={onClose}>
      <div className="w-full max-w-md p-6">
        <h2 className="text-xl font-bold mb-4">Help & Support</h2>
        <div className="mb-4">
          <h3 className="font-semibold mb-2">Frequently Asked Questions</h3>
          <ul className="space-y-2">
            {FAQ.map((item, i) => (
              <li key={i}>
                <div className="font-medium text-gray-800 dark:text-gray-100">Q: {item.q}</div>
                <div className="text-gray-600 dark:text-gray-300 ml-2">A: {item.a}</div>
              </li>
            ))}
          </ul>
        </div>
        <div className="mb-4">
          <h3 className="font-semibold mb-2">Contact & Feedback</h3>
          <p className="text-gray-600 dark:text-gray-300 mb-2">For support or feedback, email <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 underline"><EMAIL></a>.</p>
          <a href="https://forms.gle/your-feedback-form" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 underline">Submit Feedback</a>
        </div>
        <div className="flex justify-end gap-2 mt-6">
          <Button type="button" onClick={onStartTour}>Start Tour</Button>
          <Button type="button" onClick={onClose} variant="secondary">Close</Button>
        </div>
        <div className="mt-6 text-center text-xs text-gray-500 dark:text-gray-400">
          <a href="/privacy-policy.html" target="_blank" rel="noopener noreferrer" className="underline mr-4">Privacy Policy</a>
          <a href="/terms.html" target="_blank" rel="noopener noreferrer" className="underline">Terms of Service</a>
        </div>
      </div>
    </Modal>
  );
} 