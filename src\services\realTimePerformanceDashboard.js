/**
 * Real-time Performance Dashboard Service for Trading Signals App
 * 
 * Provides comprehensive real-time monitoring of WebSocket connections,
 * system performance metrics, and data throughput visualization.
 */

const { EventEmitter } = require('events');
const { structuredLogger } = require('../utils/structuredLogger');

class RealTimePerformanceDashboard extends EventEmitter {
  constructor(webSocketService, options = {}) {
    super();
    
    this.wsService = webSocketService;
    this.options = {
      updateInterval: 1000,        // 1 second updates
      historyLength: 300,          // 5 minutes of history
      alertThresholds: {
        latency: 1000,             // 1 second
        errorRate: 0.05,           // 5%
        connectionDropRate: 0.1,   // 10%
        memoryUsage: 0.8,          // 80%
        cpuUsage: 0.8              // 80%
      },
      ...options
    };

    // Performance data storage
    this.metrics = {
      websocket: {
        connections: [],
        latency: [],
        throughput: [],
        errors: [],
        quality: []
      },
      system: {
        memory: [],
        cpu: [],
        network: []
      },
      application: {
        signalGeneration: [],
        apiCalls: [],
        databaseQueries: [],
        cacheHits: []
      }
    };

    // Real-time statistics
    this.currentStats = {
      timestamp: Date.now(),
      websocket: {
        activeConnections: 0,
        averageLatency: 0,
        messagesPerSecond: 0,
        bytesPerSecond: 0,
        errorRate: 0,
        qualityDistribution: {}
      },
      system: {
        memoryUsage: 0,
        cpuUsage: 0,
        networkIO: 0
      },
      alerts: []
    };

    // Dashboard subscribers
    this.dashboardClients = new Set();

    this.setupEventListeners();
    this.startPerformanceMonitoring();
    this.startSystemMonitoring();

    structuredLogger.info('Real-time performance dashboard initialized', {
      component: 'performance-dashboard',
      updateInterval: this.options.updateInterval
    });
  }

  /**
   * Setup event listeners for WebSocket service
   */
  setupEventListeners() {
    if (this.wsService) {
      this.wsService.on('performanceUpdate', (metrics) => {
        this.updateWebSocketMetrics(metrics);
      });

      this.wsService.on('connectionQualityUpdate', (data) => {
        this.updateConnectionQuality(data);
      });
    }
  }

  /**
   * Start performance monitoring loop
   */
  startPerformanceMonitoring() {
    setInterval(() => {
      this.collectMetrics();
      this.analyzePerformance();
      this.broadcastUpdate();
    }, this.options.updateInterval);
  }

  /**
   * Start system monitoring
   */
  startSystemMonitoring() {
    setInterval(() => {
      this.collectSystemMetrics();
    }, this.options.updateInterval);
  }

  /**
   * Collect comprehensive metrics
   */
  collectMetrics() {
    const timestamp = Date.now();

    // Collect WebSocket metrics
    if (this.wsService) {
      const wsMetrics = this.wsService.getPerformanceMetrics();
      this.updateWebSocketMetrics(wsMetrics);
    }

    // Update current stats timestamp
    this.currentStats.timestamp = timestamp;
  }

  /**
   * Update WebSocket metrics
   */
  updateWebSocketMetrics(metrics) {
    const timestamp = Date.now();

    // Store historical data
    this.addMetricPoint('websocket.connections', timestamp, metrics.activeConnections);
    this.addMetricPoint('websocket.latency', timestamp, metrics.averageLatency);
    this.addMetricPoint('websocket.throughput', timestamp, metrics.messagesPerSecond);
    this.addMetricPoint('websocket.errors', timestamp, metrics.errors);

    // Update current stats
    this.currentStats.websocket = {
      activeConnections: metrics.activeConnections,
      averageLatency: metrics.averageLatency,
      messagesPerSecond: metrics.messagesPerSecond,
      bytesPerSecond: metrics.bytesTransferred / (metrics.uptime / 1000),
      errorRate: metrics.errors / Math.max(metrics.totalConnections, 1),
      qualityDistribution: metrics.connectionQuality?.distribution || {},
      compressionRatio: metrics.compressionRatio,
      batchEfficiency: metrics.averageBatchSize
    };
  }

  /**
   * Collect system metrics
   */
  collectSystemMetrics() {
    const timestamp = Date.now();

    // Memory usage
    const memUsage = process.memoryUsage();
    const memoryUsagePercent = memUsage.heapUsed / memUsage.heapTotal;
    
    this.addMetricPoint('system.memory', timestamp, memoryUsagePercent);
    this.currentStats.system.memoryUsage = memoryUsagePercent;

    // CPU usage (approximation using process.cpuUsage())
    const cpuUsage = process.cpuUsage();
    const cpuPercent = (cpuUsage.user + cpuUsage.system) / 1000000; // Convert to seconds
    
    this.addMetricPoint('system.cpu', timestamp, cpuPercent);
    this.currentStats.system.cpuUsage = cpuPercent;

    // Network I/O (if available)
    // This would typically require additional system monitoring tools
    this.addMetricPoint('system.network', timestamp, 0);
    this.currentStats.system.networkIO = 0;
  }

  /**
   * Add metric point to historical data
   */
  addMetricPoint(metricPath, timestamp, value) {
    const pathParts = metricPath.split('.');
    let current = this.metrics;
    
    for (let i = 0; i < pathParts.length - 1; i++) {
      current = current[pathParts[i]];
    }
    
    const metricArray = current[pathParts[pathParts.length - 1]];
    metricArray.push({ timestamp, value });
    
    // Trim to history length
    if (metricArray.length > this.options.historyLength) {
      metricArray.splice(0, metricArray.length - this.options.historyLength);
    }
  }

  /**
   * Analyze performance and generate alerts
   */
  analyzePerformance() {
    const alerts = [];
    const thresholds = this.options.alertThresholds;

    // Check latency
    if (this.currentStats.websocket.averageLatency > thresholds.latency) {
      alerts.push({
        type: 'warning',
        metric: 'latency',
        message: `High WebSocket latency: ${this.currentStats.websocket.averageLatency.toFixed(2)}ms`,
        value: this.currentStats.websocket.averageLatency,
        threshold: thresholds.latency,
        timestamp: Date.now()
      });
    }

    // Check error rate
    if (this.currentStats.websocket.errorRate > thresholds.errorRate) {
      alerts.push({
        type: 'error',
        metric: 'error_rate',
        message: `High error rate: ${(this.currentStats.websocket.errorRate * 100).toFixed(2)}%`,
        value: this.currentStats.websocket.errorRate,
        threshold: thresholds.errorRate,
        timestamp: Date.now()
      });
    }

    // Check memory usage
    if (this.currentStats.system.memoryUsage > thresholds.memoryUsage) {
      alerts.push({
        type: 'warning',
        metric: 'memory_usage',
        message: `High memory usage: ${(this.currentStats.system.memoryUsage * 100).toFixed(2)}%`,
        value: this.currentStats.system.memoryUsage,
        threshold: thresholds.memoryUsage,
        timestamp: Date.now()
      });
    }

    // Check CPU usage
    if (this.currentStats.system.cpuUsage > thresholds.cpuUsage) {
      alerts.push({
        type: 'warning',
        metric: 'cpu_usage',
        message: `High CPU usage: ${(this.currentStats.system.cpuUsage * 100).toFixed(2)}%`,
        value: this.currentStats.system.cpuUsage,
        threshold: thresholds.cpuUsage,
        timestamp: Date.now()
      });
    }

    this.currentStats.alerts = alerts;

    // Log critical alerts
    alerts.forEach(alert => {
      if (alert.type === 'error') {
        structuredLogger.error('Performance alert triggered', {
          component: 'performance-dashboard',
          alert
        });
      } else {
        structuredLogger.warn('Performance warning', {
          component: 'performance-dashboard',
          alert
        });
      }
    });
  }

  /**
   * Broadcast update to dashboard clients
   */
  broadcastUpdate() {
    const update = {
      type: 'performance_update',
      data: this.getDashboardData(),
      timestamp: Date.now()
    };

    // Broadcast to WebSocket clients subscribed to dashboard
    if (this.wsService) {
      this.wsService.broadcastToChannel('performance-dashboard', update, {}, {
        highFrequency: true
      });
    }

    // Emit event for other listeners
    this.emit('dashboardUpdate', update);
  }

  /**
   * Get dashboard data for visualization
   */
  getDashboardData() {
    return {
      current: this.currentStats,
      history: this.getHistoricalData(),
      summary: this.generateSummary(),
      recommendations: this.generateRecommendations()
    };
  }

  /**
   * Get historical data for charts
   */
  getHistoricalData() {
    const timeRange = 300000; // 5 minutes
    const now = Date.now();
    const cutoff = now - timeRange;

    return {
      websocket: {
        connections: this.filterByTime(this.metrics.websocket.connections, cutoff),
        latency: this.filterByTime(this.metrics.websocket.latency, cutoff),
        throughput: this.filterByTime(this.metrics.websocket.throughput, cutoff),
        errors: this.filterByTime(this.metrics.websocket.errors, cutoff)
      },
      system: {
        memory: this.filterByTime(this.metrics.system.memory, cutoff),
        cpu: this.filterByTime(this.metrics.system.cpu, cutoff),
        network: this.filterByTime(this.metrics.system.network, cutoff)
      }
    };
  }

  /**
   * Filter metrics by time
   */
  filterByTime(metrics, cutoff) {
    return metrics.filter(point => point.timestamp >= cutoff);
  }

  /**
   * Generate performance summary
   */
  generateSummary() {
    const wsMetrics = this.metrics.websocket;
    
    return {
      websocket: {
        peakConnections: this.getMaxValue(wsMetrics.connections),
        averageLatency: this.getAverageValue(wsMetrics.latency),
        totalErrors: this.getSumValue(wsMetrics.errors),
        uptimePercentage: this.calculateUptime()
      },
      system: {
        peakMemoryUsage: this.getMaxValue(this.metrics.system.memory),
        averageCpuUsage: this.getAverageValue(this.metrics.system.cpu),
        totalNetworkIO: this.getSumValue(this.metrics.system.network)
      }
    };
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations() {
    const recommendations = [];

    // WebSocket recommendations
    if (this.currentStats.websocket.averageLatency > 500) {
      recommendations.push({
        type: 'optimization',
        category: 'websocket',
        message: 'Consider enabling message batching to reduce latency',
        priority: 'medium'
      });
    }

    if (this.currentStats.websocket.errorRate > 0.02) {
      recommendations.push({
        type: 'reliability',
        category: 'websocket',
        message: 'High error rate detected - review connection handling',
        priority: 'high'
      });
    }

    // System recommendations
    if (this.currentStats.system.memoryUsage > 0.7) {
      recommendations.push({
        type: 'resource',
        category: 'system',
        message: 'Memory usage is high - consider implementing garbage collection tuning',
        priority: 'medium'
      });
    }

    return recommendations;
  }

  /**
   * Utility methods for metric calculations
   */
  getMaxValue(metrics) {
    return metrics.length > 0 ? Math.max(...metrics.map(m => m.value)) : 0;
  }

  getAverageValue(metrics) {
    if (metrics.length === 0) return 0;
    return metrics.reduce((sum, m) => sum + m.value, 0) / metrics.length;
  }

  getSumValue(metrics) {
    return metrics.reduce((sum, m) => sum + m.value, 0);
  }

  calculateUptime() {
    // Calculate uptime based on error rate and connection stability
    const errorRate = this.currentStats.websocket.errorRate;
    return Math.max(0, (1 - errorRate) * 100);
  }

  /**
   * Subscribe to dashboard updates
   */
  subscribe(callback) {
    this.on('dashboardUpdate', callback);
  }

  /**
   * Unsubscribe from dashboard updates
   */
  unsubscribe(callback) {
    this.off('dashboardUpdate', callback);
  }

  /**
   * Get current performance snapshot
   */
  getSnapshot() {
    return {
      timestamp: Date.now(),
      metrics: this.currentStats,
      alerts: this.currentStats.alerts,
      summary: this.generateSummary()
    };
  }

  /**
   * Reset metrics (for testing or maintenance)
   */
  resetMetrics() {
    this.metrics = {
      websocket: {
        connections: [],
        latency: [],
        throughput: [],
        errors: [],
        quality: []
      },
      system: {
        memory: [],
        cpu: [],
        network: []
      },
      application: {
        signalGeneration: [],
        apiCalls: [],
        databaseQueries: [],
        cacheHits: []
      }
    };

    structuredLogger.info('Performance dashboard metrics reset', {
      component: 'performance-dashboard'
    });
  }
}

module.exports = { RealTimePerformanceDashboard };
