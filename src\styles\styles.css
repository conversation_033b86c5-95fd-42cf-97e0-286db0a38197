/**
 * Main styles for Trading Signals App
 */

:root {
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --success-color: #198754;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #0dcaf0;
  --light-color: #f8f9fa;
  --dark-color: #212529;
  
  --body-bg: #f8f9fa;
  --body-color: #212529;
  --card-bg: #ffffff;
  --card-border: #dee2e6;
  --input-bg: #ffffff;
  --input-border: #ced4da;
  
  --chart-grid-color: rgba(0, 0, 0, 0.1);
  --chart-text-color: rgba(0, 0, 0, 0.7);
}

[data-theme="dark"] {
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --success-color: #198754;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #0dcaf0;
  --light-color: #f8f9fa;
  --dark-color: #212529;
  
  --body-bg: #121212;
  --body-color: #e0e0e0;
  --card-bg: #1e1e1e;
  --card-border: #333333;
  --input-bg: #2c2c2c;
  --input-border: #444444;
  
  --chart-grid-color: rgba(255, 255, 255, 0.1);
  --chart-text-color: rgba(255, 255, 255, 0.7);
}

body {
  background-color: var(--body-bg);
  color: var(--body-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.card {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.form-control, .form-select {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--body-color);
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Chart container */
.chart-container {
  position: relative;
  height: 400px;
  width: 100%;
  margin-bottom: 1rem;
}

/* Trading signals */
.signal-card {
  border-left-width: 4px;
  transition: transform 0.2s ease;
}

.signal-card:hover {
  transform: translateY(-2px);
}

.signal-buy {
  border-left-color: var(--success-color);
}

.signal-sell {
  border-left-color: var(--danger-color);
}

/* Economic calendar */
.event-high {
  border-left: 4px solid var(--danger-color);
}

.event-medium {
  border-left: 4px solid var(--warning-color);
}

.event-low {
  border-left: 4px solid var(--info-color);
}

/* Loading spinner */
.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s ease;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0px); }
}

.btn:hover {
  animation: float 2s ease infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chart-container {
    height: 300px;
  }
}

@media (max-width: 576px) {
  .chart-container {
    height: 250px;
  }
}

/* NProgress styles */
#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  background: var(--primary-color);
  position: fixed;
  z-index: 1031;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
}

#nprogress .peg {
  display: block;
  position: absolute;
  right: 0px;
  width: 100px;
  height: 100%;
  box-shadow: 0 0 10px var(--primary-color), 0 0 5px var(--primary-color);
  opacity: 1.0;
  transform: rotate(3deg) translate(0px, -4px);
}

/* Service worker notification */
.sw-notification {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 4px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
  padding: 15px;
  max-width: 350px;
  animation: fadeIn 0.3s ease;
}

.sw-notification .notification-content {
  display: flex;
  flex-direction: column;
}

.sw-notification h4 {
  margin-top: 0;
  margin-bottom: 5px;
  color: var(--primary-color);
}

.sw-notification p {
  margin-bottom: 10px;
}

.sw-notification .notification-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.sw-notification button {
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.sw-notification .btn-update {
  background-color: var(--primary-color);
  color: white;
}

.sw-notification .btn-dismiss {
  background-color: var(--secondary-color);
  color: white;
}

/* Offline indicator */
.offline-indicator {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background-color: var(--warning-color);
  color: black;
  padding: 10px 15px;
  border-radius: 4px;
  z-index: 1000;
  display: none;
}

body.offline .offline-indicator {
  display: block;
}

/* Tooltip styles */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 120px;
  background-color: var(--dark-color);
  color: var(--light-color);
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}
