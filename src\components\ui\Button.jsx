import React from 'react';
export default function Button({ children, type = 'button', size = 'md', className = '', ...props }) {
  const sizeClass = size === 'sm' ? 'px-3 py-1 text-sm' : size === 'lg' ? 'px-6 py-3 text-lg' : 'px-4 py-2';
  return (
    <button
      type={type}
      className={`rounded font-semibold transition bg-navy-500 text-white hover:bg-navy-700 shadow-trading ${sizeClass} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
}
