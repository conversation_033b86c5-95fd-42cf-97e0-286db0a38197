/**
 * Economic Calendar module for the Trading Signals App
 *
 * This file contains functions for fetching and displaying economic events
 * that may impact the financial markets.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize economic calendar
    initEconomicCalendar();

    // Add event listener for refresh button
    const refreshCalendarBtn = document.getElementById('refreshCalendarBtn');
    if (refreshCalendarBtn) {
        refreshCalendarBtn.addEventListener('click', function() {
            refreshEconomicCalendar();
        });
    }

    // Add event listener for filter changes
    const calendarFilterSelect = document.getElementById('calendarFilter');
    if (calendarFilterSelect) {
        calendarFilterSelect.addEventListener('change', function() {
            filterEconomicCalendar(this.value);
        });
    }
});

// Function to initialize economic calendar
function initEconomicCalendar() {
    // Get current date
    const today = new Date();
    const formattedDate = formatDate(today);

    // Update date display
    const calendarDateElement = document.getElementById('calendarDate');
    if (calendarDateElement) {
        calendarDateElement.textContent = formattedDate;
    }

    // Load economic events
    loadEconomicEvents();
}

// Function to refresh economic calendar
function refreshEconomicCalendar() {
    // Show loading indicator
    const calendarBody = document.getElementById('calendarBody');
    if (calendarBody) {
        calendarBody.innerHTML = '<tr><td colspan="5" class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></td></tr>';
    }

    // Load economic events
    loadEconomicEvents();
}

// Function to filter economic calendar
function filterEconomicCalendar(filter) {
    // Get all event rows
    const eventRows = document.querySelectorAll('#calendarBody tr');

    // Show/hide rows based on filter
    eventRows.forEach(row => {
        const importance = row.getAttribute('data-importance');

        if (filter === 'all' || filter === importance) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Function to load economic events
async function loadEconomicEvents() {
    try {
        // Show loading indicator
        const calendarBody = document.getElementById('calendarBody');
        if (calendarBody) {
            calendarBody.innerHTML = '<tr><td colspan="5" class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></td></tr>';
        }

        // Fetch economic events from the API
        const response = await fetch('/api/economic-calendar');

        if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
        }

        const data = await response.json();

        // Check if we have events
        if (data && data.events && data.events.length > 0) {
            // Display events from API
            displayEconomicEvents(data.events);
        } else {
            // Fallback to sample data if no events
            console.warn('No economic events found in API response, using sample data');
            const events = getSampleEconomicEvents();
            displayEconomicEvents(events);
        }
    } catch (error) {
        console.error('Error loading economic events:', error);

        // Fallback to sample data on error
        const events = getSampleEconomicEvents();
        displayEconomicEvents(events);

        // Show error message
        const calendarStatus = document.getElementById('calendarStatus');
        if (calendarStatus) {
            calendarStatus.innerHTML = `<div class="alert alert-warning">Failed to load real-time data. Showing sample data instead.</div>`;
        }
    }
}

// Function to display economic events
function displayEconomicEvents(events) {
    const calendarBody = document.getElementById('calendarBody');
    if (!calendarBody) return;

    // Clear existing events
    calendarBody.innerHTML = '';

    // Check if there are events
    if (events.length === 0) {
        calendarBody.innerHTML = '<tr><td colspan="5" class="text-center">No economic events found for today</td></tr>';
        return;
    }

    // Sort events by time
    events.sort((a, b) => {
        return new Date('1970/01/01 ' + a.time) - new Date('1970/01/01 ' + b.time);
    });

    // Add events to table
    events.forEach(event => {
        const row = document.createElement('tr');
        row.setAttribute('data-importance', event.importance);

        // Set row class based on importance
        if (event.importance === 'high') {
            row.classList.add('table-danger');
        } else if (event.importance === 'medium') {
            row.classList.add('table-warning');
        }

        // Create time cell
        const timeCell = document.createElement('td');
        timeCell.textContent = event.time;
        row.appendChild(timeCell);

        // Create currency cell
        const currencyCell = document.createElement('td');
        currencyCell.textContent = event.currency;
        row.appendChild(currencyCell);

        // Create event cell
        const eventCell = document.createElement('td');
        eventCell.textContent = event.event;
        row.appendChild(eventCell);

        // Create importance cell
        const importanceCell = document.createElement('td');
        const importanceBadge = document.createElement('span');
        importanceBadge.classList.add('badge');

        if (event.importance === 'high') {
            importanceBadge.classList.add('bg-danger');
            importanceBadge.textContent = 'High';
        } else if (event.importance === 'medium') {
            importanceBadge.classList.add('bg-warning', 'text-dark');
            importanceBadge.textContent = 'Medium';
        } else {
            importanceBadge.classList.add('bg-secondary');
            importanceBadge.textContent = 'Low';
        }

        importanceCell.appendChild(importanceBadge);
        row.appendChild(importanceCell);

        // Create forecast cell
        const forecastCell = document.createElement('td');
        forecastCell.textContent = event.forecast;
        row.appendChild(forecastCell);

        // Add row to table
        calendarBody.appendChild(row);
    });
}

// Function to format date
function formatDate(date) {
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
}

// Function to get sample economic events
function getSampleEconomicEvents() {
    return [
        {
            time: '08:30',
            currency: 'USD',
            event: 'Non-Farm Payrolls',
            importance: 'high',
            forecast: '200K'
        },
        {
            time: '10:00',
            currency: 'EUR',
            event: 'ECB Interest Rate Decision',
            importance: 'high',
            forecast: '4.50%'
        },
        {
            time: '12:30',
            currency: 'GBP',
            event: 'Manufacturing PMI',
            importance: 'medium',
            forecast: '52.3'
        },
        {
            time: '14:00',
            currency: 'USD',
            event: 'ISM Manufacturing PMI',
            importance: 'medium',
            forecast: '49.8'
        },
        {
            time: '15:30',
            currency: 'USD',
            event: 'Crude Oil Inventories',
            importance: 'medium',
            forecast: '-2.1M'
        },
        {
            time: '18:00',
            currency: 'USD',
            event: 'FOMC Meeting Minutes',
            importance: 'high',
            forecast: 'N/A'
        },
        {
            time: '23:50',
            currency: 'JPY',
            event: 'GDP (QoQ)',
            importance: 'medium',
            forecast: '0.3%'
        },
        {
            time: '01:30',
            currency: 'AUD',
            event: 'Retail Sales',
            importance: 'medium',
            forecast: '0.2%'
        },
        {
            time: '03:45',
            currency: 'CNY',
            event: 'Caixin Services PMI',
            importance: 'low',
            forecast: '51.5'
        }
    ];
}

// Function to get upcoming high-impact events
async function getUpcomingHighImpactEvents() {
    try {
        // Try to fetch from API first
        const response = await fetch('/api/economic-calendar');

        if (response.ok) {
            const data = await response.json();

            if (data && data.events && data.events.length > 0) {
                // Filter high-impact events
                const highImpactEvents = data.events.filter(event => event.importance === 'high');

                // Sort by time
                highImpactEvents.sort((a, b) => {
                    return new Date('1970/01/01 ' + a.time) - new Date('1970/01/01 ' + b.time);
                });

                // Get current time
                const now = new Date();
                const currentHour = now.getHours();
                const currentMinute = now.getMinutes();

                // Filter upcoming events
                const upcomingEvents = highImpactEvents.filter(event => {
                    const [hour, minute] = event.time.split(':').map(Number);

                    if (hour > currentHour) {
                        return true;
                    } else if (hour === currentHour && minute > currentMinute) {
                        return true;
                    }

                    return false;
                });

                return upcomingEvents.slice(0, 3); // Return next 3 events
            }
        }
    } catch (error) {
        console.warn('Error fetching upcoming events from API:', error);
    }

    // Fallback to sample data if API fails or returns no events
    const events = getSampleEconomicEvents();

    // Filter high-impact events
    const highImpactEvents = events.filter(event => event.importance === 'high');

    // Sort by time
    highImpactEvents.sort((a, b) => {
        return new Date('1970/01/01 ' + a.time) - new Date('1970/01/01 ' + b.time);
    });

    // Get current time
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    // Filter upcoming events
    const upcomingEvents = highImpactEvents.filter(event => {
        const [hour, minute] = event.time.split(':').map(Number);

        if (hour > currentHour) {
            return true;
        } else if (hour === currentHour && minute > currentMinute) {
            return true;
        }

        return false;
    });

    return upcomingEvents.slice(0, 3); // Return next 3 events
}

// Export functions
window.economicCalendar = {
    initEconomicCalendar,
    refreshEconomicCalendar,
    filterEconomicCalendar,
    getUpcomingHighImpactEvents
};
