import calendarService from '../services/calendarService.js';

export const getUpcomingEvents = async (req, res, next) => {
  try {
    const { country, impact } = req.query;
    const events = await calendarService.getUpcomingEvents({ country, impact });
    res.json({ status: 'success', data: events });
  } catch (error) {
    next(error);
  }
};

export const getHistoricalEvents = async (req, res, next) => {
  try {
    const { event, country } = req.query;
    const events = await calendarService.getHistoricalEvents({ event, country });
    res.json({ status: 'success', data: events });
  } catch (error) {
    next(error);
  }
};

export default {
  getUpcomingEvents,
  getHistoricalEvents,
}; 