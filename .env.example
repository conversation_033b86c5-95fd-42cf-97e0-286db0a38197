# Trading Signals App - Environment Variables Example
# Copy this file to .env and replace the values with your own

# Node environment: development, production, test
NODE_ENV=development

# Server port
PORT=3000

# Logging
LOG_LEVEL=debug # debug, info, warn, error

# MongoDB connection
MONGODB_URI=mongodb://localhost:27017/trading-signals

# Redis configuration
USE_REDIS=false # set to true to use Redis
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600 # Cache TTL in seconds (1 hour)

# CORS configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# API Keys for various data providers (replace with your own)
ALPHA_VANTAGE_API_KEY=your_key_here
TWELVE_DATA_API_KEY=your_key_here
FINNHUB_API_KEY=your_key_here
FRED_API_KEY=your_key_here
POLYGON_API_KEY=your_key_here
FMP_API_KEY=your_key_here

# Security
JWT_SECRET=change_this_to_a_secure_random_string
RATE_LIMIT_WINDOW_MS=900000 # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Optional features
ENABLE_MOCK_DATA=true # Use mock data if API/DB unavailable

# ===== PHASE 1: DATABASE OPTIMIZATION & CACHING =====

# Database Connection Pool Settings
DB_MIN_POOL_SIZE=5
DB_MAX_POOL_SIZE=20

# Database Performance Monitoring
DB_MONITORING_ENABLED=true
DB_SLOW_QUERY_THRESHOLD=100
DB_CRITICAL_QUERY_THRESHOLD=500
DB_METRICS_INTERVAL=30000
DB_ALERTS_ENABLED=true

# Redis Cache Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Cache TTL Settings (in seconds)
CACHE_TTL_MARKET_DATA=30
CACHE_TTL_TECHNICAL_INDICATORS=300
CACHE_TTL_AI_ANALYSIS=600
CACHE_TTL_USER_SESSIONS=86400
CACHE_TTL_NEWS_SENTIMENT=1800

# ===== AI SERVICES CONFIGURATION =====
# OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here

# Circuit Breaker Configuration
AI_CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
AI_CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60000

# API Circuit Breaker
API_CIRCUIT_BREAKER_FAILURE_THRESHOLD=3
API_CIRCUIT_BREAKER_RECOVERY_TIMEOUT=30000

# ===== API KEY ROTATION =====
# Alpha Vantage API Keys (for rotation)
ALPHA_VANTAGE_KEY_1=OFU3DJH5JWW6Z29Z
ALPHA_VANTAGE_KEY_2=your_second_alpha_vantage_key
ALPHA_VANTAGE_KEY_3=your_third_alpha_vantage_key

# Twelve Data API Keys (for rotation)
TWELVE_DATA_KEY_1=your_twelve_data_key_1
TWELVE_DATA_KEY_2=your_twelve_data_key_2
TWELVE_DATA_KEY_3=your_twelve_data_key_3

# Finnhub API Keys (for rotation)
FINNHUB_KEY_1=your_finnhub_key_1
FINNHUB_KEY_2=your_finnhub_key_2

# FRED API Key
FRED_API_KEY=de959b1589a8cd6ed0772b9127853054

# ===== PERFORMANCE MONITORING =====
# Request timeout settings (in milliseconds)
REQUEST_TIMEOUT=30000
API_TIMEOUT=10000
DATABASE_TIMEOUT=5000

# Concurrency limits
MAX_CONCURRENT_REQUESTS=100
MAX_CONCURRENT_AI_REQUESTS=10

# ===== STRIPE CONFIGURATION =====
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret