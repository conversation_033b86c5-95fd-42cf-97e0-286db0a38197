@echo off
SETLOCAL enabledelayedexpansion

echo ===================================================
echo     Trading Signals App - Directory Setup
echo ===================================================
echo.

echo Creating project directories...

:: Create main directories
if not exist "logs" mkdir logs
if not exist "public" mkdir public
if not exist "utils" mkdir utils
if not exist "cache" mkdir cache
if not exist "logging" mkdir logging
if not exist "database" mkdir database
if not exist "config" mkdir config
if not exist "api-clients" mkdir api-clients
if not exist "fallback" mkdir fallback
if not exist "tests" mkdir tests
if not exist ".github" mkdir .github
if not exist "archived_files" mkdir archived_files

:: Create subdirectories
if not exist "public\js" mkdir public\js
if not exist "public\css" mkdir public\css
if not exist "public\icons" mkdir public\icons
if not exist "public\tests" mkdir public\tests
if not exist "tests\unit" mkdir tests\unit
if not exist "tests\integration" mkdir tests\integration

echo.
echo The following directories have been created:
echo.
echo - logs (Application logs)
echo - public (Static frontend files)
echo   - js (JavaScript files)
echo   - css (Stylesheets)
echo   - icons (Icons and images)
echo   - tests (Frontend test files)
echo - utils (Utility modules)
echo - cache (Caching module)
echo - logging (Logging module)
echo - database (Database module)
echo - config (Configuration files)
echo - api-clients (API clients for external services)
echo - fallback (Fallback handlers)
echo - tests (Test files)
echo   - unit (Unit tests)
echo   - integration (Integration tests)
echo - .github (GitHub configuration)
echo - archived_files (Archive of old files)
echo.

echo Directory structure setup complete!
echo.
echo Next steps:
echo 1. Run install-dependencies.bat to install required packages
echo 2. Configure your environment variables in .env file
echo 3. Start the application with npm start or npm run dev
echo.
echo ===================================================

ENDLOCAL 