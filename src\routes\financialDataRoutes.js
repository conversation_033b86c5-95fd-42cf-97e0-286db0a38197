/**
 * Financial Data Routes for Trading Signals App
 * 
 * This file provides routes for accessing financial data with:
 * - Enhanced rate-limit handling
 * - Data storage capabilities
 * - Webhook endpoints
 * - Dynamic parameter customization
 */

const express = require('express');
const { protect, authorize, rateLimiter } = require('../middleware/auth');
const logger = require('../utils/logger');

// Create router
const router = express.Router();

/**
 * Set up financial data routes
 * @param {Object} app - Express app
 * @param {Object} financialDataService - Financial Data Service instance
 */
module.exports = (app, financialDataService) => {
  /**
   * Get market data
   * 
   * @route GET /api/financial/market/:symbol
   * @group Financial Data - Financial data endpoints
   * @param {string} symbol.path.required - Trading symbol
   * @param {string} interval.query - Time interval (default: 1min)
   * @param {string} startDate.query - Start date (ISO format)
   * @param {string} endDate.query - End date (ISO format)
   * @param {string} provider.query - Data provider (default: ALPHA_VANTAGE)
   * @returns {Object} 200 - Market data response
   * @returns {Error} 400 - Bad request
   * @returns {Error} 401 - Unauthorized
   * @returns {Error} 429 - Too many requests
   * @returns {Error} 500 - Server error
   */
  router.get('/market/:symbol', protect, rateLimiter, async (req, res, next) => {
    try {
      const { symbol } = req.params;
      const { interval = '1min', startDate, endDate, provider } = req.query;
      
      logger.debug('Fetching market data:', { symbol, interval, startDate, endDate, provider });
      
      // Validate symbol
      if (!symbol) {
        return res.status(400).json({
          status: 'error',
          message: 'Symbol is required'
        });
      }
      
      // Prepare options
      const options = {
        provider: provider || 'ALPHA_VANTAGE'
      };
      
      // Add date range if provided
      if (startDate && endDate) {
        options.outputsize = 'full';
        options.startDate = startDate;
        options.endDate = endDate;
      }
      
      // Fetch data
      const data = await financialDataService.fetchMarketData(symbol, interval, options);
      
      res.json({
        status: 'success',
        data
      });
    } catch (error) {
      next(error);
    }
  });
  
  /**
   * Get quote data
   * 
   * @route GET /api/financial/quote/:symbol
   * @group Financial Data - Financial data endpoints
   * @param {string} symbol.path.required - Trading symbol
   * @param {string} provider.query - Data provider (default: ALPHA_VANTAGE)
   * @returns {Object} 200 - Quote data response
   * @returns {Error} 400 - Bad request
   * @returns {Error} 401 - Unauthorized
   * @returns {Error} 429 - Too many requests
   * @returns {Error} 500 - Server error
   */
  router.get('/quote/:symbol', protect, rateLimiter, async (req, res, next) => {
    try {
      const { symbol } = req.params;
      const { provider } = req.query;
      
      logger.debug('Fetching quote data:', { symbol, provider });
      
      // Validate symbol
      if (!symbol) {
        return res.status(400).json({
          status: 'error',
          message: 'Symbol is required'
        });
      }
      
      // Prepare options
      const options = {
        provider: provider || 'ALPHA_VANTAGE'
      };
      
      // Fetch data
      const data = await financialDataService.fetchQuote(symbol, options);
      
      res.json({
        status: 'success',
        data
      });
    } catch (error) {
      next(error);
    }
  });
  
  /**
   * Get historical data from database
   * 
   * @route GET /api/financial/historical/:symbol
   * @group Financial Data - Financial data endpoints
   * @param {string} symbol.path.required - Trading symbol
   * @param {string} timeframe.query - Time interval (default: 1min)
   * @param {string} startDate.query - Start date (ISO format)
   * @param {string} endDate.query - End date (ISO format)
   * @param {number} limit.query - Maximum number of data points (default: 100)
   * @returns {Object} 200 - Historical data response
   * @returns {Error} 400 - Bad request
   * @returns {Error} 401 - Unauthorized
   * @returns {Error} 429 - Too many requests
   * @returns {Error} 500 - Server error
   */
  router.get('/historical/:symbol', protect, rateLimiter, async (req, res, next) => {
    try {
      const { symbol } = req.params;
      const { 
        timeframe = '1min', 
        startDate, 
        endDate, 
        limit = 100 
      } = req.query;
      
      logger.debug('Fetching historical data:', { symbol, timeframe, startDate, endDate, limit });
      
      // Validate symbol
      if (!symbol) {
        return res.status(400).json({
          status: 'error',
          message: 'Symbol is required'
        });
      }
      
      // Check if we have the repository
      if (!financialDataService.repositories.marketData) {
        return res.status(500).json({
          status: 'error',
          message: 'Database not available'
        });
      }
      
      // Prepare dates
      const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
      const end = endDate ? new Date(endDate) : new Date();
      
      // Fetch data from database
      const data = await financialDataService.repositories.marketData.findByDateRange(
        symbol,
        timeframe,
        start,
        end,
        { limit: parseInt(limit) }
      );
      
      res.json({
        status: 'success',
        data: {
          symbol,
          timeframe,
          startDate: start.toISOString(),
          endDate: end.toISOString(),
          count: data.length,
          data
        }
      });
    } catch (error) {
      next(error);
    }
  });
  
  /**
   * Get aggregated OHLC data
   * 
   * @route GET /api/financial/ohlc/:symbol
   * @group Financial Data - Financial data endpoints
   * @param {string} symbol.path.required - Trading symbol
   * @param {string} baseTimeframe.query - Base timeframe (default: 1min)
   * @param {string} aggregateTimeframe.query - Target timeframe (default: 1hour)
   * @param {string} startDate.query - Start date (ISO format)
   * @param {string} endDate.query - End date (ISO format)
   * @returns {Object} 200 - Aggregated OHLC data response
   * @returns {Error} 400 - Bad request
   * @returns {Error} 401 - Unauthorized
   * @returns {Error} 429 - Too many requests
   * @returns {Error} 500 - Server error
   */
  router.get('/ohlc/:symbol', protect, rateLimiter, async (req, res, next) => {
    try {
      const { symbol } = req.params;
      const { 
        baseTimeframe = '1min', 
        aggregateTimeframe = '1hour', 
        startDate, 
        endDate 
      } = req.query;
      
      logger.debug('Fetching aggregated OHLC data:', { 
        symbol, baseTimeframe, aggregateTimeframe, startDate, endDate 
      });
      
      // Validate symbol
      if (!symbol) {
        return res.status(400).json({
          status: 'error',
          message: 'Symbol is required'
        });
      }
      
      // Check if we have the repository
      if (!financialDataService.repositories.marketData) {
        return res.status(500).json({
          status: 'error',
          message: 'Database not available'
        });
      }
      
      // Prepare dates
      const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
      const end = endDate ? new Date(endDate) : new Date();
      
      // Fetch aggregated data
      const data = await financialDataService.repositories.marketData.getAggregatedOHLC(
        symbol,
        baseTimeframe,
        aggregateTimeframe,
        start,
        end
      );
      
      res.json({
        status: 'success',
        data: {
          symbol,
          baseTimeframe,
          aggregateTimeframe,
          startDate: start.toISOString(),
          endDate: end.toISOString(),
          count: data.length,
          data
        }
      });
    } catch (error) {
      next(error);
    }
  });
  
  /**
   * Get available symbols
   * 
   * @route GET /api/financial/symbols
   * @group Financial Data - Financial data endpoints
   * @returns {Object} 200 - Available symbols response
   * @returns {Error} 401 - Unauthorized
   * @returns {Error} 429 - Too many requests
   * @returns {Error} 500 - Server error
   */
  router.get('/symbols', protect, rateLimiter, async (req, res, next) => {
    try {
      logger.debug('Fetching available symbols');
      
      // Check if we have the repository
      if (!financialDataService.repositories.marketData) {
        return res.status(500).json({
          status: 'error',
          message: 'Database not available'
        });
      }
      
      // Fetch symbols
      const symbols = await financialDataService.repositories.marketData.getAvailableSymbols();
      
      res.json({
        status: 'success',
        data: {
          count: symbols.length,
          symbols
        }
      });
    } catch (error) {
      next(error);
    }
  });
  
  /**
   * Get API service status
   * 
   * @route GET /api/financial/status
   * @group Financial Data - Financial data endpoints
   * @returns {Object} 200 - API service status response
   * @returns {Error} 401 - Unauthorized
   * @returns {Error} 500 - Server error
   */
  router.get('/status', protect, async (req, res, next) => {
    try {
      logger.debug('Fetching API service status');
      
      // Get status
      const status = {
        apiService: financialDataService.apiService.getStatus(),
        webSocket: financialDataService.webSocketService ? 
                  financialDataService.webSocketService.getStats() : null,
        webhook: financialDataService.webhookService ? 
                financialDataService.webhookService.getStats() : null,
        timestamp: new Date().toISOString()
      };
      
      res.json({
        status: 'success',
        data: status
      });
    } catch (error) {
      next(error);
    }
  });
  
  /**
   * Webhook endpoint
   * 
   * @route POST /api/financial/webhook
   * @group Financial Data - Financial data endpoints
   * @param {Object} req.body - Webhook payload
   * @returns {Object} 200 - Webhook processing result
   * @returns {Error} 400 - Bad request
   * @returns {Error} 500 - Server error
   */
  router.post('/webhook', express.json(), async (req, res, next) => {
    try {
      logger.debug('Received webhook request');
      
      // Check if webhook service is available
      if (!financialDataService.webhookService) {
        return res.status(500).json({
          status: 'error',
          message: 'Webhook service not available'
        });
      }
      
      // Process webhook
      const result = await financialDataService.webhookService.processIncomingWebhook(req);
      
      if (result.success) {
        res.json({
          status: 'success',
          data: result
        });
      } else {
        res.status(400).json({
          status: 'error',
          message: result.error
        });
      }
    } catch (error) {
      next(error);
    }
  });
  
  // Mount the router
  app.use('/api/financial', router);
  
  logger.info('Financial data routes initialized');
  
  return router;
};

module.exports.router = router;
