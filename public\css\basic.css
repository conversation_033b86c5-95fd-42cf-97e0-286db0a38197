/* Basic CSS styles for all pages */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h1, h2, h3, h4, h5, h6 {
    color: #333;
    margin-top: 0;
}

p {
    color: #666;
    margin-bottom: 20px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

.btn {
    display: inline-block;
    padding: 10px 15px;
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-size: 16px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #0056b3;
    text-decoration: none;
}

.btn-secondary {
    background-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.text-center {
    text-align: center;
}

.mb-20 {
    margin-bottom: 20px;
}

.mt-20 {
    margin-top: 20px;
}

/* Navigation styles */
.navbar {
    background-color: #343a40;
    padding: 15px 20px;
    color: #fff;
    margin-bottom: 20px;
}

.navbar a {
    color: #fff;
    margin-right: 15px;
}

.navbar a:hover {
    color: #ccc;
}

/* Form styles */
.form-group {
    margin-bottom: 15px;
}

.form-control {
    display: block;
    width: 100%;
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

table, th, td {
    border: 1px solid #dee2e6;
}

th, td {
    padding: 12px;
    text-align: left;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
}

tr:nth-child(even) {
    background-color: #f2f2f2;
}

/* Responsive media queries */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
} } 
 
h1, h2, h3, h4, h5, h6 { 
    color: #333; 
    margin-top: 0; 
} 
 
p { 
    color: #666; 
    margin-bottom: 20px; 
} 
 
a { 
    color: #007bff; 
    text-decoration: none; 
} 
 
a:hover { 
    text-decoration: underline; 
} 
 
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    background-color: #007bff; 
    color: #fff; 
    border: none; 
    border-radius: 4px; 
    cursor: pointer; 
    text-decoration: none; 
    font-size: 16px; 
    transition: background-color 0.3s; 
} 
 
.btn:hover { 
    background-color: #0056b3; 
    text-decoration: none; 
} 
 
.btn-secondary { 
    background-color: #6c757d; 
} 
 
.btn-secondary:hover { 
    background-color: #5a6268; 
} 
 
.alert { 
    padding: 15px; 
    margin-bottom: 20px; 
    border-radius: 4px; 
} 
 
.alert-info { 
    background-color: #d1ecf1; 
    color: #0c5460; 
    border: 1px solid #bee5eb; 
} 
 
.text-center { 
    text-align: center; 
} 
 
.mt-20 { 
    margin-top: 20px; 
} 
} 
 
h1, h2, h3, h4, h5, h6 { 
    color: #333; 
    margin-top: 0; 
} 
 
p { 
    color: #666; 
    margin-bottom: 20px; 
} 
 
a { 
    color: #007bff; 
    text-decoration: none; 
} 
 
a:hover { 
    text-decoration: underline; 
} 
 
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    background-color: #007bff; 
    color: #fff; 
    border: none; 
    border-radius: 4px; 
    cursor: pointer; 
    text-decoration: none; 
    font-size: 16px; 
    transition: background-color 0.3s; 
} 
 
.btn:hover { 
    background-color: #0056b3; 
    text-decoration: none; 
} 
 
.btn-secondary { 
    background-color: #6c757d; 
} 
 
.btn-secondary:hover { 
    background-color: #5a6268; 
} 
 
.alert { 
    padding: 15px; 
    margin-bottom: 20px; 
    border-radius: 4px; 
} 
 
.alert-info { 
    background-color: #d1ecf1; 
    color: #0c5460; 
    border: 1px solid #bee5eb; 
} 
 
.text-center { 
    text-align: center; 
} 
 
.mt-20 { 
    margin-top: 20px; 
} 
} 
 
h1, h2, h3, h4, h5, h6 { 
    color: #333; 
    margin-top: 0; 
} 
 
p { 
    color: #666; 
    margin-bottom: 20px; 
} 
 
a { 
    color: #007bff; 
    text-decoration: none; 
} 
 
a:hover { 
    text-decoration: underline; 
} 
 
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    background-color: #007bff; 
    color: #fff; 
    border: none; 
    border-radius: 4px; 
    cursor: pointer; 
    text-decoration: none; 
    font-size: 16px; 
    transition: background-color 0.3s; 
} 
 
.btn:hover { 
    background-color: #0056b3; 
    text-decoration: none; 
} 
 
.btn-secondary { 
    background-color: #6c757d; 
} 
 
.btn-secondary:hover { 
    background-color: #5a6268; 
} 
 
.alert { 
    padding: 15px; 
    margin-bottom: 20px; 
    border-radius: 4px; 
} 
 
.alert-info { 
    background-color: #d1ecf1; 
    color: #0c5460; 
    border: 1px solid #bee5eb; 
} 
 
.text-center { 
    text-align: center; 
} 
 
.mt-20 { 
    margin-top: 20px; 
} 
} 
 
h1, h2, h3, h4, h5, h6 { 
    color: #333; 
    margin-top: 0; 
} 
 
p { 
    color: #666; 
    margin-bottom: 20px; 
} 
 
a { 
    color: #007bff; 
    text-decoration: none; 
} 
 
a:hover { 
    text-decoration: underline; 
} 
 
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    background-color: #007bff; 
    color: #fff; 
    border: none; 
    border-radius: 4px; 
    cursor: pointer; 
    text-decoration: none; 
    font-size: 16px; 
    transition: background-color 0.3s; 
} 
 
.btn:hover { 
    background-color: #0056b3; 
    text-decoration: none; 
} 
 
.btn-secondary { 
    background-color: #6c757d; 
} 
 
.btn-secondary:hover { 
    background-color: #5a6268; 
} 
 
.alert { 
    padding: 15px; 
    margin-bottom: 20px; 
    border-radius: 4px; 
} 
 
.alert-info { 
    background-color: #d1ecf1; 
    color: #0c5460; 
    border: 1px solid #bee5eb; 
} 
 
.text-center { 
    text-align: center; 
} 
 
.mt-20 { 
    margin-top: 20px; 
} 
} 
 
h1, h2, h3, h4, h5, h6 { 
    color: #333; 
    margin-top: 0; 
} 
 
p { 
    color: #666; 
    margin-bottom: 20px; 
} 
 
a { 
    color: #007bff; 
    text-decoration: none; 
} 
 
a:hover { 
    text-decoration: underline; 
} 
 
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    background-color: #007bff; 
    color: #fff; 
    border: none; 
    border-radius: 4px; 
    cursor: pointer; 
    text-decoration: none; 
    font-size: 16px; 
    transition: background-color 0.3s; 
} 
 
.btn:hover { 
    background-color: #0056b3; 
    text-decoration: none; 
} 
 
.btn-secondary { 
    background-color: #6c757d; 
} 
 
.btn-secondary:hover { 
    background-color: #5a6268; 
} 
 
.alert { 
    padding: 15px; 
    margin-bottom: 20px; 
    border-radius: 4px; 
} 
 
.alert-info { 
    background-color: #d1ecf1; 
    color: #0c5460; 
    border: 1px solid #bee5eb; 
} 
 
.text-center { 
    text-align: center; 
} 
 
.mt-20 { 
    margin-top: 20px; 
} 
} 
 
h1, h2, h3, h4, h5, h6 { 
    color: #333; 
    margin-top: 0; 
} 
 
p { 
    color: #666; 
    margin-bottom: 20px; 
} 
 
a { 
    color: #007bff; 
    text-decoration: none; 
} 
 
a:hover { 
    text-decoration: underline; 
} 
 
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    background-color: #007bff; 
    color: #fff; 
    border: none; 
    border-radius: 4px; 
    cursor: pointer; 
    text-decoration: none; 
    font-size: 16px; 
    transition: background-color 0.3s; 
} 
 
.btn:hover { 
    background-color: #0056b3; 
    text-decoration: none; 
} 
 
.btn-secondary { 
    background-color: #6c757d; 
} 
 
.btn-secondary:hover { 
    background-color: #5a6268; 
} 
 
.alert { 
    padding: 15px; 
    margin-bottom: 20px; 
    border-radius: 4px; 
} 
 
.alert-info { 
    background-color: #d1ecf1; 
    color: #0c5460; 
    border: 1px solid #bee5eb; 
} 
 
.text-center { 
    text-align: center; 
} 
 
.mt-20 { 
    margin-top: 20px; 
} 
} 
 
h1, h2, h3, h4, h5, h6 { 
    color: #333; 
    margin-top: 0; 
} 
 
p { 
    color: #666; 
    margin-bottom: 20px; 
} 
 
a { 
    color: #007bff; 
    text-decoration: none; 
} 
 
a:hover { 
    text-decoration: underline; 
} 
 
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    background-color: #007bff; 
    color: #fff; 
    border: none; 
    border-radius: 4px; 
    cursor: pointer; 
    text-decoration: none; 
    font-size: 16px; 
    transition: background-color 0.3s; 
} 
 
.btn:hover { 
    background-color: #0056b3; 
    text-decoration: none; 
} 
 
.btn-secondary { 
    background-color: #6c757d; 
} 
 
.btn-secondary:hover { 
    background-color: #5a6268; 
} 
 
.alert { 
    padding: 15px; 
    margin-bottom: 20px; 
    border-radius: 4px; 
} 
 
.alert-info { 
    background-color: #d1ecf1; 
    color: #0c5460; 
    border: 1px solid #bee5eb; 
} 
 
.text-center { 
    text-align: center; 
} 
 
.mt-20 { 
    margin-top: 20px; 
} 
} 
 
h1, h2, h3, h4, h5, h6 { 
    color: #333; 
    margin-top: 0; 
} 
 
p { 
    color: #666; 
    margin-bottom: 20px; 
} 
 
a { 
    color: #007bff; 
    text-decoration: none; 
} 
 
a:hover { 
    text-decoration: underline; 
} 
 
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    background-color: #007bff; 
    color: #fff; 
    border: none; 
    border-radius: 4px; 
    cursor: pointer; 
    text-decoration: none; 
    font-size: 16px; 
    transition: background-color 0.3s; 
} 
 
.btn:hover { 
    background-color: #0056b3; 
    text-decoration: none; 
} 
 
.btn-secondary { 
    background-color: #6c757d; 
} 
 
.btn-secondary:hover { 
    background-color: #5a6268; 
} 
 
.alert { 
    padding: 15px; 
    margin-bottom: 20px; 
    border-radius: 4px; 
} 
 
.alert-info { 
    background-color: #d1ecf1; 
    color: #0c5460; 
    border: 1px solid #bee5eb; 
} 
 
.text-center { 
    text-align: center; 
} 
 
.mt-20 { 
    margin-top: 20px; 
} 
} 
 
h1, h2, h3, h4, h5, h6 { 
    color: #333; 
    margin-top: 0; 
} 
 
p { 
    color: #666; 
    margin-bottom: 20px; 
} 
 
a { 
    color: #007bff; 
    text-decoration: none; 
} 
 
a:hover { 
    text-decoration: underline; 
} 
 
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    background-color: #007bff; 
    color: #fff; 
    border: none; 
    border-radius: 4px; 
    cursor: pointer; 
    text-decoration: none; 
    font-size: 16px; 
    transition: background-color 0.3s; 
} 
 
.btn:hover { 
    background-color: #0056b3; 
    text-decoration: none; 
} 
 
.btn-secondary { 
    background-color: #6c757d; 
} 
 
.btn-secondary:hover { 
    background-color: #5a6268; 
} 
 
.alert { 
    padding: 15px; 
    margin-bottom: 20px; 
    border-radius: 4px; 
} 
 
.alert-info { 
    background-color: #d1ecf1; 
    color: #0c5460; 
    border: 1px solid #bee5eb; 
} 
 
.text-center { 
    text-align: center; 
} 
 
.mt-20 { 
    margin-top: 20px; 
} 
} 
 
h1, h2, h3, h4, h5, h6 { 
    color: #333; 
    margin-top: 0; 
} 
 
p { 
    color: #666; 
    margin-bottom: 20px; 
} 
 
a { 
    color: #007bff; 
    text-decoration: none; 
} 
 
a:hover { 
    text-decoration: underline; 
} 
 
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    background-color: #007bff; 
    color: #fff; 
    border: none; 
    border-radius: 4px; 
    cursor: pointer; 
    text-decoration: none; 
    font-size: 16px; 
    transition: background-color 0.3s; 
} 
 
.btn:hover { 
    background-color: #0056b3; 
    text-decoration: none; 
} 
 
.btn-secondary { 
    background-color: #6c757d; 
} 
 
.btn-secondary:hover { 
    background-color: #5a6268; 
} 
 
.alert { 
    padding: 15px; 
    margin-bottom: 20px; 
    border-radius: 4px; 
} 
 
.alert-info { 
    background-color: #d1ecf1; 
    color: #0c5460; 
    border: 1px solid #bee5eb; 
} 
 
.text-center { 
    text-align: center; 
} 
 
.mt-20 { 
    margin-top: 20px; 
} 
