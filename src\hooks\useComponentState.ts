/**
 * Component State Management Hook
 * 
 * Comprehensive hook for managing component state with integrated
 * loading, error, and empty state handling. Provides standardized
 * patterns for data fetching and state management.
 * 
 * Features:
 * - Integrated loading, error, and empty states
 * - Automatic error handling and reporting
 * - Retry mechanisms with exponential backoff
 * - Data validation and transformation
 * - Performance optimization with caching
 * - Accessibility support
 * - TypeScript support with generics
 * 
 * @version 1.0.0
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useError, useApiError } from '../context/ErrorContext';
import { api } from '../services/enhancedApiService';

// ============================================================================
// INTERFACES
// ============================================================================

export interface ComponentState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  isEmpty: boolean;
  lastFetch: string | null;
  retryCount: number;
}

export interface UseComponentStateOptions<T> {
  // Data fetching
  fetchFn?: () => Promise<T>;
  initialData?: T;
  
  // Validation and transformation
  validateData?: (data: any) => boolean;
  transformData?: (data: any) => T;
  
  // Error handling
  onError?: (error: Error) => void;
  onSuccess?: (data: T) => void;
  
  // Retry configuration
  enableRetry?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  
  // Caching
  enableCache?: boolean;
  cacheKey?: string;
  cacheTimeout?: number;
  
  // Behavior
  fetchOnMount?: boolean;
  refetchOnWindowFocus?: boolean;
  refetchInterval?: number;
  
  // Empty state detection
  isEmptyFn?: (data: T) => boolean;
}

export interface UseAsyncDataOptions<T> extends UseComponentStateOptions<T> {
  url?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  requestData?: any;
  dependencies?: any[];
}

export interface UseFormStateOptions {
  initialValues: Record<string, any>;
  validationSchema?: Record<string, (value: any) => string | null>;
  onSubmit: (values: Record<string, any>) => Promise<void>;
  resetOnSuccess?: boolean;
}

// ============================================================================
// MAIN COMPONENT STATE HOOK
// ============================================================================

export function useComponentState<T>(options: UseComponentStateOptions<T> = {}) {
  const {
    fetchFn,
    initialData = null,
    validateData,
    transformData,
    onError,
    onSuccess,
    enableRetry = true,
    maxRetries = 3,
    retryDelay = 1000,
    enableCache = false,
    cacheKey,
    cacheTimeout = 300000, // 5 minutes
    fetchOnMount = true,
    refetchOnWindowFocus = false,
    refetchInterval,
    isEmptyFn
  } = options;

  const { reportError } = useError();
  const { handleApiError } = useApiError();

  // State
  const [state, setState] = useState<ComponentState<T>>({
    data: initialData,
    loading: false,
    error: null,
    isEmpty: false,
    lastFetch: null,
    retryCount: 0
  });

  // Refs
  const retryTimeoutRef = useRef<NodeJS.Timeout>();
  const refetchIntervalRef = useRef<NodeJS.Timeout>();
  const mountedRef = useRef(true);

  // ========================================================================
  // CACHE MANAGEMENT
  // ========================================================================

  const getCachedData = useCallback((): T | null => {
    if (!enableCache || !cacheKey) return null;

    try {
      const cached = localStorage.getItem(`cache_${cacheKey}`);
      if (!cached) return null;

      const { data, timestamp } = JSON.parse(cached);
      const isExpired = Date.now() - timestamp > cacheTimeout;

      return isExpired ? null : data;
    } catch {
      return null;
    }
  }, [enableCache, cacheKey, cacheTimeout]);

  const setCachedData = useCallback((data: T): void => {
    if (!enableCache || !cacheKey) return;

    try {
      const cacheData = {
        data,
        timestamp: Date.now()
      };
      localStorage.setItem(`cache_${cacheKey}`, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Failed to cache data:', error);
    }
  }, [enableCache, cacheKey]);

  // ========================================================================
  // DATA FETCHING
  // ========================================================================

  const fetchData = useCallback(async (isRetry = false): Promise<void> => {
    if (!fetchFn || !mountedRef.current) return;

    // Check cache first
    if (!isRetry) {
      const cachedData = getCachedData();
      if (cachedData) {
        setState(prev => ({
          ...prev,
          data: cachedData,
          loading: false,
          error: null,
          isEmpty: isEmptyFn ? isEmptyFn(cachedData) : false,
          lastFetch: new Date().toISOString()
        }));
        return;
      }
    }

    setState(prev => ({
      ...prev,
      loading: true,
      error: null
    }));

    try {
      let data = await fetchFn();

      // Validate data
      if (validateData && !validateData(data)) {
        throw new Error('Data validation failed');
      }

      // Transform data
      if (transformData) {
        data = transformData(data);
      }

      if (!mountedRef.current) return;

      // Update state
      setState(prev => ({
        ...prev,
        data,
        loading: false,
        error: null,
        isEmpty: isEmptyFn ? isEmptyFn(data) : (Array.isArray(data) ? data.length === 0 : !data),
        lastFetch: new Date().toISOString(),
        retryCount: isRetry ? prev.retryCount : 0
      }));

      // Cache data
      setCachedData(data);

      // Call success callback
      onSuccess?.(data);

    } catch (error) {
      if (!mountedRef.current) return;

      const apiError = error as Error;

      setState(prev => ({
        ...prev,
        loading: false,
        error: apiError,
        retryCount: isRetry ? prev.retryCount + 1 : 0
      }));

      // Handle error
      handleApiError(apiError, { action: 'data-fetch' });
      onError?.(apiError);

      // Retry if enabled
      if (enableRetry && state.retryCount < maxRetries && !isRetry) {
        const delay = retryDelay * Math.pow(2, state.retryCount);
        retryTimeoutRef.current = setTimeout(() => {
          fetchData(true);
        }, delay);
      }
    }
  }, [
    fetchFn,
    validateData,
    transformData,
    onSuccess,
    onError,
    enableRetry,
    maxRetries,
    retryDelay,
    state.retryCount,
    getCachedData,
    setCachedData,
    handleApiError,
    isEmptyFn
  ]);

  // ========================================================================
  // RETRY MECHANISM
  // ========================================================================

  const retry = useCallback((): void => {
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
    fetchData(true);
  }, [fetchData]);

  const reset = useCallback((): void => {
    setState({
      data: initialData,
      loading: false,
      error: null,
      isEmpty: false,
      lastFetch: null,
      retryCount: 0
    });

    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
  }, [initialData]);

  // ========================================================================
  // EFFECTS
  // ========================================================================

  // Initial fetch
  useEffect(() => {
    if (fetchOnMount && fetchFn) {
      fetchData();
    }
  }, [fetchOnMount, fetchFn, fetchData]);

  // Refetch interval
  useEffect(() => {
    if (refetchInterval && fetchFn) {
      refetchIntervalRef.current = setInterval(() => {
        fetchData();
      }, refetchInterval);

      return () => {
        if (refetchIntervalRef.current) {
          clearInterval(refetchIntervalRef.current);
        }
      };
    }
  }, [refetchInterval, fetchFn, fetchData]);

  // Window focus refetch
  useEffect(() => {
    if (refetchOnWindowFocus && fetchFn) {
      const handleFocus = () => fetchData();
      window.addEventListener('focus', handleFocus);
      return () => window.removeEventListener('focus', handleFocus);
    }
  }, [refetchOnWindowFocus, fetchFn, fetchData]);

  // Cleanup
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (refetchIntervalRef.current) {
        clearInterval(refetchIntervalRef.current);
      }
    };
  }, []);

  return {
    ...state,
    refetch: fetchData,
    retry,
    reset,
    canRetry: enableRetry && state.retryCount < maxRetries
  };
}

// ============================================================================
// ASYNC DATA HOOK
// ============================================================================

export function useAsyncData<T>(options: UseAsyncDataOptions<T>) {
  const {
    url,
    method = 'GET',
    requestData,
    dependencies = [],
    ...componentStateOptions
  } = options;

  const fetchFn = useCallback(async (): Promise<T> => {
    if (!url) throw new Error('URL is required for async data fetching');

    switch (method) {
      case 'GET':
        return api.get<T>(url);
      case 'POST':
        return api.post<T>(url, requestData);
      case 'PUT':
        return api.put<T>(url, requestData);
      case 'DELETE':
        return api.delete<T>(url);
      default:
        throw new Error(`Unsupported method: ${method}`);
    }
  }, [url, method, requestData]);

  const state = useComponentState<T>({
    ...componentStateOptions,
    fetchFn: url ? fetchFn : undefined
  });

  // Refetch when dependencies change
  useEffect(() => {
    if (url && dependencies.length > 0) {
      state.refetch();
    }
  }, dependencies);

  return state;
}

// ============================================================================
// FORM STATE HOOK
// ============================================================================

export function useFormState(options: UseFormStateOptions) {
  const {
    initialValues,
    validationSchema = {},
    onSubmit,
    resetOnSuccess = true
  } = options;

  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { handleValidationError } = useError();

  const validateField = useCallback((name: string, value: any): string | null => {
    const validator = validationSchema[name];
    return validator ? validator(value) : null;
  }, [validationSchema]);

  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    Object.keys(values).forEach(name => {
      const error = validateField(name, values[name]);
      if (error) {
        newErrors[name] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  }, [values, validateField]);

  const setValue = useCallback((name: string, value: any): void => {
    setValues(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  }, [errors]);

  const setFieldTouched = useCallback((name: string, isTouched = true): void => {
    setTouched(prev => ({ ...prev, [name]: isTouched }));
    
    // Validate field when touched
    if (isTouched) {
      const error = validateField(name, values[name]);
      if (error) {
        setErrors(prev => ({ ...prev, [name]: error }));
      }
    }
  }, [validateField, values]);

  const handleSubmit = useCallback(async (event?: React.FormEvent): Promise<void> => {
    event?.preventDefault();

    if (isSubmitting) return;

    // Mark all fields as touched
    const allTouched = Object.keys(values).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {} as Record<string, boolean>);
    setTouched(allTouched);

    // Validate form
    if (!validateForm()) {
      handleValidationError(errors, 'form-submission');
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit(values);
      
      if (resetOnSuccess) {
        setValues(initialValues);
        setErrors({});
        setTouched({});
      }
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [
    values,
    errors,
    isSubmitting,
    validateForm,
    onSubmit,
    resetOnSuccess,
    initialValues,
    handleValidationError
  ]);

  const reset = useCallback((): void => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  }, [initialValues]);

  return {
    values,
    errors,
    touched,
    isSubmitting,
    setValue,
    setFieldTouched,
    handleSubmit,
    reset,
    isValid: Object.keys(errors).length === 0,
    isDirty: JSON.stringify(values) !== JSON.stringify(initialValues)
  };
}

// ============================================================================
// CONVENIENCE HOOKS
// ============================================================================

export function useSignalsData(symbol?: string) {
  return useAsyncData({
    url: symbol ? `/signals?symbol=${symbol}` : '/signals',
    cacheKey: `signals-${symbol || 'all'}`,
    enableCache: true,
    cacheTimeout: 60000, // 1 minute
    refetchInterval: 30000, // 30 seconds
    isEmptyFn: (data: any[]) => data.length === 0,
    dependencies: [symbol]
  });
}

export function useMarketData(symbol: string, interval = '1h') {
  return useAsyncData({
    url: `/market-data/${symbol}?interval=${interval}`,
    cacheKey: `market-data-${symbol}-${interval}`,
    enableCache: true,
    cacheTimeout: 300000, // 5 minutes
    isEmptyFn: (data: any) => !data || Object.keys(data).length === 0,
    dependencies: [symbol, interval]
  });
}

export function useUserSettings() {
  return useAsyncData({
    url: '/user/settings',
    cacheKey: 'user-settings',
    enableCache: true,
    cacheTimeout: 600000 // 10 minutes
  });
}
