import notificationService from '../services/notificationService.js';
import { APIError } from '../middleware/errorHandler.js';

export const getNotifications = async (req, res, next) => {
  try {
    const notifications = await notificationService.getUserNotifications(req.user.id);
    res.json({ status: 'success', data: notifications });
  } catch (error) {
    next(error);
  }
};

export const createNotification = async (req, res, next) => {
  try {
    const { type, message, link } = req.body;
    const notification = await notificationService.createNotification({
      user: req.user.id,
      type,
      message,
      link,
    });
    res.status(201).json({ status: 'success', data: notification });
  } catch (error) {
    next(error);
  }
};

export const markAsRead = async (req, res, next) => {
  try {
    const notification = await notificationService.markAsRead(req.params.id, req.user.id);
    if (!notification) return next(new APIError(404, 'Notification not found'));
    res.json({ status: 'success', data: notification });
  } catch (error) {
    next(error);
  }
};

export const markAllAsRead = async (req, res, next) => {
  try {
    await notificationService.markAllAsRead(req.user.id);
    res.json({ status: 'success', message: 'All notifications marked as read' });
  } catch (error) {
    next(error);
  }
};

export const deleteNotification = async (req, res, next) => {
  try {
    await notificationService.deleteNotification(req.params.id, req.user.id);
    res.json({ status: 'success', message: 'Notification deleted' });
  } catch (error) {
    next(error);
  }
};

export default {
  getNotifications,
  createNotification,
  markAsRead,
  markAllAsRead,
  deleteNotification,
};
