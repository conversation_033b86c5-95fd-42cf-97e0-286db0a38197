import React, { useState } from 'react';
import PatternBacktestingForm from './PatternBacktestingForm';
import PatternBacktestResults from './PatternBacktestResults';
import PatternOptimizer from './PatternOptimizer';
import { alphaVantageAPI } from '../../../services/apiService';
import patternStrategyService from '../../../services/patternStrategyService';
import patternRecognitionService from '../../../services/patternRecognitionService';
import mlPatternService from '../../../services/mlPatternService';

/**
 * PatternBacktesting Component
 *
 * Component for backtesting trading strategies based on candlestick and chart patterns
 */
const PatternBacktesting = () => {
  // State for backtest results
  const [results, setResults] = useState(null);

  // State for loading status
  const [loading, setLoading] = useState(false);

  // State for error message
  const [error, setError] = useState(null);

  // Run pattern backtest with the given configuration
  const runPatternBacktest = async (config) => {
    setLoading(true);
    setError(null);

    try {
      // Fetch historical data for the selected symbol and timeframe
      const historicalData = await fetchHistoricalData(config.symbol, config.timeframe, config.startDate, config.endDate);

      // Detect patterns in the historical data
      const patterns = await detectPatterns(historicalData, config);

      // Run the pattern backtest with the fetched data and detected patterns
      const backtestResults = await runPatternStrategy(historicalData, patterns, config);

      // Add historical data and patterns to results for visualization
      backtestResults.historicalData = historicalData;
      backtestResults.patterns = patterns;

      // Set the results
      setResults(backtestResults);
    } catch (error) {
      console.error('Error running pattern backtest:', error);
      setError('Failed to run pattern backtest. Please try again later.');

      // For demo purposes, generate mock results if API fails
      generateMockPatternResults(config);
    } finally {
      setLoading(false);
    }
  };

  // Detect patterns in the historical data
  const detectPatterns = async (data, config) => {
    try {
      const patterns = [];

      // Detect candlestick patterns
      if (config.patternTypes.includes('bullish') || config.patternTypes.includes('bearish')) {
        for (let i = 0; i < data.length - 5; i++) {
          // Get a slice of data for pattern detection
          const slice = data.slice(i, i + 5);

          // Detect candlestick patterns
          const candlestickPatterns = patternRecognitionService.detectCandlestickPatterns(slice);

          // Filter patterns by selected types and patterns
          const filteredPatterns = candlestickPatterns.filter(pattern => {
            // Check if pattern type is selected
            if (!config.patternTypes.includes(pattern.type)) return false;

            // Check if specific patterns are selected
            if (config.selectedPatterns.length > 0 && !config.selectedPatterns.includes(pattern.pattern)) return false;

            // Check if pattern significance meets minimum threshold
            if (pattern.significance < config.minPatternSignificance) return false;

            return true;
          });

          // Add index to patterns and add to results
          filteredPatterns.forEach(pattern => {
            patterns.push({
              ...pattern,
              index: i
            });
          });
        }
      }

      // Detect chart patterns
      if (config.patternTypes.includes('reversal') || config.patternTypes.includes('continuation')) {
        // Detect head and shoulders pattern
        const headAndShoulders = patternRecognitionService.checkHeadAndShoulders(data);
        if (headAndShoulders) patterns.push(headAndShoulders);

        // Detect double top pattern
        const doubleTop = patternRecognitionService.checkDoubleTop(data);
        if (doubleTop) patterns.push(doubleTop);

        // Detect double bottom pattern
        const doubleBottom = patternRecognitionService.checkDoubleBottom(data);
        if (doubleBottom) patterns.push(doubleBottom);

        // Detect triangle patterns
        const triangles = patternRecognitionService.checkTriangles(data);
        patterns.push(...triangles);

        // Detect channel patterns
        const channels = patternRecognitionService.checkChannels(data);
        patterns.push(...channels);

        // Detect flag patterns
        const flags = patternRecognitionService.checkFlags(data);
        patterns.push(...flags);

        // Detect pennant patterns
        const pennants = patternRecognitionService.checkPennants(data);
        patterns.push(...pennants);

        // Detect cup and handle pattern
        const cupAndHandle = patternRecognitionService.checkCupAndHandle(data);
        if (cupAndHandle) patterns.push(cupAndHandle);
      }

      // Use machine learning for pattern detection if enabled
      if (config.useML) {
        try {
          // Configure ML options
          const mlOptions = {
            modelType: config.mlModelType || 'cnn',
            confidence: config.mlConfidenceThreshold || 0.7
          };

          // Detect patterns using ML
          const mlPatterns = await mlPatternService.detectPatternsWithML(data, mlOptions);

          // Filter ML patterns
          const filteredMLPatterns = mlPatterns.filter(pattern => {
            // Check if pattern type is selected
            if (!config.patternTypes.includes(pattern.type)) return false;

            // Check if specific patterns are selected
            if (config.selectedPatterns.length > 0 && !config.selectedPatterns.includes(pattern.pattern)) return false;

            // Check if pattern significance meets minimum threshold
            if (pattern.significance < config.minPatternSignificance) return false;

            return true;
          });

          // Add ML patterns to results
          patterns.push(...filteredMLPatterns);
        } catch (mlError) {
          console.error('Error detecting patterns with ML:', mlError);
        }
      }

      return patterns;
    } catch (error) {
      console.error('Error detecting patterns:', error);
      return [];
    }
  };

  // Fetch historical data from API
  const fetchHistoricalData = async (symbol, timeframe, startDate, endDate) => {
    try {
      // Convert timeframe to Alpha Vantage interval
      const interval = convertTimeframeToInterval(timeframe);

      // Determine if we're dealing with forex, crypto, or stock
      let functionName = 'TIME_SERIES_INTRADAY';
      let dataKey = `Time Series (${interval})`;

      if (timeframe === 'D1') {
        functionName = 'TIME_SERIES_DAILY';
        dataKey = 'Time Series (Daily)';
      } else if (timeframe === 'W1') {
        functionName = 'TIME_SERIES_WEEKLY';
        dataKey = 'Weekly Time Series';
      } else if (timeframe === 'MN') {
        functionName = 'TIME_SERIES_MONTHLY';
        dataKey = 'Monthly Time Series';
      }

      // Handle different asset types
      if (symbol.includes('USD') && symbol.length === 6 && !symbol.includes('USD=')) {
        // Forex data
        const fromCurrency = symbol.substring(0, 3);
        const toCurrency = symbol.substring(3, 6);

        functionName = timeframe === 'D1' ? 'FX_DAILY' :
                      timeframe === 'W1' ? 'FX_WEEKLY' :
                      timeframe === 'MN' ? 'FX_MONTHLY' : 'FX_INTRADAY';

        dataKey = timeframe === 'D1' ? 'Time Series FX (Daily)' :
                 timeframe === 'W1' ? 'Time Series FX (Weekly)' :
                 timeframe === 'MN' ? 'Time Series FX (Monthly)' :
                 `Time Series FX (${interval})`;

        // Make API call
        const response = await alphaVantageAPI.get('query', {
          function: functionName,
          from_symbol: fromCurrency,
          to_symbol: toCurrency,
          interval: interval,
          outputsize: 'full',
          // Use the API service which already has the API key configured
          // This avoids hardcoding the API key directly in the code
        });

        return processHistoricalData(response, dataKey, startDate, endDate);
      } else if (symbol === 'BTCUSD' || symbol === 'ETHUSD') {
        // Crypto data
        const cryptoSymbol = symbol.substring(0, 3);

        functionName = timeframe === 'D1' ? 'DIGITAL_CURRENCY_DAILY' :
                      timeframe === 'W1' ? 'DIGITAL_CURRENCY_WEEKLY' :
                      timeframe === 'MN' ? 'DIGITAL_CURRENCY_MONTHLY' : 'CRYPTO_INTRADAY';

        dataKey = timeframe === 'D1' ? 'Time Series (Digital Currency Daily)' :
                 timeframe === 'W1' ? 'Time Series (Digital Currency Weekly)' :
                 timeframe === 'MN' ? 'Time Series (Digital Currency Monthly)' :
                 `Time Series Crypto (${interval})`;

        // Make API call
        const response = await alphaVantageAPI.get('query', {
          function: functionName,
          symbol: cryptoSymbol,
          market: 'USD',
          interval: interval,
          outputsize: 'full',
          // Use the API service which already has the API key configured
          // This avoids hardcoding the API key directly in the code
        });

        return processHistoricalData(response, dataKey, startDate, endDate, true);
      } else {
        // Stock data
        // Make API call
        const response = await alphaVantageAPI.get('query', {
          function: functionName,
          symbol: symbol,
          interval: interval,
          outputsize: 'full',
          // Use the API service which already has the API key configured
          // This avoids hardcoding the API key directly in the code
        });

        return processHistoricalData(response, dataKey, startDate, endDate);
      }
    } catch (error) {
      console.error('Error fetching historical data:', error);

      // Fallback to mock data if API fails
      return generateMockHistoricalData(symbol, timeframe, startDate, endDate);
    }
  };

  // Convert app timeframe to Alpha Vantage interval
  const convertTimeframeToInterval = (timeframe) => {
    const mapping = {
      'M1': '1min',
      'M5': '5min',
      'M15': '15min',
      'M30': '30min',
      'H1': '60min',
      'H4': '240min',
      'D1': 'daily',
      'W1': 'weekly',
      'MN': 'monthly'
    };

    return mapping[timeframe] || 'daily';
  };

  // Process historical data from API response
  const processHistoricalData = (response, dataKey, startDate, endDate, isCrypto = false) => {
    if (!response || !response[dataKey]) {
      console.warn('Invalid data received from API:', response);
      throw new Error('Invalid data received from API.');
    }

    const timeSeriesData = response[dataKey];
    const dates = Object.keys(timeSeriesData).sort();

    const startTimestamp = new Date(startDate).getTime();
    const endTimestamp = new Date(endDate).getTime();

    const filteredData = [];

    dates.forEach(date => {
      const timestamp = new Date(date).getTime();

      if (timestamp >= startTimestamp && timestamp <= endTimestamp) {
        const data = timeSeriesData[date];

        // Handle different API response formats
        let open, high, low, close, volume;

        if (isCrypto) {
          // Crypto data format
          open = parseFloat(data['1a. open (USD)'] || data['1. open (USD)'] || 0);
          high = parseFloat(data['2a. high (USD)'] || data['2. high (USD)'] || 0);
          low = parseFloat(data['3a. low (USD)'] || data['3. low (USD)'] || 0);
          close = parseFloat(data['4a. close (USD)'] || data['4. close (USD)'] || 0);
          volume = parseFloat(data['5. volume'] || data['6. volume'] || 0);
        } else {
          // Standard format for stocks and forex
          open = parseFloat(data['1. open'] || 0);
          high = parseFloat(data['2. high'] || 0);
          low = parseFloat(data['3. low'] || 0);
          close = parseFloat(data['4. close'] || 0);
          volume = parseFloat(data['5. volume'] || data['6. volume'] || 0);
        }

        filteredData.push({
          date,
          timestamp,
          open,
          high,
          low,
          close,
          volume,
          x: date, // For chart compatibility
          o: open,
          h: high,
          l: low,
          c: close,
          v: volume
        });
      }
    });

    // Sort by date (oldest first)
    filteredData.sort((a, b) => a.timestamp - b.timestamp);

    return filteredData;
  };

  // Run pattern strategy backtest
  const runPatternStrategy = async (data, patterns, config) => {
    try {
      // Use the pattern strategy service to run the backtest
      return patternStrategyService.backtestPatternStrategy(data, patterns, config);
    } catch (error) {
      console.error('Error running pattern strategy:', error);

      // For demo purposes, generate mock results
      return generateMockPatternResults(config);
    }
  };

  // Handle optimization application
  const handleApplyOptimization = (optimizationResults) => {
    if (!results || !optimizationResults) return;

    // In a real implementation, this would update the pattern detection parameters
    // and re-run the backtest with the optimized parameters

    // For demo purposes, we'll just update the results with improved performance
    const updatedResults = { ...results };

    // Update the performance for the optimized pattern
    updatedResults.patternPerformance = {
      ...updatedResults.patternPerformance,
      [optimizationResults.pattern]: optimizationResults.bestResult
    };

    // Recalculate overall performance metrics
    let totalTrades = 0;
    let winningTrades = 0;
    let grossProfit = 0;
    let grossLoss = 0;

    Object.values(updatedResults.patternPerformance).forEach(performance => {
      totalTrades += performance.totalTrades;
      winningTrades += performance.winningTrades;
      grossProfit += performance.grossProfit;
      grossLoss += performance.grossLoss;
    });

    const losingTrades = totalTrades - winningTrades;
    const netProfit = grossProfit - grossLoss;
    const winRate = (winningTrades / totalTrades) * 100;
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0;

    updatedResults.totalTrades = totalTrades;
    updatedResults.winningTrades = winningTrades;
    updatedResults.losingTrades = losingTrades;
    updatedResults.grossProfit = grossProfit;
    updatedResults.grossLoss = grossLoss;
    updatedResults.netProfit = netProfit;
    updatedResults.winRate = winRate;
    updatedResults.profitFactor = profitFactor;
    updatedResults.finalCapital = updatedResults.initialCapital + netProfit;

    // Update the equity curve
    if (updatedResults.equityCurve && updatedResults.equityCurve.length > 0) {
      const lastDate = updatedResults.equityCurve[updatedResults.equityCurve.length - 1].date;
      updatedResults.equityCurve[updatedResults.equityCurve.length - 1] = {
        date: lastDate,
        equity: updatedResults.finalCapital
      };
    }

    // Set the updated results
    setResults(updatedResults);
  };

  // Generate mock historical data for demo purposes
  const generateMockHistoricalData = (symbol, timeframe, startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24));

    const data = [];
    let currentDate = new Date(start);
    let price = symbol.includes('USD') ? 1.1000 : 100.00; // Starting price

    for (let i = 0; i < days; i++) {
      // Skip weekends
      if (currentDate.getDay() === 0 || currentDate.getDay() === 6) {
        currentDate.setDate(currentDate.getDate() + 1);
        continue;
      }

      // Random price movement
      const change = (Math.random() - 0.5) * 0.02; // -1% to +1%
      price = price * (1 + change);

      // Add data point
      data.push({
        date: currentDate.toISOString().split('T')[0],
        open: price * (1 - Math.random() * 0.005),
        high: price * (1 + Math.random() * 0.01),
        low: price * (1 - Math.random() * 0.01),
        close: price,
        volume: Math.floor(Math.random() * 10000) + 5000
      });

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return data;
  };

  // Generate mock pattern backtest results for demo purposes
  const generateMockPatternResults = (config) => {
    // Create mock results similar to the regular backtest but with pattern-specific data
    const mockResults = {
      symbol: config.symbol,
      timeframe: config.timeframe,
      startDate: config.startDate,
      endDate: config.endDate,
      initialCapital: config.initialCapital,
      finalCapital: config.initialCapital * (1 + Math.random() * 0.5),
      netProfit: config.initialCapital * Math.random() * 0.5,
      totalTrades: Math.floor(Math.random() * 50) + 30,
      winningTrades: 0,
      losingTrades: 0,
      winRate: 0,
      profitFactor: 0,
      maxDrawdown: Math.random() * 15,
      averageWin: 0,
      averageLoss: 0,
      averageReturn: 0,
      sharpeRatio: 1 + Math.random() * 1.5,
      sortinoRatio: 1.2 + Math.random() * 1.8,
      expectancy: 0,
      averageHoldingTime: '3.5 days',
      trades: [],
      equityCurve: [],
      monthlyReturns: {},
      patternPerformance: {}
    };

    // Generate pattern performance data
    const patterns = [
      'doji', 'hammer', 'engulfing', 'morning_star', 'evening_star',
      'three_white_soldiers', 'three_black_crows', 'harami',
      'head_and_shoulders', 'double_top', 'double_bottom', 'triangle'
    ];

    patterns.forEach(pattern => {
      const totalTrades = Math.floor(Math.random() * 20) + 5;
      const winningTrades = Math.floor(totalTrades * (0.4 + Math.random() * 0.4));
      const losingTrades = totalTrades - winningTrades;
      const grossProfit = winningTrades * (Math.random() * 200 + 100);
      const grossLoss = losingTrades * (Math.random() * 100 + 50);

      mockResults.patternPerformance[pattern] = {
        totalTrades,
        winningTrades,
        losingTrades,
        winRate: (winningTrades / totalTrades) * 100,
        grossProfit,
        grossLoss,
        netProfit: grossProfit - grossLoss,
        averageProfit: (grossProfit - grossLoss) / totalTrades,
        profitFactor: grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0
      };
    });

    // Set the results
    setResults(mockResults);

    return mockResults;
  };

  return (
    <div className="pattern-backtesting p-4" aria-labelledby="pattern-backtesting-title" aria-describedby="pattern-backtesting-description">
      <h2 id="pattern-backtesting-title" className="text-xl font-bold mb-4">Pattern-Based Strategy Backtesting</h2>
      <p id="pattern-backtesting-description" className="mb-4 text-gray-600 dark:text-gray-300">
        Backtest trading strategies based on candlestick and chart patterns to evaluate their performance.
      </p>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      {!results ? (
        <PatternBacktestingForm onRunBacktest={runPatternBacktest} isLoading={loading} />
      ) : (
        <div>
          <div className="mb-6">
            <PatternBacktestResults results={results} />
          </div>

          <div className="mb-6">
            <PatternOptimizer results={results} onApplyOptimization={handleApplyOptimization} />
          </div>

          <div className="flex justify-center">
            <button
              className="mt-4 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded"
              onClick={() => setResults(null)}
            >
              Run Another Backtest
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PatternBacktesting;
