/**
 * Enhanced Error Logger
 * 
 * Comprehensive error logging system with structured logging,
 * performance tracking, and integration with monitoring services.
 * 
 * Features:
 * - Structured error logging
 * - Performance impact tracking
 * - Error correlation and grouping
 * - Integration with external monitoring
 * - Local storage fallback
 * - Error analytics and metrics
 * 
 * @version 1.0.0
 */

import { formatTimestamp } from '../types/common';
import { AppError, ErrorType, ErrorCategory, ErrorSeverity } from '../context/ErrorContext';

// ============================================================================
// INTERFACES
// ============================================================================

export interface LogEntry {
  id: string;
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
  context?: LogContext;
  performance?: PerformanceMetrics;
  correlation?: CorrelationData;
}

export interface LogContext {
  component?: string;
  feature?: string;
  action?: string;
  userId?: string;
  sessionId?: string;
  url?: string;
  userAgent?: string;
  viewport?: {
    width: number;
    height: number;
  };
  connection?: {
    type: string;
    effectiveType?: string;
  };
}

export interface PerformanceMetrics {
  memoryUsage?: number;
  renderTime?: number;
  networkLatency?: number;
  errorImpact?: {
    userFlowInterrupted: boolean;
    featureUnavailable: boolean;
    dataLoss: boolean;
  };
}

export interface CorrelationData {
  traceId?: string;
  spanId?: string;
  parentErrorId?: string;
  relatedErrors?: string[];
  userJourneyStep?: string;
}

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal';

export interface ErrorLoggerConfig {
  enableConsoleLogging: boolean;
  enableRemoteLogging: boolean;
  enableLocalStorage: boolean;
  maxLocalStorageEntries: number;
  logLevel: LogLevel;
  remoteEndpoint?: string;
  batchSize: number;
  flushInterval: number;
  enablePerformanceTracking: boolean;
  enableCorrelation: boolean;
}

// ============================================================================
// ERROR LOGGER CLASS
// ============================================================================

class ErrorLogger {
  private config: ErrorLoggerConfig;
  private logBuffer: LogEntry[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  private sessionId: string;
  private correlationMap: Map<string, string[]> = new Map();

  constructor(config: Partial<ErrorLoggerConfig> = {}) {
    this.config = {
      enableConsoleLogging: process.env.NODE_ENV === 'development',
      enableRemoteLogging: true,
      enableLocalStorage: true,
      maxLocalStorageEntries: 100,
      logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'warn',
      batchSize: 10,
      flushInterval: 5000,
      enablePerformanceTracking: true,
      enableCorrelation: true,
      ...config
    };

    this.sessionId = this.generateSessionId();
    this.startFlushTimer();
    this.setupUnloadHandler();
  }

  // ========================================================================
  // PUBLIC LOGGING METHODS
  // ========================================================================

  public logError(error: AppError, context?: Partial<LogContext>): void {
    const logEntry = this.createLogEntry(
      'error',
      `${error.type} error: ${error.message}`,
      {
        error,
        errorId: error.id,
        userMessage: error.userMessage,
        retryCount: error.retryCount,
        resolved: error.resolved
      },
      context
    );

    this.addLog(logEntry);

    // Track error correlation
    if (this.config.enableCorrelation) {
      this.trackErrorCorrelation(error, logEntry);
    }
  }

  public logPerformanceImpact(errorId: string, metrics: PerformanceMetrics): void {
    const logEntry = this.createLogEntry(
      'warn',
      `Performance impact from error: ${errorId}`,
      { errorId, metrics },
      undefined,
      metrics
    );

    this.addLog(logEntry);
  }

  public logRecovery(errorId: string, recoveryMethod: string, success: boolean): void {
    const logEntry = this.createLogEntry(
      success ? 'info' : 'warn',
      `Error recovery ${success ? 'succeeded' : 'failed'}: ${recoveryMethod}`,
      {
        errorId,
        recoveryMethod,
        success,
        timestamp: formatTimestamp()
      }
    );

    this.addLog(logEntry);
  }

  public logUserAction(action: string, context?: Partial<LogContext>, data?: any): void {
    const logEntry = this.createLogEntry(
      'info',
      `User action: ${action}`,
      { action, ...data },
      context
    );

    this.addLog(logEntry);
  }

  public logDebug(message: string, data?: any, context?: Partial<LogContext>): void {
    if (this.shouldLog('debug')) {
      const logEntry = this.createLogEntry('debug', message, data, context);
      this.addLog(logEntry);
    }
  }

  public logInfo(message: string, data?: any, context?: Partial<LogContext>): void {
    if (this.shouldLog('info')) {
      const logEntry = this.createLogEntry('info', message, data, context);
      this.addLog(logEntry);
    }
  }

  public logWarn(message: string, data?: any, context?: Partial<LogContext>): void {
    if (this.shouldLog('warn')) {
      const logEntry = this.createLogEntry('warn', message, data, context);
      this.addLog(logEntry);
    }
  }

  // ========================================================================
  // PRIVATE METHODS
  // ========================================================================

  private createLogEntry(
    level: LogLevel,
    message: string,
    data?: any,
    context?: Partial<LogContext>,
    performance?: PerformanceMetrics
  ): LogEntry {
    const logEntry: LogEntry = {
      id: this.generateLogId(),
      timestamp: formatTimestamp(),
      level,
      message,
      data,
      context: {
        sessionId: this.sessionId,
        url: window.location.href,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        ...this.getConnectionInfo(),
        ...context
      },
      performance: performance || (this.config.enablePerformanceTracking ? this.getPerformanceMetrics() : undefined)
    };

    return logEntry;
  }

  private addLog(logEntry: LogEntry): void {
    // Console logging
    if (this.config.enableConsoleLogging) {
      this.logToConsole(logEntry);
    }

    // Add to buffer for remote logging
    if (this.config.enableRemoteLogging) {
      this.logBuffer.push(logEntry);
      
      if (this.logBuffer.length >= this.config.batchSize) {
        this.flushLogs();
      }
    }

    // Local storage logging
    if (this.config.enableLocalStorage) {
      this.logToLocalStorage(logEntry);
    }
  }

  private logToConsole(logEntry: LogEntry): void {
    const consoleMethod = logEntry.level === 'debug' ? 'debug' :
                         logEntry.level === 'info' ? 'info' :
                         logEntry.level === 'warn' ? 'warn' :
                         'error';

    console.group(`📊 ${logEntry.level.toUpperCase()}: ${logEntry.message}`);
    console[consoleMethod]('Message:', logEntry.message);
    console.log('Timestamp:', logEntry.timestamp);
    console.log('Context:', logEntry.context);
    
    if (logEntry.data) {
      console.log('Data:', logEntry.data);
    }
    
    if (logEntry.performance) {
      console.log('Performance:', logEntry.performance);
    }
    
    if (logEntry.correlation) {
      console.log('Correlation:', logEntry.correlation);
    }
    
    console.groupEnd();
  }

  private logToLocalStorage(logEntry: LogEntry): void {
    try {
      const existingLogs = JSON.parse(localStorage.getItem('errorLogs') || '[]');
      const updatedLogs = [logEntry, ...existingLogs].slice(0, this.config.maxLocalStorageEntries);
      localStorage.setItem('errorLogs', JSON.stringify(updatedLogs));
    } catch (error) {
      console.warn('Failed to save log to localStorage:', error);
    }
  }

  private async flushLogs(): Promise<void> {
    if (this.logBuffer.length === 0 || !this.config.remoteEndpoint) {
      return;
    }

    const logsToSend = [...this.logBuffer];
    this.logBuffer = [];

    try {
      const response = await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          logs: logsToSend,
          sessionId: this.sessionId,
          timestamp: formatTimestamp()
        })
      });

      if (!response.ok) {
        throw new Error(`Logging failed: ${response.status}`);
      }
    } catch (error) {
      console.warn('Failed to send logs to remote endpoint:', error);
      
      // Return logs to buffer for retry
      this.logBuffer.unshift(...logsToSend);
    }
  }

  private trackErrorCorrelation(error: AppError, logEntry: LogEntry): void {
    const correlationKey = `${error.type}-${error.source}`;
    
    if (!this.correlationMap.has(correlationKey)) {
      this.correlationMap.set(correlationKey, []);
    }
    
    const relatedErrors = this.correlationMap.get(correlationKey)!;
    relatedErrors.push(error.id);
    
    // Keep only recent errors (last 10)
    if (relatedErrors.length > 10) {
      relatedErrors.splice(0, relatedErrors.length - 10);
    }
    
    // Add correlation data to log entry
    logEntry.correlation = {
      traceId: this.sessionId,
      spanId: logEntry.id,
      relatedErrors: relatedErrors.filter(id => id !== error.id),
      userJourneyStep: this.getCurrentUserJourneyStep()
    };
  }

  private getPerformanceMetrics(): PerformanceMetrics {
    const metrics: PerformanceMetrics = {};

    // Memory usage
    if ('memory' in performance) {
      const memInfo = (performance as any).memory;
      metrics.memoryUsage = memInfo.usedJSHeapSize / 1024 / 1024; // MB
    }

    // Network information
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      metrics.networkLatency = connection.rtt;
    }

    return metrics;
  }

  private getConnectionInfo(): Partial<LogContext> {
    const context: Partial<LogContext> = {};

    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      context.connection = {
        type: connection.type || 'unknown',
        effectiveType: connection.effectiveType
      };
    }

    return context;
  }

  private getCurrentUserJourneyStep(): string {
    // Determine current step based on URL and application state
    const path = window.location.pathname;
    
    if (path.includes('/dashboard')) return 'dashboard';
    if (path.includes('/signals')) return 'signals';
    if (path.includes('/charts')) return 'charts';
    if (path.includes('/settings')) return 'settings';
    if (path.includes('/login')) return 'authentication';
    
    return 'unknown';
  }

  private shouldLog(level: LogLevel): boolean {
    const levels = ['debug', 'info', 'warn', 'error', 'fatal'];
    const currentLevelIndex = levels.indexOf(this.config.logLevel);
    const requestedLevelIndex = levels.indexOf(level);
    
    return requestedLevelIndex >= currentLevelIndex;
  }

  private generateLogId(): string {
    return `log-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  }

  private generateSessionId(): string {
    const existing = sessionStorage.getItem('sessionId');
    if (existing) return existing;
    
    const newSessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
    sessionStorage.setItem('sessionId', newSessionId);
    return newSessionId;
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushLogs();
    }, this.config.flushInterval);
  }

  private setupUnloadHandler(): void {
    window.addEventListener('beforeunload', () => {
      this.flushLogs();
    });
  }

  // ========================================================================
  // PUBLIC UTILITY METHODS
  // ========================================================================

  public getStoredLogs(): LogEntry[] {
    try {
      return JSON.parse(localStorage.getItem('errorLogs') || '[]');
    } catch (error) {
      console.warn('Failed to retrieve stored logs:', error);
      return [];
    }
  }

  public clearStoredLogs(): void {
    localStorage.removeItem('errorLogs');
  }

  public getSessionId(): string {
    return this.sessionId;
  }

  public updateConfig(newConfig: Partial<ErrorLoggerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    
    this.flushLogs();
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

const errorLogger = new ErrorLogger({
  remoteEndpoint: '/api/logs/errors'
});

export default errorLogger;

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

export const logError = (error: AppError, context?: Partial<LogContext>) => 
  errorLogger.logError(error, context);

export const logPerformanceImpact = (errorId: string, metrics: PerformanceMetrics) => 
  errorLogger.logPerformanceImpact(errorId, metrics);

export const logRecovery = (errorId: string, recoveryMethod: string, success: boolean) => 
  errorLogger.logRecovery(errorId, recoveryMethod, success);

export const logUserAction = (action: string, context?: Partial<LogContext>, data?: any) => 
  errorLogger.logUserAction(action, context, data);

export const logDebug = (message: string, data?: any, context?: Partial<LogContext>) => 
  errorLogger.logDebug(message, data, context);

export const logInfo = (message: string, data?: any, context?: Partial<LogContext>) => 
  errorLogger.logInfo(message, data, context);

export const logWarn = (message: string, data?: any, context?: Partial<LogContext>) => 
  errorLogger.logWarn(message, data, context);
