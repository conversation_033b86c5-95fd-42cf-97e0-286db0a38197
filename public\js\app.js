/**
 * Trading Signals App - Main Application Script
 *
 * This script handles the main functionality of the Trading Signals App,
 * including fetching and displaying real-time market data, updating charts,
 * and generating trading signals.
 */

// Application state
const appState = {
    // Market data
    market: {
        currentSymbol: 'EURUSD',
        currentTimeframe: 'M5',
        currentAssetType: 'forex',
        data: null,
        isLoading: false,
        hasError: false,
        errorMessage: '',
        lastUpdated: null,
        updateInterval: null
    },

    // UI state
    ui: {
        isDarkMode: false,
        isMenuOpen: false,
        activeTab: 'market'
    },

    // Chart state
    chart: {
        instance: null,
        type: 'candlestick',
        indicators: ['sma', 'volume']
    }
};

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', initApp);

// Wait for enhanced services to be initialized
document.addEventListener('enhanced-services:initialized', () => {
    console.log('Enhanced services are ready');

    // Update app state to use enhanced services
    if (window.enhancedServicesIntegration) {
        appState.enhancedServices = {
            available: true,
            apiClient: window.enhancedAPIClient,
            marketData: window.enhancedMarketDataServices,
            aiAnalysis: window.enhancedAIServices,
            errorHandler: window.enhancedErrorHandler
        };

        console.log('Enhanced services integrated into app state');
    }
});

/**
 * Initialize the application
 */
function initApp() {
    console.log('Initializing Trading Signals App...');

    // Start progress indicator
    if (typeof NProgress !== 'undefined') {
        NProgress.start();
    }

    // Set up event listeners
    setupEventListeners();

    // Set up settings event listeners
    setupSettingsEventListeners();

    // Initialize UI components
    initializeUI();

    // Load initial market data
    loadMarketData();

    // Set up auto-refresh
    setupAutoRefresh();

    console.log('App initialization complete');

    // Complete progress indicator
    if (typeof NProgress !== 'undefined') {
        setTimeout(() => {
            NProgress.done();
        }, 500);
    }
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
    // Market selection controls
    const symbolSelect = document.getElementById('symbol');
    const timeframeSelect = document.getElementById('timeframe');
    const marketTypeSelect = document.getElementById('marketType');
    const analyzeBtn = document.getElementById('analyzeBtn');
    const refreshBtn = document.getElementById('refreshMarketBtn');

    if (symbolSelect) {
        symbolSelect.addEventListener('change', handleSymbolChange);
    }

    if (timeframeSelect) {
        timeframeSelect.addEventListener('change', handleTimeframeChange);
    }

    if (marketTypeSelect) {
        marketTypeSelect.addEventListener('change', handleMarketTypeChange);
    }

    if (analyzeBtn) {
        analyzeBtn.addEventListener('click', analyzeMarket);
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadMarketData);
    }

    // Theme toggle
    const themeToggle = document.getElementById('darkModeToggle');
    if (themeToggle) {
        themeToggle.addEventListener('change', toggleTheme);
    }

    console.log('Event listeners set up');
}

/**
 * Set up settings event listeners
 */
function setupSettingsEventListeners() {
    // Settings button in navbar
    const settingsBtn = document.getElementById('settingsBtn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', openSettings);
    }

    // Settings link in user dropdown
    const settingsLink = document.getElementById('settingsLink');
    if (settingsLink) {
        settingsLink.addEventListener('click', function(event) {
            event.preventDefault();
            openSettings();
        });
    }

    console.log('Settings event listeners set up');
}

/**
 * Open settings modal
 * @param {string} tab - Tab to open (optional)
 */
function openSettings(tab) {
    // If settings manager is available, use it
    if (window.settingsManager && typeof window.settingsManager.openSettings === 'function') {
        window.settingsManager.openSettings(tab);
    } else {
        console.warn('Settings manager not available');
        showNotification('Settings manager not available. Please refresh the page and try again.', 'warning');
    }
}

/**
 * Initialize UI components
 */
function initializeUI() {
    // Set initial values for selects
    const symbolSelect = document.getElementById('symbol');
    const timeframeSelect = document.getElementById('timeframe');
    const marketTypeSelect = document.getElementById('marketType');

    if (symbolSelect) {
        symbolSelect.value = appState.market.currentSymbol;
    }

    if (timeframeSelect) {
        timeframeSelect.value = appState.market.currentTimeframe;
    }

    if (marketTypeSelect) {
        marketTypeSelect.value = appState.market.currentAssetType;
    }

    // Initialize theme
    initializeTheme();

    console.log('UI initialized');
}

/**
 * Initialize theme
 */
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme_preference');
    if (savedTheme) {
        document.documentElement.setAttribute('data-theme', savedTheme);
        appState.ui.isDarkMode = savedTheme === 'dark';

        const themeToggle = document.getElementById('darkModeToggle');
        if (themeToggle) {
            themeToggle.checked = appState.ui.isDarkMode;
        }
    }
}

/**
 * Toggle theme between light and dark
 */
function toggleTheme(event) {
    const isDarkMode = event.target.checked;
    appState.ui.isDarkMode = isDarkMode;

    document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');
    localStorage.setItem('theme_preference', isDarkMode ? 'dark' : 'light');
}

/**
 * Handle symbol change
 */
function handleSymbolChange(event) {
    appState.market.currentSymbol = event.target.value;
    console.log(`Symbol changed to: ${appState.market.currentSymbol}`);
    loadMarketData();
}

/**
 * Handle timeframe change
 */
function handleTimeframeChange(event) {
    appState.market.currentTimeframe = event.target.value;
    console.log(`Timeframe changed to: ${appState.market.currentTimeframe}`);
    loadMarketData();
}

/**
 * Handle market type change
 */
function handleMarketTypeChange(event) {
    appState.market.currentAssetType = event.target.value;
    console.log(`Market type changed to: ${appState.market.currentAssetType}`);

    // Update symbol options based on market type
    updateSymbolOptions(appState.market.currentAssetType);

    // Load market data for new market type
    loadMarketData();
}

/**
 * Update symbol options based on market type
 */
function updateSymbolOptions(marketType) {
    const symbolSelect = document.getElementById('symbol');
    if (!symbolSelect) return;

    // Clear current options
    symbolSelect.innerHTML = '';

    // Add new options based on market type
    let options = [];

    switch (marketType) {
        case 'forex':
            options = [
                { value: 'EURUSD', text: 'EUR/USD' },
                { value: 'GBPUSD', text: 'GBP/USD' },
                { value: 'USDJPY', text: 'USD/JPY' },
                { value: 'AUDUSD', text: 'AUD/USD' },
                { value: 'USDCAD', text: 'USD/CAD' }
            ];
            break;
        case 'commodities':
            options = [
                { value: 'XAUUSD', text: 'Gold (XAU/USD)' },
                { value: 'XAGUSD', text: 'Silver (XAG/USD)' },
                { value: 'USOIL', text: 'WTI Crude Oil' },
                { value: 'UKOIL', text: 'Brent Crude Oil' }
            ];
            break;
        case 'indices':
            options = [
                { value: 'US30', text: 'Dow Jones (US30)' },
                { value: 'SPX500', text: 'S&P 500' },
                { value: 'NASDAQ', text: 'NASDAQ' },
                { value: 'GER30', text: 'DAX (GER30)' }
            ];
            break;
        case 'crypto':
            options = [
                { value: 'BTCUSD', text: 'Bitcoin (BTC/USD)' },
                { value: 'ETHUSD', text: 'Ethereum (ETH/USD)' },
                { value: 'LTCUSD', text: 'Litecoin (LTC/USD)' }
            ];
            break;
    }

    // Add options to select
    options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option.value;
        optionElement.textContent = option.text;
        symbolSelect.appendChild(optionElement);
    });

    // Set first option as selected
    if (options.length > 0) {
        appState.market.currentSymbol = options[0].value;
        symbolSelect.value = appState.market.currentSymbol;
    }
}

/**
 * Set up auto-refresh for market data - simplified version
 */
function setupAutoRefresh() {
    // Set up interval (refresh every 2 minutes)
    appState.market.updateInterval = setInterval(() => {
        loadMarketData(true);
    }, 120000); // 2 minutes
}

/**
 * Load market data - enhanced version with new services integration
 * @param {boolean} silent - Whether to show loading indicators
 */
async function loadMarketData(silent = false) {
    try {
        // Update loading state
        if (!silent) {
            updateLoadingState(true);
        }

        let data;

        // Use enhanced services if available
        if (appState.enhancedServices && appState.enhancedServices.available) {
            console.log('Using enhanced market data services');

            try {
                const response = await appState.enhancedServices.marketData.getMarketData(
                    appState.market.currentSymbol,
                    appState.market.currentTimeframe,
                    {
                        limit: 100,
                        assetType: appState.market.currentAssetType
                    }
                );

                data = response.data;

                // Add metadata
                if (data && Array.isArray(data) && data.length > 0) {
                    const latestPrice = data[data.length - 1];
                    data.currentPrice = latestPrice.close;
                    data.symbol = appState.market.currentSymbol;
                    data.timeframe = appState.market.currentTimeframe;
                    data.source = response.meta?.source || 'enhanced-api';
                    data.isMockData = response.meta?.mockData || false;

                    // Calculate daily change if we have enough data
                    if (data.length >= 2) {
                        const previousPrice = data[data.length - 2].close;
                        data.dailyChange = ((latestPrice.close - previousPrice) / previousPrice) * 100;
                    }
                }

            } catch (enhancedError) {
                console.warn('Enhanced services failed, falling back to legacy services:', enhancedError);

                // Fall back to legacy services
                data = await loadMarketDataLegacy();
            }
        } else {
            // Use legacy services
            console.log('Enhanced services not available, using legacy services');
            data = await loadMarketDataLegacy();
        }

        // Update app state with new data
        appState.market.data = data;
        appState.market.lastUpdated = new Date();
        appState.market.hasError = false;
        appState.market.errorMessage = '';

        // Update UI
        updateMarketUI();
        updateChart();
        generateTradingSignals();

        return data;
    } catch (error) {
        console.error('Error loading market data:', error);

        // Use enhanced error handler if available
        if (appState.enhancedServices && appState.enhancedServices.errorHandler) {
            appState.enhancedServices.errorHandler.handleError(error, {
                service: 'market-data',
                operation: 'loadMarketData',
                symbol: appState.market.currentSymbol,
                timeframe: appState.market.currentTimeframe
            });
        }

        // Update error state
        updateErrorState('Data loading failed: ' + error.message);
        return null;
    } finally {
        // Always reset loading state
        if (!silent) {
            updateLoadingState(false);
        }
    }
}

/**
 * Legacy market data loading function
 */
async function loadMarketDataLegacy() {
    // Generate cache key
    const cacheKey = `market_data_${appState.market.currentSymbol}_${appState.market.currentTimeframe}_${appState.market.currentAssetType}`;

    // Check cache first if available
    if (window.enhancedCache) {
        const cachedData = window.enhancedCache.get(cacheKey, 'marketData');
        if (cachedData) {
            console.log('Using cached market data');
            return cachedData;
        }
    }

    let data;

    // Try to use data source registry if available
    if (window.dataSourceRegistry && typeof window.dataSourceRegistry.fetchData === 'function') {
        try {
            console.log('Using data source registry to fetch market data');

            // Fetch data using the registry
            data = await window.dataSourceRegistry.fetchData(
                appState.market.currentSymbol,
                appState.market.currentTimeframe,
                appState.market.currentAssetType
            );
        } catch (registryError) {
            console.warn('Data source registry failed:', registryError);

            // Fall back to market data service
            console.log('Falling back to market data service');
            data = await window.MarketDataService.getRealTimeMarketData(
                appState.market.currentSymbol,
                appState.market.currentTimeframe,
                appState.market.currentAssetType
            );
        }
    } else {
        // Fall back to market data service
        console.log('Data source registry not available, using market data service');
        data = await window.MarketDataService.getRealTimeMarketData(
            appState.market.currentSymbol,
            appState.market.currentTimeframe,
            appState.market.currentAssetType
        );
    }

    // Cache the data if cache is available
    if (window.enhancedCache && data) {
        window.enhancedCache.set(cacheKey, data, 'marketData');
    }

    return data;
}

/**
 * Update market UI with current data
 */
function updateMarketUI() {
    // Reset loading state
    updateLoadingState(false);

    const data = appState.market.data;
    if (!data) return;

    // Update current symbol
    const currentSymbolElement = document.getElementById('currentSymbol');
    if (currentSymbolElement) {
        currentSymbolElement.textContent = data.symbol;
    }

    // Update current price
    const currentPriceElement = document.getElementById('currentPrice');
    if (currentPriceElement) {
        currentPriceElement.textContent = data.currentPrice.toFixed(5);

        // Add data source indicator
        if (data.isMockData) {
            currentPriceElement.innerHTML += ' <span class="badge bg-warning text-dark" title="Using simulated data due to API limitations">DEMO</span>';
        } else if (data.source) {
            let badgeClass = 'bg-info';
            let badgeTitle = 'Data source';

            // Customize badge based on source
            if (data.source === 'integrated-api') {
                badgeClass = 'bg-success';
                badgeTitle = 'Using integrated multi-API data source';
            } else if (data.source === 'alpha-vantage') {
                badgeClass = 'bg-primary';
                badgeTitle = 'Using Alpha Vantage API';
            } else if (data.source === 'polygon') {
                badgeClass = 'bg-info';
                badgeTitle = 'Using Polygon.io API';
            } else if (data.source === 'fmp') {
                badgeClass = 'bg-secondary';
                badgeTitle = 'Using Financial Modeling Prep API';
            }

            currentPriceElement.innerHTML += ` <span class="badge ${badgeClass}" title="${badgeTitle}">${data.source.toUpperCase()}</span>`;
        }
    }

    // Update daily change
    const dailyChangeElement = document.getElementById('dailyChange');
    if (dailyChangeElement && data.dailyChange !== undefined) {
        const isPositive = data.dailyChange >= 0;
        dailyChangeElement.textContent = `${isPositive ? '+' : ''}${data.dailyChange.toFixed(2)}%`;
        dailyChangeElement.className = `badge ${isPositive ? 'bg-success' : 'bg-danger'}`;
    }

    // Update last update time
    const lastUpdateElement = document.getElementById('lastUpdate');
    if (lastUpdateElement) {
        lastUpdateElement.textContent = new Date().toLocaleTimeString();
    }

    // Update market sentiment
    updateSentimentUI(data.sentiment);

    // Show notification based on data source
    if (data.isMockData) {
        showNotification('Using simulated market data due to API limitations. The data shown is for demonstration purposes only.', 'warning');
    } else if (data.source === 'integrated-api') {
        showNotification('Using real market data from the integrated multi-API service.', 'success');
    }
}

/**
 * Update sentiment UI
 * @param {number} sentiment - Sentiment value (0-100)
 */
function updateSentimentUI(sentiment) {
    const progressBar = document.querySelector('.progress-bar');
    if (!progressBar) return;

    progressBar.style.width = `${sentiment}%`;
    progressBar.setAttribute('aria-valuenow', sentiment);

    // Update color based on sentiment
    if (sentiment > 60) {
        progressBar.className = 'progress-bar bg-success';
        progressBar.textContent = `${sentiment}% Bullish`;
    } else if (sentiment < 40) {
        progressBar.className = 'progress-bar bg-danger';
        progressBar.textContent = `${sentiment}% Bearish`;
    } else {
        progressBar.className = 'progress-bar bg-warning';
        progressBar.textContent = `${sentiment}% Neutral`;
    }
}

/**
 * Update loading state
 * @param {boolean} isLoading - Whether the app is loading data
 */
function updateLoadingState(isLoading) {
    const currentPriceElement = document.getElementById('currentPrice');
    if (currentPriceElement && isLoading) {
        currentPriceElement.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
    }
}

/**
 * Update error state
 * @param {string} errorMessage - Error message to display
 */
function updateErrorState(errorMessage) {
    const currentPriceElement = document.getElementById('currentPrice');
    if (currentPriceElement) {
        currentPriceElement.innerHTML = `<span class="text-danger"><i class="fas fa-exclamation-triangle"></i> Error: ${errorMessage}</span>`;
    }
}

/**
 * Show notification to the user - simplified version
 * @param {string} message - Notification message
 * @param {string} type - Notification type (success, info, warning, danger)
 */
function showNotification(message, type = 'info') {
    // Create simple alert
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.innerHTML = message;
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.maxWidth = '350px';

    // Add to body
    document.body.appendChild(alert);

    // Remove after 3 seconds
    setTimeout(() => {
        alert.remove();
    }, 3000);
}

/**
 * Update chart with current data
 */
function updateChart() {
    console.log('Updating chart with new data...');

    // Check if we have market data
    if (!appState.market.data) {
        console.warn('No market data available for chart update');
        return;
    }

    try {
        // If chart.js is loaded and updateChart function is available, call it
        if (window.updateChart && typeof window.updateChart === 'function') {
            window.updateChart(appState.market.data);
        } else if (window.chartModule && typeof window.chartModule.updateChart === 'function') {
            window.chartModule.updateChart(appState.market.data);
        } else {
            console.error('Chart update function not found. Make sure chart.js is properly loaded.');
        }
    } catch (error) {
        console.error('Error updating chart:', error);
    }
}

/**
 * Analyze market and generate trading signals
 */
function analyzeMarket() {
    console.log('Analyzing market...');
    generateTradingSignals();
}

/**
 * Generate trading signals based on market data
 */
function generateTradingSignals() {
    try {
        // This function generates trading signals based on technical indicators and market data
        console.log('Generating trading signals...');

        // Check if market data is available
        if (!appState.market.data) {
            console.warn('No market data available for signal generation');
            updateSignalsContainer(null);
            return;
        }

        // If technical-analysis.js is loaded, use its generateTradingSignal function
        if (window.technicalAnalysis && typeof window.technicalAnalysis.generateTradingSignal === 'function') {
            // Create a copy of the data to avoid reference issues
            const marketData = { ...appState.market.data };

            // Generate the signal
            const signal = window.technicalAnalysis.generateTradingSignal(marketData);
            console.log('Generated trading signal:', signal);

            // Update signals container
            updateSignalsContainer(signal);
        } else {
            console.warn('Technical analysis module not loaded');

            // Show warning in signals container
            const signalsContainer = document.getElementById('signalsContainer');
            if (signalsContainer) {
                signalsContainer.innerHTML = `
                    <div class="alert alert-warning">
                        <h4>Technical Analysis Not Available</h4>
                        <p>The technical analysis module is not loaded. Please refresh the page and try again.</p>
                    </div>
                `;
            }
        }
    } catch (error) {
        console.error('Error generating trading signals:', error);

        // Show error in signals container
        const signalsContainer = document.getElementById('signalsContainer');
        if (signalsContainer) {
            signalsContainer.innerHTML = `
                <div class="alert alert-danger">
                    <h4>Error Generating Signals</h4>
                    <p>${error.message}</p>
                </div>
            `;
        }
    }
}

/**
 * Update signals container with trading signals
 * @param {Object} signal - Trading signal data
 */
function updateSignalsContainer(signal) {
    try {
        const signalsContainer = document.getElementById('signalsContainer');
        if (!signalsContainer) return;

        // Clear container
        signalsContainer.innerHTML = '';

        // Check if signal is valid
        if (!signal) {
            console.error('Invalid signal data');
            signalsContainer.innerHTML = `
                <div class="alert alert-warning">
                    <h4>No Trading Signal Available</h4>
                    <p>Unable to generate a trading signal with the current market data.</p>
                </div>
            `;
            return;
        }

        // Create signal element
        const signalElement = document.createElement('div');
        signalElement.className = `alert alert-${getSignalAlertClass(signal.type)}`;

        // Format numbers safely
        const formatNumber = (num, decimals = 5) => {
            if (num === null || num === undefined || isNaN(num)) return 'N/A';
            return parseFloat(num).toFixed(decimals);
        };

        // Set signal content
        signalElement.innerHTML = `
            <h4>${getSignalTypeText(signal.type)}</h4>
            <p><strong>Entry Price:</strong> ${formatNumber(signal.entryPrice)}</p>
            <p><strong>Stop Loss:</strong> ${formatNumber(signal.stopLoss)}</p>
            <p><strong>Take Profit:</strong> ${formatNumber(signal.takeProfit)}</p>
            <p><strong>Risk/Reward Ratio:</strong> 1:${formatNumber(signal.riskRewardRatio, 2)}</p>
            <p><strong>Confidence:</strong> ${formatNumber(signal.confidence * 100, 0)}%</p>
            <p><strong>Generated:</strong> ${new Date().toLocaleTimeString()}</p>
        `;

        // Add signal to container
        signalsContainer.appendChild(signalElement);
    } catch (error) {
        console.error('Error updating signals container:', error);

        // Show error message in container
        const signalsContainer = document.getElementById('signalsContainer');
        if (signalsContainer) {
            signalsContainer.innerHTML = `
                <div class="alert alert-danger">
                    <h4>Error Generating Signal</h4>
                    <p>${error.message}</p>
                </div>
            `;
        }
    }
}

/**
 * Get signal alert class based on signal type
 * @param {string} type - Signal type (buy, sell, neutral)
 * @returns {string} - Alert class
 */
function getSignalAlertClass(type) {
    switch (type) {
        case 'buy':
            return 'success';
        case 'sell':
            return 'danger';
        case 'neutral':
            return 'warning';
        default:
            return 'info';
    }
}

/**
 * Get signal type text based on signal type
 * @param {string} type - Signal type (buy, sell, neutral)
 * @returns {string} - Signal type text
 */
function getSignalTypeText(type) {
    switch (type) {
        case 'buy':
            return 'Buy Signal';
        case 'sell':
            return 'Sell Signal';
        case 'neutral':
            return 'Neutral Signal';
        default:
            return 'Unknown Signal';
    }
}

/**
 * Analyze market using enhanced AI and technical analysis
 */
async function analyzeMarket() {
    try {
        console.log('Starting enhanced market analysis...');

        // Show loading state
        updateLoadingState(true);

        // Get current market data
        const marketData = appState.market.data;
        if (!marketData) {
            throw new Error('No market data available for analysis');
        }

        let analysisResults = {};

        // Use unified signal generation service
        try {
            console.log('Generating unified signals (AI + Traditional)');

            // Calculate technical indicators first
            const indicators = calculateTechnicalIndicators(marketData);

            // Call unified signals endpoint
            const response = await fetch('/api/signals/unified', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...window.Auth?.getAuthHeaders() || {}
                },
                body: JSON.stringify({
                    symbol: appState.market.currentSymbol,
                    marketData: [marketData], // Convert to array format expected by service
                    indicators,
                    options: {
                        enableAI: true,
                        enableTraditional: true,
                        minConfidence: 50
                    }
                })
            });

            if (response.ok) {
                const result = await response.json();

                analysisResults = {
                    unifiedSignals: result.data || [],
                    metadata: result.metadata || {},
                    indicators,
                    enhanced: true,
                    source: 'unified'
                };

                console.log('Unified signal generation complete:', analysisResults);

                // Subscribe to real-time updates for this symbol
                if (window.enhancedWebSocket && window.enhancedWebSocket.isConnected()) {
                    window.enhancedWebSocket.subscribeToSymbol(appState.market.currentSymbol);
                }

            } else {
                throw new Error(`Unified signals API error: ${response.status}`);
            }

        } catch (unifiedError) {
            console.warn('Unified signal generation failed, falling back to enhanced AI services:', unifiedError);

            // Fall back to enhanced AI services if available
            if (appState.enhancedServices && appState.enhancedServices.available) {
                try {
                    console.log('Using enhanced AI analysis services');

                    // Calculate technical indicators first
                    const indicators = calculateTechnicalIndicators(marketData);

                    // Get AI market analysis
                    const aiAnalysis = await appState.enhancedServices.aiAnalysis.analyzeMarket(
                        appState.market.currentSymbol,
                        marketData,
                        indicators
                    );

                    // Get AI trading signals
                    const aiSignals = await appState.enhancedServices.aiAnalysis.generateTradingSignals(
                        appState.market.currentSymbol,
                        {
                            currentPrice: marketData.currentPrice,
                            volume: marketData.volume || 'N/A'
                        },
                        indicators
                    );

                    analysisResults = {
                        aiAnalysis,
                        aiSignals,
                        indicators,
                        enhanced: true,
                        source: 'ai-only'
                    };

                    console.log('Enhanced AI analysis complete:', analysisResults);

                } catch (aiError) {
                    console.warn('AI analysis failed, falling back to traditional analysis:', aiError);

                    // Fall back to traditional analysis
                    analysisResults = await performTraditionalAnalysis(marketData);
                }
            } else {
                // Use traditional analysis
                console.log('Enhanced services not available, using traditional analysis');
                analysisResults = await performTraditionalAnalysis(marketData);
            }
        }

        // Update UI with analysis results
        updateAnalysisUI(analysisResults);

        console.log('Market analysis complete');

        // Show success notification with signal count
        const signalCount = analysisResults.unifiedSignals?.length ||
                           analysisResults.aiSignals?.length || 0;
        showNotification(`Market analysis completed successfully. Generated ${signalCount} signals.`, 'success');

    } catch (error) {
        console.error('Error analyzing market:', error);

        // Use enhanced error handler if available
        if (appState.enhancedServices && appState.enhancedServices.errorHandler) {
            appState.enhancedServices.errorHandler.handleError(error, {
                service: 'market-analysis',
                operation: 'analyzeMarket',
                symbol: appState.market.currentSymbol
            });
        }

        showNotification('Market analysis failed: ' + error.message, 'error');
    } finally {
        updateLoadingState(false);
    }
}

/**
 * Perform traditional technical analysis
 */
async function performTraditionalAnalysis(marketData) {
    // Calculate technical indicators
    const indicators = calculateTechnicalIndicators(marketData);

    // Generate trading signals
    const signals = generateTradingSignals(marketData, indicators);

    return {
        indicators,
        signals,
        enhanced: false
    };
}

/**
 * Update analysis UI with results
 */
function updateAnalysisUI(analysisResults) {
    // Update signals display
    if (analysisResults.aiSignals) {
        updateAISignalsDisplay(analysisResults.aiSignals);
    } else if (analysisResults.signals) {
        updateTraditionalSignalsDisplay(analysisResults.signals);
    }

    // Update AI analysis display if available
    if (analysisResults.aiAnalysis) {
        updateAIAnalysisDisplay(analysisResults.aiAnalysis);
    }

    // Update indicators display
    if (analysisResults.indicators) {
        updateIndicatorsDisplay(analysisResults.indicators);
    }

    // Add enhanced badge if using AI
    if (analysisResults.enhanced) {
        addEnhancedAnalysisBadge();
    }
}

/**
 * Update AI signals display
 */
function updateAISignalsDisplay(aiSignals) {
    const signalsContainer = document.getElementById('signalsContainer');
    if (!signalsContainer) return;

    // Create AI signal card
    const signalCard = document.createElement('div');
    signalCard.className = `alert ${aiSignals.signal === 'buy' ? 'alert-success' : aiSignals.signal === 'sell' ? 'alert-danger' : 'alert-warning'}`;

    signalCard.innerHTML = `
        <div class="d-flex justify-content-between align-items-start">
            <h4>AI ${aiSignals.signal.toUpperCase()} Signal</h4>
            <div>
                <span class="badge bg-info">${appState.market.currentSymbol}</span>
                <span class="badge bg-secondary">${appState.market.currentTimeframe}</span>
                <span class="badge bg-primary">AI Enhanced</span>
            </div>
        </div>
        <p><strong>Entry Point:</strong> ${aiSignals.entry_price || 'N/A'}</p>
        <p><strong>Stop Loss:</strong> ${aiSignals.stop_loss || 'N/A'}</p>
        <p><strong>Take Profit:</strong> ${aiSignals.take_profit || 'N/A'}</p>
        <p><strong>Risk/Reward Ratio:</strong> <span class="badge bg-success">${aiSignals.risk_reward_ratio || 'N/A'}</span></p>
        <p><strong>AI Analysis:</strong> ${aiSignals.reasoning || 'No detailed analysis available'}</p>
        <div class="progress mt-2" style="height: 5px;">
            <div class="progress-bar ${aiSignals.confidence > 70 ? 'bg-success' : aiSignals.confidence > 50 ? 'bg-warning' : 'bg-danger'}"
                 role="progressbar" style="width: ${aiSignals.confidence || 50}%;"
                 title="AI Confidence: ${aiSignals.confidence || 50}%"></div>
        </div>
        <small class="text-muted">Generated: ${new Date().toLocaleString()}</small>
    `;

    // Replace existing signals or add to container
    signalsContainer.innerHTML = '';
    signalsContainer.appendChild(signalCard);
}

/**
 * Update AI analysis display
 */
function updateAIAnalysisDisplay(aiAnalysis) {
    // Update market trend indicator
    const trendElement = document.querySelector('.progress-bar');
    if (trendElement && aiAnalysis.trend) {
        const trendPercentage = aiAnalysis.trend === 'bullish' ? 75 : aiAnalysis.trend === 'bearish' ? 25 : 50;
        trendElement.style.width = `${trendPercentage}%`;
        trendElement.textContent = `${trendPercentage}% ${aiAnalysis.trend.charAt(0).toUpperCase() + aiAnalysis.trend.slice(1)}`;
        trendElement.className = `progress-bar ${aiAnalysis.trend === 'bullish' ? 'bg-success' : aiAnalysis.trend === 'bearish' ? 'bg-danger' : 'bg-warning'}`;
    }

    // Add AI analysis details to a dedicated section
    let aiAnalysisSection = document.getElementById('ai-analysis-section');
    if (!aiAnalysisSection) {
        aiAnalysisSection = document.createElement('div');
        aiAnalysisSection.id = 'ai-analysis-section';
        aiAnalysisSection.className = 'card mt-3';

        const marketOverview = document.getElementById('marketOverview');
        if (marketOverview && marketOverview.parentNode) {
            marketOverview.parentNode.appendChild(aiAnalysisSection);
        }
    }

    aiAnalysisSection.innerHTML = `
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-robot me-2"></i>AI Market Analysis
                <span class="badge bg-primary ms-2">Enhanced</span>
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Trend:</strong> <span class="badge ${aiAnalysis.trend === 'bullish' ? 'bg-success' : aiAnalysis.trend === 'bearish' ? 'bg-danger' : 'bg-warning'}">${aiAnalysis.trend || 'Unknown'}</span></p>
                    <p><strong>Confidence:</strong> ${aiAnalysis.confidence || 'N/A'}%</p>
                    <p><strong>Risk Level:</strong> <span class="badge ${aiAnalysis.risk_level === 'low' ? 'bg-success' : aiAnalysis.risk_level === 'high' ? 'bg-danger' : 'bg-warning'}">${aiAnalysis.risk_level || 'Unknown'}</span></p>
                </div>
                <div class="col-md-6">
                    <p><strong>Recommendation:</strong> <span class="badge ${aiAnalysis.recommendation === 'buy' ? 'bg-success' : aiAnalysis.recommendation === 'sell' ? 'bg-danger' : 'bg-warning'}">${aiAnalysis.recommendation || 'Hold'}</span></p>
                    <p><strong>Support Levels:</strong> ${aiAnalysis.support_levels ? aiAnalysis.support_levels.join(', ') : 'N/A'}</p>
                    <p><strong>Resistance Levels:</strong> ${aiAnalysis.resistance_levels ? aiAnalysis.resistance_levels.join(', ') : 'N/A'}</p>
                </div>
            </div>
            <div class="mt-3">
                <p><strong>AI Reasoning:</strong></p>
                <p class="text-muted">${aiAnalysis.reasoning || 'No detailed reasoning available'}</p>
            </div>
        </div>
    `;
}

/**
 * Add enhanced analysis badge
 */
function addEnhancedAnalysisBadge() {
    const analyzeBtn = document.getElementById('analyzeBtn');
    if (analyzeBtn && !analyzeBtn.querySelector('.enhanced-badge')) {
        const badge = document.createElement('span');
        badge.className = 'badge bg-primary ms-2 enhanced-badge';
        badge.textContent = 'AI Enhanced';
        analyzeBtn.appendChild(badge);
    }
}

// Export functions for use in other modules
window.app = {
    loadMarketData,
    updateChart,
    generateTradingSignals,
    analyzeMarket,
    appState
};
