# Trading Signals App - API Keys
# Copy this file to .env and replace the placeholder values with your actual API keys

# Server Configuration
NODE_ENV=development
PORT=3000

# Security
JWT_SECRET=41bf815babc6bfa787adf0ffb2b828bdd5c997587d912fa8050c5c39bddb1c26
CORS_ORIGIN=http://localhost:3000

# API Keys
ALPHA_VANTAGE_API_KEY=OFU3DJH5JWW6Z29Z
FRED_API_KEY=de959b1589a8cd6ed0772b9127853054
POLYGON_API_KEY=6FKjerAmCYij4q88up_aK6bfGX92hH3a
FINNHUB_API_KEY=d0chd71r01ql2j3d70cgd0chd71r01ql2j3d70d0
TWELVE_DATA_API_KEY=421877eb6ed94817922c6d382257767a
OPENAI_API_KEY=***********************************************************************************************************************************************************************
FMP_API_KEY=vknBnOwDfKemKATFGD8LhK9jTK5FBFSI

# Payment Processing
STRIPE_SECRET_KEY=sk_test_51ROMviQ7sBel8Rbu4s64eyZqWOk0yO4rcjY7379Lu33iodBIuZhlGoM3hK5wemhgRi4ZDGLvrKAkMCqjpzJpVcQx00gWEVslq3
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Error Monitoring
SENTRY_DSN=https://<EMAIL>/4509316906811472


# Logging
LOG_LEVEL=info

# Database (PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=trading_signals
DB_USER=postgres
DB_PASSWORD=your-db-password
