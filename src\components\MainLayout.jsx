import React, { useState, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import NotificationBell from './notifications/NotificationBell.jsx';
import ProfileModal from './ProfileModal.jsx';
import HelpModal from './HelpModal.jsx';
import Joyride, { STATUS } from 'react-joyride';

export default function MainLayout({ children }) {
  const [profileOpen, setProfileOpen] = useState(false);
  const [helpOpen, setHelpOpen] = useState(false);
  const [tourRun, setTourRun] = useState(false);
  const [tourStepIndex, setTourStepIndex] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const location = useLocation();

  // Navigation items
  const navItems = [
    { path: '/dashboard', label: 'Dashboard', icon: '📊' },
    { path: '/signals', label: 'Trading Signals', icon: '🔔' },
    { path: '/analysis', label: 'Technical Analysis', icon: '📈' },
    { path: '/backtesting', label: 'Backtesting', icon: '🧪' },
    { path: '/alerts', label: 'Pattern Alerts', icon: '🔍' },
    { path: '/calendar', label: 'Economic Calendar', icon: '📅' },
    { path: '/news', label: 'Market News', icon: '📰' },
    { path: '/settings', label: 'Settings', icon: '⚙️' }
  ];

  // Joyride steps
  const tourSteps = [
    {
      target: '[data-tour="notification-bell"]',
      content: 'This is your Notification Center. Click to view alerts and updates.',
      placement: 'bottom',
    },
    {
      target: '[data-tour="help-icon"]',
      content: 'Need help? Click here for FAQs, contact, and to start this tour again.',
      placement: 'bottom',
    },
    {
      target: '[data-tour="profile-icon"]',
      content: 'Manage your profile, password, avatar, and preferences here.',
      placement: 'bottom',
    },
    {
      target: '[data-tour="main-content"]',
      content: 'This is where your main content and features will appear.',
      placement: 'top',
    },
  ];

  // Handle tour finish/close
  const handleJoyrideCallback = (data) => {
    const { status, index, type } = data;
    if ([STATUS.FINISHED, STATUS.SKIPPED].includes(status)) {
      setTourRun(false);
      setTourStepIndex(0);
    } else if (type === 'step:after') {
      setTourStepIndex(index + 1);
    }
  };

  // Start tour from HelpModal
  const handleStartTour = () => {
    setHelpOpen(false);
    setTimeout(() => setTourRun(true), 300); // Delay to allow modal to close
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      <Joyride
        steps={tourSteps}
        run={tourRun}
        stepIndex={tourStepIndex}
        continuous
        showSkipButton
        showProgress
        styles={{ options: { zIndex: 10000 } }}
        callback={handleJoyrideCallback}
      />
      {/* Top Navigation Bar */}
      <nav className="sticky top-0 z-40 flex items-center justify-between px-4 sm:px-6 py-3 bg-white dark:bg-gray-800 shadow w-full">
        <div className="flex items-center gap-2 sm:gap-4">
          <button
            className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none md:hidden"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            aria-label="Toggle Sidebar"
          >
            <span role="img" aria-label="Menu" className="text-xl">☰</span>
          </button>
          <span className="text-lg sm:text-xl font-bold text-blue-600 dark:text-blue-300 whitespace-nowrap">Trading Signals App</span>
        </div>
        <div className="flex items-center gap-2 sm:gap-4">
          {/* Add more nav items here if needed */}
          <span data-tour="notification-bell"><NotificationBell /></span>
          <button
            className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none"
            aria-label="Help"
            onClick={() => setHelpOpen(true)}
            data-tour="help-icon"
          >
            <span role="img" aria-label="Help" className="text-xl">❓</span>
          </button>
          <button
            className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none"
            aria-label="Profile & Settings"
            onClick={() => setProfileOpen(true)}
            data-tour="profile-icon"
          >
            {/* Simple user icon (emoji or SVG) */}
            <span role="img" aria-label="User" className="text-xl">👤</span>
          </button>
        </div>
      </nav>

      {/* Main Content with Sidebar */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar Navigation */}
        <aside
          className={`bg-white dark:bg-gray-800 w-64 shadow-md transition-all duration-300 ease-in-out ${
            sidebarOpen ? 'translate-x-0' : '-translate-x-full'
          } md:translate-x-0 fixed md:static h-[calc(100vh-3.5rem)] z-30 top-14`}
        >
          <nav className="p-4 h-full overflow-y-auto">
            <ul className="space-y-2">
              {navItems.map((item) => (
                <li key={item.path}>
                  <Link
                    to={item.path}
                    className={`flex items-center p-3 rounded-lg transition-colors ${
                      location.pathname === item.path
                        ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200'
                        : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                  >
                    <span className="mr-3">{item.icon}</span>
                    <span>{item.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </aside>

        {/* Main Content */}
        <main
          className={`flex-1 p-4 overflow-auto transition-all duration-300 ease-in-out ${
            sidebarOpen ? 'md:ml-0' : 'md:ml-0'
          }`}
          data-tour="main-content"
        >
          <div className="max-w-4xl mx-auto">
            {children}
          </div>
        </main>
      </div>

      {/* Backdrop for mobile sidebar */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}

      <ProfileModal open={profileOpen} onClose={() => setProfileOpen(false)} />
      <HelpModal open={helpOpen} onClose={() => setHelpOpen(false)} onStartTour={handleStartTour} />
    </div>
  );
}