/**
 * @swagger
 * tags:
 *   name: Signals
 *   description: Trading signals management
 */

/**
 * @swagger
 * /api/signals:
 *   get:
 *     summary: Get trading signals with pagination
 *     tags: [Signals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: symbol
 *         schema:
 *           type: string
 *         description: Filter by trading symbol
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [buy, sell]
 *         description: Filter by signal type
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, executed, expired]
 *         description: Filter by signal status
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [M1, M5, M15, M30, H1, H4, D1, W1, MN]
 *         description: Filter by timeframe
 *     responses:
 *       200:
 *         description: Trading signals retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/TradingSignal'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                           example: 1
 *                         limit:
 *                           type: integer
 *                           example: 20
 *                         totalItems:
 *                           type: integer
 *                           example: 50
 *                         totalPages:
 *                           type: integer
 *                           example: 3
 *                         hasNextPage:
 *                           type: boolean
 *                           example: true
 *                         hasPrevPage:
 *                           type: boolean
 *                           example: false
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   post:
 *     summary: Create a new trading signal
 *     tags: [Signals]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symbol
 *               - type
 *               - timeframe
 *               - entryPrice
 *             properties:
 *               symbol:
 *                 type: string
 *                 description: Trading symbol
 *                 example: EURUSD
 *               type:
 *                 type: string
 *                 enum: [buy, sell]
 *                 description: Signal type
 *                 example: buy
 *               strength:
 *                 type: integer
 *                 minimum: 0
 *                 maximum: 100
 *                 description: Signal strength (0-100)
 *                 example: 75
 *               timeframe:
 *                 type: string
 *                 enum: [M1, M5, M15, M30, H1, H4, D1, W1, MN]
 *                 description: Chart timeframe
 *                 example: H1
 *               entryPrice:
 *                 type: number
 *                 description: Entry price
 *                 example: 1.0875
 *               stopLoss:
 *                 type: number
 *                 description: Stop loss price
 *                 example: 1.0850
 *               takeProfit:
 *                 type: number
 *                 description: Take profit price
 *                 example: 1.0925
 *               notes:
 *                 type: string
 *                 description: Signal notes
 *                 example: Strong bullish momentum
 *     responses:
 *       201:
 *         description: Signal created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Signal created successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     signal:
 *                       $ref: '#/components/schemas/TradingSignal'
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /api/signals/{id}:
 *   get:
 *     summary: Get a trading signal by ID
 *     tags: [Signals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Signal ID
 *     responses:
 *       200:
 *         description: Trading signal retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     signal:
 *                       $ref: '#/components/schemas/TradingSignal'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   put:
 *     summary: Update a trading signal
 *     tags: [Signals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Signal ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               symbol:
 *                 type: string
 *                 description: Trading symbol
 *                 example: EURUSD
 *               type:
 *                 type: string
 *                 enum: [buy, sell]
 *                 description: Signal type
 *                 example: buy
 *               strength:
 *                 type: integer
 *                 minimum: 0
 *                 maximum: 100
 *                 description: Signal strength (0-100)
 *                 example: 75
 *               timeframe:
 *                 type: string
 *                 enum: [M1, M5, M15, M30, H1, H4, D1, W1, MN]
 *                 description: Chart timeframe
 *                 example: H1
 *               entryPrice:
 *                 type: number
 *                 description: Entry price
 *                 example: 1.0875
 *               stopLoss:
 *                 type: number
 *                 description: Stop loss price
 *                 example: 1.0850
 *               takeProfit:
 *                 type: number
 *                 description: Take profit price
 *                 example: 1.0925
 *               notes:
 *                 type: string
 *                 description: Signal notes
 *                 example: Strong bullish momentum
 *               status:
 *                 type: string
 *                 enum: [active, executed, expired]
 *                 description: Signal status
 *                 example: executed
 *     responses:
 *       200:
 *         description: Signal updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Signal updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     signal:
 *                       $ref: '#/components/schemas/TradingSignal'
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   delete:
 *     summary: Delete a trading signal
 *     tags: [Signals]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Signal ID
 *     responses:
 *       200:
 *         description: Signal deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Signal deleted successfully
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         $ref: '#/components/responses/NotFoundError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
