import Signal from '../models/Signal.js';

class SignalsService {
  async getAllSignals() {
    return Signal.find().sort({ createdAt: -1 });
  }
  async createSignal(data) {
    const signal = new Signal(data);
    await signal.save();
    return signal;
  }
  async getSignalById(id) {
    return Signal.findById(id);
  }
  async deleteSignal(id) {
    return Signal.findByIdAndDelete(id);
  }
}

export default new SignalsService(); 