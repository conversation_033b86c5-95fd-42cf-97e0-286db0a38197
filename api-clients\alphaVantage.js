const axios = require('axios');
const logger = console;

// Rate limiting and retry configuration
const RATE_LIMIT_DELAY = 1000; // 1 second between requests
const MAX_RETRIES = 3;

// Error handling wrapper with retries
const handleApiRequest = async (requestFn) => {
  let attempts = 0;
  while (attempts < MAX_RETRIES) {
    try {
      if (attempts > 0) {
        await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY));
      }
      return await requestFn();
    } catch (error) {
      attempts++;
      if (attempts === MAX_RETRIES) {
        if (error.response) {
          logger.error(`Alpha Vantage API error: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
          throw new Error(`Alpha Vantage API error: ${error.response.status}`);
        } else if (error.request) {
          logger.error('Alpha Vantage API no response');
          throw new Error('Alpha Vantage API no response');
        } else {
          logger.error(`Alpha Vantage API request failed: ${error.message}`);
          throw new Error(`Alpha Vantage API request failed: ${error.message}`);
        }
      }
      logger.warn(`Retrying request, attempt ${attempts} of ${MAX_RETRIES}`);
    }
  }
};

/**
 * Alpha Vantage API client
 * @param {Object} query - Query parameters
 * @param {string} query.type - Market type (forex, stocks, commodities, economics)
 * @param {string} query.symbol - Market symbol
 * @param {string} query.timeframe - Time frame (M1, M5, M15, M30, H1, H4, D1, W1, MN)
 * @returns {Promise<Object>} - Market data
 */
module.exports = async function alphaVantage(query) {
  // Check if API key is available
  const apiKey = process.env.ALPHA_VANTAGE_API_KEY;
  if (!apiKey) {
    throw new Error('Alpha Vantage API key is not set');
  }

  // Map timeframe to Alpha Vantage interval
  const timeframeMap = {
    'M1': '1min',
    'M5': '5min', 
    'M15': '15min',
    'M30': '30min',
    'H1': '60min',
    'H4': '4h', // Not supported directly, will use 60min
    'D1': 'daily',
    'W1': 'weekly',
    'MN': 'monthly'
  };

  const interval = timeframeMap[query.timeframe] || 'daily';
  let params = {};
  let url = 'https://www.alphavantage.co/query';
  let endpoint = '';

  try {
    if (query.type === 'commodities') {
      // For time series data
      if (interval !== 'daily') {
        params = {
          function: 'TIME_SERIES_INTRADAY',
          symbol: `${query.symbol.toUpperCase()}.COM`, // Commodity format
          interval: interval === 'daily' ? '60min' : interval,
          apikey: apiKey,
          datatype: 'json',
          outputsize: 'compact'
        };
        endpoint = 'TIME_SERIES_INTRADAY';
      } else {
        params = {
          function: 'COMMODITY_DAILY',
          symbol: query.symbol,
          apikey: apiKey
        };
        endpoint = 'COMMODITY_DAILY';
      }
    } else if (query.type === 'forex') {
      const fromCurrency = query.symbol.slice(0, 3);
      const toCurrency = query.symbol.slice(3, 6) || 'USD';
      
      if (interval.includes('min')) {
        params = {
          function: 'FX_INTRADAY',
          from_symbol: fromCurrency,
          to_symbol: toCurrency,
          interval: interval,
          apikey: apiKey,
          datatype: 'json',
          outputsize: 'compact'
        };
        endpoint = 'FX_INTRADAY';
      } else {
        params = {
          function: 'FX_DAILY',
          from_symbol: fromCurrency,
          to_symbol: toCurrency,
          apikey: apiKey,
          datatype: 'json',
          outputsize: 'compact'
        };
        endpoint = 'FX_DAILY';
      }
    } else if (query.type === 'stocks') {
      if (interval.includes('min')) {
        params = {
          function: 'TIME_SERIES_INTRADAY',
          symbol: query.symbol,
          interval: interval,
          apikey: apiKey,
          datatype: 'json',
          outputsize: 'compact'
        };
        endpoint = 'TIME_SERIES_INTRADAY';
      } else if (interval === 'daily') {
        params = {
          function: 'TIME_SERIES_DAILY',
          symbol: query.symbol,
          apikey: apiKey,
          datatype: 'json',
          outputsize: 'compact'
        };
        endpoint = 'TIME_SERIES_DAILY';
      } else if (interval === 'weekly') {
        params = {
          function: 'TIME_SERIES_WEEKLY',
          symbol: query.symbol,
          apikey: apiKey,
          datatype: 'json'
        };
        endpoint = 'TIME_SERIES_WEEKLY';
      } else if (interval === 'monthly') {
        params = {
          function: 'TIME_SERIES_MONTHLY',
          symbol: query.symbol,
          apikey: apiKey,
          datatype: 'json'
        };
        endpoint = 'TIME_SERIES_MONTHLY';
      }
    } else if (query.type === 'economics') {
      params = {
        function: 'ECONOMIC_INDICATOR',
        indicator: query.symbol,
        apikey: apiKey
      };
      endpoint = 'ECONOMIC_INDICATOR';
    } else {
      throw new Error(`Unsupported market type for Alpha Vantage: ${query.type}`);
    }

    console.log(`Calling Alpha Vantage API: ${endpoint} for ${query.symbol}`);
    const response = await handleApiRequest(async () => {
      return await axios.get(url, {
        params,
        timeout: 10000,
        headers: {
          'User-Agent': 'TradingSignalsApp/1.0'
        }
      });
    });

    if (response.data && response.data['Error Message']) {
      throw new Error(`Alpha Vantage error: ${response.data['Error Message']}`);
    }

    if (response.data && response.data['Note'] && response.data['Note'].includes('API call frequency')) {
      throw new Error('Alpha Vantage API call frequency exceeded');
    }

    return formatResponse(response.data, query, endpoint);
  } catch (error) {
    console.error('Alpha Vantage API error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    throw error;
  }
};

/**
 * Format the Alpha Vantage response to a standard format
 * @param {Object} data - Alpha Vantage response
 * @param {Object} query - Original query
 * @param {string} endpoint - API endpoint used
 * @returns {Object} - Formatted response
 */
function formatResponse(data, query, endpoint) {
  if (!data) {
    throw new Error('Empty response from Alpha Vantage');
  }

  // Return standardized format for all market types
  const result = {
    symbol: query.symbol,
    timeframe: query.timeframe,
    type: query.type,
    source: 'alpha_vantage',
    timestamp: new Date().toISOString(),
    data: []
  };

  try {
    // Extract time series data based on the endpoint
    if (endpoint.includes('INTRADAY')) {
      const timeSeriesKey = `Time Series (${data.Meta?.Interval || '5min'})`;
      const timeSeries = data[timeSeriesKey];
      
      if (timeSeries) {
        result.data = Object.entries(timeSeries).map(([timestamp, values]) => ({
          timestamp,
          open: parseFloat(values['1. open']),
          high: parseFloat(values['2. high']),
          low: parseFloat(values['3. low']),
          close: parseFloat(values['4. close']),
          volume: parseInt(values['5. volume'], 10)
        })).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      }
    } else if (endpoint.includes('DAILY')) {
      const timeSeriesKey = 'Time Series (Daily)';
      const timeSeries = data[timeSeriesKey];
      
      if (timeSeries) {
        result.data = Object.entries(timeSeries).map(([timestamp, values]) => ({
          timestamp,
          open: parseFloat(values['1. open']),
          high: parseFloat(values['2. high']),
          low: parseFloat(values['3. low']),
          close: parseFloat(values['4. close']),
          volume: parseInt(values['5. volume'], 10)
        })).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      }
    } else if (endpoint.includes('WEEKLY')) {
      const timeSeriesKey = 'Weekly Time Series';
      const timeSeries = data[timeSeriesKey];
      
      if (timeSeries) {
        result.data = Object.entries(timeSeries).map(([timestamp, values]) => ({
          timestamp,
          open: parseFloat(values['1. open']),
          high: parseFloat(values['2. high']),
          low: parseFloat(values['3. low']),
          close: parseFloat(values['4. close']),
          volume: parseInt(values['5. volume'], 10)
        })).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      }
    } else if (endpoint.includes('MONTHLY')) {
      const timeSeriesKey = 'Monthly Time Series';
      const timeSeries = data[timeSeriesKey];
      
      if (timeSeries) {
        result.data = Object.entries(timeSeries).map(([timestamp, values]) => ({
          timestamp,
          open: parseFloat(values['1. open']),
          high: parseFloat(values['2. high']),
          low: parseFloat(values['3. low']),
          close: parseFloat(values['4. close']),
          volume: parseInt(values['5. volume'], 10)
        })).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      }
    } else if (endpoint === 'GLOBAL_QUOTE') {
      const quote = data['Global Quote'];
      if (quote) {
        result.data = [{
          timestamp: new Date().toISOString(),
          open: parseFloat(quote['02. open']),
          high: parseFloat(quote['03. high']),
          low: parseFloat(quote['04. low']),
          close: parseFloat(quote['05. price']),
          volume: parseInt(quote['06. volume'], 10),
          change: parseFloat(quote['09. change']),
          changePercent: quote['10. change percent'].replace('%', '')
        }];
      }
    } else if (endpoint === 'CURRENCY_EXCHANGE_RATE' || endpoint === 'COMMODITY_EXCHANGE_RATE') {
      const exchangeRate = data['Realtime Currency Exchange Rate'];
      if (exchangeRate) {
        result.data = [{
          timestamp: exchangeRate['6. Last Refreshed'],
          rate: parseFloat(exchangeRate['5. Exchange Rate']),
          bid: parseFloat(exchangeRate['8. Bid Price'] || exchangeRate['5. Exchange Rate']),
          ask: parseFloat(exchangeRate['9. Ask Price'] || exchangeRate['5. Exchange Rate'])
        }];
      }
    }
    
    // Add additional metadata if available
    if (data.Meta) {
      result.meta = {
        information: data.Meta.Information,
        symbol: data.Meta.Symbol,
        lastRefreshed: data.Meta['Last Refreshed'],
        timezone: data.Meta['Time Zone']
      };
    }
    
    return result;
  } catch (error) {
    console.error('Error formatting Alpha Vantage response:', error);
    throw new Error(`Failed to format Alpha Vantage response: ${error.message}`);
  }
}