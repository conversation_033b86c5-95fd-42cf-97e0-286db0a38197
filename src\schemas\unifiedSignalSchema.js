/**
 * Unified Signal Schema
 * 
 * This file defines the standardized signal data structure that all components
 * across the application must follow. This ensures consistency between
 * frontend, backend, database, and API documentation.
 */

const { z } = require('zod');

/**
 * Signal Types Enum
 */
const SIGNAL_TYPES = {
  BUY: 'BUY',
  SELL: 'SELL',
  HOLD: 'HOLD'
};

/**
 * Signal Sources Enum
 */
const SIGNAL_SOURCES = {
  AI: 'AI',
  TRADITIONAL: 'TRADITIONAL',
  UNIFIED: 'UNIFIED',
  MANUAL: 'MANUAL'
};

/**
 * Timeframes Enum
 */
const TIMEFRAMES = {
  M1: 'M1',
  M5: 'M5',
  M15: 'M15',
  M30: 'M30',
  H1: 'H1',
  H4: 'H4',
  D1: 'D1',
  W1: 'W1',
  MN: 'MN'
};

/**
 * Signal Status Enum
 */
const SIGNAL_STATUS = {
  ACTIVE: 'ACTIVE',
  CLOSED: 'CLOSED',
  EXPIRED: 'EXPIRED',
  CANCELLED: 'CANCELLED'
};

/**
 * Core Signal Schema (Zod validation)
 */
const UnifiedSignalSchema = z.object({
  // Core identification
  id: z.string().optional(), // Generated by system
  _id: z.string().optional(), // MongoDB ObjectId
  
  // Basic signal information
  symbol: z.string().min(3).max(20),
  type: z.enum([SIGNAL_TYPES.BUY, SIGNAL_TYPES.SELL, SIGNAL_TYPES.HOLD]),
  source: z.enum([SIGNAL_SOURCES.AI, SIGNAL_SOURCES.TRADITIONAL, SIGNAL_SOURCES.UNIFIED, SIGNAL_SOURCES.MANUAL]),
  subSource: z.string().optional(), // e.g., 'openai', 'rsi', 'macd'
  
  // Price information (all required for trading signals)
  entryPrice: z.number().positive(),
  stopLoss: z.number().positive().optional(),
  takeProfit: z.number().positive().optional(),
  
  // Signal quality metrics
  confidence: z.number().min(0).max(100), // 0-100 percentage
  strength: z.number().min(0).max(1).optional(), // 0-1 decimal (legacy support)
  qualityScore: z.number().min(0).max(100).optional(), // Overall quality assessment
  
  // Timing information
  timeframe: z.enum([TIMEFRAMES.M1, TIMEFRAMES.M5, TIMEFRAMES.M15, TIMEFRAMES.M30, 
                     TIMEFRAMES.H1, TIMEFRAMES.H4, TIMEFRAMES.D1, TIMEFRAMES.W1, TIMEFRAMES.MN]),
  timestamp: z.string().datetime(), // ISO 8601 format
  expiresAt: z.string().datetime().optional(),
  
  // Analysis and reasoning
  message: z.string().min(1).max(500), // Short description
  reasoning: z.string().max(2000).optional(), // Detailed analysis
  analysis: z.string().max(2000).optional(), // Legacy support
  
  // Technical indicators (optional)
  technicalIndicators: z.object({
    rsi: z.number().optional(),
    macd: z.object({
      line: z.number(),
      signal: z.number(),
      histogram: z.number()
    }).optional(),
    movingAverages: z.object({
      sma20: z.number().optional(),
      sma50: z.number().optional(),
      sma200: z.number().optional(),
      ema20: z.number().optional()
    }).optional(),
    bollingerBands: z.object({
      upper: z.number(),
      middle: z.number(),
      lower: z.number()
    }).optional(),
    volume: z.number().optional()
  }).optional(),
  
  // Risk management
  riskReward: z.number().positive().optional(), // Risk/reward ratio
  positionSize: z.number().positive().optional(), // Suggested position size
  maxRisk: z.number().min(0).max(100).optional(), // Max risk percentage
  
  // Status and lifecycle
  status: z.enum([SIGNAL_STATUS.ACTIVE, SIGNAL_STATUS.CLOSED, SIGNAL_STATUS.EXPIRED, SIGNAL_STATUS.CANCELLED]).default(SIGNAL_STATUS.ACTIVE),
  
  // User and system information
  userId: z.string().optional(), // User who created/owns the signal
  generatedBy: z.string().optional(), // System/user identifier
  
  // Performance tracking (for closed signals)
  performance: z.object({
    actualEntry: z.number().optional(),
    actualExit: z.number().optional(),
    pnl: z.number().optional(), // Profit/loss
    pnlPercentage: z.number().optional(),
    accuracy: z.number().min(0).max(100).optional(), // How accurate the signal was
    duration: z.number().optional() // Duration in milliseconds
  }).optional(),
  
  // Metadata
  metadata: z.object({
    model: z.string().optional(), // AI model used
    processingTime: z.number().optional(), // Generation time in ms
    tokens: z.number().optional(), // AI tokens used
    version: z.string().optional(), // Schema version
    tags: z.array(z.string()).optional() // Custom tags
  }).optional(),
  
  // Audit fields
  createdAt: z.string().datetime().optional(),
  updatedAt: z.string().datetime().optional(),
  
  // Legacy support fields (will be deprecated)
  notes: z.string().optional(),
  market: z.string().optional(),
  indicators: z.array(z.string()).optional()
});

/**
 * MongoDB Schema Definition
 */
const MongoSignalSchema = {
  // Core identification
  symbol: { type: String, required: true, index: true },
  type: { type: String, enum: Object.values(SIGNAL_TYPES), required: true },
  source: { type: String, enum: Object.values(SIGNAL_SOURCES), required: true },
  subSource: { type: String },
  
  // Price information
  entryPrice: { type: Number, required: true },
  stopLoss: { type: Number },
  takeProfit: { type: Number },
  
  // Signal quality metrics
  confidence: { type: Number, min: 0, max: 100, required: true },
  strength: { type: Number, min: 0, max: 1 }, // Legacy support
  qualityScore: { type: Number, min: 0, max: 100 },
  
  // Timing information
  timeframe: { type: String, enum: Object.values(TIMEFRAMES), required: true },
  timestamp: { type: Date, default: Date.now, index: true },
  expiresAt: { type: Date },
  
  // Analysis and reasoning
  message: { type: String, required: true, maxlength: 500 },
  reasoning: { type: String, maxlength: 2000 },
  analysis: { type: String, maxlength: 2000 }, // Legacy support
  
  // Technical indicators
  technicalIndicators: {
    rsi: Number,
    macd: {
      line: Number,
      signal: Number,
      histogram: Number
    },
    movingAverages: {
      sma20: Number,
      sma50: Number,
      sma200: Number,
      ema20: Number
    },
    bollingerBands: {
      upper: Number,
      middle: Number,
      lower: Number
    },
    volume: Number
  },
  
  // Risk management
  riskReward: { type: Number },
  positionSize: { type: Number },
  maxRisk: { type: Number, min: 0, max: 100 },
  
  // Status and lifecycle
  status: { type: String, enum: Object.values(SIGNAL_STATUS), default: SIGNAL_STATUS.ACTIVE, index: true },
  
  // User and system information
  userId: { type: String, index: true },
  generatedBy: { type: String, default: 'system' },
  
  // Performance tracking
  performance: {
    actualEntry: Number,
    actualExit: Number,
    pnl: Number,
    pnlPercentage: Number,
    accuracy: { type: Number, min: 0, max: 100 },
    duration: Number
  },
  
  // Metadata
  metadata: {
    model: String,
    processingTime: Number,
    tokens: Number,
    version: { type: String, default: '1.0' },
    tags: [String]
  },
  
  // Audit fields
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  
  // Legacy support fields
  notes: String,
  market: String,
  indicators: [String]
};

/**
 * Swagger/OpenAPI Schema Definition
 */
const SwaggerSignalSchema = {
  type: 'object',
  required: ['symbol', 'type', 'source', 'entryPrice', 'confidence', 'timeframe', 'message'],
  properties: {
    id: {
      type: 'string',
      description: 'Unique signal identifier',
      example: 'signal_1234567890_abc123'
    },
    _id: {
      type: 'string',
      description: 'MongoDB ObjectId',
      example: '60d21b4667d0d8992e610c85'
    },
    symbol: {
      type: 'string',
      description: 'Trading symbol',
      example: 'EURUSD',
      minLength: 3,
      maxLength: 20
    },
    type: {
      type: 'string',
      enum: Object.values(SIGNAL_TYPES),
      description: 'Signal type',
      example: 'BUY'
    },
    source: {
      type: 'string',
      enum: Object.values(SIGNAL_SOURCES),
      description: 'Signal source',
      example: 'AI'
    },
    subSource: {
      type: 'string',
      description: 'Specific source identifier',
      example: 'openai'
    },
    entryPrice: {
      type: 'number',
      description: 'Entry price for the signal',
      example: 1.0850,
      minimum: 0
    },
    stopLoss: {
      type: 'number',
      description: 'Stop loss price',
      example: 1.0800,
      minimum: 0
    },
    takeProfit: {
      type: 'number',
      description: 'Take profit price',
      example: 1.0950,
      minimum: 0
    },
    confidence: {
      type: 'integer',
      description: 'Signal confidence percentage (0-100)',
      example: 85,
      minimum: 0,
      maximum: 100
    },
    timeframe: {
      type: 'string',
      enum: Object.values(TIMEFRAMES),
      description: 'Chart timeframe',
      example: 'H1'
    },
    timestamp: {
      type: 'string',
      format: 'date-time',
      description: 'Signal generation timestamp',
      example: '2024-01-15T10:30:00.000Z'
    },
    message: {
      type: 'string',
      description: 'Short signal description',
      example: 'Bullish momentum detected with RSI oversold recovery',
      maxLength: 500
    },
    reasoning: {
      type: 'string',
      description: 'Detailed analysis and reasoning',
      example: 'Technical analysis shows strong bullish momentum...',
      maxLength: 2000
    },
    status: {
      type: 'string',
      enum: Object.values(SIGNAL_STATUS),
      description: 'Signal status',
      example: 'ACTIVE'
    }
  }
};

/**
 * Utility functions for signal transformation
 */
const SignalTransformers = {
  /**
   * Transform legacy signal to unified format
   */
  fromLegacy: (legacySignal) => {
    return {
      symbol: legacySignal.symbol,
      type: legacySignal.type?.toUpperCase() || 'HOLD',
      source: legacySignal.generatedBy === 'ai' ? SIGNAL_SOURCES.AI : SIGNAL_SOURCES.TRADITIONAL,
      entryPrice: legacySignal.entryPoint || legacySignal.entryPrice || legacySignal.entry_price,
      stopLoss: legacySignal.stopLoss,
      takeProfit: legacySignal.takeProfit,
      confidence: legacySignal.confidence || (legacySignal.strength ? Math.round(legacySignal.strength * 100) : 50),
      timeframe: legacySignal.timeframe || 'H1',
      timestamp: legacySignal.timestamp || legacySignal.createdAt || new Date().toISOString(),
      message: legacySignal.message || legacySignal.analysis || 'Signal generated',
      reasoning: legacySignal.reasoning || legacySignal.analysis,
      status: SIGNAL_STATUS.ACTIVE,
      generatedBy: legacySignal.generatedBy || 'system',
      notes: legacySignal.notes
    };
  },

  /**
   * Transform unified signal to API response format
   */
  toApiResponse: (signal) => {
    const { _id, __v, ...cleanSignal } = signal;
    return {
      id: signal.id || signal._id,
      ...cleanSignal
    };
  },

  /**
   * Validate signal data
   */
  validate: (signalData) => {
    try {
      return UnifiedSignalSchema.parse(signalData);
    } catch (error) {
      throw new Error(`Signal validation failed: ${error.message}`);
    }
  }
};

module.exports = {
  SIGNAL_TYPES,
  SIGNAL_SOURCES,
  TIMEFRAMES,
  SIGNAL_STATUS,
  UnifiedSignalSchema,
  MongoSignalSchema,
  SwaggerSignalSchema,
  SignalTransformers
};
