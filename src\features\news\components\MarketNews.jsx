import React, { useState, useEffect } from 'react';
import { finnhubAPI } from '../../../services/apiService.js';

/**
 * MarketNews Component
 * 
 * Displays financial market news and analysis
 */
const MarketNews = () => {
  // State for news data
  const [newsData, setNewsData] = useState({
    articles: [],
    loading: true,
    error: null
  });
  
  // State for filters
  const [filters, setFilters] = useState({
    category: 'general',
    symbol: '',
    date: new Date().toISOString().split('T')[0]
  });
  
  // Available categories
  const categories = [
    { value: 'general', label: 'General' },
    { value: 'forex', label: 'Forex' },
    { value: 'crypto', label: 'Cryptocurrency' },
    { value: 'merger', label: 'Mergers & Acquisitions' },
    { value: 'economic', label: 'Economic' }
  ];
  
  // Fetch news data
  const fetchNewsData = async () => {
    setNewsData(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      // In a real app, this would be an API call to your backend
      // which would then fetch data from Finnhub or another news provider
      
      let response;
      
      if (filters.symbol) {
        // Fetch company news
        response = await finnhubAPI.get('company-news', {
          symbol: filters.symbol,
          from: filters.date,
          to: filters.date
        });
      } else {
        // Fetch market news
        response = await finnhubAPI.get('news', {
          category: filters.category
        });
      }
      
      if (Array.isArray(response)) {
        setNewsData({
          articles: response,
          loading: false,
          error: null
        });
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching news data:', error);
      setNewsData(prev => ({
        ...prev,
        loading: false,
        error: 'Failed to load market news. Please try again later.'
      }));
      
      // For demo purposes, load mock news data if API fails
      loadMockNewsData();
    }
  };
  
  // Load mock news data (for demo purposes)
  const loadMockNewsData = () => {
    const mockArticles = [
      {
        id: 1,
        headline: 'Fed Signals Potential Rate Hike Pause in June Meeting',
        summary: 'Federal Reserve officials indicated they might skip an interest rate increase at their June meeting, though they remained concerned about inflation and emphasized that borrowing costs would likely need to rise further this year.',
        source: 'Wall Street Journal',
        url: 'https://www.wsj.com/articles/fed-signals-potential-rate-hike-pause-in-june-meeting-minutes-show-e3b9c8d1',
        datetime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        image: 'https://images.wsj.net/im-781321/social',
        category: 'economic',
        related: 'USD,EUR,JPY'
      },
      {
        id: 2,
        headline: 'Oil Prices Fall as U.S. Debt Ceiling Concerns Weigh on Market',
        summary: 'Oil prices dropped on Wednesday as concerns about a potential U.S. debt default and weak economic data from major economies raised demand worries, offsetting expectations of tighter supplies.',
        source: 'Reuters',
        url: 'https://www.reuters.com/business/energy/oil-prices-fall-us-debt-ceiling-concerns-weigh-market-2023-05-24/',
        datetime: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(), // 5 hours ago
        image: 'https://www.reuters.com/resizer/f8hy7EJ9JQbfEU9q89d6u71i9Yk=/1200x628/smart/filters:quality(80)/cloudfront-us-east-2.images.arcpublishing.com/reuters/XZLZJFWDAVPQ5GVXU7YORRPTPI.jpg',
        category: 'general',
        related: 'OIL,WTI,BRENT'
      },
      {
        id: 3,
        headline: 'EUR/USD Rises Above 1.0750 as Dollar Weakens on Debt Ceiling Concerns',
        summary: 'The EUR/USD pair extended its daily advance and climbed above 1.0750 in the American session on Wednesday. The broad-based selling pressure surrounding the US Dollar (USD) helps the pair push higher as investors assess the latest developments surrounding the debt ceiling negotiations.',
        source: 'FXStreet',
        url: 'https://www.fxstreet.com/news/eur-usd-rises-above-10750-as-dollar-weakens-on-debt-ceiling-concerns-202305241503',
        datetime: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago
        image: 'https://editorial.fxstreet.com/images/Markets/Currencies/Majors/EURUSD/euro-dollar-flag_20200427153211.jpg',
        category: 'forex',
        related: 'EUR,USD'
      },
      {
        id: 4,
        headline: 'Bitcoin Struggles to Break $27,000 as Crypto Market Remains Cautious',
        summary: 'Bitcoin continues to face resistance at the $27,000 level as the broader cryptocurrency market remains cautious amid regulatory concerns and macroeconomic uncertainties. Trading volume has decreased in recent days, indicating a potential consolidation phase.',
        source: 'CoinDesk',
        url: 'https://www.coindesk.com/markets/2023/05/24/bitcoin-struggles-to-break-27000-as-crypto-market-remains-cautious/',
        datetime: new Date(Date.now() - 10 * 60 * 60 * 1000).toISOString(), // 10 hours ago
        image: 'https://www.coindesk.com/resizer/9B_JBBg5jKEQQatcGHACxNJVSEA=/1200x628/center/middle/cloudfront-us-east-1.images.arcpublishing.com/coindesk/DPYXPRJSFVDKFPVDXQMDYIPCVA.jpg',
        category: 'crypto',
        related: 'BTC,ETH,XRP'
      },
      {
        id: 5,
        headline: 'Microsoft to Acquire Cybersecurity Firm for $3.5 Billion',
        summary: 'Microsoft announced plans to acquire a leading cybersecurity firm in a deal valued at approximately $3.5 billion, as the tech giant continues to strengthen its security offerings amid increasing cyber threats globally.',
        source: 'Bloomberg',
        url: 'https://www.bloomberg.com/news/articles/2023-05-24/microsoft-to-acquire-cybersecurity-firm-for-3-5-billion',
        datetime: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago
        image: 'https://assets.bwbx.io/images/users/iqjWHBFdfxIU/iKIWgaiJUtss/v0/1200x800.jpg',
        category: 'merger',
        related: 'MSFT'
      },
      {
        id: 6,
        headline: 'Gold Prices Steady as Investors Await Fed Minutes',
        summary: 'Gold prices held steady on Wednesday as investors awaited the release of minutes from the Federal Reserve\'s latest policy meeting for clues on the central bank\'s interest rate path, while a stronger dollar limited gains.',
        source: 'CNBC',
        url: 'https://www.cnbc.com/2023/05/24/gold-prices-steady-as-investors-await-fed-minutes.html',
        datetime: new Date(Date.now() - 15 * 60 * 60 * 1000).toISOString(), // 15 hours ago
        image: 'https://image.cnbcfm.com/api/v1/image/*********-*************-gettyimages-**********-AFP_32LN7NZ.jpeg',
        category: 'general',
        related: 'GOLD,XAU'
      }
    ];
    
    // Filter mock articles based on category
    const filteredArticles = filters.category === 'general' 
      ? mockArticles 
      : mockArticles.filter(article => article.category === filters.category);
    
    setNewsData({
      articles: filteredArticles,
      loading: false,
      error: null
    });
  };
  
  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Apply filters
  const applyFilters = () => {
    fetchNewsData();
  };
  
  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  // Fetch news data on component mount
  useEffect(() => {
    fetchNewsData();
  }, []);

  return (
    <div className="market-news p-4">
      <h1 className="text-2xl font-bold mb-6">Market News</h1>
      
      {/* Filters Section */}
      <section className="mb-8">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Filters</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Category
              </label>
              <select
                name="category"
                value={filters.category}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {categories.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
            
            {/* Symbol Filter (optional) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Symbol (optional)
              </label>
              <input
                type="text"
                name="symbol"
                value={filters.symbol}
                onChange={handleFilterChange}
                placeholder="e.g., AAPL, EURUSD"
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
            
            {/* Date Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Date
              </label>
              <input
                type="date"
                name="date"
                value={filters.date}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          </div>
          
          <div className="mt-4">
            <button
              onClick={applyFilters}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </section>
      
      {/* News Section */}
      <section>
        {newsData.loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : newsData.error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{newsData.error}</span>
          </div>
        ) : newsData.articles.length === 0 ? (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">No news articles found for the selected filters.</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {newsData.articles.map(article => (
              <div key={article.id} className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
                {article.image && (
                  <div className="h-48 overflow-hidden">
                    <img 
                      src={article.image} 
                      alt={article.headline} 
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-2">
                    <a 
                      href={article.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 dark:text-blue-400 hover:underline"
                    >
                      {article.headline}
                    </a>
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                    {article.summary}
                  </p>
                  
                  <div className="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
                    <span>{article.source}</span>
                    <span>{formatDate(article.datetime)}</span>
                  </div>
                  
                  {article.related && (
                    <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                      <div className="flex flex-wrap gap-2">
                        {article.related.split(',').map((tag, index) => (
                          <span 
                            key={index}
                            className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs"
                          >
                            {tag.trim()}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </section>
    </div>
  );
};

export default MarketNews;
