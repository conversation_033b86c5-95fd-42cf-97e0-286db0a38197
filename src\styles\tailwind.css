@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles that extend Tailwind */
@layer components {
  .btn-primary {
    @apply bg-navy-600 hover:bg-navy-700 text-white font-semibold py-2 px-4 rounded shadow-trading hover:shadow-trading-hover transition-all duration-300 hover:-translate-y-1;
  }

  .btn-secondary {
    @apply bg-teal-500 hover:bg-teal-600 text-white font-semibold py-2 px-4 rounded shadow-trading hover:shadow-trading-hover transition-all duration-300 hover:-translate-y-1;
  }

  .btn-gold {
    @apply bg-gold-500 hover:bg-gold-600 text-navy-900 font-semibold py-2 px-4 rounded shadow-trading hover:shadow-trading-hover transition-all duration-300 hover:-translate-y-1;
  }

  .card {
    @apply bg-white dark:bg-navy-800 rounded-lg shadow-trading hover:shadow-trading-hover transition-all duration-300 overflow-hidden;
  }

  .card-header {
    @apply bg-navy-600 dark:bg-navy-700 text-white font-bold p-4 rounded-t-lg;
  }

  .card-body {
    @apply p-4;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-navy-600 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 dark:bg-navy-900 dark:text-white transition-all duration-300;
  }

  .select-field {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-navy-600 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 dark:bg-navy-900 dark:text-white transition-all duration-300;
  }

  .badge-success {
    @apply bg-teal-500 text-white px-2 py-1 rounded-full text-xs font-semibold;
  }

  .badge-danger {
    @apply bg-red-500 text-white px-2 py-1 rounded-full text-xs font-semibold;
  }

  .badge-warning {
    @apply bg-gold-500 text-navy-900 px-2 py-1 rounded-full text-xs font-semibold;
  }

  .badge-info {
    @apply bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-semibold;
  }

  .animate-float {
    @apply hover:animate-float;
  }
}

/* Custom animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Micro-animations for button hover */
.btn-float {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.btn-float:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Dark mode adjustments */
[data-theme="dark"] {
  --tw-text-opacity: 1;
  color: rgba(248, 249, 250, var(--tw-text-opacity));
}

/* Progress bar styling */
.nprogress-bar {
  background: linear-gradient(to right, #2DBBB7, #FAC42D) !important;
  height: 3px !important;
}

/* Offline fallback page styling */
.offline-container {
  @apply flex flex-col items-center justify-center min-h-screen bg-gray-100 dark:bg-navy-900 p-4 text-center;
}

.offline-icon {
  @apply text-6xl text-navy-600 dark:text-teal-400 mb-4;
}

.offline-title {
  @apply text-2xl font-bold text-navy-800 dark:text-white mb-2;
}

.offline-message {
  @apply text-gray-600 dark:text-gray-300 mb-6 max-w-md;
}

.offline-button {
  @apply btn-primary mb-8;
}

.cached-content {
  @apply mt-8 border-t border-gray-200 dark:border-navy-700 pt-8 w-full max-w-md;
}

.cached-content h3 {
  @apply text-lg font-semibold mb-4 text-navy-800 dark:text-white;
}

.cached-content ul {
  @apply list-disc pl-5 text-left;
}

.cached-content li {
  @apply mb-2;
}

.cached-content a {
  @apply text-teal-600 dark:text-teal-400 hover:underline;
}

/* Voice input styling */
.voice-button {
  @apply fixed bottom-20 right-6 z-50 w-14 h-14 rounded-full bg-navy-600 text-white flex items-center justify-center shadow-trading hover:shadow-trading-hover cursor-pointer transition-all duration-300;
}

.voice-button:hover {
  @apply bg-navy-700 transform -translate-y-1;
}

.voice-button.listening {
  @apply bg-teal-500;
}

.voice-button i {
  @apply text-xl;
}

.listening-indicator {
  @apply fixed top-0 left-0 w-full h-full flex flex-col items-center justify-center bg-black bg-opacity-50 z-50;
  display: none;
}

.listening-ripple {
  @apply relative w-24 h-24 mb-4;
}

.listening-ripple div {
  @apply absolute border-4 border-teal-500 rounded-full animate-ping;
}

.listening-ripple div:nth-child(1) {
  @apply w-full h-full opacity-100;
  animation-duration: 1.8s;
}

.listening-ripple div:nth-child(2) {
  @apply w-full h-full opacity-70;
  animation-duration: 1.8s;
  animation-delay: 0.6s;
}

.listening-text {
  @apply text-white text-xl font-semibold;
}
