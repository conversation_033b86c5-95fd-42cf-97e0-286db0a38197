/**
 * API Key Manager
 * 
 * A robust system for managing multiple API keys with intelligent rotation,
 * error tracking, and automatic fallback.
 */

class ApiKeyManager {
    /**
     * Create a new API key manager
     * @param {string} providerName - Name of the API provider
     * @param {Array<string>} keys - Array of API keys
     * @param {Object} options - Configuration options
     */
    constructor(providerName, keys, options = {}) {
        this.providerName = providerName;
        this.keys = keys || [];
        this.currentIndex = 0;
        
        // Default options
        this.options = {
            cooldownPeriod: 60 * 60 * 1000, // 1 hour cooldown for failed keys
            maxFailures: 3,                 // Number of failures before cooldown
            retryDelay: 5000,               // Initial retry delay (5 seconds)
            maxRetryDelay: 60 * 60 * 1000,  // Maximum retry delay (1 hour)
            persistState: true,             // Whether to persist state to localStorage
            logLevel: 'info',               // Log level (debug, info, warn, error)
            ...options
        };
        
        // Key status tracking
        this.keyStatus = {};
        
        // Initialize key status
        this.keys.forEach(key => {
            this.keyStatus[key] = {
                failures: 0,
                lastFailure: null,
                cooldownUntil: null,
                totalCalls: 0,
                successfulCalls: 0
            };
        });
        
        // Load state from localStorage if enabled
        if (this.options.persistState) {
            this.loadState();
        }
        
        this.log('debug', `Initialized with ${this.keys.length} keys`);
    }
    
    /**
     * Get the next available API key
     * @returns {string} - API key
     */
    getNextKey() {
        if (this.keys.length === 0) {
            this.log('error', 'No API keys available');
            return null;
        }
        
        // Try to find a key that's not in cooldown
        const now = Date.now();
        let attempts = 0;
        let bestKey = null;
        let bestKeyFailures = Infinity;
        
        // First try to find a key that's not in cooldown
        while (attempts < this.keys.length) {
            const key = this.keys[this.currentIndex];
            const status = this.keyStatus[key];
            
            // If key is not in cooldown, use it
            if (!status.cooldownUntil || status.cooldownUntil < now) {
                // Track this call
                status.totalCalls++;
                
                this.log('debug', `Using key ${this.maskKey(key)} (index ${this.currentIndex})`);
                
                // Rotate to next key for next time
                this.currentIndex = (this.currentIndex + 1) % this.keys.length;
                
                // Save state
                if (this.options.persistState) {
                    this.saveState();
                }
                
                return key;
            }
            
            // Keep track of the key with the fewest failures as a fallback
            if (status.failures < bestKeyFailures) {
                bestKey = key;
                bestKeyFailures = status.failures;
            }
            
            // Try next key
            this.currentIndex = (this.currentIndex + 1) % this.keys.length;
            attempts++;
        }
        
        // If all keys are in cooldown, use the one with the fewest failures
        if (bestKey) {
            this.log('warn', `All keys in cooldown. Using key with fewest failures: ${this.maskKey(bestKey)}`);
            
            // Reset its cooldown since we're forced to use it
            this.keyStatus[bestKey].cooldownUntil = null;
            this.keyStatus[bestKey].totalCalls++;
            
            // Save state
            if (this.options.persistState) {
                this.saveState();
            }
            
            return bestKey;
        }
        
        // This should never happen, but just in case
        this.log('error', 'No suitable API key found');
        return this.keys[0];
    }
    
    /**
     * Mark an API key as successful
     * @param {string} key - API key
     */
    markSuccess(key) {
        if (!this.keyStatus[key]) return;
        
        const status = this.keyStatus[key];
        status.successfulCalls++;
        
        // If the key had failures but is now working, reduce the failure count
        if (status.failures > 0) {
            status.failures = Math.max(0, status.failures - 1);
            this.log('info', `Reduced failure count for key ${this.maskKey(key)} to ${status.failures}`);
        }
        
        // If the key was in cooldown but is now working, remove the cooldown
        if (status.cooldownUntil && status.cooldownUntil > Date.now()) {
            status.cooldownUntil = null;
            this.log('info', `Removed cooldown for key ${this.maskKey(key)}`);
        }
        
        // Save state
        if (this.options.persistState) {
            this.saveState();
        }
    }
    
    /**
     * Mark an API key as failed
     * @param {string} key - API key
     * @param {string} reason - Reason for failure
     */
    markFailure(key, reason = 'unknown') {
        if (!this.keyStatus[key]) return;
        
        const status = this.keyStatus[key];
        status.failures++;
        status.lastFailure = Date.now();
        
        this.log('warn', `Key ${this.maskKey(key)} failed (${reason}). Failure count: ${status.failures}`);
        
        // If the key has failed too many times, put it in cooldown
        if (status.failures >= this.options.maxFailures) {
            status.cooldownUntil = Date.now() + this.options.cooldownPeriod;
            
            const cooldownMinutes = this.options.cooldownPeriod / 60000;
            this.log('warn', `Key ${this.maskKey(key)} placed in cooldown for ${cooldownMinutes} minutes`);
        }
        
        // Save state
        if (this.options.persistState) {
            this.saveState();
        }
    }
    
    /**
     * Reset a key's status
     * @param {string} key - API key
     */
    resetKey(key) {
        if (!this.keyStatus[key]) return;
        
        this.keyStatus[key] = {
            failures: 0,
            lastFailure: null,
            cooldownUntil: null,
            totalCalls: this.keyStatus[key].totalCalls,
            successfulCalls: this.keyStatus[key].successfulCalls
        };
        
        this.log('info', `Reset status for key ${this.maskKey(key)}`);
        
        // Save state
        if (this.options.persistState) {
            this.saveState();
        }
    }
    
    /**
     * Reset all keys
     */
    resetAllKeys() {
        this.keys.forEach(key => this.resetKey(key));
        this.log('info', 'Reset all keys');
    }
    
    /**
     * Get the status of all keys
     * @returns {Object} - Key status
     */
    getStatus() {
        const now = Date.now();
        const result = {
            provider: this.providerName,
            totalKeys: this.keys.length,
            availableKeys: 0,
            keysInCooldown: 0,
            keys: {}
        };
        
        this.keys.forEach(key => {
            const status = this.keyStatus[key];
            const isInCooldown = status.cooldownUntil && status.cooldownUntil > now;
            
            if (!isInCooldown) {
                result.availableKeys++;
            } else {
                result.keysInCooldown++;
            }
            
            result.keys[this.maskKey(key)] = {
                failures: status.failures,
                inCooldown: isInCooldown,
                cooldownRemaining: isInCooldown ? Math.round((status.cooldownUntil - now) / 1000) : 0,
                successRate: status.totalCalls > 0 ? (status.successfulCalls / status.totalCalls * 100).toFixed(1) + '%' : 'N/A'
            };
        });
        
        return result;
    }
    
    /**
     * Save state to localStorage
     */
    saveState() {
        try {
            const state = {
                currentIndex: this.currentIndex,
                keyStatus: this.keyStatus,
                lastUpdated: Date.now()
            };
            
            localStorage.setItem(`apiKeyManager_${this.providerName}`, JSON.stringify(state));
            this.log('debug', 'Saved state to localStorage');
        } catch (error) {
            this.log('error', `Failed to save state: ${error.message}`);
        }
    }
    
    /**
     * Load state from localStorage
     */
    loadState() {
        try {
            const savedState = localStorage.getItem(`apiKeyManager_${this.providerName}`);
            
            if (savedState) {
                const state = JSON.parse(savedState);
                
                // Only restore if the keys match
                if (state && state.keyStatus) {
                    // Only restore status for keys that still exist
                    this.keys.forEach(key => {
                        if (state.keyStatus[key]) {
                            this.keyStatus[key] = state.keyStatus[key];
                        }
                    });
                    
                    this.currentIndex = state.currentIndex % this.keys.length;
                    this.log('info', `Loaded state from localStorage (last updated: ${new Date(state.lastUpdated).toLocaleString()})`);
                }
            }
        } catch (error) {
            this.log('error', `Failed to load state: ${error.message}`);
        }
    }
    
    /**
     * Mask an API key for logging (show only first 4 and last 4 characters)
     * @param {string} key - API key
     * @returns {string} - Masked API key
     */
    maskKey(key) {
        if (!key || key.length < 8) return '****';
        return `${key.substring(0, 4)}...${key.substring(key.length - 4)}`;
    }
    
    /**
     * Log a message
     * @param {string} level - Log level (debug, info, warn, error)
     * @param {string} message - Message to log
     */
    log(level, message) {
        const logLevels = {
            debug: 0,
            info: 1,
            warn: 2,
            error: 3
        };
        
        // Only log if the level is high enough
        if (logLevels[level] >= logLevels[this.options.logLevel]) {
            const timestamp = new Date().toISOString();
            const formattedMessage = `[${timestamp}] [${this.providerName}] [${level.toUpperCase()}] ${message}`;
            
            switch (level) {
                case 'debug':
                    console.debug(formattedMessage);
                    break;
                case 'info':
                    console.info(formattedMessage);
                    break;
                case 'warn':
                    console.warn(formattedMessage);
                    break;
                case 'error':
                    console.error(formattedMessage);
                    break;
            }
        }
    }
}

// Export the class
window.ApiKeyManager = ApiKeyManager;
