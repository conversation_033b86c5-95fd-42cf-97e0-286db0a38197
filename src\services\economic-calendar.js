/**
 * Economic Calendar Service for Trading Signals App
 * 
 * This service provides functions to fetch and process economic calendar data
 * from the FRED API and other sources.
 */

// Economic calendar configuration
const ECONOMIC_CALENDAR_CONFIG = {
  // Default settings
  DEFAULT_DAYS_RANGE: 14, // 7 days before and 7 days after today
  
  // Event importance levels
  IMPORTANCE_LEVELS: {
    HIGH: 'high',
    MEDIUM: 'medium',
    LOW: 'low'
  },
  
  // Country codes
  COUNTRY_CODES: {
    US: 'United States',
    EU: 'Euro Area',
    GB: 'United Kingdom',
    JP: 'Japan',
    CN: 'China',
    CA: 'Canada',
    AU: 'Australia',
    NZ: 'New Zealand',
    CH: 'Switzerland'
  },
  
  // Event categories
  EVENT_CATEGORIES: {
    INTEREST_RATE: 'Interest Rate',
    GDP: 'GDP',
    EMPLOYMENT: 'Employment',
    INFLATION: 'Inflation',
    TRADE: 'Trade',
    MANUFACTURING: 'Manufacturing',
    HOUSING: 'Housing',
    CONSUMER: 'Consumer',
    CENTRAL_BANK: 'Central Bank'
  },
  
  // Important economic indicators by country
  IMPORTANT_INDICATORS: {
    US: [
      { id: 'UNRATE', name: 'Unemployment Rate', importance: 'high', category: 'EMPLOYMENT' },
      { id: 'PAYEMS', name: 'Nonfarm Payrolls', importance: 'high', category: 'EMPLOYMENT' },
      { id: 'CPIAUCSL', name: 'Consumer Price Index', importance: 'high', category: 'INFLATION' },
      { id: 'GDPC1', name: 'GDP', importance: 'high', category: 'GDP' },
      { id: 'FEDFUNDS', name: 'Federal Funds Rate', importance: 'high', category: 'INTEREST_RATE' },
      { id: 'INDPRO', name: 'Industrial Production', importance: 'medium', category: 'MANUFACTURING' },
      { id: 'RSAFS', name: 'Retail Sales', importance: 'medium', category: 'CONSUMER' },
      { id: 'HOUST', name: 'Housing Starts', importance: 'medium', category: 'HOUSING' }
    ],
    EU: [
      { id: 'LRHUTTTTEZM156S', name: 'Unemployment Rate', importance: 'high', category: 'EMPLOYMENT' },
      { id: 'CP0000EZ19M086NEST', name: 'Consumer Price Index', importance: 'high', category: 'INFLATION' },
      { id: 'CLVMEURSCAB1GQEA19', name: 'GDP', importance: 'high', category: 'GDP' },
      { id: 'INTDSREZM193N', name: 'ECB Interest Rate', importance: 'high', category: 'INTEREST_RATE' }
    ]
  }
};

/**
 * Economic Calendar Service Class
 */
class EconomicCalendarService {
  constructor() {
    // Current state
    this.events = [];
    this.lastUpdate = null;
    
    // Event listeners
    this.eventListeners = {
      dataUpdated: [],
      error: []
    };
    
    console.log('Economic Calendar Service initialized');
  }
  
  /**
   * Get economic calendar events
   * 
   * @param {Object} options - Options
   * @param {string} options.startDate - Start date (YYYY-MM-DD)
   * @param {string} options.endDate - End date (YYYY-MM-DD)
   * @param {string} options.country - Country code
   * @param {string} options.importance - Importance level
   * @returns {Promise<Array<Object>>} - Economic calendar events
   */
  async getEconomicCalendar(options = {}) {
    try {
      // Set default dates if not provided
      const today = new Date();
      const startDate = options.startDate || new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const endDate = options.endDate || new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      // Fetch data using unified API service
      const response = await window.unifiedAPIService.fetchData({
        provider: 'FRED',
        endpoint: 'releases/dates',
        params: {
          realtime_start: startDate,
          realtime_end: endDate,
          limit: 1000,
          order_by: 'release_date',
          sort_order: 'asc'
        },
        useCache: true,
        cacheTTL: 60 * 60 * 1000 // 1 hour
      });
      
      // Process the data
      const events = this.processEconomicCalendarData(response.data, options);
      
      // Update state
      this.events = events;
      this.lastUpdate = new Date();
      
      // Trigger data updated event
      this.triggerEvent('dataUpdated', events);
      
      return events;
    } catch (error) {
      console.error('Error fetching economic calendar data:', error);
      
      // Trigger error event
      this.triggerEvent('error', {
        message: `Failed to fetch economic calendar data: ${error.message}`,
        error
      });
      
      // Return mock data as fallback
      return this.getMockEconomicCalendarEvents(options);
    }
  }
  
  /**
   * Process economic calendar data from FRED API
   * 
   * @param {Object} data - FRED API response data
   * @param {Object} options - Filter options
   * @returns {Array<Object>} - Processed economic calendar events
   */
  processEconomicCalendarData(data, options = {}) {
    try {
      // Check if data is valid
      if (!data || !data.release_dates || !Array.isArray(data.release_dates)) {
        throw new Error('Invalid FRED API response format');
      }
      
      // Transform release dates to events
      const events = data.release_dates.map(release => {
        // Determine country based on release name
        let country = 'US'; // Default to US
        
        if (release.name.includes('Euro') || release.name.includes('ECB')) {
          country = 'EU';
        } else if (release.name.includes('UK') || release.name.includes('British')) {
          country = 'GB';
        } else if (release.name.includes('Japan') || release.name.includes('BOJ')) {
          country = 'JP';
        } else if (release.name.includes('China')) {
          country = 'CN';
        } else if (release.name.includes('Canada')) {
          country = 'CA';
        } else if (release.name.includes('Australia')) {
          country = 'AU';
        }
        
        // Determine importance based on release name
        let importance = ECONOMIC_CALENDAR_CONFIG.IMPORTANCE_LEVELS.MEDIUM; // Default to medium
        
        if (release.name.includes('GDP') || 
            release.name.includes('Unemployment') || 
            release.name.includes('CPI') || 
            release.name.includes('Interest Rate') || 
            release.name.includes('Nonfarm Payrolls')) {
          importance = ECONOMIC_CALENDAR_CONFIG.IMPORTANCE_LEVELS.HIGH;
        } else if (release.name.includes('Building Permits') || 
                  release.name.includes('Retail Sales') || 
                  release.name.includes('Manufacturing')) {
          importance = ECONOMIC_CALENDAR_CONFIG.IMPORTANCE_LEVELS.MEDIUM;
        } else {
          importance = ECONOMIC_CALENDAR_CONFIG.IMPORTANCE_LEVELS.LOW;
        }
        
        // Determine category based on release name
        let category = 'Other';
        
        if (release.name.includes('GDP')) {
          category = ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.GDP;
        } else if (release.name.includes('Unemployment') || release.name.includes('Payrolls') || release.name.includes('Employment')) {
          category = ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.EMPLOYMENT;
        } else if (release.name.includes('CPI') || release.name.includes('Inflation') || release.name.includes('Price Index')) {
          category = ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.INFLATION;
        } else if (release.name.includes('Interest Rate') || release.name.includes('Fed') || release.name.includes('ECB')) {
          category = ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.INTEREST_RATE;
        } else if (release.name.includes('Trade') || release.name.includes('Export') || release.name.includes('Import')) {
          category = ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.TRADE;
        } else if (release.name.includes('Manufacturing') || release.name.includes('Industrial')) {
          category = ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.MANUFACTURING;
        } else if (release.name.includes('Housing') || release.name.includes('Home') || release.name.includes('Building')) {
          category = ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.HOUSING;
        } else if (release.name.includes('Consumer') || release.name.includes('Retail')) {
          category = ECONOMIC_CALENDAR_CONFIG.EVENT_CATEGORIES.CONSUMER;
        }
        
        // Extract date and time
        const releaseDate = new Date(release.release_date);
        const date = releaseDate.toISOString().split('T')[0];
        const time = releaseDate.toTimeString().split(' ')[0].substring(0, 5);
        
        return {
          id: release.release_id,
          title: release.name,
          date,
          time,
          country,
          importance,
          category,
          forecast: null, // FRED doesn't provide forecast data
          previous: null, // FRED doesn't provide previous data
          actual: null, // FRED doesn't provide actual data yet
          url: `https://fred.stlouisfed.org/release?rid=${release.release_id}`
        };
      });
      
      // Apply filters
      let filteredEvents = events;
      
      if (options.country && options.country !== 'all') {
        filteredEvents = filteredEvents.filter(event => event.country === options.country);
      }
      
      if (options.importance && options.importance !== 'all') {
        filteredEvents = filteredEvents.filter(event => event.importance === options.importance);
      }
      
      if (options.category && options.category !== 'all') {
        filteredEvents = filteredEvents.filter(event => event.category === options.category);
      }
      
      return filteredEvents;
    } catch (error) {
      console.error('Error processing economic calendar data:', error);
      throw error;
    }
  }
  
  /**
   * Get mock economic calendar events as fallback
   * 
   * @param {Object} options - Filter options
   * @returns {Array<Object>} - Mock economic calendar events
   */
  getMockEconomicCalendarEvents(options = {}) {
    console.log('Generating mock economic calendar events');
    
    // Set default dates if not provided
    const today = new Date();
    const startDate = options.startDate ? new Date(options.startDate) : new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const endDate = options.endDate ? new Date(options.endDate) : new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    // Generate mock events
    const events = [
      {
        id: 1,
        title: 'US Non-Farm Payrolls',
        date: this.getNextFriday(today).toISOString().split('T')[0],
        time: '08:30',
        country: 'US',
        importance: 'high',
        category: 'EMPLOYMENT',
        forecast: '+180K',
        previous: '+236K',
        actual: null
      },
      {
        id: 2,
        title: 'US CPI m/m',
        date: this.getNextWednesday(today).toISOString().split('T')[0],
        time: '08:30',
        country: 'US',
        importance: 'high',
        category: 'INFLATION',
        forecast: '0.2%',
        previous: '0.4%',
        actual: null
      },
      {
        id: 3,
        title: 'ECB Interest Rate Decision',
        date: this.getNextThursday(today).toISOString().split('T')[0],
        time: '07:45',
        country: 'EU',
        importance: 'high',
        category: 'INTEREST_RATE',
        forecast: '3.75%',
        previous: '3.50%',
        actual: null
      },
      {
        id: 4,
        title: 'UK GDP m/m',
        date: this.getNextTuesday(today).toISOString().split('T')[0],
        time: '02:00',
        country: 'GB',
        importance: 'medium',
        category: 'GDP',
        forecast: '0.2%',
        previous: '-0.3%',
        actual: null
      },
      {
        id: 5,
        title: 'US Retail Sales m/m',
        date: this.getNextTuesday(today).toISOString().split('T')[0],
        time: '08:30',
        country: 'US',
        importance: 'medium',
        category: 'CONSUMER',
        forecast: '0.3%',
        previous: '0.7%',
        actual: null
      },
      {
        id: 6,
        title: 'Japan GDP q/q',
        date: this.getNextMonday(today).toISOString().split('T')[0],
        time: '19:50',
        country: 'JP',
        importance: 'high',
        category: 'GDP',
        forecast: '0.5%',
        previous: '0.1%',
        actual: null
      },
      {
        id: 7,
        title: 'China Manufacturing PMI',
        date: this.getNextMonday(today).toISOString().split('T')[0],
        time: '01:00',
        country: 'CN',
        importance: 'high',
        category: 'MANUFACTURING',
        forecast: '50.2',
        previous: '49.8',
        actual: null
      },
      {
        id: 8,
        title: 'US Building Permits',
        date: this.getNextWednesday(today).toISOString().split('T')[0],
        time: '08:30',
        country: 'US',
        importance: 'low',
        category: 'HOUSING',
        forecast: '1.42M',
        previous: '1.39M',
        actual: null
      }
    ];
    
    // Filter events by date range
    const filteredEvents = events.filter(event => {
      const eventDate = new Date(event.date);
      return eventDate >= startDate && eventDate <= endDate;
    });
    
    // Apply additional filters
    let result = filteredEvents;
    
    if (options.country && options.country !== 'all') {
      result = result.filter(event => event.country === options.country);
    }
    
    if (options.importance && options.importance !== 'all') {
      result = result.filter(event => event.importance === options.importance);
    }
    
    return result;
  }
  
  /**
   * Get next Monday from a date
   * 
   * @param {Date} date - Starting date
   * @returns {Date} - Next Monday
   */
  getNextMonday(date) {
    const result = new Date(date);
    result.setDate(date.getDate() + (8 - date.getDay()) % 7);
    return result;
  }
  
  /**
   * Get next Tuesday from a date
   * 
   * @param {Date} date - Starting date
   * @returns {Date} - Next Tuesday
   */
  getNextTuesday(date) {
    const result = new Date(date);
    result.setDate(date.getDate() + (9 - date.getDay()) % 7);
    return result;
  }
  
  /**
   * Get next Wednesday from a date
   * 
   * @param {Date} date - Starting date
   * @returns {Date} - Next Wednesday
   */
  getNextWednesday(date) {
    const result = new Date(date);
    result.setDate(date.getDate() + (10 - date.getDay()) % 7);
    return result;
  }
  
  /**
   * Get next Thursday from a date
   * 
   * @param {Date} date - Starting date
   * @returns {Date} - Next Thursday
   */
  getNextThursday(date) {
    const result = new Date(date);
    result.setDate(date.getDate() + (11 - date.getDay()) % 7);
    return result;
  }
  
  /**
   * Get next Friday from a date
   * 
   * @param {Date} date - Starting date
   * @returns {Date} - Next Friday
   */
  getNextFriday(date) {
    const result = new Date(date);
    result.setDate(date.getDate() + (12 - date.getDay()) % 7);
    return result;
  }
  
  /**
   * Add event listener
   * 
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  addEventListener(event, callback) {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [];
    }
    
    this.eventListeners[event].push(callback);
  }
  
  /**
   * Remove event listener
   * 
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  removeEventListener(event, callback) {
    if (!this.eventListeners[event]) {
      return;
    }
    
    this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
  }
  
  /**
   * Trigger event
   * 
   * @param {string} event - Event name
   * @param {*} data - Event data
   */
  triggerEvent(event, data) {
    if (!this.eventListeners[event]) {
      return;
    }
    
    this.eventListeners[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in ${event} event listener:`, error);
      }
    });
  }
}

// Create singleton instance
const economicCalendarService = new EconomicCalendarService();

// Export for use in other modules
if (typeof window !== 'undefined') {
  window.economicCalendarService = economicCalendarService;
}

export default economicCalendarService;
