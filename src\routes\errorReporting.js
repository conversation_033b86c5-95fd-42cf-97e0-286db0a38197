/**
 * Error Reporting API Routes
 * 
 * Backend endpoints for receiving and processing error reports from the frontend.
 * Integrates with our standardized error handling and logging system.
 * 
 * Features:
 * - Error report collection and storage
 * - Error analytics and aggregation
 * - Integration with monitoring services
 * - Error trend analysis
 * - Automated alerting for critical errors
 * 
 * @version 1.0.0
 */

const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const logger = require('../utils/logger');
const { APIError } = require('../middleware/errorHandler');

// ============================================================================
// ERROR STORAGE (In production, use a proper database)
// ============================================================================

// In-memory storage for demo purposes
// In production, replace with MongoDB, PostgreSQL, or dedicated error tracking service
const errorReports = new Map();
const errorStats = {
  totalErrors: 0,
  errorsByType: {},
  errorsByCategory: {},
  errorsByHour: {},
  criticalErrors: 0,
  lastReset: new Date().toISOString()
};

// ============================================================================
// VALIDATION MIDDLEWARE
// ============================================================================

const validateErrorReport = [
  body('id').isString().notEmpty().withMessage('Error ID is required'),
  body('timestamp').isISO8601().withMessage('Valid timestamp is required'),
  body('error.name').isString().notEmpty().withMessage('Error name is required'),
  body('error.message').isString().notEmpty().withMessage('Error message is required'),
  body('context.url').isURL().withMessage('Valid URL is required'),
  body('context.userAgent').isString().notEmpty().withMessage('User agent is required')
];

const validateLogBatch = [
  body('logs').isArray().withMessage('Logs must be an array'),
  body('logs.*.id').isString().notEmpty().withMessage('Log ID is required'),
  body('logs.*.timestamp').isISO8601().withMessage('Valid timestamp is required'),
  body('logs.*.level').isIn(['debug', 'info', 'warn', 'error', 'fatal']).withMessage('Valid log level is required'),
  body('logs.*.message').isString().notEmpty().withMessage('Log message is required'),
  body('sessionId').isString().notEmpty().withMessage('Session ID is required')
];

// ============================================================================
// ERROR REPORTING ENDPOINTS
// ============================================================================

/**
 * POST /api/errors/report
 * Report a single error from the frontend
 */
router.post('/report', validateErrorReport, async (req, res) => {
  try {
    // Check validation results
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.validationError(errors.array(), 'Invalid error report data');
    }

    const errorReport = req.body;
    const errorId = errorReport.id;

    // Store error report
    errorReports.set(errorId, {
      ...errorReport,
      receivedAt: new Date().toISOString(),
      processed: false
    });

    // Update statistics
    updateErrorStats(errorReport);

    // Process error based on severity
    await processErrorReport(errorReport);

    // Log the error report
    logger.error('Frontend Error Report Received', {
      errorId,
      type: errorReport.error?.name,
      message: errorReport.error?.message,
      url: errorReport.context?.url,
      userAgent: errorReport.context?.userAgent,
      level: errorReport.context?.level,
      retryCount: errorReport.context?.retryCount
    });

    res.success(
      { errorId, processed: true },
      'Error report received successfully'
    );

  } catch (error) {
    logger.error('Error processing error report', {
      error: error.message,
      stack: error.stack,
      requestBody: req.body
    });

    res.error(
      'INTERNAL_SERVER_ERROR',
      'Failed to process error report',
      500,
      null,
      'Please try again later or contact support'
    );
  }
});

/**
 * POST /api/errors/feature
 * Report a feature-specific error
 */
router.post('/feature', async (req, res) => {
  try {
    const featureErrorReport = req.body;
    const errorId = featureErrorReport.id;

    // Store feature error
    errorReports.set(errorId, {
      ...featureErrorReport,
      type: 'feature_error',
      receivedAt: new Date().toISOString(),
      processed: false
    });

    // Log feature error
    logger.warn('Feature Error Report', {
      errorId,
      featureName: featureErrorReport.featureName,
      level: featureErrorReport.level,
      error: featureErrorReport.error?.message,
      retryCount: featureErrorReport.context?.retryCount
    });

    // Alert if critical feature error
    if (featureErrorReport.level === 'critical') {
      await alertCriticalFeatureError(featureErrorReport);
    }

    res.success(
      { errorId, processed: true },
      'Feature error report received successfully'
    );

  } catch (error) {
    logger.error('Error processing feature error report', {
      error: error.message,
      stack: error.stack
    });

    res.error(
      'INTERNAL_SERVER_ERROR',
      'Failed to process feature error report'
    );
  }
});

/**
 * POST /api/logs/errors
 * Batch log processing endpoint
 */
router.post('/logs/errors', validateLogBatch, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.validationError(errors.array(), 'Invalid log batch data');
    }

    const { logs, sessionId } = req.body;

    // Process each log entry
    for (const logEntry of logs) {
      await processLogEntry(logEntry, sessionId);
    }

    logger.info('Log batch processed', {
      sessionId,
      logCount: logs.length,
      timestamp: new Date().toISOString()
    });

    res.success(
      { 
        processed: logs.length,
        sessionId,
        timestamp: new Date().toISOString()
      },
      'Log batch processed successfully'
    );

  } catch (error) {
    logger.error('Error processing log batch', {
      error: error.message,
      stack: error.stack
    });

    res.error(
      'INTERNAL_SERVER_ERROR',
      'Failed to process log batch'
    );
  }
});

// ============================================================================
// ERROR ANALYTICS ENDPOINTS
// ============================================================================

/**
 * GET /api/errors/stats
 * Get error statistics and analytics
 */
router.get('/stats', async (req, res) => {
  try {
    const { timeframe = '24h', groupBy = 'hour' } = req.query;

    const stats = {
      ...errorStats,
      timeframe,
      groupBy,
      recentErrors: getRecentErrors(timeframe),
      trends: getErrorTrends(timeframe, groupBy),
      topErrors: getTopErrors(),
      criticalErrorsLast24h: getCriticalErrorsCount('24h')
    };

    res.success(stats, 'Error statistics retrieved successfully');

  } catch (error) {
    logger.error('Error retrieving error statistics', {
      error: error.message,
      stack: error.stack
    });

    res.error(
      'INTERNAL_SERVER_ERROR',
      'Failed to retrieve error statistics'
    );
  }
});

/**
 * GET /api/errors/recent
 * Get recent error reports
 */
router.get('/recent', async (req, res) => {
  try {
    const { limit = 50, category, type } = req.query;

    let recentErrors = Array.from(errorReports.values())
      .sort((a, b) => new Date(b.receivedAt).getTime() - new Date(a.receivedAt).getTime())
      .slice(0, parseInt(limit));

    // Apply filters
    if (category) {
      recentErrors = recentErrors.filter(error => error.context?.level === category);
    }

    if (type) {
      recentErrors = recentErrors.filter(error => error.error?.name === type);
    }

    res.success(
      recentErrors,
      `Retrieved ${recentErrors.length} recent error reports`
    );

  } catch (error) {
    logger.error('Error retrieving recent errors', {
      error: error.message,
      stack: error.stack
    });

    res.error(
      'INTERNAL_SERVER_ERROR',
      'Failed to retrieve recent errors'
    );
  }
});

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

function updateErrorStats(errorReport) {
  errorStats.totalErrors++;

  // Update error type stats
  const errorType = errorReport.error?.name || 'Unknown';
  errorStats.errorsByType[errorType] = (errorStats.errorsByType[errorType] || 0) + 1;

  // Update category stats
  const category = errorReport.context?.level || 'unknown';
  errorStats.errorsByCategory[category] = (errorStats.errorsByCategory[category] || 0) + 1;

  // Update hourly stats
  const hour = new Date().getHours();
  errorStats.errorsByHour[hour] = (errorStats.errorsByHour[hour] || 0) + 1;

  // Update critical error count
  if (category === 'critical') {
    errorStats.criticalErrors++;
  }
}

async function processErrorReport(errorReport) {
  const category = errorReport.context?.level;
  
  // Handle critical errors immediately
  if (category === 'critical') {
    await alertCriticalError(errorReport);
  }

  // Check for error patterns
  await checkErrorPatterns(errorReport);

  // Update monitoring services
  await updateMonitoringServices(errorReport);

  // Mark as processed
  errorReports.set(errorReport.id, {
    ...errorReports.get(errorReport.id),
    processed: true,
    processedAt: new Date().toISOString()
  });
}

async function processLogEntry(logEntry, sessionId) {
  // Store log entry (in production, use proper database)
  const logId = `${sessionId}-${logEntry.id}`;
  
  // Log based on level
  const logMessage = `[${sessionId}] ${logEntry.message}`;
  
  switch (logEntry.level) {
    case 'debug':
      logger.debug(logMessage, { logEntry, sessionId });
      break;
    case 'info':
      logger.info(logMessage, { logEntry, sessionId });
      break;
    case 'warn':
      logger.warn(logMessage, { logEntry, sessionId });
      break;
    case 'error':
    case 'fatal':
      logger.error(logMessage, { logEntry, sessionId });
      break;
  }
}

async function alertCriticalError(errorReport) {
  // In production, integrate with alerting services (PagerDuty, Slack, etc.)
  logger.error('CRITICAL ERROR ALERT', {
    errorId: errorReport.id,
    message: errorReport.error?.message,
    url: errorReport.context?.url,
    timestamp: errorReport.timestamp,
    userAgent: errorReport.context?.userAgent
  });

  // Send to monitoring services
  if (process.env.SLACK_WEBHOOK_URL) {
    // Send Slack notification
    try {
      await fetch(process.env.SLACK_WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: `🚨 Critical Error Alert`,
          attachments: [{
            color: 'danger',
            fields: [
              { title: 'Error ID', value: errorReport.id, short: true },
              { title: 'Message', value: errorReport.error?.message, short: false },
              { title: 'URL', value: errorReport.context?.url, short: false }
            ]
          }]
        })
      });
    } catch (slackError) {
      logger.error('Failed to send Slack alert', { error: slackError.message });
    }
  }
}

async function alertCriticalFeatureError(featureErrorReport) {
  logger.error('CRITICAL FEATURE ERROR', {
    featureName: featureErrorReport.featureName,
    errorId: featureErrorReport.id,
    message: featureErrorReport.error?.message
  });
}

async function checkErrorPatterns(errorReport) {
  // Implement error pattern detection
  // Check for recurring errors, error spikes, etc.
}

async function updateMonitoringServices(errorReport) {
  // Update external monitoring services (Sentry, DataDog, etc.)
}

function getRecentErrors(timeframe) {
  const now = new Date();
  const timeframeMs = parseTimeframe(timeframe);
  const cutoff = new Date(now.getTime() - timeframeMs);

  return Array.from(errorReports.values())
    .filter(error => new Date(error.receivedAt) > cutoff)
    .length;
}

function getErrorTrends(timeframe, groupBy) {
  // Implement error trend analysis
  return {};
}

function getTopErrors() {
  return Object.entries(errorStats.errorsByType)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([type, count]) => ({ type, count }));
}

function getCriticalErrorsCount(timeframe) {
  const now = new Date();
  const timeframeMs = parseTimeframe(timeframe);
  const cutoff = new Date(now.getTime() - timeframeMs);

  return Array.from(errorReports.values())
    .filter(error => 
      new Date(error.receivedAt) > cutoff && 
      error.context?.level === 'critical'
    )
    .length;
}

function parseTimeframe(timeframe) {
  const match = timeframe.match(/^(\d+)([hmd])$/);
  if (!match) return 24 * 60 * 60 * 1000; // Default 24 hours

  const [, amount, unit] = match;
  const multipliers = { m: 60 * 1000, h: 60 * 60 * 1000, d: 24 * 60 * 60 * 1000 };
  
  return parseInt(amount) * multipliers[unit];
}

module.exports = router;
