<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Authentication - Trading Signals App</title>
  <link rel="stylesheet" href="css/styles.css">
  <style>
    /* Additional styles for auth page */
    .auth-container {
      max-width: 500px;
      margin: 2rem auto;
      padding: 2rem;
      background-color: #f8f9fa;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .auth-form-container {
      padding: 1rem;
    }
    
    .form-group {
      margin-bottom: 1rem;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
    }
    
    .form-group input {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #ced4da;
      border-radius: 4px;
      font-size: 1rem;
    }
    
    .btn {
      display: inline-block;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      font-weight: 500;
      text-align: center;
      text-decoration: none;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .btn-primary {
      background-color: #0d6efd;
      color: white;
      border: none;
    }
    
    .btn-primary:hover {
      background-color: #0b5ed7;
    }
    
    .btn-outline {
      background-color: transparent;
      color: #0d6efd;
      border: 1px solid #0d6efd;
    }
    
    .btn-outline:hover {
      background-color: #0d6efd;
      color: white;
    }
    
    .message {
      margin-top: 1rem;
      padding: 0.75rem;
      border-radius: 4px;
    }
    
    .message.success {
      background-color: #d1e7dd;
      color: #0f5132;
    }
    
    .message.error {
      background-color: #f8d7da;
      color: #842029;
    }
    
    .auth-nav {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
      margin-bottom: 2rem;
    }
    
    .user-info {
      margin-bottom: 1rem;
      padding: 1rem;
      background-color: #e9ecef;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <header>
    <nav class="main-nav">
      <div class="logo">
        <a href="/">Trading Signals App</a>
      </div>
      <div id="auth-nav" class="auth-nav">
        <!-- Auth navigation will be inserted here by JavaScript -->
      </div>
    </nav>
  </header>
  
  <main>
    <div class="auth-container">
      <div id="user-info" class="user-info" style="display: none;">
        <!-- User info will be inserted here by JavaScript -->
      </div>
      
      <div id="auth-forms">
        <!-- Auth forms will be inserted here by JavaScript -->
      </div>
    </div>
  </main>
  
  <footer>
    <p>&copy; 2023 Trading Signals App. All rights reserved.</p>
  </footer>
  
  <script src="js/auth.js"></script>
</body>
</html>
