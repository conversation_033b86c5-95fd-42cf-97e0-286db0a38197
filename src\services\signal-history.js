/**
 * Signal History Module for Trading Signals App
 * 
 * This module provides functionality for tracking and displaying trading signal history.
 */

// Signal history state
const signalHistory = {
    signals: [],
    maxSignals: 50, // Maximum number of signals to store
    lastUpdated: null,
    container: null, // DOM element to display signals
    isInitialized: false
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initSignalHistory);

/**
 * Initialize signal history
 */
function initSignalHistory() {
    console.log('Initializing signal history module...');
    
    // Find signal history container
    signalHistory.container = document.getElementById('signalHistoryContainer');
    
    // Load saved signals
    loadSignalHistory();
    
    // Set up event listeners
    setupSignalHistoryEvents();
    
    // Set initialization flag
    signalHistory.isInitialized = true;
    
    // Set last updated timestamp
    signalHistory.lastUpdated = new Date().toISOString();
    
    // Update UI
    updateSignalHistoryUI();
    
    console.log('Signal history module initialized');
}

/**
 * Load signal history from localStorage
 */
function loadSignalHistory() {
    const savedSignals = localStorage.getItem('signal_history');
    
    if (savedSignals) {
        try {
            signalHistory.signals = JSON.parse(savedSignals);
            console.log(`Loaded ${signalHistory.signals.length} signals from history`);
        } catch (error) {
            console.error('Error loading signal history:', error);
            signalHistory.signals = [];
        }
    }
}

/**
 * Save signal history to localStorage
 */
function saveSignalHistory() {
    try {
        localStorage.setItem('signal_history', JSON.stringify(signalHistory.signals));
    } catch (error) {
        console.error('Error saving signal history:', error);
    }
}

/**
 * Set up signal history event listeners
 */
function setupSignalHistoryEvents() {
    // Clear history button
    const clearHistoryBtn = document.getElementById('clearSignalHistoryBtn');
    if (clearHistoryBtn) {
        clearHistoryBtn.addEventListener('click', clearSignalHistory);
    }
    
    // Export history button
    const exportHistoryBtn = document.getElementById('exportSignalHistoryBtn');
    if (exportHistoryBtn) {
        exportHistoryBtn.addEventListener('click', exportSignalHistory);
    }
}

/**
 * Update signal history UI
 */
function updateSignalHistoryUI() {
    if (!signalHistory.container) {
        return;
    }
    
    // Clear container
    signalHistory.container.innerHTML = '';
    
    // Check if we have signals
    if (signalHistory.signals.length === 0) {
        signalHistory.container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                لا توجد إشارات تداول في السجل.
            </div>
        `;
        return;
    }
    
    // Create table
    const table = document.createElement('table');
    table.className = 'table table-striped table-hover';
    
    // Create table header
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th>التاريخ</th>
            <th>الزوج</th>
            <th>الإطار الزمني</th>
            <th>الإشارة</th>
            <th>السعر</th>
            <th>الثقة</th>
        </tr>
    `;
    table.appendChild(thead);
    
    // Create table body
    const tbody = document.createElement('tbody');
    
    // Add signals to table (most recent first)
    signalHistory.signals.slice().reverse().forEach(signal => {
        const row = document.createElement('tr');
        
        // Format date
        const date = new Date(signal.timestamp);
        const formattedDate = date.toLocaleString('ar-EG');
        
        // Format signal type
        let signalTypeText = '';
        let signalTypeClass = '';
        
        if (signal.type === 'buy') {
            signalTypeText = 'شراء';
            signalTypeClass = 'bg-success';
        } else if (signal.type === 'sell') {
            signalTypeText = 'بيع';
            signalTypeClass = 'bg-danger';
        } else {
            signalTypeText = 'محايد';
            signalTypeClass = 'bg-warning text-dark';
        }
        
        // Create row content
        row.innerHTML = `
            <td>${formattedDate}</td>
            <td>${signal.symbol}</td>
            <td>${signal.timeframe}</td>
            <td><span class="badge ${signalTypeClass}">${signalTypeText}</span></td>
            <td>${signal.entryPoint}</td>
            <td>${signal.confidence.toFixed(1)}%</td>
        `;
        
        // Add click event to show signal details
        row.style.cursor = 'pointer';
        row.addEventListener('click', () => showSignalDetails(signal));
        
        tbody.appendChild(row);
    });
    
    table.appendChild(tbody);
    signalHistory.container.appendChild(table);
}

/**
 * Show signal details in a modal
 * @param {Object} signal - Signal object
 */
function showSignalDetails(signal) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('signalDetailsModal');
    
    if (!modal) {
        modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'signalDetailsModal';
        modal.tabIndex = '-1';
        modal.setAttribute('aria-labelledby', 'signalDetailsModalLabel');
        modal.setAttribute('aria-hidden', 'true');
        
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="signalDetailsModalLabel">تفاصيل إشارة التداول</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="signalDetailsModalBody">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }
    
    // Get modal body
    const modalBody = document.getElementById('signalDetailsModalBody');
    
    // Format date
    const date = new Date(signal.timestamp);
    const formattedDate = date.toLocaleString('ar-EG');
    
    // Format signal type
    let signalTypeText = '';
    let signalTypeClass = '';
    
    if (signal.type === 'buy') {
        signalTypeText = 'شراء';
        signalTypeClass = 'bg-success';
    } else if (signal.type === 'sell') {
        signalTypeText = 'بيع';
        signalTypeClass = 'bg-danger';
    } else {
        signalTypeText = 'محايد';
        signalTypeClass = 'bg-warning text-dark';
    }
    
    // Create signal details HTML
    let detailsHTML = `
        <div class="row mb-3">
            <div class="col-md-6">
                <h5>معلومات الإشارة</h5>
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>الزوج</span>
                        <span class="fw-bold">${signal.symbol}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>الإطار الزمني</span>
                        <span class="fw-bold">${signal.timeframe}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>التاريخ</span>
                        <span class="fw-bold">${formattedDate}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>نوع الإشارة</span>
                        <span class="badge ${signalTypeClass}">${signalTypeText}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>الثقة</span>
                        <span class="fw-bold">${signal.confidence.toFixed(1)}%</span>
                    </li>
                </ul>
            </div>
            <div class="col-md-6">
                <h5>مستويات السعر</h5>
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>سعر الدخول</span>
                        <span class="fw-bold">${signal.entryPoint}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>وقف الخسارة</span>
                        <span class="fw-bold">${signal.stopLoss || 'غير محدد'}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>هدف الربح</span>
                        <span class="fw-bold">${signal.takeProfit || 'غير محدد'}</span>
                    </li>
                </ul>
            </div>
        </div>
    `;
    
    // Add indicator signals if available
    if (signal.signals && signal.signals.length > 0) {
        detailsHTML += `
            <h5>إشارات المؤشرات الفنية</h5>
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>المؤشر</th>
                            <th>الإشارة</th>
                            <th>القوة</th>
                            <th>القيمة</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        signal.signals.forEach(indicatorSignal => {
            let signalTypeText = '';
            let signalTypeClass = '';
            
            if (indicatorSignal.type === 'buy') {
                signalTypeText = 'شراء';
                signalTypeClass = 'bg-success';
            } else if (indicatorSignal.type === 'sell') {
                signalTypeText = 'بيع';
                signalTypeClass = 'bg-danger';
            } else {
                signalTypeText = 'محايد';
                signalTypeClass = 'bg-warning text-dark';
            }
            
            detailsHTML += `
                <tr>
                    <td>${indicatorSignal.name}</td>
                    <td><span class="badge ${signalTypeClass}">${signalTypeText}</span></td>
                    <td>${(indicatorSignal.strength * 100).toFixed(1)}%</td>
                    <td>${indicatorSignal.value}</td>
                </tr>
            `;
        });
        
        detailsHTML += `
                    </tbody>
                </table>
            </div>
        `;
    }
    
    // Add analysis text if available
    if (signal.analysis) {
        detailsHTML += `
            <h5>التحليل</h5>
            <div class="alert alert-info">
                ${signal.analysis}
            </div>
        `;
    }
    
    // Set modal body content
    modalBody.innerHTML = detailsHTML;
    
    // Show modal
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}

/**
 * Add a signal to history
 * @param {Object} signal - Signal object
 */
function addSignal(signal) {
    if (!signal) {
        return;
    }
    
    // Add signal to history
    signalHistory.signals.push(signal);
    
    // Limit number of signals
    if (signalHistory.signals.length > signalHistory.maxSignals) {
        signalHistory.signals.shift();
    }
    
    // Save to localStorage
    saveSignalHistory();
    
    // Update UI
    updateSignalHistoryUI();
    
    // Show notification if enabled
    if (window.notifications && typeof window.notifications.showSignalNotification === 'function') {
        window.notifications.showSignalNotification(signal);
    }
    
    console.log('Added signal to history:', signal);
}

/**
 * Clear signal history
 */
function clearSignalHistory() {
    // Ask for confirmation
    if (confirm('هل أنت متأكد من رغبتك في مسح سجل الإشارات؟')) {
        signalHistory.signals = [];
        saveSignalHistory();
        updateSignalHistoryUI();
        console.log('Signal history cleared');
    }
}

/**
 * Export signal history to CSV
 */
function exportSignalHistory() {
    if (signalHistory.signals.length === 0) {
        alert('لا توجد إشارات لتصديرها');
        return;
    }
    
    // Create CSV content
    let csv = 'التاريخ,الزوج,الإطار الزمني,الإشارة,سعر الدخول,وقف الخسارة,هدف الربح,الثقة\n';
    
    signalHistory.signals.forEach(signal => {
        const date = new Date(signal.timestamp);
        const formattedDate = date.toLocaleString('ar-EG');
        
        let signalTypeText = '';
        if (signal.type === 'buy') {
            signalTypeText = 'شراء';
        } else if (signal.type === 'sell') {
            signalTypeText = 'بيع';
        } else {
            signalTypeText = 'محايد';
        }
        
        csv += `"${formattedDate}","${signal.symbol}","${signal.timeframe}","${signalTypeText}","${signal.entryPoint}","${signal.stopLoss || ''}","${signal.takeProfit || ''}","${signal.confidence.toFixed(1)}%"\n`;
    });
    
    // Create download link
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `trading_signals_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    console.log('Signal history exported to CSV');
}

// Export the module
window.signalHistory = {
    addSignal,
    clearSignalHistory,
    exportSignalHistory,
    getSignals: () => signalHistory.signals
};
