/**
 * Service Worker for Trading Signals App
 *
 * This service worker provides offline functionality and caching
 * for the Trading Signals App.
 */

// Cache name (update version when making changes)
const CACHE_NAME = 'trading-signals-cache-v6';

// Files to cache
const CACHE_FILES = [
    '/',
    '/index_en.html',
    '/login.html',
    '/redirect.html',
    '/offline.html',
    '/styles.css',
    '/styles_en.min.css',
    '/styles.tailwind.css',
    '/auth-styles.css',
    '/js/app.js',
    '/js/chart-fix.js',
    '/js/api-key-manager.js',
    '/js/market-data-service.js',
    '/src/utils/theme-switcher.min.js',
    '/src/utils/chart.js',
    '/src/utils/chart-integration.js',
    '/src/utils/advanced-chart.min.js',
    '/src/utils/register-sw.min.js',
    '/src/services/technical-analysis.js',
    '/src/services/api-service.js',
    '/src/services/notifications.js',
    '/src/services/user-auth.js',
    '/src/services/unified-api-service.js',
    '/icon-192.png',
    '/icon-512.png',
    '/manifest.json',
    '/icons/dashboard.png',
    '/icons/signals.png',
    '/icons/chart.png',
    '/icons/calendar.png',
    '/en',
    '/ar',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
    'https://cdn.jsdelivr.net/npm/chart.js',
    'https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns',
    'https://cdn.jsdelivr.net/npm/chartjs-chart-financial',
    'https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom',
    'https://cdn.jsdelivr.net/npm/nprogress@0.2.0/nprogress.min.css',
    'https://cdn.jsdelivr.net/npm/nprogress@0.2.0/nprogress.min.js',
    'https://cdn.jsdelivr.net/npm/luxon',
    'https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon'
];

// Install event - cache files
self.addEventListener('install', event => {
    console.log('[Service Worker] Installing Service Worker');

    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('[Service Worker] Caching app shell');
                return cache.addAll(CACHE_FILES);
            })
            .then(() => {
                console.log('[Service Worker] Installation complete');
                return self.skipWaiting();
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('[Service Worker] Activating Service Worker');

    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('[Service Worker] Removing old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('[Service Worker] Activation complete');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', event => {
    // Skip cross-origin requests
    if (!event.request.url.startsWith(self.location.origin) &&
        !event.request.url.includes('cdn.jsdelivr.net') &&
        !event.request.url.includes('cdnjs.cloudflare.com')) {
        return;
    }

    // For API requests, use network first, then cache
    if (event.request.url.includes('/api/')) {
        event.respondWith(
            fetch(event.request)
                .then(response => {
                    // Clone the response
                    const responseToCache = response.clone();

                    // Cache the response
                    caches.open(CACHE_NAME)
                        .then(cache => {
                            cache.put(event.request, responseToCache);
                        });

                    return response;
                })
                .catch(error => {
                    console.log('[Service Worker] API request failed, trying cache:', error);

                    // Notify clients about offline status
                    notifyClientsAboutOfflineStatus();

                    // If network fails, try to get from cache
                    return caches.match(event.request);
                })
        );
    } else {
        // For non-API requests, use cache first, then network
        event.respondWith(
            caches.match(event.request)
                .then(response => {
                    // Return cached response if found
                    if (response) {
                        return response;
                    }

                    // Clone the request
                    const fetchRequest = event.request.clone();

                    // Make network request
                    return fetch(fetchRequest)
                        .then(response => {
                            // Check if valid response
                            if (!response || response.status !== 200 || response.type !== 'basic') {
                                return response;
                            }

                            // Clone the response
                            const responseToCache = response.clone();

                            // Cache the response
                            caches.open(CACHE_NAME)
                                .then(cache => {
                                    cache.put(event.request, responseToCache);
                                });

                            return response;
                        })
                        .catch(error => {
                            console.log('[Service Worker] Fetch failed, likely offline:', error);

                            // Notify clients about offline status
                            notifyClientsAboutOfflineStatus();

                            // If the fetch fails (offline) and this is a navigation request,
                            // show the offline page
                            if (event.request.mode === 'navigate') {
                                console.log('[Service Worker] Serving offline page due to network error');
                                return caches.match('/offline.html');
                            }

                            // For other requests, just throw the error
                            throw error;
                        });
                })
        );
    }
});

// Function to notify all clients about offline status
async function notifyClientsAboutOfflineStatus() {
    const clients = await self.clients.matchAll();
    clients.forEach(client => {
        client.postMessage({
            type: 'offline',
            message: 'You appear to be offline. Some features may not work properly.'
        });
    });
}

// Handle root path to serve index_en.html
self.addEventListener('fetch', event => {
    if (event.request.url === self.location.origin + '/' ||
        event.request.url === self.location.origin) {
        event.respondWith(
            caches.match('/index_en.html')
                .then(response => {
                    return response || fetch('/index_en.html');
                })
                .catch(() => {
                    return caches.match('/offline.html');
                })
        );
    }
});

// Push event - handle push notifications
self.addEventListener('push', event => {
    console.log('[Service Worker] Push received');

    let data = {};
    if (event.data) {
        data = event.data.json();
    }

    const title = data.title || 'Trading Signals App';
    const options = {
        body: data.body || 'New update available',
        icon: data.icon || 'favicon.ico',
        badge: data.badge || 'notification-badge.png',
        data: data.data || {}
    };

    event.waitUntil(
        self.registration.showNotification(title, options)
    );
});

// Notification click event
self.addEventListener('notificationclick', event => {
    console.log('[Service Worker] Notification click received');

    event.notification.close();

    // Open the app and focus on it
    event.waitUntil(
        clients.matchAll({ type: 'window' })
            .then(clientList => {
                // Check if there's already a window open
                for (const client of clientList) {
                    if (client.url.includes(self.location.origin) && 'focus' in client) {
                        return client.focus();
                    }
                }

                // If no window is open, open a new one
                if (clients.openWindow) {
                    return clients.openWindow('/');
                }
            })
    );
});

// Background sync event
self.addEventListener('sync', event => {
    console.log('[Service Worker] Background sync event:', event.tag);

    if (event.tag === 'sync-trading-signals') {
        event.waitUntil(
            // Sync trading signals data
            syncTradingSignals()
        );
    } else if (event.tag === 'sync-economic-calendar') {
        event.waitUntil(
            // Sync economic calendar data
            syncEconomicCalendar()
        );
    }
});

// Function to sync trading signals
async function syncTradingSignals() {
    try {
        // Get saved signals from IndexedDB
        const savedSignals = await getSavedSignals();

        // Send signals to server
        if (savedSignals && savedSignals.length > 0) {
            const response = await fetch('/api/sync-signals', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ signals: savedSignals })
            });

            if (response.ok) {
                // Clear saved signals
                await clearSavedSignals();
            }
        }
    } catch (error) {
        console.error('[Service Worker] Error syncing trading signals:', error);
    }
}

// Function to sync economic calendar
async function syncEconomicCalendar() {
    try {
        // Fetch latest economic calendar data
        const response = await fetch('/api/economic-calendar');

        if (response.ok) {
            const data = await response.json();

            // Cache the data
            const cache = await caches.open(CACHE_NAME);
            await cache.put('/api/economic-calendar', new Response(JSON.stringify(data)));

            // Notify clients about the update
            const clients = await self.clients.matchAll();
            clients.forEach(client => {
                client.postMessage({
                    type: 'economic-calendar-updated',
                    data: data
                });
            });
        }
    } catch (error) {
        console.error('[Service Worker] Error syncing economic calendar:', error);
    }
}

// Helper functions for IndexedDB operations
// These would be implemented in a real application
async function getSavedSignals() {
    // Placeholder for IndexedDB operation
    return [];
}

async function clearSavedSignals() {
    // Placeholder for IndexedDB operation
}
