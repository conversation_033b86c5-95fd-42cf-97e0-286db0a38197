/**
 * Export Utilities
 * 
 * Provides functions for exporting data in various formats
 */

import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';

/**
 * Export backtest results to PDF
 * 
 * @param {Object} results - Backtest results
 * @param {Object} config - Backtest configuration
 * @returns {Blob} - PDF file as Blob
 */
export const exportToPDF = (results, config) => {
  // Create new PDF document
  const doc = new jsPDF();
  
  // Add title
  doc.setFontSize(18);
  doc.text('Backtest Results Report', 14, 22);
  
  // Add date
  doc.setFontSize(10);
  doc.text(`Generated on: ${new Date().toLocaleString()}`, 14, 30);
  
  // Add configuration section
  doc.setFontSize(14);
  doc.text('Backtest Configuration', 14, 40);
  
  // Add configuration details
  doc.setFontSize(10);
  const configDetails = [
    [`Symbol: ${config.symbol}`, `Timeframe: ${config.timeframe}`],
    [`Period: ${config.startDate} to ${config.endDate}`, `Initial Capital: $${config.initialCapital.toLocaleString()}`],
    [`Pattern Types: ${config.patternTypes.join(', ')}`, `Min. Significance: ${config.minPatternSignificance}/10`]
  ];
  
  doc.autoTable({
    startY: 45,
    head: [['Parameter', 'Value']],
    body: [
      ['Symbol', config.symbol],
      ['Timeframe', config.timeframe],
      ['Start Date', config.startDate],
      ['End Date', config.endDate],
      ['Initial Capital', `$${config.initialCapital.toLocaleString()}`],
      ['Position Size', getPositionSizeDescription(config)],
      ['Stop Loss', `${config.stopLoss} pips`],
      ['Take Profit', `${config.takeProfit} pips`],
      ['Pattern Types', config.patternTypes.join(', ')],
      ['Min. Significance', `${config.minPatternSignificance}/10`],
      ['Entry Delay', `${config.entryDelay} candles`],
      ['Exit Strategy', formatExitStrategy(config.exitStrategy)]
    ],
    theme: 'striped',
    headStyles: { fillColor: [66, 139, 202] }
  });
  
  // Add performance summary
  doc.setFontSize(14);
  const performanceY = doc.autoTable.previous.finalY + 10;
  doc.text('Performance Summary', 14, performanceY);
  
  doc.autoTable({
    startY: performanceY + 5,
    head: [['Metric', 'Value']],
    body: [
      ['Net Profit', `$${results.netProfit.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`],
      ['Win Rate', `${(results.winRate * 100).toFixed(2)}%`],
      ['Total Trades', results.totalTrades],
      ['Winning Trades', results.winningTrades],
      ['Losing Trades', results.losingTrades],
      ['Profit Factor', results.profitFactor.toFixed(2)],
      ['Max Drawdown', `$${results.maxDrawdown.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`],
      ['Sharpe Ratio', results.sharpeRatio.toFixed(2)],
      ['Average Trade', `$${results.averageTrade.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`],
      ['Average Win', `$${results.averageWin.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`],
      ['Average Loss', `$${results.averageLoss.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`]
    ],
    theme: 'striped',
    headStyles: { fillColor: [66, 139, 202] }
  });
  
  // Add trade list
  if (results.trades && results.trades.length > 0) {
    doc.addPage();
    doc.setFontSize(14);
    doc.text('Trade List', 14, 22);
    
    // Prepare trade data
    const tradeData = results.trades.map((trade, index) => [
      index + 1,
      trade.pattern,
      trade.entryDate,
      trade.exitDate,
      trade.type,
      `$${trade.entryPrice.toFixed(4)}`,
      `$${trade.exitPrice.toFixed(4)}`,
      `$${trade.profit.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
      `${(trade.profitPercent * 100).toFixed(2)}%`
    ]);
    
    doc.autoTable({
      startY: 30,
      head: [['#', 'Pattern', 'Entry Date', 'Exit Date', 'Type', 'Entry Price', 'Exit Price', 'Profit', 'Profit %']],
      body: tradeData,
      theme: 'striped',
      headStyles: { fillColor: [66, 139, 202] },
      columnStyles: {
        0: { cellWidth: 10 },
        7: { halign: 'right' },
        8: { halign: 'right' }
      },
      didDrawPage: (data) => {
        // Add page number at the bottom
        doc.setFontSize(10);
        doc.text(`Page ${doc.internal.getNumberOfPages()}`, data.settings.margin.left, doc.internal.pageSize.height - 10);
      }
    });
  }
  
  // Add page numbers to all pages
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(10);
    doc.text(`Page ${i} of ${pageCount}`, 14, doc.internal.pageSize.height - 10);
  }
  
  return doc.output('blob');
};

/**
 * Export backtest results to Excel
 * 
 * @param {Object} results - Backtest results
 * @param {Object} config - Backtest configuration
 * @returns {Blob} - Excel file as Blob
 */
export const exportToExcel = (results, config) => {
  // Create workbook
  const wb = XLSX.utils.book_new();
  
  // Create summary worksheet
  const summaryData = [
    ['Backtest Results Summary'],
    ['Generated on:', new Date().toLocaleString()],
    [''],
    ['Configuration'],
    ['Symbol:', config.symbol],
    ['Timeframe:', config.timeframe],
    ['Period:', `${config.startDate} to ${config.endDate}`],
    ['Initial Capital:', `$${config.initialCapital.toLocaleString()}`],
    ['Position Size:', getPositionSizeDescription(config)],
    ['Stop Loss:', `${config.stopLoss} pips`],
    ['Take Profit:', `${config.takeProfit} pips`],
    ['Pattern Types:', config.patternTypes.join(', ')],
    ['Min. Significance:', `${config.minPatternSignificance}/10`],
    ['Entry Delay:', `${config.entryDelay} candles`],
    ['Exit Strategy:', formatExitStrategy(config.exitStrategy)],
    [''],
    ['Performance Summary'],
    ['Net Profit:', `$${results.netProfit.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`],
    ['Win Rate:', `${(results.winRate * 100).toFixed(2)}%`],
    ['Total Trades:', results.totalTrades],
    ['Winning Trades:', results.winningTrades],
    ['Losing Trades:', results.losingTrades],
    ['Profit Factor:', results.profitFactor.toFixed(2)],
    ['Max Drawdown:', `$${results.maxDrawdown.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`],
    ['Sharpe Ratio:', results.sharpeRatio.toFixed(2)],
    ['Average Trade:', `$${results.averageTrade.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`],
    ['Average Win:', `$${results.averageWin.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`],
    ['Average Loss:', `$${results.averageLoss.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`]
  ];
  
  const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);
  XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary');
  
  // Create trades worksheet if there are trades
  if (results.trades && results.trades.length > 0) {
    // Prepare headers
    const tradeHeaders = ['#', 'Pattern', 'Entry Date', 'Exit Date', 'Type', 'Entry Price', 'Exit Price', 'Profit', 'Profit %'];
    
    // Prepare trade data
    const tradeData = results.trades.map((trade, index) => [
      index + 1,
      trade.pattern,
      trade.entryDate,
      trade.exitDate,
      trade.type,
      trade.entryPrice,
      trade.exitPrice,
      trade.profit,
      trade.profitPercent
    ]);
    
    // Combine headers and data
    const tradesSheet = [tradeHeaders, ...tradeData];
    
    const tradesWs = XLSX.utils.aoa_to_sheet(tradesSheet);
    XLSX.utils.book_append_sheet(wb, tradesWs, 'Trades');
  }
  
  // Create equity curve worksheet
  if (results.equityCurve && results.equityCurve.length > 0) {
    // Prepare headers
    const equityHeaders = ['Date', 'Equity'];
    
    // Prepare equity data
    const equityData = results.equityCurve.map(point => [
      point.date,
      point.equity
    ]);
    
    // Combine headers and data
    const equitySheet = [equityHeaders, ...equityData];
    
    const equityWs = XLSX.utils.aoa_to_sheet(equitySheet);
    XLSX.utils.book_append_sheet(wb, equityWs, 'Equity Curve');
  }
  
  // Generate Excel file
  const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });
  
  // Convert to Blob
  const buf = new ArrayBuffer(wbout.length);
  const view = new Uint8Array(buf);
  for (let i = 0; i < wbout.length; i++) {
    view[i] = wbout.charCodeAt(i) & 0xFF;
  }
  
  return new Blob([buf], { type: 'application/octet-stream' });
};

/**
 * Export backtest results to CSV
 * 
 * @param {Object} results - Backtest results
 * @returns {Blob} - CSV file as Blob
 */
export const exportToCSV = (results) => {
  // Check if there are trades
  if (!results.trades || results.trades.length === 0) {
    return new Blob(['No trades to export'], { type: 'text/csv' });
  }
  
  // Prepare headers
  const headers = ['#', 'Pattern', 'Entry Date', 'Exit Date', 'Type', 'Entry Price', 'Exit Price', 'Profit', 'Profit %'];
  
  // Prepare trade data
  const tradeData = results.trades.map((trade, index) => [
    index + 1,
    trade.pattern,
    trade.entryDate,
    trade.exitDate,
    trade.type,
    trade.entryPrice,
    trade.exitPrice,
    trade.profit,
    trade.profitPercent
  ]);
  
  // Combine headers and data
  const csvContent = [
    headers.join(','),
    ...tradeData.map(row => row.join(','))
  ].join('\n');
  
  return new Blob([csvContent], { type: 'text/csv' });
};

/**
 * Get position size description based on configuration
 * 
 * @param {Object} config - Backtest configuration
 * @returns {string} - Position size description
 */
const getPositionSizeDescription = (config) => {
  switch (config.positionSize) {
    case 'fixed':
      return `Fixed (${config.fixedSize} lots)`;
    case 'percentage':
      return `Percentage (${config.percentageSize}% of capital)`;
    case 'risk_based':
      return `Risk-based (${config.riskPercentage}% risk per trade)`;
    default:
      return 'Unknown';
  }
};

/**
 * Format exit strategy for display
 * 
 * @param {string} exitStrategy - Exit strategy code
 * @returns {string} - Formatted exit strategy
 */
const formatExitStrategy = (exitStrategy) => {
  switch (exitStrategy) {
    case 'take_profit_stop_loss':
      return 'Take Profit / Stop Loss';
    case 'trailing_stop':
      return 'Trailing Stop';
    case 'time_based':
      return 'Time-based Exit';
    case 'opposite_pattern':
      return 'Opposite Pattern';
    default:
      return exitStrategy;
  }
};

export default {
  exportToPDF,
  exportToExcel,
  exportToCSV
};
