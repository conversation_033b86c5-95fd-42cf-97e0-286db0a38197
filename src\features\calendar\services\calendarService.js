import axios from 'axios';

class CalendarService {
  async getUpcomingEvents({ country, impact }) {
    const apiKey = process.env.FMP_API_KEY;
    const today = new Date().toISOString().slice(0, 10);
    const nextWeek = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10);
    let url = `https://financialmodelingprep.com/api/v3/economic_calendar?from=${today}&to=${nextWeek}&apikey=${apiKey}`;
    const { data } = await axios.get(url);
    // Filter by country/impact if provided
    return data.filter(event =>
      (!country || event.country === country) &&
      (!impact || (event.impact && event.impact.toLowerCase() === impact.toLowerCase()))
    );
  }

  async getHistoricalEvents({ event, country }) {
    const apiKey = process.env.FMP_API_KEY;
    const lastMonth = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10);
    const today = new Date().toISOString().slice(0, 10);
    let url = `https://financialmodelingprep.com/api/v3/economic_calendar?from=${lastMonth}&to=${today}&apikey=${apiKey}`;
    const { data } = await axios.get(url);
    // Filter by event/country if provided
    return data.filter(e =>
      (!event || e.event === event) &&
      (!country || e.country === country)
    );
  }
}

export default new CalendarService();
