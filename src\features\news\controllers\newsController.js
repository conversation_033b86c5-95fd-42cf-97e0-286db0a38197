import newsService, { getSentimentHistory, getCOTHistory } from '../services/newsService.js';

export const getNews = async (req, res, next) => {
  try {
    const { symbol } = req.query;
    const news = await newsService.getNews({ symbol });
    res.json({ status: 'success', data: news });
  } catch (error) {
    next(error);
  }
};

export const getSentiment = async (req, res, next) => {
  try {
    const { symbol } = req.query;
    const sentiment = await newsService.getSentiment({ symbol });
    res.json({ status: 'success', data: sentiment });
  } catch (error) {
    next(error);
  }
};

export const getCOT = async (req, res, next) => {
  try {
    const { symbol } = req.query;
    const cot = await newsService.getCOT({ symbol });
    res.json({ status: 'success', data: cot });
  } catch (error) {
    next(error);
  }
};

export const sentimentHistory = async (req, res, next) => {
  try {
    const { symbol } = req.query;
    const history = await getSentimentHistory(symbol);
    res.json({ status: 'success', data: history });
  } catch (error) {
    next(error);
  }
};

export const cotHistory = async (req, res, next) => {
  try {
    const { symbol } = req.query;
    const history = await getCOTHistory(symbol);
    res.json({ status: 'success', data: history });
  } catch (error) {
    next(error);
  }
};

export default {
  getNews,
  getSentiment,
  getCOT,
  sentimentHistory,
  cotHistory,
}; 