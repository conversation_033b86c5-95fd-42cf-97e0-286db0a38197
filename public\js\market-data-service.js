/**
 * Market Data Service
 *
 * This module provides functions for fetching real-time market data from various APIs.
 * It includes caching, error handling, and fallback mechanisms.
 */

// API Configuration
const API_CONFIG = {
    // Alpha Vantage API
    ALPHA_VANTAGE: {
        BASE_URL: 'https://www.alphavantage.co/query',
        // API keys for rotation
        API_KEYS: [
            'OFU3DJH5JWW6Z29Z', // Primary Alpha Vantage API key
            'YVPVZ8R9L8HXGMGJ', // Secondary Alpha Vantage API key
            'RNZPXZ6Q9FMCV2BM', // Tertiary Alpha Vantage API key
            // Add more keys as needed
        ],
        ENDPOINTS: {
            INTRADAY: 'TIME_SERIES_INTRADAY',
            DAILY: 'TIME_SERIES_DAILY',
            QUOTE: 'GLOBAL_QUOTE',
            FOREX: 'FX_INTRADAY',
            CRYPTO: 'CRYPTO_INTRADAY'
        }
    },

    // Local API
    LOCAL_API: {
        BASE_URL: '/api',
        ENDPOINTS: {
            MARKET_DATA: '/market-data',
            TECHNICAL_INDICATORS: '/technical-indicators',
            ECONOMIC_CALENDAR: '/economic-calendar',
            TRADING_SIGNALS: '/trading-signals'
        }
    },

    // Polygon.io API (alternative data source)
    POLYGON: {
        BASE_URL: 'https://api.polygon.io',
        API_KEYS: [
            'YOUR_POLYGON_API_KEY', // Replace with your Polygon.io API key
        ],
        ENDPOINTS: {
            FOREX: '/v2/aggs/ticker',
            STOCKS: '/v2/aggs/ticker',
            CRYPTO: '/v2/aggs/ticker'
        }
    },

    // Financial Modeling Prep API (another alternative)
    FMP: {
        BASE_URL: 'https://financialmodelingprep.com/api/v3',
        API_KEYS: [
            'YOUR_FMP_API_KEY', // Replace with your FMP API key
        ],
        ENDPOINTS: {
            QUOTE: '/quote',
            HISTORICAL: '/historical-price-full'
        }
    }
};

// Cache for API responses
const apiCache = new Map();
const CACHE_TTL = 60000; // 1 minute cache TTL for real-time data

// Initialize API key managers
const keyManagers = {};

/**
 * Initialize API key managers for all providers
 */
function initializeKeyManagers() {
    // Alpha Vantage key manager
    keyManagers.ALPHA_VANTAGE = new ApiKeyManager('ALPHA_VANTAGE', API_CONFIG.ALPHA_VANTAGE.API_KEYS, {
        cooldownPeriod: 60 * 60 * 1000, // 1 hour cooldown
        maxFailures: 3,
        logLevel: 'info'
    });

    // Polygon.io key manager
    if (API_CONFIG.POLYGON.API_KEYS[0] !== 'YOUR_POLYGON_API_KEY') {
        keyManagers.POLYGON = new ApiKeyManager('POLYGON', API_CONFIG.POLYGON.API_KEYS, {
            cooldownPeriod: 30 * 60 * 1000, // 30 minutes cooldown
            maxFailures: 3,
            logLevel: 'info'
        });
    }

    // FMP key manager
    if (API_CONFIG.FMP.API_KEYS[0] !== 'YOUR_FMP_API_KEY') {
        keyManagers.FMP = new ApiKeyManager('FMP', API_CONFIG.FMP.API_KEYS, {
            cooldownPeriod: 30 * 60 * 1000, // 30 minutes cooldown
            maxFailures: 3,
            logLevel: 'info'
        });
    }

    console.log('API key managers initialized');
}

// Initialize key managers when the script loads
if (typeof ApiKeyManager !== 'undefined') {
    initializeKeyManagers();
} else {
    console.warn('ApiKeyManager not loaded. Key rotation will not be available.');
}

/**
 * Fetch data from Alpha Vantage API with robust key rotation
 * @param {string} endpoint - API endpoint
 * @param {Object} params - Query parameters
 * @param {number} retryCount - Number of retries (default: 0)
 * @returns {Promise<Object>} - API response
 */
async function fetchAlphaVantage(endpoint, params = {}, retryCount = 0) {
    // Check if key manager is available
    if (!keyManagers.ALPHA_VANTAGE) {
        if (typeof ApiKeyManager !== 'undefined') {
            initializeKeyManagers();
        } else {
            console.error('ApiKeyManager not loaded. Using simple key rotation.');
            return fetchAlphaVantageSimple(endpoint, params);
        }
    }

    try {
        // Create cache key (without API key to share cache across keys)
        const cacheKey = `av_${endpoint}_${JSON.stringify(params)}`;

        // Check cache
        const cachedData = apiCache.get(cacheKey);
        if (cachedData && (Date.now() - cachedData.timestamp < CACHE_TTL)) {
            console.log(`Using cached Alpha Vantage data for ${endpoint}`);
            return cachedData.data;
        }

        // Get next available API key
        const apiKey = keyManagers.ALPHA_VANTAGE.getNextKey();

        if (!apiKey) {
            throw new Error('No valid Alpha Vantage API key available');
        }

        // Build URL
        const url = new URL(API_CONFIG.ALPHA_VANTAGE.BASE_URL);
        url.searchParams.append('function', endpoint);
        url.searchParams.append('apikey', apiKey);

        // Add all params to URL
        Object.entries(params).forEach(([key, value]) => {
            url.searchParams.append(key, value);
        });

        console.log(`Fetching Alpha Vantage data for ${endpoint}`);

        // Fetch data with timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        try {
            const response = await fetch(url.toString(), { signal: controller.signal });
            clearTimeout(timeoutId);

            if (!response.ok) {
                // Mark this key as failed
                keyManagers.ALPHA_VANTAGE.markFailure(apiKey, `HTTP error ${response.status}`);

                // Retry with a different key if we haven't exceeded retry limit
                if (retryCount < API_CONFIG.ALPHA_VANTAGE.API_KEYS.length - 1) {
                    console.log(`Retrying Alpha Vantage request with a different key (retry ${retryCount + 1})`);
                    return fetchAlphaVantage(endpoint, params, retryCount + 1);
                }

                throw new Error(`Alpha Vantage API error: ${response.status}`);
            }

            const data = await response.json();

            // Check for API limit message
            if (data['Note'] && data['Note'].includes('API call frequency')) {
                console.warn('Alpha Vantage API rate limit reached:', data['Note']);

                // Mark this key as failed
                keyManagers.ALPHA_VANTAGE.markFailure(apiKey, 'Rate limit reached');

                // Retry with a different key if we haven't exceeded retry limit
                if (retryCount < API_CONFIG.ALPHA_VANTAGE.API_KEYS.length - 1) {
                    console.log(`Retrying Alpha Vantage request with a different key (retry ${retryCount + 1})`);
                    return fetchAlphaVantage(endpoint, params, retryCount + 1);
                }

                throw new Error('All Alpha Vantage API keys have reached their rate limit');
            }

            // Check for error message
            if (data['Error Message']) {
                console.warn('Alpha Vantage API error:', data['Error Message']);

                // Mark this key as failed
                keyManagers.ALPHA_VANTAGE.markFailure(apiKey, data['Error Message']);

                // Retry with a different key if we haven't exceeded retry limit
                if (retryCount < API_CONFIG.ALPHA_VANTAGE.API_KEYS.length - 1) {
                    console.log(`Retrying Alpha Vantage request with a different key (retry ${retryCount + 1})`);
                    return fetchAlphaVantage(endpoint, params, retryCount + 1);
                }

                throw new Error(`Alpha Vantage API error: ${data['Error Message']}`);
            }

            // Check if the response is empty or doesn't contain expected data
            if (Object.keys(data).length === 0) {
                console.warn('Alpha Vantage API returned empty data');

                // Mark this key as failed
                keyManagers.ALPHA_VANTAGE.markFailure(apiKey, 'Empty response');

                // Retry with a different key if we haven't exceeded retry limit
                if (retryCount < API_CONFIG.ALPHA_VANTAGE.API_KEYS.length - 1) {
                    console.log(`Retrying Alpha Vantage request with a different key (retry ${retryCount + 1})`);
                    return fetchAlphaVantage(endpoint, params, retryCount + 1);
                }

                throw new Error('Alpha Vantage API returned empty data');
            }

            // Mark this key as successful
            keyManagers.ALPHA_VANTAGE.markSuccess(apiKey);

            // Cache data
            apiCache.set(cacheKey, {
                data,
                timestamp: Date.now()
            });

            console.log(`Successfully fetched Alpha Vantage data for ${endpoint}`);
            return data;

        } catch (fetchError) {
            clearTimeout(timeoutId);

            if (fetchError.name === 'AbortError') {
                console.warn('Alpha Vantage request timed out');
                keyManagers.ALPHA_VANTAGE.markFailure(apiKey, 'Request timeout');
            } else {
                keyManagers.ALPHA_VANTAGE.markFailure(apiKey, fetchError.message);
            }

            // Retry with a different key if we haven't exceeded retry limit
            if (retryCount < API_CONFIG.ALPHA_VANTAGE.API_KEYS.length - 1) {
                console.log(`Retrying Alpha Vantage request with a different key (retry ${retryCount + 1})`);
                return fetchAlphaVantage(endpoint, params, retryCount + 1);
            }

            throw fetchError;
        }

    } catch (error) {
        console.error('Error fetching from Alpha Vantage:', error);

        // If we've tried all keys and still failed, try alternative data sources
        if (retryCount >= API_CONFIG.ALPHA_VANTAGE.API_KEYS.length - 1) {
            console.log('All Alpha Vantage keys failed, trying alternative data sources');

            // Try to fetch from Polygon.io if configured
            if (keyManagers.POLYGON) {
                try {
                    return await fetchPolygon(params.symbol || params.from_symbol + params.to_symbol, params.interval);
                } catch (polygonError) {
                    console.error('Polygon.io API failed:', polygonError);
                }
            }

            // Try to fetch from FMP if configured
            if (keyManagers.FMP) {
                try {
                    return await fetchFMP(params.symbol || params.from_symbol + params.to_symbol);
                } catch (fmpError) {
                    console.error('FMP API failed:', fmpError);
                }
            }
        }

        throw error;
    }
}

/**
 * Simple fallback function for fetching from Alpha Vantage when ApiKeyManager is not available
 * @param {string} endpoint - API endpoint
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} - API response
 */
async function fetchAlphaVantageSimple(endpoint, params = {}) {
    try {
        // Create cache key
        const cacheKey = `av_simple_${endpoint}_${JSON.stringify(params)}`;

        // Check cache
        const cachedData = apiCache.get(cacheKey);
        if (cachedData && (Date.now() - cachedData.timestamp < CACHE_TTL)) {
            return cachedData.data;
        }

        // Simple key rotation
        const keys = API_CONFIG.ALPHA_VANTAGE.API_KEYS;
        const keyIndex = Math.floor(Math.random() * keys.length);
        const apiKey = keys[keyIndex];

        // Build URL
        const url = new URL(API_CONFIG.ALPHA_VANTAGE.BASE_URL);
        url.searchParams.append('function', endpoint);
        url.searchParams.append('apikey', apiKey);

        // Add all params to URL
        Object.entries(params).forEach(([key, value]) => {
            url.searchParams.append(key, value);
        });

        // Fetch data
        const response = await fetch(url.toString());

        if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
        }

        const data = await response.json();

        // Cache data
        apiCache.set(cacheKey, {
            data,
            timestamp: Date.now()
        });

        return data;
    } catch (error) {
        console.error('Error in simple Alpha Vantage fetch:', error);
        throw error;
    }
}

/**
 * Fetch data from local API - simplified version
 * @param {string} endpoint - API endpoint
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} - API response
 */
async function fetchLocalAPI(endpoint, params = {}) {
    try {
        // Create cache key
        const cacheKey = `local_${endpoint}_${JSON.stringify(params)}`;

        // Check cache
        const cachedData = apiCache.get(cacheKey);
        if (cachedData && (Date.now() - cachedData.timestamp < CACHE_TTL)) {
            return cachedData.data;
        }

        // Build URL
        let url = `${API_CONFIG.LOCAL_API.BASE_URL}${endpoint}`;

        // Add query parameters if any
        if (Object.keys(params).length > 0) {
            const queryString = new URLSearchParams(params).toString();
            url += `?${queryString}`;
        }

        // Fetch data
        const response = await fetch(url);

        if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
        }

        const data = await response.json();

        // Cache data
        apiCache.set(cacheKey, {
            data,
            timestamp: Date.now()
        });

        return data;
    } catch (error) {
        throw error;
    }
}

/**
 * Get real-time market data for a symbol with robust API key rotation
 * @param {string} symbol - Market symbol (e.g., EURUSD, AAPL)
 * @param {string} interval - Time interval (e.g., M1, M5, M15, M30, H1, H4, D1)
 * @param {string} assetType - Asset type (forex, stock, crypto, commodity)
 * @returns {Promise<Object>} - Market data
 */
async function getRealTimeMarketData(symbol, interval = 'M5', assetType = 'forex') {
    try {
        console.log(`Getting real-time market data for ${symbol} (${interval}, ${assetType})`);

        // Validate inputs
        if (!symbol) {
            console.warn('No symbol provided, using default');
            return generateMockMarketData('EURUSD', interval, assetType);
        }

        // Create a list of data sources to try in order
        const dataSources = [];

        // Try the integrated financial data client first if available
        if (window.FinancialDataClient) {
            dataSources.push(async () => {
                console.log('Trying integrated financial data client...');
                try {
                    // Map interval format
                    const mappedInterval = mapIntervalToAlphaVantage(interval);

                    // Get market data from the financial data client
                    const response = await window.FinancialDataClient.getMarketData(symbol, mappedInterval);

                    // Format the response to match our expected format
                    return formatIntegratedApiData(response, symbol, interval, assetType);
                } catch (error) {
                    console.error('Error using integrated financial data client:', error);
                    throw error;
                }
            });
        }

        // Always try local API next
        dataSources.push(async () => {
            console.log('Trying local API...');
            return await fetchLocalAPI(`${API_CONFIG.LOCAL_API.ENDPOINTS.MARKET_DATA}/${symbol}`, {
                interval,
                assetType
            });
        });

        // Add Alpha Vantage as the third source
        dataSources.push(async () => {
            console.log('Trying Alpha Vantage API...');

            // Determine endpoint and parameters
            let endpoint, params;

            if (assetType === 'forex') {
                // Parse forex symbol
                let fromCurrency, toCurrency;

                if (symbol.includes('/')) {
                    [fromCurrency, toCurrency] = symbol.split('/');
                } else if (symbol.length === 6) {
                    fromCurrency = symbol.substring(0, 3);
                    toCurrency = symbol.substring(3, 6);
                } else {
                    throw new Error(`Invalid forex symbol format: ${symbol}`);
                }

                endpoint = API_CONFIG.ALPHA_VANTAGE.ENDPOINTS.FOREX;
                params = {
                    from_symbol: fromCurrency,
                    to_symbol: toCurrency,
                    interval: mapIntervalToAlphaVantage(interval),
                    outputsize: 'compact'
                };
            } else if (assetType === 'crypto') {
                // For crypto, we need to handle different formats
                let cryptoSymbol, market;

                if (symbol.includes('/')) {
                    [cryptoSymbol, market] = symbol.split('/');
                } else if (symbol.length >= 6) {
                    // Assume format like BTCUSD
                    cryptoSymbol = symbol.substring(0, 3);
                    market = symbol.substring(3);
                } else {
                    throw new Error(`Invalid crypto symbol format: ${symbol}`);
                }

                endpoint = API_CONFIG.ALPHA_VANTAGE.ENDPOINTS.CRYPTO;
                params = {
                    symbol: cryptoSymbol,
                    market: market,
                    interval: mapIntervalToAlphaVantage(interval),
                    outputsize: 'compact'
                };
            } else {
                endpoint = API_CONFIG.ALPHA_VANTAGE.ENDPOINTS.INTRADAY;
                params = {
                    symbol,
                    interval: mapIntervalToAlphaVantage(interval),
                    outputsize: 'compact'
                };
            }

            // Fetch data
            const data = await fetchAlphaVantage(endpoint, params);
            return formatAlphaVantageData(data, symbol, interval, assetType);
        });

        // Add Polygon.io as the fourth source if configured
        if (keyManagers.POLYGON) {
            dataSources.push(async () => {
                console.log('Trying Polygon.io API...');
                return await fetchPolygon(symbol, interval);
            });
        }

        // Add FMP as the fifth source if configured
        if (keyManagers.FMP) {
            dataSources.push(async () => {
                console.log('Trying FMP API...');
                return await fetchFMP(symbol);
            });
        }

        // Try each data source in order until one succeeds
        let lastError = null;
        for (const dataSource of dataSources) {
            try {
                const data = await dataSource();
                console.log(`Successfully fetched data from ${data.source || 'unknown source'}`);

                // Add a UI indicator if the data is not from the primary source
                if (data.source && data.source !== 'local-api' && data.source !== 'alpha-vantage' && data.source !== 'integrated-api') {
                    data.isAlternativeSource = true;
                    data.sourceInfo = `Data provided by ${data.source}`;
                }

                return data;
            } catch (error) {
                console.warn(`Data source failed: ${error.message}`);
                lastError = error;
                // Continue to the next data source
            }
        }

        // If all data sources failed, fall back to mock data
        console.warn('All data sources failed, falling back to mock data');
        const mockData = generateMockMarketData(symbol, interval, assetType);
        mockData.errorMessage = lastError ? lastError.message : 'All data sources failed';
        return mockData;

    } catch (error) {
        console.error('Unexpected error in getRealTimeMarketData:', error);
        return generateMockMarketData(symbol, interval, assetType);
    }
}



/**
 * Format Alpha Vantage data to standard format - simplified version
 * @param {Object} data - Alpha Vantage data
 * @param {string} symbol - Market symbol
 * @param {string} interval - Time interval
 * @param {string} assetType - Asset type
 * @returns {Object} - Formatted data
 */
function formatAlphaVantageData(data, symbol, interval, assetType) {
    try {
        // Check for API errors
        if (data['Note'] || data['Error Message']) {
            throw new Error('API error');
        }

        // Determine time series key
        let timeSeriesKey;
        if (assetType === 'forex') {
            timeSeriesKey = `Time Series FX (${mapIntervalToAlphaVantage(interval)})`;
        } else if (assetType === 'crypto') {
            timeSeriesKey = `Time Series Crypto (${mapIntervalToAlphaVantage(interval)})`;
        } else {
            timeSeriesKey = `Time Series (${mapIntervalToAlphaVantage(interval)})`;
        }

        // Get time series data
        const timeSeries = data[timeSeriesKey] ||
                          data[Object.keys(data).find(k => k.includes('Time Series'))] ||
                          null;

        if (!timeSeries) {
            throw new Error('No time series data');
        }

        // Convert to arrays
        const dates = [];
        const opens = [];
        const highs = [];
        const lows = [];
        const closes = [];
        const volumes = [];

        // Process data
        Object.entries(timeSeries).forEach(([date, values]) => {
            // Extract OHLCV data
            const open = parseFloat(values['1. open'] || values.open || 0);
            const high = parseFloat(values['2. high'] || values.high || 0);
            const low = parseFloat(values['3. low'] || values.low || 0);
            const close = parseFloat(values['4. close'] || values.close || 0);
            const volume = parseInt(values['5. volume'] || values['6. volume'] || values.volume || 0, 10);

            // Add to arrays
            dates.push(date);
            opens.push(open);
            highs.push(high);
            lows.push(low);
            closes.push(close);
            volumes.push(volume);
        });

        if (dates.length === 0) {
            throw new Error('No data points');
        }

        // Calculate metrics
        const currentPrice = closes[0];
        const previousPrice = closes[1] || closes[0];
        const dailyChange = ((currentPrice - previousPrice) / previousPrice) * 100;

        // Return formatted data
        return {
            symbol,
            interval,
            assetType,
            currentPrice,
            dailyChange,
            sentiment: 50, // Default sentiment
            dates: dates.reverse(),
            opens: opens.reverse(),
            highs: highs.reverse(),
            lows: lows.reverse(),
            closes: closes.reverse(),
            volumes: volumes.reverse(),
            lastUpdated: new Date().toISOString(),
            source: 'alpha-vantage'
        };
    } catch (error) {
        return generateMockMarketData(symbol, interval, assetType);
    }
}

/**
 * Calculate market sentiment based on price data
 * @param {Array<number>} prices - Array of prices
 * @returns {number} - Sentiment value (0-100)
 */
function calculateSentiment(prices) {
    if (!prices || prices.length < 10) {
        return 50; // Neutral if not enough data
    }

    // Simple algorithm: compare current price to moving averages
    const shortTermMA = calculateSMA(prices, 5);
    const mediumTermMA = calculateSMA(prices, 10);
    const longTermMA = calculateSMA(prices, 20);

    let sentiment = 50; // Start neutral

    // Price above moving averages is bullish
    if (prices[0] > shortTermMA) sentiment += 10;
    if (prices[0] > mediumTermMA) sentiment += 10;
    if (prices[0] > longTermMA) sentiment += 10;

    // Short-term MA above longer-term MAs is bullish
    if (shortTermMA > mediumTermMA) sentiment += 10;
    if (mediumTermMA > longTermMA) sentiment += 10;

    // Ensure sentiment is between 0 and 100
    return Math.max(0, Math.min(100, sentiment));
}

/**
 * Calculate Simple Moving Average
 * @param {Array<number>} prices - Array of prices
 * @param {number} period - Period for SMA
 * @returns {number} - SMA value
 */
function calculateSMA(prices, period) {
    if (prices.length < period) {
        return prices[0]; // Not enough data
    }

    const sum = prices.slice(0, period).reduce((a, b) => a + b, 0);
    return sum / period;
}

/**
 * Fetch data from Polygon.io API
 * @param {string} symbol - Market symbol
 * @param {string} interval - Time interval
 * @returns {Promise<Object>} - API response
 */
async function fetchPolygon(symbol, interval) {
    // Check if key manager is available
    if (!keyManagers.POLYGON) {
        throw new Error('Polygon.io API not configured');
    }

    try {
        // Create cache key
        const cacheKey = `polygon_${symbol}_${interval}`;

        // Check cache
        const cachedData = apiCache.get(cacheKey);
        if (cachedData && (Date.now() - cachedData.timestamp < CACHE_TTL)) {
            console.log(`Using cached Polygon.io data for ${symbol}`);
            return cachedData.data;
        }

        // Get API key
        const apiKey = keyManagers.POLYGON.getNextKey();

        if (!apiKey) {
            throw new Error('No valid Polygon.io API key available');
        }

        // Format symbol for Polygon.io
        let formattedSymbol = symbol;
        if (symbol.length === 6 && !symbol.includes('/')) {
            // Forex pair like EURUSD
            formattedSymbol = `C:${symbol.substring(0, 3)}${symbol.substring(3, 6)}`;
        }

        // Map interval to Polygon.io format
        const multiplier = 5; // Default to 5 minutes
        const timespan = 'minute';

        // Build URL
        const from = new Date();
        from.setDate(from.getDate() - 7); // 7 days ago

        const to = new Date();

        const fromStr = from.toISOString().split('T')[0];
        const toStr = to.toISOString().split('T')[0];

        const url = new URL(`${API_CONFIG.POLYGON.BASE_URL}${API_CONFIG.POLYGON.ENDPOINTS.FOREX}/${formattedSymbol}/range/${multiplier}/${timespan}/${fromStr}/${toStr}`);
        url.searchParams.append('apiKey', apiKey);
        url.searchParams.append('limit', '120');

        console.log(`Fetching Polygon.io data for ${symbol}`);

        // Fetch data with timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        try {
            const response = await fetch(url.toString(), { signal: controller.signal });
            clearTimeout(timeoutId);

            if (!response.ok) {
                keyManagers.POLYGON.markFailure(apiKey, `HTTP error ${response.status}`);
                throw new Error(`Polygon.io API error: ${response.status}`);
            }

            const polygonData = await response.json();

            if (polygonData.status === 'ERROR' || !polygonData.results || polygonData.results.length === 0) {
                keyManagers.POLYGON.markFailure(apiKey, 'Invalid or empty response');
                throw new Error('Polygon.io API returned invalid or empty data');
            }

            // Format data to match our standard format
            const data = formatPolygonData(polygonData, symbol, interval);

            // Mark key as successful
            keyManagers.POLYGON.markSuccess(apiKey);

            // Cache data
            apiCache.set(cacheKey, {
                data,
                timestamp: Date.now()
            });

            console.log(`Successfully fetched Polygon.io data for ${symbol}`);
            return data;

        } catch (fetchError) {
            clearTimeout(timeoutId);

            if (fetchError.name === 'AbortError') {
                keyManagers.POLYGON.markFailure(apiKey, 'Request timeout');
            } else {
                keyManagers.POLYGON.markFailure(apiKey, fetchError.message);
            }

            throw fetchError;
        }

    } catch (error) {
        console.error('Error fetching from Polygon.io:', error);
        throw error;
    }
}

/**
 * Format Polygon.io data to match our standard format
 * @param {Object} polygonData - Polygon.io response
 * @param {string} symbol - Market symbol
 * @param {string} interval - Time interval
 * @returns {Object} - Formatted data
 */
function formatPolygonData(polygonData, symbol, interval) {
    const results = polygonData.results || [];

    // Extract data
    const dates = [];
    const opens = [];
    const highs = [];
    const lows = [];
    const closes = [];
    const volumes = [];

    // Process results
    results.forEach(bar => {
        const date = new Date(bar.t);
        dates.push(date.toISOString());
        opens.push(bar.o);
        highs.push(bar.h);
        lows.push(bar.l);
        closes.push(bar.c);
        volumes.push(bar.v || 0);
    });

    // Calculate metrics
    const currentPrice = closes[closes.length - 1] || 0;
    const previousPrice = closes[closes.length - 2] || currentPrice;
    const dailyChange = ((currentPrice - previousPrice) / previousPrice) * 100;

    return {
        symbol,
        interval,
        assetType: symbol.length === 6 ? 'forex' : 'stock',
        currentPrice,
        dailyChange,
        sentiment: 50, // Default sentiment
        dates,
        opens,
        highs,
        lows,
        closes,
        volumes,
        lastUpdated: new Date().toISOString(),
        source: 'polygon'
    };
}

/**
 * Fetch data from Financial Modeling Prep API
 * @param {string} symbol - Market symbol
 * @returns {Promise<Object>} - API response
 */
async function fetchFMP(symbol) {
    // Check if key manager is available
    if (!keyManagers.FMP) {
        throw new Error('FMP API not configured');
    }

    try {
        // Create cache key
        const cacheKey = `fmp_${symbol}`;

        // Check cache
        const cachedData = apiCache.get(cacheKey);
        if (cachedData && (Date.now() - cachedData.timestamp < CACHE_TTL)) {
            console.log(`Using cached FMP data for ${symbol}`);
            return cachedData.data;
        }

        // Get API key
        const apiKey = keyManagers.FMP.getNextKey();

        if (!apiKey) {
            throw new Error('No valid FMP API key available');
        }

        // Format symbol for FMP
        let formattedSymbol = symbol;
        if (symbol.length === 6 && !symbol.includes('/')) {
            // Forex pair like EURUSD
            formattedSymbol = `${symbol.substring(0, 3)}/${symbol.substring(3, 6)}`;
        }

        // Build URL
        const url = new URL(`${API_CONFIG.FMP.BASE_URL}${API_CONFIG.FMP.ENDPOINTS.HISTORICAL}/${formattedSymbol}`);
        url.searchParams.append('apikey', apiKey);
        url.searchParams.append('timeseries', '120');

        console.log(`Fetching FMP data for ${symbol}`);

        // Fetch data with timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        try {
            const response = await fetch(url.toString(), { signal: controller.signal });
            clearTimeout(timeoutId);

            if (!response.ok) {
                keyManagers.FMP.markFailure(apiKey, `HTTP error ${response.status}`);
                throw new Error(`FMP API error: ${response.status}`);
            }

            const fmpData = await response.json();

            if (!fmpData.historical || fmpData.historical.length === 0) {
                keyManagers.FMP.markFailure(apiKey, 'Invalid or empty response');
                throw new Error('FMP API returned invalid or empty data');
            }

            // Format data to match our standard format
            const data = formatFMPData(fmpData, symbol);

            // Mark key as successful
            keyManagers.FMP.markSuccess(apiKey);

            // Cache data
            apiCache.set(cacheKey, {
                data,
                timestamp: Date.now()
            });

            console.log(`Successfully fetched FMP data for ${symbol}`);
            return data;

        } catch (fetchError) {
            clearTimeout(timeoutId);

            if (fetchError.name === 'AbortError') {
                keyManagers.FMP.markFailure(apiKey, 'Request timeout');
            } else {
                keyManagers.FMP.markFailure(apiKey, fetchError.message);
            }

            throw fetchError;
        }

    } catch (error) {
        console.error('Error fetching from FMP:', error);
        throw error;
    }
}

/**
 * Format FMP data to match our standard format
 * @param {Object} fmpData - FMP response
 * @param {string} symbol - Market symbol
 * @returns {Object} - Formatted data
 */
function formatFMPData(fmpData, symbol) {
    const historical = fmpData.historical || [];

    // Extract data
    const dates = [];
    const opens = [];
    const highs = [];
    const lows = [];
    const closes = [];
    const volumes = [];

    // Process historical data
    historical.forEach(bar => {
        dates.push(bar.date);
        opens.push(parseFloat(bar.open));
        highs.push(parseFloat(bar.high));
        lows.push(parseFloat(bar.low));
        closes.push(parseFloat(bar.close));
        volumes.push(parseInt(bar.volume || '0', 10));
    });

    // Calculate metrics
    const currentPrice = closes[0] || 0;
    const previousPrice = closes[1] || currentPrice;
    const dailyChange = ((currentPrice - previousPrice) / previousPrice) * 100;

    return {
        symbol,
        interval: 'D1', // FMP provides daily data
        assetType: symbol.length === 6 ? 'forex' : 'stock',
        currentPrice,
        dailyChange,
        sentiment: 50, // Default sentiment
        dates,
        opens,
        highs,
        lows,
        closes,
        volumes,
        lastUpdated: new Date().toISOString(),
        source: 'fmp'
    };
}

/**
 * Generate mock market data with realistic values
 * @param {string} symbol - Market symbol
 * @param {string} interval - Time interval
 * @param {string} assetType - Asset type
 * @returns {Object} - Mock market data
 */
function generateMockMarketData(symbol, interval, assetType) {
    console.log(`Generating mock data for ${symbol} (${interval}, ${assetType})`);

    // Set base price based on the symbol and asset type
    let basePrice;

    switch (symbol) {
        // Forex pairs
        case 'EURUSD': basePrice = 1.08; break;
        case 'GBPUSD': basePrice = 1.26; break;
        case 'USDJPY': basePrice = 149.5; break;
        case 'AUDUSD': basePrice = 0.65; break;
        case 'USDCAD': basePrice = 1.35; break;

        // Commodities
        case 'XAUUSD': basePrice = 2340.5; break;
        case 'XAGUSD': basePrice = 27.85; break;
        case 'USOIL': basePrice = 78.35; break;

        // Crypto
        case 'BTCUSD': basePrice = 62450.25; break;
        case 'ETHUSD': basePrice = 3050.75; break;

        // Indices
        case 'US30': basePrice = 38750.25; break;
        case 'SPX500': basePrice = 5215.75; break;
        case 'NASDAQ': basePrice = 16325.50; break;

        // Default - random price
        default: basePrice = Math.random() * 100 + 50;
    }

    // Determine price volatility based on asset type
    let volatility;
    switch (assetType) {
        case 'forex': volatility = 0.0005; break;      // 0.05% volatility for forex
        case 'commodities': volatility = 0.005; break; // 0.5% volatility for commodities
        case 'crypto': volatility = 0.02; break;       // 2% volatility for crypto
        case 'indices': volatility = 0.005; break;     // 0.5% volatility for indices
        default: volatility = 0.01;                    // 1% default volatility
    }

    // Generate data points
    const numPoints = 50;
    const dates = [];
    const opens = [];
    const highs = [];
    const lows = [];
    const closes = [];
    const volumes = [];

    // Generate realistic price movement
    let currentPrice = basePrice;
    let trend = 0;
    const trendStrength = 0.2;  // How strong the trend is
    const trendChangeProb = 0.05; // Probability of trend direction change

    const now = new Date();

    for (let i = 0; i < numPoints; i++) {
        // Calculate date based on interval
        const date = new Date(now);
        date.setMinutes(now.getMinutes() - i * getIntervalMinutes(interval));

        // Randomly change trend direction occasionally
        if (Math.random() < trendChangeProb) {
            trend = (Math.random() - 0.5) * 2 * trendStrength;
        }

        // Add trend and random noise to price
        const noise = (Math.random() - 0.5) * 2 * volatility * basePrice;
        currentPrice = currentPrice * (1 + trend) + noise;

        // Ensure price doesn't go negative
        currentPrice = Math.max(currentPrice, basePrice * 0.5);

        // Generate OHLC data
        const open = currentPrice * (1 + (Math.random() - 0.5) * 0.5 * volatility);
        const high = Math.max(currentPrice, open) * (1 + Math.random() * volatility);
        const low = Math.min(currentPrice, open) * (1 - Math.random() * volatility);
        const close = currentPrice;

        // Generate volume based on asset type
        let volume;
        switch (assetType) {
            case 'forex': volume = Math.floor(Math.random() * 500) + 100; break;
            case 'commodities': volume = Math.floor(Math.random() * 1000) + 200; break;
            case 'crypto': volume = Math.floor(Math.random() * 2000) + 500; break;
            case 'indices': volume = Math.floor(Math.random() * 5000) + 1000; break;
            default: volume = Math.floor(Math.random() * 1000) + 100;
        }

        // Add data point (in reverse order - oldest first)
        dates.unshift(date.toISOString());
        opens.unshift(open);
        highs.unshift(high);
        lows.unshift(low);
        closes.unshift(close);
        volumes.unshift(volume);
    }

    // Calculate daily change percentage
    const lastPrice = closes[closes.length - 1];
    const prevPrice = closes[closes.length - 2] || closes[closes.length - 1];
    const dailyChange = ((lastPrice - prevPrice) / prevPrice) * 100;

    return {
        symbol,
        interval,
        assetType,
        currentPrice: lastPrice,
        dailyChange,
        sentiment: 50,
        dates,
        opens,
        highs,
        lows,
        closes,
        volumes,
        lastUpdated: new Date().toISOString(),
        source: 'mock-data',
        isMockData: true
    };
}

/**
 * Get interval in minutes
 * @param {string} interval - Interval string (e.g., M1, M5, H1, D1)
 * @returns {number} - Interval in minutes
 */
function getIntervalMinutes(interval) {
    switch (interval) {
        case 'M1': return 1;
        case 'M5': return 5;
        case 'M15': return 15;
        case 'M30': return 30;
        case 'H1': return 60;
        case 'H4': return 240;
        case 'D1': return 1440;
        default: return 5;
    }
}

/**
 * Map interval to Alpha Vantage format
 * @param {string} interval - Interval (e.g., M1, M5, M15, M30, H1, H4, D1)
 * @returns {string} - Alpha Vantage interval
 */
function mapIntervalToAlphaVantage(interval) {
    const map = {
        'M1': '1min',
        'M5': '5min',
        'M15': '15min',
        'M30': '30min',
        'H1': '60min',
        'H4': '60min', // Alpha Vantage doesn't have 4h, use 1h instead
        'D1': 'daily'
    };

    return map[interval] || '5min'; // Default to 5min
}


/**
 * Format data from the integrated API to match our standard format
 * @param {Object} response - Integrated API response
 * @param {string} symbol - Market symbol
 * @param {string} interval - Time interval
 * @param {string} assetType - Asset type
 * @returns {Object} - Formatted data
 */
function formatIntegratedApiData(response, symbol, interval, assetType) {
    try {
        // Check if response is valid
        if (!response || !response.data) {
            throw new Error('Invalid response from integrated API');
        }

        // Extract data from response
        const data = response.data;

        // Check which source provided the data
        let source = 'integrated-api';
        if (response.source) {
            source = response.source;
        }

        // Extract time series data
        let timeSeries = null;

        // Try to find time series data in different formats
        if (data['Time Series (5min)']) {
            timeSeries = data['Time Series (5min)'];
        } else if (data['Time Series FX (5min)']) {
            timeSeries = data['Time Series FX (5min)'];
        } else if (data.results) {
            // Polygon.io format
            timeSeries = data.results;
        } else if (data.historical) {
            // FMP format
            timeSeries = data.historical;
        } else if (data.candles) {
            // Generic candles format
            timeSeries = data.candles;
        }

        if (!timeSeries) {
            console.warn('No time series data found in integrated API response');
            return generateMockMarketData(symbol, interval, assetType);
        }

        // Convert to arrays
        const dates = [];
        const opens = [];
        const highs = [];
        const lows = [];
        const closes = [];
        const volumes = [];

        // Process data based on format
        if (Array.isArray(timeSeries)) {
            // Array format (Polygon, FMP, etc.)
            timeSeries.forEach(candle => {
                // Extract date
                let date;
                if (candle.date) {
                    date = candle.date;
                } else if (candle.t) {
                    date = new Date(candle.t).toISOString();
                } else if (candle.timestamp) {
                    date = new Date(candle.timestamp).toISOString();
                } else {
                    date = new Date().toISOString();
                }

                // Extract OHLCV
                const open = parseFloat(candle.o || candle.open || 0);
                const high = parseFloat(candle.h || candle.high || 0);
                const low = parseFloat(candle.l || candle.low || 0);
                const close = parseFloat(candle.c || candle.close || 0);
                const volume = parseInt(candle.v || candle.volume || 0, 10);

                // Add to arrays
                dates.push(date);
                opens.push(open);
                highs.push(high);
                lows.push(low);
                closes.push(close);
                volumes.push(volume);
            });
        } else {
            // Object format (Alpha Vantage, etc.)
            Object.entries(timeSeries).forEach(([date, values]) => {
                // Extract OHLCV data
                const open = parseFloat(values['1. open'] || values.open || 0);
                const high = parseFloat(values['2. high'] || values.high || 0);
                const low = parseFloat(values['3. low'] || values.low || 0);
                const close = parseFloat(values['4. close'] || values.close || 0);
                const volume = parseInt(values['5. volume'] || values['6. volume'] || values.volume || 0, 10);

                // Add to arrays
                dates.push(date);
                opens.push(open);
                highs.push(high);
                lows.push(low);
                closes.push(close);
                volumes.push(volume);
            });
        }

        if (dates.length === 0) {
            throw new Error('No data points found in integrated API response');
        }

        // Calculate metrics
        const currentPrice = closes[0];
        const previousPrice = closes[1] || closes[0];
        const dailyChange = ((currentPrice - previousPrice) / previousPrice) * 100;

        // Return formatted data
        return {
            symbol,
            interval,
            assetType,
            currentPrice,
            dailyChange,
            sentiment: 50, // Default sentiment
            dates: dates.reverse(),
            opens: opens.reverse(),
            highs: highs.reverse(),
            lows: lows.reverse(),
            closes: closes.reverse(),
            volumes: volumes.reverse(),
            lastUpdated: new Date().toISOString(),
            source
        };
    } catch (error) {
        console.error('Error formatting integrated API data:', error);
        return generateMockMarketData(symbol, interval, assetType);
    }
}

// Export functions for external use
window.MarketDataService = {
    getRealTimeMarketData,
    fetchAlphaVantage,
    fetchPolygon,
    fetchFMP,
    fetchLocalAPI,
    generateMockMarketData,
    formatIntegratedApiData,

    // Add API key management functions
    getApiKeyStatus: function() {
        const status = {};

        // Get status for each provider
        Object.keys(keyManagers).forEach(provider => {
            status[provider] = keyManagers[provider].getStatus();
        });

        return status;
    },

    resetApiKey: function(provider, key) {
        if (keyManagers[provider]) {
            keyManagers[provider].resetKey(key);
            return true;
        }
        return false;
    },

    resetAllApiKeys: function(provider) {
        if (keyManagers[provider]) {
            keyManagers[provider].resetAllKeys();
            return true;
        }
        return false;
    }
};
