/**
 * Main Routes Index for Trading Signals App
 *
 * This file exports a function that sets up all routes for the application.
 */

const axios = require('axios');
const path = require('path');
const logger = require('../utils/logger');
const { APIError } = require('../middleware/errorHandler');

module.exports = function(app, apiCache, API_CONFIG) {
  // Helper function to format date for API requests
  function getFormattedDate(daysOffset = 0) {
    const date = new Date();
    date.setDate(date.getDate() + daysOffset);
    return date.toISOString().split('T')[0];
  }

  // Helper function to transform FRED calendar data to economic events
  function transformFredCalendarToEvents(fredData) {
    if (!fredData || !fredData.releases) {
      return [];
    }

    return fredData.releases.map(release => ({
      id: release.id,
      title: release.name,
      date: release.date,
      time: '08:30:00', // Default time if not provided
      country: 'US',
      importance: release.importance || 'medium',
      actual: release.value || null,
      forecast: null,
      previous: null,
      unit: release.units || '',
      description: release.notes || ''
    }));
  }

  // Helper function to map app interval to Alpha Vantage interval
  function mapIntervalToAlphaVantage(interval) {
    const map = {
      'M1': '1min',
      'M5': '5min',
      'M15': '15min',
      'M30': '30min',
      'H1': '60min',
      'H4': '4hour',
      'D1': 'daily',
      'W1': 'weekly',
      'MN': 'monthly'
    };
    return map[interval] || '5min';
  }

  // Helper function to get mock market data
  function getMockMarketData(symbol, interval, assetType) {
    // Set starting price based on symbol
    let price;
    let dailyChange;
    let sentiment;

    switch (symbol) {
      case 'EURUSD':
        price = 1.0875;
        dailyChange = 0.25;
        sentiment = 65;
        break;
      case 'GBPUSD':
        price = 1.2650;
        dailyChange = -0.15;
        sentiment = 45;
        break;
      case 'USDJPY':
        price = 149.50;
        dailyChange = 0.10;
        sentiment = 55;
        break;
      case 'XAUUSD':
        price = 2340.50;
        dailyChange = 0.45;
        sentiment = 75;
        break;
      case 'XAGUSD':
        price = 27.85;
        dailyChange = 0.30;
        sentiment = 70;
        break;
      case 'USOIL':
        price = 78.35;
        dailyChange = -0.65;
        sentiment = 40;
        break;
      case 'BTCUSD':
        price = 62450.25;
        dailyChange = 1.25;
        sentiment = 80;
        break;
      case 'ETHUSD':
        price = 3050.75;
        dailyChange = 0.95;
        sentiment = 75;
        break;
      default:
        price = 100.00;
        dailyChange = 0.00;
        sentiment = 50;
    }

    return {
      currentPrice: price,
      dailyChange,
      sentiment
    };
  }

  /**
   * Market Data Routes
   */

  // Get market data for a symbol
  app.get('/api/market-data/:symbol', async (req, res, next) => {
    try {
      const { symbol } = req.params;
      const { interval = '1d', outputsize = 'compact' } = req.query;

      // Create cache key
      const cacheKey = `market_data_${symbol}_${interval}_${outputsize}`;

      // Check cache first
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        return res.json(cachedData);
      }

      // Determine if this is forex, crypto, or stock
      let functionName, dataKey;

      if (symbol.includes('USD') && symbol !== 'BTCUSD') {
        // Forex data
        const fromCurrency = symbol.substring(0, 3);
        const toCurrency = symbol.substring(3, 6);

        functionName = interval === '1d' ? 'FX_DAILY' : 'FX_INTRADAY';
        dataKey = interval === '1d' ? 'Time Series FX (Daily)' : `Time Series FX (${interval})`;

        const response = await axios.get(API_CONFIG.ALPHA_VANTAGE.BASE_URL, {
          params: {
            function: functionName,
            from_symbol: fromCurrency,
            to_symbol: toCurrency,
            interval,
            outputsize,
            apikey: API_CONFIG.ALPHA_VANTAGE.API_KEY
          }
        });

        // Cache the response
        apiCache.set(cacheKey, response.data, 300); // 5 minutes TTL

        return res.json(response.data);
      } else if (symbol === 'BTCUSD') {
        // Crypto data
        functionName = interval === '1d' ? 'DIGITAL_CURRENCY_DAILY' : 'DIGITAL_CURRENCY_INTRADAY';
        dataKey = interval === '1d' ? 'Time Series (Digital Currency Daily)' : `Time Series (Digital Currency ${interval})`;

        const response = await axios.get(API_CONFIG.ALPHA_VANTAGE.BASE_URL, {
          params: {
            function: functionName,
            symbol: 'BTC',
            market: 'USD',
            interval,
            outputsize,
            apikey: API_CONFIG.ALPHA_VANTAGE.API_KEY
          }
        });

        // Cache the response
        apiCache.set(cacheKey, response.data, 300); // 5 minutes TTL

        return res.json(response.data);
      } else {
        // Stock data
        functionName = interval === '1d' ? 'TIME_SERIES_DAILY' : 'TIME_SERIES_INTRADAY';
        dataKey = interval === '1d' ? 'Time Series (Daily)' : `Time Series (${interval})`;

        const response = await axios.get(API_CONFIG.ALPHA_VANTAGE.BASE_URL, {
          params: {
            function: functionName,
            symbol,
            interval,
            outputsize,
            apikey: API_CONFIG.ALPHA_VANTAGE.API_KEY
          }
        });

        // Cache the response
        apiCache.set(cacheKey, response.data, 300); // 5 minutes TTL

        return res.json(response.data);
      }
    } catch (error) {
      next(new APIError(500, 'Error fetching market data', error.message));
    }
  });

  /**
   * Technical Indicators Routes
   */

  // Get technical indicator for a symbol
  app.get('/api/technical/:indicator/:symbol', async (req, res, next) => {
    try {
      const { indicator, symbol } = req.params;
      const {
        interval = '1d',
        time_period = 14,
        series_type = 'close'
      } = req.query;

      // Create cache key
      const cacheKey = `technical_${indicator}_${symbol}_${interval}_${time_period}_${series_type}`;

      // Check cache first
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        return res.json(cachedData);
      }

      // Get technical indicator from Alpha Vantage
      const response = await axios.get(API_CONFIG.ALPHA_VANTAGE.BASE_URL, {
        params: {
          function: indicator,
          symbol,
          interval,
          time_period,
          series_type,
          apikey: API_CONFIG.ALPHA_VANTAGE.API_KEY
        }
      });

      // Cache the response
      apiCache.set(cacheKey, response.data, 300); // 5 minutes TTL

      return res.json(response.data);
    } catch (error) {
      next(new APIError(500, 'Error fetching technical indicator', error.message));
    }
  });

  // Root route
  app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../../../public/index_en.html'));
  });

  // Language-specific routes (for backward compatibility)
  app.get('/en', (req, res) => {
    res.sendFile(path.join(__dirname, '../../../public/index_en.html'));
  });

  // Redirect /index.html to /index_en.html
  app.get('/index.html', (req, res) => {
    res.redirect('/index_en.html');
  });

  // Login route
  app.get('/login', (req, res) => {
    res.sendFile(path.join(__dirname, '../../../public/login.html'));
  });

  // API documentation route
  app.get('/api', (req, res) => {
    res.sendFile(path.join(__dirname, '../../../public/api-docs.html'));
  });

  // API status route
  app.get('/api/status', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date(),
      version: '1.0.0'
    });
  });

  // Market data route
  app.get('/api/market-data', (req, res) => {
    // Mock market data
    const marketData = {
      forex: {
        EURUSD: { price: 1.0875, change: "+0.25%", sentiment: 65 },
        GBPUSD: { price: 1.2650, change: "-0.15%", sentiment: 45 },
        USDJPY: { price: 149.50, change: "+0.10%", sentiment: 55 }
      },
      commodities: {
        XAUUSD: { price: 2340.50, change: "+0.45%", sentiment: 75 },
        XAGUSD: { price: 27.85, change: "+0.30%", sentiment: 70 },
        USOIL: { price: 78.35, change: "-0.65%", sentiment: 40 }
      },
      indices: {
        US30: { price: 38750.25, change: "+0.35%", sentiment: 60 },
        SPX500: { price: 5215.75, change: "+0.40%", sentiment: 65 },
        NASDAQ: { price: 16325.50, change: "+0.55%", sentiment: 70 }
      },
      crypto: {
        BTCUSD: { price: 62450.25, change: "+1.25%", sentiment: 80 },
        ETHUSD: { price: 3050.75, change: "+0.95%", sentiment: 75 }
      }
    };

    res.json(marketData);
  });

  // Load additional route modules
  try {
    require('./marketDataRoutes')(app, apiCache, API_CONFIG, getMockMarketData, mapIntervalToAlphaVantage);
    require('./technicalIndicatorsRoutes')(app, apiCache, API_CONFIG);
    require('./economicCalendarRoutes')(app, apiCache, API_CONFIG, getFormattedDate, transformFredCalendarToEvents);
    require('./tradingSignalsRoutes')(app, apiCache, API_CONFIG);
    require('./newsRoutes')(app, apiCache, API_CONFIG);
    require('./proxyRoutes')(app, apiCache, API_CONFIG);
    logger.info('All route modules loaded successfully');
  } catch (error) {
    logger.error('Error loading route modules:', error);
  }

  // 404 handler for API routes
  app.use('/api/*', (req, res) => {
    logger.warn(`API route not found: ${req.originalUrl}`);
    res.status(404).json({
      error: 'Not Found',
      message: `The requested API endpoint ${req.originalUrl} does not exist.`
    });
  });

  // Catch-all route for client-side routing
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../../../public/index_en.html'));
  });
};
