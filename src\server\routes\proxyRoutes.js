/**
 * API Proxy Routes for Trading Signals App
 *
 * This file provides proxy routes for external APIs to avoid CORS issues
 * and to centralize API key management.
 */

const axios = require('axios');
const logger = require('../utils/logger');

// Add custom axios instance with improved error handling
const apiAxios = axios.create({
  timeout: 15000, // Increased timeout to 15 seconds
  headers: {
    'User-Agent': 'Trading Signals App/1.0',
    'Accept': 'application/json'
  }
});

// Add response interceptor for better error handling
apiAxios.interceptors.response.use(
  response => response,
  error => {
    // Create detailed error log
    const errorDetails = {
      message: error.message,
      code: error.code
    };

    // Add response data if available
    if (error.response) {
      errorDetails.status = error.response.status;
      errorDetails.statusText = error.response.statusText;
      errorDetails.data = error.response.data;
    }

    // Add request data if available
    if (error.config) {
      errorDetails.url = error.config.url.replace(/apikey=([^&]*)/, 'apikey=HIDDEN')
                                        .replace(/api_key=([^&]*)/, 'api_key=HIDDEN')
                                        .replace(/token=([^&]*)/, 'token=HIDDEN');
      errorDetails.method = error.config.method;
      errorDetails.timeout = error.config.timeout;
    }

    // Log the error with detailed information
    logger.error('API Request Failed:', errorDetails);

    // Rethrow the error for the calling code to handle
    return Promise.reject(error);
  }
);

module.exports = function(app, apiCache, API_CONFIG) {
  // Alpha Vantage API proxy with improved error handling and retry mechanism
  app.get('/api/proxy/alpha-vantage', async (req, res) => {
    try {
      const cacheKey = `alpha_vantage_${JSON.stringify(req.query)}`;

      // Check if data is in cache
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        logger.info('Returning cached Alpha Vantage data');
        return res.json(cachedData);
      }

      // Forward request to Alpha Vantage
      const config = API_CONFIG.ALPHA_VANTAGE;
      const params = new URLSearchParams(req.query);

      // Ensure API key is set and valid
      let apiKey = params.get('apikey') || config.API_KEY;

      // Validate API key format
      if (!apiKey || apiKey === 'demo' || apiKey === 'your-api-key') {
        // Use the one from .env file
        apiKey = process.env.ALPHA_VANTAGE_API_KEY;
        logger.info('Using Alpha Vantage API key from environment variables');
      }

      // Remove any existing apikey parameter
      params.delete('apikey');

      // Add the validated API key
      params.append('apikey', apiKey);

      const apiUrl = `${config.BASE_URL}?${params.toString()}`;
      logger.info(`Making Alpha Vantage API request: ${apiUrl}`);

      // Add timeout and retry logic
      const maxRetries = 2;
      let retries = 0;
      let response;

      while (retries <= maxRetries) {
        try {
          logger.info(`Making Alpha Vantage API request (attempt ${retries + 1}/${maxRetries + 1}): ${apiUrl.replace(/apikey=([^&]*)/, 'apikey=HIDDEN')}`);

          // Use the improved apiAxios instance
          response = await apiAxios.get(apiUrl);

          // Check if the response contains an error message from Alpha Vantage
          if (response.data && (
              response.data.hasOwnProperty('Error Message') ||
              response.data.hasOwnProperty('Information') ||
              response.data.hasOwnProperty('Note')
          )) {
            if (response.data['Error Message']) {
              throw new Error(`Alpha Vantage API error: ${response.data['Error Message']}`);
            } else if (response.data['Note'] && response.data['Note'].includes('API call frequency')) {
              // This is a rate limit error
              logger.warn(`Alpha Vantage rate limit reached: ${response.data['Note']}`);

              if (retries < maxRetries) {
                // Wait before retrying (exponential backoff)
                const delay = Math.pow(2, retries) * 1000;
                logger.info(`Retrying after ${delay}ms (attempt ${retries + 1}/${maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, delay));
                retries++;
                continue;
              } else {
                throw new Error(`Alpha Vantage rate limit reached: ${response.data['Note']}`);
              }
            } else if (response.data['Information']) {
              logger.info(`Alpha Vantage information: ${response.data['Information']}`);
            }
          }

          logger.info(`Alpha Vantage API request successful: ${response.status}`);

          // If we got here, the request was successful
          break;
        } catch (retryError) {
          if (retries < maxRetries) {
            // Wait before retrying (exponential backoff)
            const delay = Math.pow(2, retries) * 1000;
            logger.warn(`Alpha Vantage request failed, retrying after ${delay}ms (attempt ${retries + 1}/${maxRetries}): ${retryError.message}`);
            await new Promise(resolve => setTimeout(resolve, delay));
            retries++;
          } else {
            // Max retries reached, rethrow the error
            throw retryError;
          }
        }
      }

      // Validate the response data
      if (!response || !response.data) {
        throw new Error('Empty response from Alpha Vantage API');
      }

      // Check if the response is valid JSON
      if (typeof response.data !== 'object') {
        throw new Error('Invalid response format from Alpha Vantage API');
      }

      // Cache the response (5 minutes TTL)
      apiCache.set(cacheKey, response.data, 300);

      // Return the data
      return res.json(response.data);
    } catch (error) {
      logger.error('Alpha Vantage API error:', error.message);

      // Return a more detailed error response
      return res.status(500).json({
        error: 'Failed to fetch data from Alpha Vantage',
        message: error.message,
        timestamp: new Date().toISOString(),
        requestId: `404a5da4984-e2ee-409e-8c8e-0c8d1c48d8ab-${Date.now()}`
      });
    }
  });

  // Finnhub API proxy
  app.get('/api/proxy/finnhub', async (req, res) => {
    try {
      const cacheKey = `finnhub_${JSON.stringify(req.query)}`;

      // Check if data is in cache
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        logger.info('Returning cached Finnhub data');
        return res.json(cachedData);
      }

      // Forward request to Finnhub
      const config = API_CONFIG.FINNHUB;
      const params = new URLSearchParams(req.query);
      const endpoint = params.get('endpoint') || '';
      params.delete('endpoint');

      // Add API key if not provided
      if (!params.has('token')) {
        params.append('token', config.API_KEY);
      }

      const url = `${config.BASE_URL}${endpoint}?${params.toString()}`;
      logger.info(`Making Finnhub API request: ${url.replace(/token=([^&]*)/, 'token=HIDDEN')}`);
      const response = await apiAxios.get(url);

      // Cache the response
      apiCache.set(cacheKey, response.data);

      res.json(response.data);
    } catch (error) {
      logger.error('Finnhub API error:', error.message);
      res.status(500).json({ error: 'Failed to fetch data from Finnhub' });
    }
  });

  // Twelve Data API proxy
  app.get('/api/proxy/twelve-data', async (req, res) => {
    try {
      const cacheKey = `twelve_data_${JSON.stringify(req.query)}`;

      // Check if data is in cache
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        logger.info('Returning cached Twelve Data');
        return res.json(cachedData);
      }

      // Forward request to Twelve Data
      const config = API_CONFIG.TWELVE_DATA;
      const params = new URLSearchParams(req.query);
      const endpoint = params.get('endpoint') || '';
      params.delete('endpoint');

      // Add API key if not provided
      if (!params.has('apikey')) {
        params.append('apikey', config.API_KEY);
      }

      const url = `${config.BASE_URL}${endpoint}?${params.toString()}`;
      logger.info(`Making Twelve Data API request: ${url.replace(/apikey=([^&]*)/, 'apikey=HIDDEN')}`);
      const response = await apiAxios.get(url);

      // Cache the response
      apiCache.set(cacheKey, response.data);

      res.json(response.data);
    } catch (error) {
      logger.error('Twelve Data API error:', error.message);
      res.status(500).json({ error: 'Failed to fetch data from Twelve Data' });
    }
  });

  // Polygon API proxy
  app.get('/api/proxy/polygon', async (req, res) => {
    try {
      const cacheKey = `polygon_${JSON.stringify(req.query)}`;

      // Check if data is in cache
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        logger.info('Returning cached Polygon data');
        return res.json(cachedData);
      }

      // Forward request to Polygon
      const config = API_CONFIG.POLYGON;
      const params = new URLSearchParams(req.query);
      const endpoint = params.get('endpoint') || '';
      params.delete('endpoint');

      // Add API key if not provided
      if (!params.has('apiKey')) {
        params.append('apiKey', config.API_KEY);
      }

      const url = `${config.BASE_URL}${endpoint}?${params.toString()}`;
      logger.info(`Making Polygon API request: ${url.replace(/apiKey=([^&]*)/, 'apiKey=HIDDEN')}`);
      const response = await apiAxios.get(url);

      // Cache the response
      apiCache.set(cacheKey, response.data);

      res.json(response.data);
    } catch (error) {
      logger.error('Polygon API error:', error.message);
      res.status(500).json({ error: 'Failed to fetch data from Polygon' });
    }
  });

  // OpenAI API proxy
  app.get('/api/proxy/openai', async (req, res) => {
    try {
      const cacheKey = `openai_${JSON.stringify(req.query)}`;

      // Check if data is in cache
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        logger.info('Returning cached OpenAI data');
        return res.json(cachedData);
      }

      // Forward request to OpenAI
      const config = API_CONFIG.OPENAI;
      const params = new URLSearchParams(req.query);
      const endpoint = params.get('endpoint') || '';
      params.delete('endpoint');

      // OpenAI requires Authorization header instead of query param
      const headers = {
        'Authorization': `Bearer ${config.API_KEY}`,
        'Content-Type': 'application/json'
      };

      const url = `${config.BASE_URL}${endpoint}?${params.toString()}`;
      logger.info(`Making OpenAI API request: ${url}`);
      const response = await apiAxios.get(url, { headers });

      // Cache the response
      apiCache.set(cacheKey, response.data);

      res.json(response.data);
    } catch (error) {
      logger.error('OpenAI API error:', error.message);
      res.status(500).json({ error: 'Failed to fetch data from OpenAI' });
    }
  });

  // Financial Modeling Prep API proxy
  app.get('/api/proxy/fmp', async (req, res) => {
    try {
      const cacheKey = `fmp_${JSON.stringify(req.query)}`;

      // Check if data is in cache
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        logger.info('Returning cached FMP data');
        return res.json(cachedData);
      }

      // Forward request to Financial Modeling Prep
      const config = API_CONFIG.FMP;
      const params = new URLSearchParams(req.query);
      const endpoint = params.get('endpoint') || '';
      params.delete('endpoint');

      // Add API key if not provided
      if (!params.has('apikey')) {
        params.append('apikey', config.API_KEY);
      }

      const url = `${config.BASE_URL}${endpoint}?${params.toString()}`;
      logger.info(`Making FMP API request: ${url.replace(/apikey=([^&]*)/, 'apikey=HIDDEN')}`);
      const response = await apiAxios.get(url);

      // Cache the response
      apiCache.set(cacheKey, response.data);

      res.json(response.data);
    } catch (error) {
      logger.error('FMP API error:', error.message);
      res.status(500).json({ error: 'Failed to fetch data from Financial Modeling Prep' });
    }
  });

  // Options data API proxy with improved error handling
  app.get('/api/options-data/:symbol', async (req, res) => {
    try {
      const symbol = req.params.symbol;

      const cacheKey = `options_data_${symbol}`;

      // Check if data is in cache
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        logger.info(`Returning cached options data for ${symbol}`);
        return res.json(cachedData);
      }

      // Try to get real data from Alpha Vantage
      try {
        const config = API_CONFIG.ALPHA_VANTAGE;

        // Ensure API key is set and valid
        let apiKey = config.API_KEY;

        // Validate API key format
        if (!apiKey || apiKey === 'demo' || apiKey === 'your-api-key') {
          // Use the one from .env file
          apiKey = process.env.ALPHA_VANTAGE_API_KEY;
          logger.info('Using Alpha Vantage API key from environment variables');
        }

        const apiUrl = `${config.BASE_URL}?function=REALTIME_OPTIONS&symbol=${symbol}&apikey=${apiKey}`;
        logger.info(`Making Alpha Vantage options API request for ${symbol}`);

        // Add timeout and retry logic
        const maxRetries = 2;
        let retries = 0;
        let response;

        while (retries <= maxRetries) {
          try {
            logger.info(`Making Alpha Vantage options API request (attempt ${retries + 1}/${maxRetries + 1}): ${apiUrl.replace(/apikey=([^&]*)/, 'apikey=HIDDEN')}`);
            response = await apiAxios.get(apiUrl);

            // Check if the response contains an error message from Alpha Vantage
            if (response.data && (
                response.data.hasOwnProperty('Error Message') ||
                response.data.hasOwnProperty('Information') ||
                response.data.hasOwnProperty('Note')
            )) {
              if (response.data['Error Message']) {
                throw new Error(`Alpha Vantage API error: ${response.data['Error Message']}`);
              } else if (response.data['Note'] && response.data['Note'].includes('API call frequency')) {
                // This is a rate limit error
                logger.warn(`Alpha Vantage rate limit reached: ${response.data['Note']}`);

                if (retries < maxRetries) {
                  // Wait before retrying (exponential backoff)
                  const delay = Math.pow(2, retries) * 1000;
                  logger.info(`Retrying after ${delay}ms (attempt ${retries + 1}/${maxRetries})`);
                  await new Promise(resolve => setTimeout(resolve, delay));
                  retries++;
                  continue;
                } else {
                  throw new Error(`Alpha Vantage rate limit reached: ${response.data['Note']}`);
                }
              } else if (response.data['Information']) {
                logger.info(`Alpha Vantage information: ${response.data['Information']}`);
              }
            }

            // If we got here, the request was successful
            break;
          } catch (retryError) {
            if (retries < maxRetries) {
              // Wait before retrying (exponential backoff)
              const delay = Math.pow(2, retries) * 1000;
              logger.warn(`Alpha Vantage request failed, retrying after ${delay}ms (attempt ${retries + 1}/${maxRetries}): ${retryError.message}`);
              await new Promise(resolve => setTimeout(resolve, delay));
              retries++;
            } else {
              // Max retries reached, rethrow the error
              throw retryError;
            }
          }
        }

        // Validate the response data
        if (!response || !response.data) {
          throw new Error('Empty response from Alpha Vantage API');
        }

        // Check if the response is valid JSON
        if (typeof response.data !== 'object') {
          throw new Error('Invalid response format from Alpha Vantage API');
        }

        // Cache the response (10 minutes TTL for options data)
        apiCache.set(cacheKey, response.data, 600);

        return res.json(response.data);
      } catch (error) {
        logger.warn(`Failed to get options data for ${symbol}:`, error.message);

        // Log detailed error information for debugging
        logger.error({
          message: `Alpha Vantage options API error for ${symbol}`,
          error: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString(),
          requestId: `404a5da4984-e2ee-409e-8c8e-0c8d1c48d8ab-${Date.now()}`
        });

        // Return error response
        return res.status(500).json({
          error: 'Failed to fetch options data',
          symbol: symbol,
          message: error.message,
          timestamp: new Date().toISOString(),
          requestId: `404a5da4984-e2ee-409e-8c8e-0c8d1c48d8ab-${Date.now()}`
        });
      }
    } catch (error) {
      logger.error('Options data API error:', error.message);
      res.status(500).json({
        error: 'Failed to fetch options data',
        message: error.message,
        timestamp: new Date().toISOString(),
        requestId: `404a5da4984-e2ee-409e-8c8e-0c8d1c48d8ab-${Date.now()}`
      });
    }
  });

  // API test route
  app.get('/api/test-all-apis', async (req, res) => {
    try {
      logger.info('Testing all APIs...');

      const results = {};

      // Test Alpha Vantage
      try {
        logger.info('Testing Alpha Vantage API...');
        const alphaVantageResponse = await apiAxios.get(`${API_CONFIG.ALPHA_VANTAGE.BASE_URL}?function=GLOBAL_QUOTE&symbol=AAPL&apikey=${API_CONFIG.ALPHA_VANTAGE.API_KEY}`);
        results.alphaVantage = {
          success: true,
          status: alphaVantageResponse.status,
          data: alphaVantageResponse.data
        };
      } catch (error) {
        results.alphaVantage = {
          success: false,
          error: error.message,
          status: error.response?.status,
          data: error.response?.data
        };
      }

      // Test FRED
      try {
        logger.info('Testing FRED API...');
        const today = new Date().toISOString().split('T')[0];
        const twoWeeksLater = new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        const fredResponse = await apiAxios.get(`${API_CONFIG.FRED.BASE_URL}/releases/dates?include_release_dates_with_no_data=true&start_date=${today}&end_date=${twoWeeksLater}&api_key=${API_CONFIG.FRED.API_KEY}&file_type=json`);
        results.fred = {
          success: true,
          status: fredResponse.status,
          data: fredResponse.data
        };
      } catch (error) {
        results.fred = {
          success: false,
          error: error.message,
          status: error.response?.status,
          data: error.response?.data
        };
      }

      // Test Polygon
      try {
        logger.info('Testing Polygon API...');
        const polygonResponse = await apiAxios.get(`${API_CONFIG.POLYGON.BASE_URL}/v2/aggs/ticker/AAPL/prev?apiKey=${API_CONFIG.POLYGON.API_KEY}`);
        results.polygon = {
          success: true,
          status: polygonResponse.status,
          data: polygonResponse.data
        };
      } catch (error) {
        results.polygon = {
          success: false,
          error: error.message,
          status: error.response?.status,
          data: error.response?.data
        };
      }

      // Test Finnhub
      try {
        logger.info('Testing Finnhub API...');
        const finnhubResponse = await apiAxios.get(`${API_CONFIG.FINNHUB.BASE_URL}/quote?symbol=AAPL&token=${API_CONFIG.FINNHUB.API_KEY}`);
        results.finnhub = {
          success: true,
          status: finnhubResponse.status,
          data: finnhubResponse.data
        };
      } catch (error) {
        results.finnhub = {
          success: false,
          error: error.message,
          status: error.response?.status,
          data: error.response?.data
        };
      }

      // Test Twelve Data
      try {
        logger.info('Testing Twelve Data API...');
        const twelveDataResponse = await apiAxios.get(`${API_CONFIG.TWELVE_DATA.BASE_URL}/price?symbol=AAPL&apikey=${API_CONFIG.TWELVE_DATA.API_KEY}`);
        results.twelveData = {
          success: true,
          status: twelveDataResponse.status,
          data: twelveDataResponse.data
        };
      } catch (error) {
        results.twelveData = {
          success: false,
          error: error.message,
          status: error.response?.status,
          data: error.response?.data
        };
      }

      // Return results
      res.json({
        message: 'API tests completed',
        timestamp: new Date().toISOString(),
        results
      });
    } catch (error) {
      logger.error('Error testing APIs:', error.message);
      res.status(500).json({
        error: 'Error testing APIs',
        message: error.message,
        timestamp: new Date().toISOString()
      });
    }
  });
};
