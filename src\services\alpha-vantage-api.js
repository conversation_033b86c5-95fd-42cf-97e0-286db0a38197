/**
 * Alpha Vantage API Service for Trading Signals App
 * 
 * This file provides functionality for interacting with the Alpha Vantage API.
 */

// API configuration
const API_CONFIG = {
  BASE_URL: 'https://www.alphavantage.co/query',
  API_KEY: 'OFU3DJH5JWW6Z29Z', // Default key, should be replaced with environment variable
  ENDPOINTS: {
    INTRADAY: 'TIME_SERIES_INTRADAY',
    DAILY: 'TIME_SERIES_DAILY',
    FX_INTRADAY: 'FX_INTRADAY',
    CRYPTO_INTRADAY: 'CRYPTO_INTRADAY',
    GLOBAL_QUOTE: 'GLOBAL_QUOTE',
    TECHNICAL_INDICATOR: {
      SMA: 'SMA',
      EMA: 'EMA',
      RSI: 'RSI',
      MACD: 'MACD',
      BBANDS: 'BBANDS'
    }
  }
};

// Cache for API responses
const apiCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

// Fetch data from Alpha Vantage API
async function fetchFromAPI(endpoint, params) {
  try {
    // Create cache key from endpoint and params
    const cacheKey = JSON.stringify({ endpoint, params });
    
    // Check if data is in cache and not expired
    const cachedData = apiCache.get(cacheKey);
    if (cachedData && Date.now() - cachedData.timestamp < CACHE_TTL) {
      console.log('Using cached data for', endpoint);
      return cachedData.data;
    }
    
    // Construct URL
    const url = new URL(API_CONFIG.BASE_URL);
    url.searchParams.append('function', endpoint);
    url.searchParams.append('apikey', API_CONFIG.API_KEY);
    
    // Add all params to URL
    Object.keys(params).forEach(key => {
      url.searchParams.append(key, params[key]);
    });
    
    console.log('Fetching data from Alpha Vantage API:', url.toString().replace(/apikey=[^&]+/, 'apikey=HIDDEN'));
    
    // Fetch data
    const response = await fetch(url.toString());
    
    // Check if response is OK
    if (!response.ok) {
      throw new Error(`Alpha Vantage API error: ${response.status} ${response.statusText}`);
    }
    
    // Parse response
    const data = await response.json();
    
    // Check for API errors
    if (data['Error Message']) {
      throw new Error(`Alpha Vantage API error: ${data['Error Message']}`);
    }
    
    if (data['Information']) {
      console.warn('Alpha Vantage API information:', data['Information']);
    }
    
    if (data['Note'] && data['Note'].includes('API call frequency')) {
      console.warn('Alpha Vantage API rate limit reached:', data['Note']);
      throw new Error('API rate limit reached. Please try again later.');
    }
    
    // Cache data
    apiCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
    
    return data;
  } catch (error) {
    console.error('Error fetching from Alpha Vantage API:', error);
    throw error;
  }
}

// Get intraday data
async function getIntraday(symbol, interval = '5min', outputsize = 'compact') {
  try {
    return await fetchFromAPI(API_CONFIG.ENDPOINTS.INTRADAY, {
      symbol,
      interval,
      outputsize
    });
  } catch (error) {
    console.error('Error getting intraday data:', error);
    return null;
  }
}

// Get daily data
async function getDaily(symbol, outputsize = 'compact') {
  try {
    return await fetchFromAPI(API_CONFIG.ENDPOINTS.DAILY, {
      symbol,
      outputsize
    });
  } catch (error) {
    console.error('Error getting daily data:', error);
    return null;
  }
}

// Get forex intraday data
async function getForexIntraday(fromSymbol, toSymbol, interval = '5min', outputsize = 'compact') {
  try {
    return await fetchFromAPI(API_CONFIG.ENDPOINTS.FX_INTRADAY, {
      from_symbol: fromSymbol,
      to_symbol: toSymbol,
      interval,
      outputsize
    });
  } catch (error) {
    console.error('Error getting forex intraday data:', error);
    return null;
  }
}

// Get crypto intraday data
async function getCryptoIntraday(symbol, market, interval = '5min', outputsize = 'compact') {
  try {
    return await fetchFromAPI(API_CONFIG.ENDPOINTS.CRYPTO_INTRADAY, {
      symbol,
      market,
      interval,
      outputsize
    });
  } catch (error) {
    console.error('Error getting crypto intraday data:', error);
    return null;
  }
}

// Get global quote
async function getGlobalQuote(symbol) {
  try {
    return await fetchFromAPI(API_CONFIG.ENDPOINTS.GLOBAL_QUOTE, {
      symbol
    });
  } catch (error) {
    console.error('Error getting global quote:', error);
    return null;
  }
}

// Get technical indicator
async function getTechnicalIndicator(indicator, symbol, interval = 'daily', timePeriod = 14, seriesType = 'close') {
  try {
    return await fetchFromAPI(indicator, {
      symbol,
      interval,
      time_period: timePeriod,
      series_type: seriesType
    });
  } catch (error) {
    console.error(`Error getting ${indicator} data:`, error);
    return null;
  }
}

// Export functions for external use
window.AlphaVantageAPI = {
  getIntraday,
  getDaily,
  getForexIntraday,
  getCryptoIntraday,
  getGlobalQuote,
  getTechnicalIndicator,
  ENDPOINTS: API_CONFIG.ENDPOINTS
};

console.log('Alpha Vantage API service loaded');
