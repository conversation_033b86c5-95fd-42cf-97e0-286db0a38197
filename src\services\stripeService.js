/**
 * Stripe Service
 * 
 * This service handles all interactions with the Stripe API, including
 * payment processing, subscription management, and webhook handling.
 */

import <PERSON><PERSON> from 'stripe';
import config from '../config/config.js';
import logger from '../utils/logger.js';

// Initialize <PERSON><PERSON> with the secret key from config
const stripe = new Stripe(config.payment.stripe.secretKey);

/**
 * Create a payment intent
 * @param {number} amount - Amount in cents
 * @param {string} currency - Currency code (e.g., 'usd')
 * @param {Object} metadata - Additional metadata for the payment
 * @returns {Promise<Object>} - Stripe payment intent object
 */
export const createPaymentIntent = async (amount, currency = 'usd', metadata = {}) => {
  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      metadata,
      automatic_payment_methods: {
        enabled: true,
      },
    });
    
    logger.info(`Payment intent created: ${paymentIntent.id}`);
    return paymentIntent;
  } catch (error) {
    logger.error('Error creating payment intent:', error);
    throw error;
  }
};

/**
 * Create a subscription
 * @param {string} customerId - Stripe customer ID
 * @param {string} priceId - Stripe price ID
 * @param {Object} metadata - Additional metadata for the subscription
 * @returns {Promise<Object>} - Stripe subscription object
 */
export const createSubscription = async (customerId, priceId, metadata = {}) => {
  try {
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      metadata,
    });
    
    logger.info(`Subscription created: ${subscription.id}`);
    return subscription;
  } catch (error) {
    logger.error('Error creating subscription:', error);
    throw error;
  }
};

/**
 * Create a customer
 * @param {string} email - Customer email
 * @param {string} name - Customer name
 * @param {Object} metadata - Additional metadata for the customer
 * @returns {Promise<Object>} - Stripe customer object
 */
export const createCustomer = async (email, name, metadata = {}) => {
  try {
    const customer = await stripe.customers.create({
      email,
      name,
      metadata,
    });
    
    logger.info(`Customer created: ${customer.id}`);
    return customer;
  } catch (error) {
    logger.error('Error creating customer:', error);
    throw error;
  }
};

/**
 * Verify a webhook signature
 * @param {string} payload - Request body as a string
 * @param {string} signature - Stripe signature from request headers
 * @returns {Object} - Event object if signature is valid
 * @throws {Error} - If signature is invalid
 */
export const verifyWebhookSignature = (payload, signature) => {
  try {
    const event = stripe.webhooks.constructEvent(
      payload,
      signature,
      config.payment.stripe.webhookSecret
    );
    
    logger.info(`Webhook verified: ${event.id}`);
    return event;
  } catch (error) {
    logger.error('Webhook signature verification failed:', error);
    throw error;
  }
};

/**
 * Handle webhook events
 * @param {Object} event - Stripe event object
 * @returns {Promise<Object>} - Response object
 */
export const handleWebhookEvent = async (event) => {
  const { type, data } = event;
  
  logger.info(`Processing webhook event: ${type}`);
  
  try {
    switch (type) {
      case 'payment_intent.succeeded':
        return await handlePaymentIntentSucceeded(data.object);
        
      case 'payment_intent.payment_failed':
        return await handlePaymentIntentFailed(data.object);
        
      case 'customer.subscription.created':
        return await handleSubscriptionCreated(data.object);
        
      case 'customer.subscription.updated':
        return await handleSubscriptionUpdated(data.object);
        
      case 'customer.subscription.deleted':
        return await handleSubscriptionDeleted(data.object);
        
      case 'invoice.payment_succeeded':
        return await handleInvoicePaymentSucceeded(data.object);
        
      case 'invoice.payment_failed':
        return await handleInvoicePaymentFailed(data.object);
        
      default:
        logger.info(`Unhandled webhook event type: ${type}`);
        return { status: 'ignored', message: `Unhandled event type: ${type}` };
    }
  } catch (error) {
    logger.error(`Error handling webhook event ${type}:`, error);
    throw error;
  }
};

// Event handlers for specific webhook events

async function handlePaymentIntentSucceeded(paymentIntent) {
  // Update order status, send confirmation email, etc.
  logger.info(`Payment succeeded: ${paymentIntent.id}`);
  return { status: 'success', message: 'Payment processed successfully' };
}

async function handlePaymentIntentFailed(paymentIntent) {
  // Update order status, notify customer, etc.
  logger.info(`Payment failed: ${paymentIntent.id}`);
  return { status: 'failed', message: 'Payment failed' };
}

async function handleSubscriptionCreated(subscription) {
  // Provision access, update user status, etc.
  logger.info(`Subscription created: ${subscription.id}`);
  return { status: 'success', message: 'Subscription created' };
}

async function handleSubscriptionUpdated(subscription) {
  // Update user access, etc.
  logger.info(`Subscription updated: ${subscription.id}`);
  return { status: 'success', message: 'Subscription updated' };
}

async function handleSubscriptionDeleted(subscription) {
  // Remove user access, update status, etc.
  logger.info(`Subscription deleted: ${subscription.id}`);
  return { status: 'success', message: 'Subscription deleted' };
}

async function handleInvoicePaymentSucceeded(invoice) {
  // Update subscription status, etc.
  logger.info(`Invoice payment succeeded: ${invoice.id}`);
  return { status: 'success', message: 'Invoice payment succeeded' };
}

async function handleInvoicePaymentFailed(invoice) {
  // Notify customer, update subscription status, etc.
  logger.info(`Invoice payment failed: ${invoice.id}`);
  return { status: 'failed', message: 'Invoice payment failed' };
}

export default {
  createPaymentIntent,
  createSubscription,
  createCustomer,
  verifyWebhookSignature,
  handleWebhookEvent,
  stripe
};
