/* Trading Pro - Professional Trading Signal Application
 * Main stylesheet with color palette and core styles
 */

:root {
  /* Primary Colors */
  --navy-blue: #1a237e;
  --teal: #00796b;
  
  /* Accent Colors */
  --gold: #ffd700;
  --success: #4caf50;
  --warning: #ff9800;
  --danger: #f44336;
  
  /* Neutral Grays */
  --gray-50: #f5f5f5;
  --gray-100: #f0f0f0;
  --gray-200: #e0e0e0;
  --gray-300: #d0d0d0;
  --gray-400: #b0b0b0;
  --gray-500: #909090;
  --gray-600: #707070;
  --gray-700: #505050;
  --gray-800: #303030;
  --gray-900: #212121;
  
  /* Light Theme (Default) */
  --body-bg: var(--gray-50);
  --card-bg: #ffffff;
  --text-color: var(--gray-900);
  --text-muted: var(--gray-600);
  --border-color: var(--gray-200);
  --shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  --hover-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  
  /* Chart Colors */
  --chart-grid: var(--gray-200);
  --chart-text: var(--gray-700);
  --chart-line: var(--navy-blue);
  --chart-up: #26a69a;
  --chart-down: #ef5350;
  
  /* Signal Colors */
  --buy-color: #26a69a;
  --sell-color: #ef5350;
  --neutral-color: var(--gray-500);
  
  /* Confidence Indicators */
  --confidence-high: #4caf50;
  --confidence-medium: #ff9800;
  --confidence-low: #f44336;
}

/* Dark Theme */
[data-theme="dark"] {
  --body-bg: var(--gray-900);
  --card-bg: var(--gray-800);
  --text-color: var(--gray-100);
  --text-muted: var(--gray-400);
  --border-color: var(--gray-700);
  --shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  --hover-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  
  /* Chart Colors in Dark Mode */
  --chart-grid: var(--gray-700);
  --chart-text: var(--gray-300);
  --chart-line: #5c6bc0;
  --chart-up: #4db6ac;
  --chart-down: #e57373;
}

/* Base Styles */
body {
  font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
  background-color: var(--body-bg);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
  margin: 0;
  padding: 0;
  line-height: 1.5;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 1rem;
}

.text-muted {
  color: var(--text-muted) !important;
}

/* Card Styles */
.card {
  background-color: var(--card-bg);
  border-radius: 12px;
  box-shadow: var(--shadow);
  border: none;
  margin-bottom: 1.5rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--hover-shadow);
}

.card-header {
  background-color: var(--navy-blue);
  color: white;
  border-radius: 12px 12px 0 0 !important;
  padding: 1rem 1.25rem;
  font-weight: 600;
  border-bottom: none;
}

.card-header.teal {
  background-color: var(--teal);
}

.card-body {
  padding: 1.25rem;
}

/* Button Styles */
.btn {
  border-radius: 6px;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background-color: var(--navy-blue);
  border-color: var(--navy-blue);
}

.btn-primary:hover, .btn-primary:focus {
  background-color: #2a3eb1;
  border-color: #2a3eb1;
}

.btn-teal {
  background-color: var(--teal);
  border-color: var(--teal);
  color: white;
}

.btn-teal:hover, .btn-teal:focus {
  background-color: #00897b;
  border-color: #00897b;
  color: white;
}

.btn-outline-primary {
  color: var(--navy-blue);
  border-color: var(--navy-blue);
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
  background-color: var(--navy-blue);
  border-color: var(--navy-blue);
}

/* Signal Indicators */
.signal-buy {
  color: var(--buy-color);
  font-weight: 600;
}

.signal-sell {
  color: var(--sell-color);
  font-weight: 600;
}

.signal-neutral {
  color: var(--neutral-color);
  font-weight: 600;
}

/* Confidence Indicators */
.confidence-indicator {
  display: inline-block;
  width: 100%;
  height: 6px;
  background-color: var(--gray-200);
  border-radius: 3px;
  overflow: hidden;
}

.confidence-level {
  height: 100%;
  border-radius: 3px;
}

.confidence-high {
  background-color: var(--confidence-high);
}

.confidence-medium {
  background-color: var(--confidence-medium);
}

.confidence-low {
  background-color: var(--confidence-low);
}

/* Floating Animation for Success Indicators */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

.success-indicator {
  color: var(--gold);
  animation: float 3s ease-in-out infinite;
}

/* Responsive Utilities */
@media (max-width: 768px) {
  .card {
    margin-bottom: 1rem;
  }
  
  .card:hover {
    transform: none;
  }
}

/* Dark Mode Toggle */
.theme-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.theme-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--navy-blue);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Micro-animations */
.btn, .card, .nav-link {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.btn:hover, .nav-link:hover {
  transform: translateY(-2px);
}

/* Loading Indicator */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--navy-blue);
  animation: spin 1s ease-in-out infinite;
  margin: 20px auto;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Chart Container */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* Data Visualization */
.data-visualization {
  border-radius: 8px;
  overflow: hidden;
}

/* Win Rate Progress */
.win-rate-progress {
  height: 8px;
  border-radius: 4px;
  background-color: var(--gray-200);
  margin-bottom: 0.5rem;
}

.win-rate-bar {
  height: 100%;
  background-color: var(--gold);
  border-radius: 4px;
}

/* Alert Styles */
.alert {
  border-radius: 8px;
  border-left: 4px solid transparent;
  padding: 1rem;
  margin-bottom: 1rem;
}

.alert-success {
  background-color: rgba(76, 175, 80, 0.1);
  border-left-color: var(--success);
  color: var(--success);
}

.alert-warning {
  background-color: rgba(255, 152, 0, 0.1);
  border-left-color: var(--warning);
  color: var(--warning);
}

.alert-danger {
  background-color: rgba(244, 67, 54, 0.1);
  border-left-color: var(--danger);
  color: var(--danger);
}

/* Table Styles */
.table {
  color: var(--text-color);
  margin-bottom: 0;
}

.table th {
  font-weight: 600;
  border-top: none;
  border-bottom: 2px solid var(--border-color);
}

.table td {
  vertical-align: middle;
  border-color: var(--border-color);
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

[data-theme="dark"] .table-hover tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.03);
}

/* Badge Styles */
.badge {
  font-weight: 500;
  padding: 0.35em 0.65em;
  border-radius: 4px;
}

.badge-success {
  background-color: var(--success);
  color: white;
}

.badge-warning {
  background-color: var(--warning);
  color: white;
}

.badge-danger {
  background-color: var(--danger);
  color: white;
}

/* Navbar */
.navbar {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-weight: 700;
  display: flex;
  align-items: center;
}

.navbar-brand img {
  margin-right: 0.5rem;
}

/* Logo Animation */
.logo-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
