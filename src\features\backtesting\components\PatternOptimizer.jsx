import React, { useState, useEffect } from 'react';
import { getPatternInfo } from '../../../utils/candlestickPatterns';

/**
 * PatternOptimizer Component
 *
 * Component for optimizing pattern parameters for better performance
 */
const PatternOptimizer = ({ results, onApplyOptimization }) => {
  // State for optimization settings
  const [optimizationTarget, setOptimizationTarget] = useState('netProfit');
  const [selectedPattern, setSelectedPattern] = useState('');
  const [optimizationProgress, setOptimizationProgress] = useState(0);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationResults, setOptimizationResults] = useState(null);
  const [optimizationParams, setOptimizationParams] = useState({});
  const [optimizationAlgorithm, setOptimizationAlgorithm] = useState('grid_search'); // 'grid_search', 'genetic', 'bayesian'
  const [optimizationIterations, setOptimizationIterations] = useState(25); // Number of iterations for genetic/bayesian

  // Available optimization targets
  const optimizationTargets = [
    { value: 'netProfit', label: 'Net Profit' },
    { value: 'winRate', label: 'Win Rate' },
    { value: 'profitFactor', label: 'Profit Factor' },
    { value: 'sharpeRatio', label: 'Sharpe Ratio' },
    { value: 'maxDrawdown', label: 'Max Drawdown (minimize)' }
  ];

  // Get available patterns from results
  const availablePatterns = results && results.patternPerformance ?
    Object.keys(results.patternPerformance).map(patternId => {
      const patternInfo = getPatternInfo(patternId);
      return {
        value: patternId,
        label: patternInfo ? patternInfo.name : patternId,
        type: patternInfo ? patternInfo.type : 'unknown'
      };
    }) : [];

  // Initialize optimization parameters when pattern changes
  useEffect(() => {
    if (!selectedPattern) return;

    // Get default parameters based on pattern type
    const patternInfo = getPatternInfo(selectedPattern);

    if (!patternInfo) {
      setOptimizationParams({});
      return;
    }

    // Set default parameters based on pattern type
    const defaultParams = {};

    if (selectedPattern.includes('doji')) {
      defaultParams.bodyToRangeRatio = 0.1;
      defaultParams.maxBodyToRangeRatio = 0.3;
    } else if (selectedPattern.includes('hammer') || selectedPattern.includes('hanging_man')) {
      defaultParams.bodyToRangeRatio = 0.3;
      defaultParams.shadowToBodyRatio = 2.0;
      defaultParams.minShadowToBodyRatio = 1.5;
    } else if (selectedPattern.includes('engulfing')) {
      defaultParams.minBodySize = 0.5;
      defaultParams.minEngulfingRatio = 1.1;
    } else if (selectedPattern.includes('star')) {
      defaultParams.bodyToRangeRatio = 0.3;
      defaultParams.gapThreshold = 0.1;
    } else if (selectedPattern.includes('head_and_shoulders')) {
      defaultParams.shoulderHeightDiffMax = 0.05;
      defaultParams.headShoulderRatioMin = 1.1;
    } else if (selectedPattern.includes('double_top') || selectedPattern.includes('double_bottom')) {
      defaultParams.peakDiffMax = 0.03;
      defaultParams.troughDepthMin = 0.03;
    } else if (selectedPattern.includes('triangle')) {
      defaultParams.slopeDiffMax = 0.0005;
      defaultParams.minCandles = 10;
    } else if (selectedPattern.includes('channel')) {
      defaultParams.slopeDiffMax = 0.0005;
      defaultParams.channelWidthMin = 0.03;
    } else {
      // Generic parameters for other patterns
      defaultParams.significance = 5;
      defaultParams.confirmationPeriod = 1;
    }

    setOptimizationParams(defaultParams);
  }, [selectedPattern]);

  // Run optimization
  const runOptimization = async () => {
    if (!selectedPattern || !optimizationTarget) return;

    setIsOptimizing(true);
    setOptimizationProgress(0);
    setOptimizationResults(null);

    try {
      // Run the selected optimization algorithm
      switch (optimizationAlgorithm) {
        case 'grid_search':
          await runGridSearch();
          break;
        case 'genetic':
          await runGeneticAlgorithm();
          break;
        case 'bayesian':
          await runBayesianOptimization();
          break;
        default:
          await runGridSearch();
      }
    } catch (error) {
      console.error('Error running optimization:', error);
    } finally {
      setIsOptimizing(false);
    }
  };

  // Run grid search optimization
  const runGridSearch = async () => {
    // Generate parameter combinations for optimization
    const paramCombinations = generateParameterCombinations(optimizationParams);

    // Track best result
    let bestResult = null;
    let bestParams = null;
    let bestScore = optimizationTarget === 'maxDrawdown' ? Infinity : -Infinity;

    // Run optimization for each parameter combination
    for (let i = 0; i < paramCombinations.length; i++) {
      const params = paramCombinations[i];

      // Simulate backtest with these parameters
      const result = await simulateBacktest(params);

      // Update progress
      setOptimizationProgress(Math.round((i + 1) / paramCombinations.length * 100));

      // Check if this is the best result
      const score = getScore(result, optimizationTarget);
      const isBetter = optimizationTarget === 'maxDrawdown' ?
        score < bestScore :
        score > bestScore;

      if (isBetter) {
        bestScore = score;
        bestResult = result;
        bestParams = params;
      }
    }

    // Set optimization results
    setOptimizationResults({
      pattern: selectedPattern,
      target: optimizationTarget,
      bestParams,
      bestResult,
      bestScore,
      algorithm: 'grid_search'
    });
  };

  // Run genetic algorithm optimization
  const runGeneticAlgorithm = async () => {
    // Population size
    const populationSize = 20;
    // Number of generations
    const generations = optimizationIterations;
    // Mutation rate
    const mutationRate = 0.1;
    // Crossover rate
    const crossoverRate = 0.7;

    // Generate initial population
    let population = [];
    for (let i = 0; i < populationSize; i++) {
      population.push(generateRandomParams(optimizationParams));
    }

    // Track best result
    let bestResult = null;
    let bestParams = null;
    let bestScore = optimizationTarget === 'maxDrawdown' ? Infinity : -Infinity;

    // Run genetic algorithm for specified number of generations
    for (let generation = 0; generation < generations; generation++) {
      // Evaluate fitness for each individual
      const fitnessResults = [];
      for (let i = 0; i < population.length; i++) {
        const params = population[i];
        const result = await simulateBacktest(params);
        const score = getScore(result, optimizationTarget);

        fitnessResults.push({ params, result, score });

        // Check if this is the best result
        const isBetter = optimizationTarget === 'maxDrawdown' ?
          score < bestScore :
          score > bestScore;

        if (isBetter) {
          bestScore = score;
          bestResult = result;
          bestParams = params;
        }
      }

      // Update progress
      setOptimizationProgress(Math.round((generation + 1) / generations * 100));

      // Sort by fitness
      fitnessResults.sort((a, b) => {
        if (optimizationTarget === 'maxDrawdown') {
          return a.score - b.score; // Lower is better for drawdown
        } else {
          return b.score - a.score; // Higher is better for other metrics
        }
      });

      // Create new population
      const newPopulation = [];

      // Elitism: Keep the best individuals
      const eliteCount = Math.max(1, Math.floor(populationSize * 0.1));
      for (let i = 0; i < eliteCount; i++) {
        newPopulation.push(fitnessResults[i].params);
      }

      // Fill the rest of the population with crossover and mutation
      while (newPopulation.length < populationSize) {
        // Select parents using tournament selection
        const parent1 = tournamentSelection(fitnessResults);
        const parent2 = tournamentSelection(fitnessResults);

        // Crossover
        let child;
        if (Math.random() < crossoverRate) {
          child = crossover(parent1, parent2);
        } else {
          child = Math.random() < 0.5 ? { ...parent1 } : { ...parent2 };
        }

        // Mutation
        if (Math.random() < mutationRate) {
          child = mutate(child, optimizationParams);
        }

        newPopulation.push(child);
      }

      // Replace old population
      population = newPopulation;
    }

    // Set optimization results
    setOptimizationResults({
      pattern: selectedPattern,
      target: optimizationTarget,
      bestParams,
      bestResult,
      bestScore,
      algorithm: 'genetic'
    });
  };

  // Tournament selection for genetic algorithm
  const tournamentSelection = (fitnessResults) => {
    const tournamentSize = 3;
    let best = null;

    for (let i = 0; i < tournamentSize; i++) {
      const randomIndex = Math.floor(Math.random() * fitnessResults.length);
      const candidate = fitnessResults[randomIndex];

      if (best === null) {
        best = candidate;
      } else {
        const isBetter = optimizationTarget === 'maxDrawdown' ?
          candidate.score < best.score :
          candidate.score > best.score;

        if (isBetter) {
          best = candidate;
        }
      }
    }

    return best.params;
  };

  // Crossover for genetic algorithm
  const crossover = (parent1, parent2) => {
    const child = {};

    // For each parameter, randomly choose from either parent
    Object.keys(parent1).forEach(key => {
      child[key] = Math.random() < 0.5 ? parent1[key] : parent2[key];
    });

    return child;
  };

  // Mutation for genetic algorithm
  const mutate = (individual, paramRanges) => {
    const mutated = { ...individual };

    // Randomly select a parameter to mutate
    const keys = Object.keys(mutated);
    const randomKey = keys[Math.floor(Math.random() * keys.length)];

    // Mutate the selected parameter
    const originalValue = mutated[randomKey];
    const min = originalValue * 0.5;
    const max = originalValue * 1.5;
    mutated[randomKey] = min + Math.random() * (max - min);

    return mutated;
  };

  // Run Bayesian optimization
  const runBayesianOptimization = async () => {
    // Number of iterations
    const iterations = optimizationIterations;

    // Initial random samples
    const initialSamples = 5;

    // Track all evaluated points
    const evaluatedPoints = [];

    // Track best result
    let bestResult = null;
    let bestParams = null;
    let bestScore = optimizationTarget === 'maxDrawdown' ? Infinity : -Infinity;

    // Generate initial random samples
    for (let i = 0; i < initialSamples; i++) {
      const params = generateRandomParams(optimizationParams);
      const result = await simulateBacktest(params);
      const score = getScore(result, optimizationTarget);

      evaluatedPoints.push({ params, score });

      // Check if this is the best result
      const isBetter = optimizationTarget === 'maxDrawdown' ?
        score < bestScore :
        score > bestScore;

      if (isBetter) {
        bestScore = score;
        bestResult = result;
        bestParams = params;
      }

      // Update progress
      setOptimizationProgress(Math.round((i + 1) / iterations * 100));
    }

    // Run Bayesian optimization iterations
    for (let i = initialSamples; i < iterations; i++) {
      // Find next point to evaluate using acquisition function
      const nextParams = findNextPoint(evaluatedPoints, optimizationParams);

      // Evaluate the point
      const result = await simulateBacktest(nextParams);
      const score = getScore(result, optimizationTarget);

      evaluatedPoints.push({ params: nextParams, score });

      // Check if this is the best result
      const isBetter = optimizationTarget === 'maxDrawdown' ?
        score < bestScore :
        score > bestScore;

      if (isBetter) {
        bestScore = score;
        bestResult = result;
        bestParams = nextParams;
      }

      // Update progress
      setOptimizationProgress(Math.round((i + 1) / iterations * 100));
    }

    // Set optimization results
    setOptimizationResults({
      pattern: selectedPattern,
      target: optimizationTarget,
      bestParams,
      bestResult,
      bestScore,
      algorithm: 'bayesian'
    });
  };

  // Find next point to evaluate using acquisition function (Expected Improvement)
  const findNextPoint = (evaluatedPoints, paramRanges) => {
    // In a real implementation, this would use Gaussian Process regression
    // For demo purposes, we'll use a simplified approach

    // Generate random candidate points
    const candidates = [];
    for (let i = 0; i < 10; i++) {
      candidates.push(generateRandomParams(paramRanges));
    }

    // Find the point with the highest expected improvement
    let bestCandidate = null;
    let bestEI = -Infinity;

    for (const candidate of candidates) {
      // Calculate expected improvement
      const ei = calculateExpectedImprovement(candidate, evaluatedPoints);

      if (ei > bestEI) {
        bestEI = ei;
        bestCandidate = candidate;
      }
    }

    return bestCandidate;
  };

  // Calculate expected improvement for a candidate point
  const calculateExpectedImprovement = (candidate, evaluatedPoints) => {
    // In a real implementation, this would use Gaussian Process regression
    // For demo purposes, we'll use a simplified approach based on distance

    // Calculate distance to each evaluated point
    const distances = evaluatedPoints.map(point => {
      return calculateDistance(candidate, point.params);
    });

    // Find the closest point
    const minDistance = Math.min(...distances);

    // Calculate expected improvement based on distance
    // Further points have higher expected improvement
    return minDistance;
  };

  // Calculate Euclidean distance between two parameter sets
  const calculateDistance = (params1, params2) => {
    let sumSquaredDiff = 0;

    Object.keys(params1).forEach(key => {
      if (params2[key] !== undefined) {
        const diff = params1[key] - params2[key];
        sumSquaredDiff += diff * diff;
      }
    });

    return Math.sqrt(sumSquaredDiff);
  };

  // Generate random parameters within the specified ranges
  const generateRandomParams = (paramRanges) => {
    const params = {};

    Object.entries(paramRanges).forEach(([key, value]) => {
      const min = value * 0.5;
      const max = value * 1.5;
      params[key] = min + Math.random() * (max - min);
    });

    return params;
  };

  // Generate parameter combinations for optimization
  const generateParameterCombinations = (params) => {
    // For each parameter, generate a range of values to test
    const paramRanges = {};

    Object.entries(params).forEach(([key, value]) => {
      // Generate 5 values around the current value
      const min = value * 0.5;
      const max = value * 1.5;
      const step = (max - min) / 4;

      paramRanges[key] = [
        min,
        min + step,
        value,
        max - step,
        max
      ];
    });

    // Generate all combinations
    const combinations = [];

    const generateCombinations = (index, current) => {
      const keys = Object.keys(paramRanges);

      if (index === keys.length) {
        combinations.push({...current});
        return;
      }

      const key = keys[index];
      const values = paramRanges[key];

      for (const value of values) {
        current[key] = value;
        generateCombinations(index + 1, current);
      }
    };

    generateCombinations(0, {});

    return combinations;
  };

  // Simulate backtest with given parameters
  const simulateBacktest = async (params) => {
    // In a real implementation, this would call the backend to run a backtest
    // For demo purposes, we'll simulate it with random variations

    // Get the original performance for this pattern
    const originalPerformance = results.patternPerformance[selectedPattern];

    if (!originalPerformance) {
      throw new Error(`No performance data found for pattern: ${selectedPattern}`);
    }

    // Calculate parameter quality (how close they are to "optimal" values)
    // This is just a simulation - in reality, you'd run actual backtests
    const paramQuality = Object.entries(params).reduce((quality, [key, value]) => {
      const originalValue = optimizationParams[key];
      const ratio = value / originalValue;

      // Simulate an optimal value that's slightly different from the original
      const optimalRatio = 1.1; // Assume optimal is 10% higher than original

      // Calculate how close this parameter is to the "optimal" value
      const paramQuality = 1 - Math.abs(ratio - optimalRatio) / optimalRatio;

      return quality * paramQuality;
    }, 1);

    // Apply random variation based on parameter quality
    const variation = (Math.random() * 0.4 - 0.2) + (paramQuality - 0.5) * 0.6;

    // Create simulated result
    return {
      totalTrades: originalPerformance.totalTrades,
      winningTrades: Math.round(originalPerformance.winningTrades * (1 + variation * 0.3)),
      losingTrades: Math.round(originalPerformance.totalTrades - originalPerformance.winningTrades * (1 + variation * 0.3)),
      winRate: originalPerformance.winRate * (1 + variation * 0.3),
      grossProfit: originalPerformance.grossProfit * (1 + variation * 0.5),
      grossLoss: originalPerformance.grossLoss * (1 - variation * 0.2),
      netProfit: originalPerformance.netProfit * (1 + variation * 0.7),
      averageProfit: originalPerformance.averageProfit * (1 + variation * 0.7),
      profitFactor: originalPerformance.profitFactor * (1 + variation * 0.6),
      maxDrawdown: originalPerformance.maxDrawdown ? originalPerformance.maxDrawdown * (1 - variation * 0.4) : 15 * (1 - variation * 0.4),
      sharpeRatio: originalPerformance.sharpeRatio ? originalPerformance.sharpeRatio * (1 + variation * 0.5) : 1.5 * (1 + variation * 0.5)
    };
  };

  // Get score for a result based on optimization target
  const getScore = (result, target) => {
    switch (target) {
      case 'netProfit':
        return result.netProfit;
      case 'winRate':
        return result.winRate;
      case 'profitFactor':
        return result.profitFactor;
      case 'sharpeRatio':
        return result.sharpeRatio;
      case 'maxDrawdown':
        return result.maxDrawdown;
      default:
        return result.netProfit;
    }
  };

  // Apply optimization
  const applyOptimization = () => {
    if (!optimizationResults) return;

    onApplyOptimization(optimizationResults);
  };

  // Format parameter name for display
  const formatParamName = (name) => {
    return name
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/([a-z])([A-Z])/g, '$1 $2') // Add space between camelCase
      .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
      .replace(/([A-Z])([A-Z][a-z])/g, '$1 $2'); // Add space between acronyms
  };

  // Get algorithm name for display
  const getAlgorithmName = (algorithm) => {
    switch (algorithm) {
      case 'grid_search':
        return 'Grid Search';
      case 'genetic':
        return 'Genetic Algorithm';
      case 'bayesian':
        return 'Bayesian Optimization';
      default:
        return algorithm;
    }
  };

  return (
    <div className="pattern-optimizer">
      <h3 className="text-lg font-medium mb-4">Pattern Optimizer</h3>

      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow mb-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Pattern to Optimize</label>
            <select
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              value={selectedPattern}
              onChange={(e) => setSelectedPattern(e.target.value)}
              disabled={isOptimizing}
            >
              <option value="">Select a pattern</option>
              {availablePatterns.map(pattern => (
                <option key={pattern.value} value={pattern.value}>{pattern.label}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Optimization Target</label>
            <select
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              value={optimizationTarget}
              onChange={(e) => setOptimizationTarget(e.target.value)}
              disabled={isOptimizing}
            >
              {optimizationTargets.map(target => (
                <option key={target.value} value={target.value}>{target.label}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Optimization Algorithm</label>
            <select
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              value={optimizationAlgorithm}
              onChange={(e) => setOptimizationAlgorithm(e.target.value)}
              disabled={isOptimizing}
            >
              <option value="grid_search">Grid Search</option>
              <option value="genetic">Genetic Algorithm</option>
              <option value="bayesian">Bayesian Optimization</option>
            </select>
          </div>

          {optimizationAlgorithm !== 'grid_search' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Iterations ({optimizationIterations})
              </label>
              <input
                type="range"
                value={optimizationIterations}
                onChange={(e) => setOptimizationIterations(parseInt(e.target.value))}
                min="10"
                max="100"
                step="5"
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                disabled={isOptimizing}
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                More iterations = better results but slower optimization
              </p>
            </div>
          )}
        </div>

        {selectedPattern && Object.keys(optimizationParams).length > 0 && (
          <div className="mb-4">
            <h4 className="text-md font-medium mb-2">Parameters to Optimize</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(optimizationParams).map(([key, value]) => (
                <div key={key}>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {formatParamName(key)}
                  </label>
                  <input
                    type="number"
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    value={value}
                    onChange={(e) => setOptimizationParams({
                      ...optimizationParams,
                      [key]: parseFloat(e.target.value)
                    })}
                    step="0.01"
                    disabled={isOptimizing}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex justify-end">
          <button
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={runOptimization}
            disabled={!selectedPattern || isOptimizing}
          >
            {isOptimizing ? 'Optimizing...' : 'Run Optimization'}
          </button>
        </div>

        {isOptimizing && (
          <div className="mt-4">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${optimizationProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 text-center">
              {optimizationProgress}% Complete
            </p>
          </div>
        )}
      </div>

      {optimizationResults && (
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h4 className="text-md font-medium mb-2">Optimization Results</h4>

          <div className="mb-4">
            <p className="text-sm text-gray-700 dark:text-gray-300">
              Optimized <span className="font-medium">{getPatternInfo(optimizationResults.pattern)?.name || optimizationResults.pattern}</span> for {
                optimizationTargets.find(t => t.value === optimizationResults.target)?.label || optimizationResults.target
              } using {getAlgorithmName(optimizationResults.algorithm)}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <h5 className="text-sm font-medium mb-2">Optimized Parameters</h5>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                {Object.entries(optimizationResults.bestParams).map(([key, value]) => (
                  <div key={key} className="flex justify-between mb-1">
                    <span className="text-sm text-gray-500 dark:text-gray-400">{formatParamName(key)}:</span>
                    <span className="text-sm font-medium">{value.toFixed(4)}</span>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h5 className="text-sm font-medium mb-2">Performance Improvement</h5>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-500 dark:text-gray-400">Original {optimizationResults.target}:</span>
                  <span className="text-sm font-medium">
                    {getScore(results.patternPerformance[optimizationResults.pattern], optimizationResults.target).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-500 dark:text-gray-400">Optimized {optimizationResults.target}:</span>
                  <span className="text-sm font-medium">{optimizationResults.bestScore.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">Improvement:</span>
                  <span className="text-sm font-medium text-green-600">
                    {optimizationResults.target === 'maxDrawdown' ?
                      ((1 - optimizationResults.bestScore / getScore(results.patternPerformance[optimizationResults.pattern], optimizationResults.target)) * 100).toFixed(2) :
                      ((optimizationResults.bestScore / getScore(results.patternPerformance[optimizationResults.pattern], optimizationResults.target) - 1) * 100).toFixed(2)
                    }%
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded"
              onClick={applyOptimization}
            >
              Apply Optimization
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PatternOptimizer;
