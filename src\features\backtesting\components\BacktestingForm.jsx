import React, { useState } from 'react';
import { ClipLoader } from 'react-spinners';

/**
 * BacktestingForm Component
 * 
 * Form for configuring and running backtests on trading strategies
 */
const BacktestingForm = ({ onRunBacktest, isLoading = false }) => {
  // State for backtest configuration
  const [config, setConfig] = useState({
    symbol: 'EURUSD',
    timeframe: 'H1',
    startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 90 days ago
    endDate: new Date().toISOString().split('T')[0], // Today
    strategy: 'macd_crossover',
    initialCapital: 10000,
    positionSize: 'fixed', // 'fixed', 'percentage', 'risk_based'
    fixedSize: 0.1, // For fixed position size (in lots)
    percentageSize: 2, // For percentage position size (% of capital)
    riskPercentage: 1, // For risk-based position size (% of capital to risk per trade)
    stopLoss: 50, // In pips
    takeProfit: 100, // In pips
    trailingStop: false,
    trailingStopDistance: 20, // In pips
    customParameters: {
      macd_fast: 12,
      macd_slow: 26,
      macd_signal: 9,
      rsi_period: 14,
      rsi_overbought: 70,
      rsi_oversold: 30,
      ma_fast: 20,
      ma_slow: 50
    }
  });
  
  // Available symbols
  const symbols = [
    { value: 'EURUSD', label: 'EUR/USD' },
    { value: 'GBPUSD', label: 'GBP/USD' },
    { value: 'USDJPY', label: 'USD/JPY' },
    { value: 'AUDUSD', label: 'AUD/USD' },
    { value: 'XAUUSD', label: 'Gold (XAU/USD)' }
  ];
  
  // Available timeframes
  const timeframes = [
    { value: 'M5', label: '5 Minutes' },
    { value: 'M15', label: '15 Minutes' },
    { value: 'M30', label: '30 Minutes' },
    { value: 'H1', label: '1 Hour' },
    { value: 'H4', label: '4 Hours' },
    { value: 'D1', label: 'Daily' }
  ];
  
  // Available strategies
  const strategies = [
    { value: 'macd_crossover', label: 'MACD Crossover' },
    { value: 'rsi_divergence', label: 'RSI Divergence' },
    { value: 'moving_average_crossover', label: 'Moving Average Crossover' },
    { value: 'bollinger_bands_breakout', label: 'Bollinger Bands Breakout' },
    { value: 'support_resistance', label: 'Support & Resistance' }
  ];
  
  // Position sizing methods
  const positionSizingMethods = [
    { value: 'fixed', label: 'Fixed Size (Lots)' },
    { value: 'percentage', label: 'Percentage of Capital' },
    { value: 'risk_based', label: 'Risk-Based' }
  ];
  
  // Handle input changes
  const handleInputChange = (section, field, value) => {
    if (section === 'root') {
      setConfig(prev => ({
        ...prev,
        [field]: value
      }));
    } else {
      setConfig(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: value
        }
      }));
    }
  };
  
  // Validation function
  const validateConfig = (config) => {
    const errors = {};
    if (!config.symbol) errors.symbol = 'Symbol is required.';
    if (!config.timeframe) errors.timeframe = 'Timeframe is required.';
    if (!config.startDate) errors.startDate = 'Start date is required.';
    if (!config.endDate) errors.endDate = 'End date is required.';
    if (!config.strategy) errors.strategy = 'Strategy is required.';
    if (config.initialCapital <= 0) errors.initialCapital = 'Initial capital must be positive.';
    if (config.fixedSize < 0) errors.fixedSize = 'Fixed size must be non-negative.';
    if (config.percentageSize < 0 || config.percentageSize > 100) errors.percentageSize = 'Percentage size must be between 0 and 100.';
    if (config.riskPercentage < 0 || config.riskPercentage > 100) errors.riskPercentage = 'Risk percentage must be between 0 and 100.';
    if (config.stopLoss <= 0) errors.stopLoss = 'Stop loss must be positive.';
    if (config.takeProfit <= 0) errors.takeProfit = 'Take profit must be positive.';
    if (config.trailingStop && config.trailingStopDistance <= 0) errors.trailingStopDistance = 'Trailing stop distance must be positive.';
    // Custom parameter checks (example for MACD/RSI/MA)
    if (config.customParameters.macd_fast < 1) errors.macd_fast = 'MACD fast period must be >= 1.';
    if (config.customParameters.macd_slow < 1) errors.macd_slow = 'MACD slow period must be >= 1.';
    if (config.customParameters.macd_signal < 1) errors.macd_signal = 'MACD signal period must be >= 1.';
    if (config.customParameters.rsi_period < 1) errors.rsi_period = 'RSI period must be >= 1.';
    if (config.customParameters.rsi_overbought < 50 || config.customParameters.rsi_overbought > 100) errors.rsi_overbought = 'RSI overbought must be between 50 and 100.';
    if (config.customParameters.rsi_oversold < 0 || config.customParameters.rsi_oversold > 50) errors.rsi_oversold = 'RSI oversold must be between 0 and 50.';
    if (config.customParameters.ma_fast < 1) errors.ma_fast = 'Fast MA period must be >= 1.';
    if (config.customParameters.ma_slow < 1) errors.ma_slow = 'Slow MA period must be >= 1.';
    return errors;
  };

  const [errors, setErrors] = useState({});
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    const validationErrors = validateConfig(config);
    setErrors(validationErrors);
    if (Object.keys(validationErrors).length > 0) return;
    try {
      await onRunBacktest(config);
    } finally {
      // Remove spinner here
    }
  };
  
  // Get strategy-specific parameters
  const getStrategyParameters = () => {
    switch (config.strategy) {
      case 'macd_crossover':
        return (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Fast Period
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={config.customParameters.macd_fast}
                onChange={(e) => handleInputChange('customParameters', 'macd_fast', parseInt(e.target.value))}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Slow Period
              </label>
              <input
                type="number"
                min="1"
                max="100"
                value={config.customParameters.macd_slow}
                onChange={(e) => handleInputChange('customParameters', 'macd_slow', parseInt(e.target.value))}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Signal Period
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={config.customParameters.macd_signal}
                onChange={(e) => handleInputChange('customParameters', 'macd_signal', parseInt(e.target.value))}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          </>
        );
      case 'rsi_divergence':
        return (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                RSI Period
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={config.customParameters.rsi_period}
                onChange={(e) => handleInputChange('customParameters', 'rsi_period', parseInt(e.target.value))}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Overbought Level
              </label>
              <input
                type="number"
                min="50"
                max="100"
                value={config.customParameters.rsi_overbought}
                onChange={(e) => handleInputChange('customParameters', 'rsi_overbought', parseInt(e.target.value))}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Oversold Level
              </label>
              <input
                type="number"
                min="0"
                max="50"
                value={config.customParameters.rsi_oversold}
                onChange={(e) => handleInputChange('customParameters', 'rsi_oversold', parseInt(e.target.value))}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          </>
        );
      case 'moving_average_crossover':
        return (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Fast MA Period
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={config.customParameters.ma_fast}
                onChange={(e) => handleInputChange('customParameters', 'ma_fast', parseInt(e.target.value))}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Slow MA Period
              </label>
              <input
                type="number"
                min="1"
                max="200"
                value={config.customParameters.ma_slow}
                onChange={(e) => handleInputChange('customParameters', 'ma_slow', parseInt(e.target.value))}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          </>
        );
      default:
        return null;
    }
  };
  
  // Get position sizing inputs based on selected method
  const getPositionSizingInputs = () => {
    switch (config.positionSize) {
      case 'fixed':
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Fixed Size (Lots)
            </label>
            <input
              type="number"
              min="0.01"
              step="0.01"
              value={config.fixedSize}
              onChange={(e) => handleInputChange('root', 'fixedSize', parseFloat(e.target.value))}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>
        );
      case 'percentage':
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Percentage of Capital (%)
            </label>
            <input
              type="number"
              min="0.1"
              max="100"
              step="0.1"
              value={config.percentageSize}
              onChange={(e) => handleInputChange('root', 'percentageSize', parseFloat(e.target.value))}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>
        );
      case 'risk_based':
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Risk per Trade (%)
            </label>
            <input
              type="number"
              min="0.1"
              max="10"
              step="0.1"
              value={config.riskPercentage}
              onChange={(e) => handleInputChange('root', 'riskPercentage', parseFloat(e.target.value))}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow" aria-label="Backtest Configuration Form" role="form">
      <h2 className="text-xl font-semibold mb-4">Backtest Configuration</h2>
      
      {/* Market Data Section */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">Market Data</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Symbol
            </label>
            <select
              id="symbol"
              aria-label="Symbol"
              value={config.symbol}
              onChange={(e) => handleInputChange('root', 'symbol', e.target.value)}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            >
              {symbols.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label htmlFor="timeframe" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Timeframe
            </label>
            <select
              id="timeframe"
              aria-label="Timeframe"
              value={config.timeframe}
              onChange={(e) => handleInputChange('root', 'timeframe', e.target.value)}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            >
              {timeframes.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Start Date
            </label>
            <input
              id="startDate"
              aria-label="Start Date"
              type="date"
              value={config.startDate}
              onChange={(e) => handleInputChange('root', 'startDate', e.target.value)}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
          </div>
          
          <div>
            <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              End Date
            </label>
            <input
              id="endDate"
              aria-label="End Date"
              type="date"
              value={config.endDate}
              onChange={(e) => handleInputChange('root', 'endDate', e.target.value)}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
          </div>
        </div>
      </div>
      
      {/* Strategy Section */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">Strategy</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="md:col-span-2">
            <label htmlFor="strategy" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Trading Strategy
            </label>
            <select
              id="strategy"
              aria-label="Trading Strategy"
              value={config.strategy}
              onChange={(e) => handleInputChange('root', 'strategy', e.target.value)}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            >
              {strategies.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>
          
          {/* Strategy-specific parameters */}
          {getStrategyParameters()}
        </div>
      </div>
      
      {/* Money Management Section */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">Money Management</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="initialCapital" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Initial Capital ($)
            </label>
            <input
              id="initialCapital"
              aria-label="Initial Capital"
              type="number"
              min="100"
              value={config.initialCapital}
              onChange={(e) => handleInputChange('root', 'initialCapital', parseFloat(e.target.value))}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
          </div>
          
          <div>
            <label htmlFor="positionSize" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Position Sizing Method
            </label>
            <select
              id="positionSize"
              aria-label="Position Sizing Method"
              value={config.positionSize}
              onChange={(e) => handleInputChange('root', 'positionSize', e.target.value)}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            >
              {positionSizingMethods.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>
          
          {/* Position sizing inputs */}
          {getPositionSizingInputs()}
        </div>
      </div>
      
      {/* Risk Management Section */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">Risk Management</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="stopLoss" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Stop Loss (pips)
            </label>
            <input
              id="stopLoss"
              aria-label="Stop Loss"
              type="number"
              min="1"
              value={config.stopLoss}
              onChange={(e) => handleInputChange('root', 'stopLoss', parseInt(e.target.value))}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
          </div>
          
          <div>
            <label htmlFor="takeProfit" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Take Profit (pips)
            </label>
            <input
              id="takeProfit"
              aria-label="Take Profit"
              type="number"
              min="1"
              value={config.takeProfit}
              onChange={(e) => handleInputChange('root', 'takeProfit', parseInt(e.target.value))}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
          </div>
          
          <div className="flex items-center">
            <input
              id="trailing-stop"
              aria-label="Use Trailing Stop"
              type="checkbox"
              checked={config.trailingStop}
              onChange={() => handleInputChange('root', 'trailingStop', !config.trailingStop)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded focus:outline-none"
            />
            <label htmlFor="trailing-stop" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Use Trailing Stop
            </label>
          </div>
          
          {config.trailingStop && (
            <div>
              <label htmlFor="trailingStopDistance" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Trailing Stop Distance (pips)
              </label>
              <input
                id="trailingStopDistance"
                aria-label="Trailing Stop Distance"
                type="number"
                min="1"
                value={config.trailingStopDistance}
                onChange={(e) => handleInputChange('root', 'trailingStopDistance', parseInt(e.target.value))}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              />
            </div>
          )}
        </div>
      </div>
      
      {/* Submit Button and Loading Indicator */}
      <div className="mt-6 flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          disabled={isLoading}
          aria-busy={isLoading}
          aria-label="Run Backtest"
        >
          {isLoading ? 'Running...' : 'Run Backtest'}
        </button>
      </div>
    </form>
  );
};

export default BacktestingForm;
