import mongoose from 'mongoose';
import { enhancedSecurityService } from '../services/enhancedSecurityService.js';
import { standardizedErrorHandler } from '../services/standardizedErrorHandler.js';
import logger from '../utils/logger.js';

/**
 * Enhanced Authentication Controller - Phase 2
 * 
 * Provides comprehensive authentication and authorization with:
 * - JWT refresh token mechanism
 * - RBAC system integration
 * - Session management
 * - Input validation
 * - Rate limiting
 * 
 * @version 2.0.0
 */

// User Schema with enhanced security
const userSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true, lowercase: true },
  password: { type: String, required: true },
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  role: { type: String, enum: ['basic', 'premium', 'admin'], default: 'basic' },
  isActive: { type: Boolean, default: true },
  emailVerified: { type: Boolean, default: false },
  lastLoginAt: { type: Date },
  loginAttempts: { type: Number, default: 0 },
  lockUntil: { type: Date },
  refreshTokens: [{ 
    token: String, 
    createdAt: { type: Date, default: Date.now },
    expiresAt: Date,
    isActive: { type: Boolean, default: true }
  }],
  preferences: {
    defaultTimeframe: { type: String, default: '1d' },
    notifications: { type: Boolean, default: true },
    theme: { type: String, enum: ['light', 'dark'], default: 'light' }
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Add indexes for performance
userSchema.index({ email: 1 });
userSchema.index({ role: 1, isActive: 1 });
userSchema.index({ lastLoginAt: -1 });

const User = mongoose.model('User', userSchema);

/**
 * Register new user
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const register = async (req, res, next) => {
  try {
    // Validate input
    const validation = enhancedSecurityService.validateInput(req.body, 'userRegistration');
    if (!validation.isValid) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validation.errors
      });
    }

    const { email, password, firstName, lastName, role } = validation.data;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        code: 'USER_EXISTS'
      });
    }

    // Hash password
    const hashedPassword = await enhancedSecurityService.hashPassword(password);

    // Create user
    const user = new User({
      email,
      password: hashedPassword,
      firstName,
      lastName,
      role: role || 'basic'
    });

    await user.save();

    // Remove password from response
    const userResponse = user.toObject();
    delete userResponse.password;
    delete userResponse.refreshTokens;

    logger.info(`User registered: ${email} with role: ${role || 'basic'}`);

    res.status(201).json({
      message: 'User registered successfully',
      user: userResponse
    });
  } catch (error) {
    logger.error('Registration error:', error);
    next(await standardizedErrorHandler.handleError(error, {
      service: 'Authentication',
      operation: 'register'
    }));
  }
};

/**
 * Login user with enhanced security
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const login = async (req, res, next) => {
  try {
    // Validate input
    const validation = enhancedSecurityService.validateInput(req.body, 'userLogin');
    if (!validation.isValid) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validation.errors
      });
    }

    const { email, password, rememberMe } = validation.data;

    // Find user
    const user = await User.findOne({ email, isActive: true });
    if (!user) {
      return res.status(401).json({
        error: 'Invalid credentials',
        code: 'INVALID_CREDENTIALS'
      });
    }

    // Check if account is locked
    if (user.lockUntil && user.lockUntil > Date.now()) {
      return res.status(423).json({
        error: 'Account temporarily locked due to too many failed attempts',
        code: 'ACCOUNT_LOCKED',
        lockUntil: user.lockUntil
      });
    }

    // Verify password
    const isValidPassword = await enhancedSecurityService.verifyPassword(password, user.password);
    if (!isValidPassword) {
      // Increment login attempts
      user.loginAttempts = (user.loginAttempts || 0) + 1;
      
      // Lock account after 5 failed attempts
      if (user.loginAttempts >= 5) {
        user.lockUntil = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
        logger.warn(`Account locked for user: ${email}`);
      }
      
      await user.save();
      
      return res.status(401).json({
        error: 'Invalid credentials',
        code: 'INVALID_CREDENTIALS'
      });
    }

    // Reset login attempts on successful login
    user.loginAttempts = 0;
    user.lockUntil = undefined;
    user.lastLoginAt = new Date();

    // Create session
    const sessionData = {
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      role: user.role
    };
    
    const sessionId = await enhancedSecurityService.createSession(user._id.toString(), sessionData);

    // Generate tokens
    const tokenPayload = {
      id: user._id,
      email: user.email,
      role: user.role,
      sessionId
    };

    const accessToken = enhancedSecurityService.generateAccessToken(tokenPayload);
    const refreshToken = enhancedSecurityService.generateRefreshToken(tokenPayload);

    // Store refresh token
    const refreshTokenExpiry = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
    user.refreshTokens.push({
      token: refreshToken,
      expiresAt: refreshTokenExpiry,
      isActive: true
    });

    // Clean up old refresh tokens
    user.refreshTokens = user.refreshTokens.filter(rt => 
      rt.expiresAt > new Date() && rt.isActive
    );

    await user.save();

    // Set httpOnly cookie for refresh token
    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: rememberMe ? 7 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000
    });

    // Remove sensitive data from response
    const userResponse = user.toObject();
    delete userResponse.password;
    delete userResponse.refreshTokens;
    delete userResponse.loginAttempts;
    delete userResponse.lockUntil;

    logger.info(`User logged in: ${email} with role: ${user.role}`);

    res.json({
      message: 'Login successful',
      accessToken,
      user: userResponse,
      sessionId
    });
  } catch (error) {
    logger.error('Login error:', error);
    next(await standardizedErrorHandler.handleError(error, {
      service: 'Authentication',
      operation: 'login'
    }));
  }
};

/**
 * Refresh access token
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const refreshToken = async (req, res, next) => {
  try {
    const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
    
    if (!refreshToken) {
      return res.status(401).json({
        error: 'Refresh token required',
        code: 'REFRESH_TOKEN_MISSING'
      });
    }

    // Verify refresh token
    const decoded = enhancedSecurityService.verifyRefreshToken(refreshToken);
    
    // Find user and validate refresh token
    const user = await User.findById(decoded.id);
    if (!user || !user.isActive) {
      return res.status(401).json({
        error: 'Invalid refresh token',
        code: 'INVALID_REFRESH_TOKEN'
      });
    }

    const storedToken = user.refreshTokens.find(rt => 
      rt.token === refreshToken && rt.isActive && rt.expiresAt > new Date()
    );

    if (!storedToken) {
      return res.status(401).json({
        error: 'Invalid or expired refresh token',
        code: 'INVALID_REFRESH_TOKEN'
      });
    }

    // Check session validity
    const session = await enhancedSecurityService.getSession(decoded.sessionId);
    if (!session || !session.isActive) {
      return res.status(401).json({
        error: 'Session expired',
        code: 'SESSION_EXPIRED'
      });
    }

    // Generate new access token
    const tokenPayload = {
      id: user._id,
      email: user.email,
      role: user.role,
      sessionId: decoded.sessionId
    };

    const newAccessToken = enhancedSecurityService.generateAccessToken(tokenPayload);

    logger.info(`Access token refreshed for user: ${user.email}`);

    res.json({
      message: 'Token refreshed successfully',
      accessToken: newAccessToken
    });
  } catch (error) {
    logger.error('Token refresh error:', error);
    next(await standardizedErrorHandler.handleError(error, {
      service: 'Authentication',
      operation: 'refreshToken'
    }));
  }
};

/**
 * Logout user
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const logout = async (req, res, next) => {
  try {
    const refreshToken = req.cookies.refreshToken;
    const sessionId = req.user?.sessionId;

    // Invalidate session
    if (sessionId) {
      await enhancedSecurityService.invalidateSession(sessionId);
    }

    // Invalidate refresh token
    if (refreshToken && req.user?.id) {
      const user = await User.findById(req.user.id);
      if (user) {
        const tokenIndex = user.refreshTokens.findIndex(rt => rt.token === refreshToken);
        if (tokenIndex !== -1) {
          user.refreshTokens[tokenIndex].isActive = false;
          await user.save();
        }
      }
    }

    // Clear refresh token cookie
    res.clearCookie('refreshToken');

    logger.info(`User logged out: ${req.user?.email || 'unknown'}`);

    res.json({
      message: 'Logout successful'
    });
  } catch (error) {
    logger.error('Logout error:', error);
    next(await standardizedErrorHandler.handleError(error, {
      service: 'Authentication',
      operation: 'logout'
    }));
  }
};

/**
 * Get current user profile
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const getProfile = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id).select('-password -refreshTokens');
    
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    res.json({
      user,
      permissions: enhancedSecurityService.roles[user.role.toUpperCase()]?.permissions || [],
      features: enhancedSecurityService.roles[user.role.toUpperCase()]?.features || []
    });
  } catch (error) {
    logger.error('Get profile error:', error);
    next(await standardizedErrorHandler.handleError(error, {
      service: 'Authentication',
      operation: 'getProfile'
    }));
  }
};

/**
 * Update user profile
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const updateProfile = async (req, res, next) => {
  try {
    // Validate input
    const validation = enhancedSecurityService.validateInput(req.body, 'userProfileUpdate');
    if (!validation.isValid) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validation.errors
      });
    }

    const updates = validation.data;
    updates.updatedAt = new Date();

    const user = await User.findByIdAndUpdate(
      req.user.id,
      { $set: updates },
      { new: true, runValidators: true }
    ).select('-password -refreshTokens');

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    logger.info(`Profile updated for user: ${user.email}`);

    res.json({
      message: 'Profile updated successfully',
      user
    });
  } catch (error) {
    logger.error('Update profile error:', error);
    next(await standardizedErrorHandler.handleError(error, {
      service: 'Authentication',
      operation: 'updateProfile'
    }));
  }
};

/**
 * Get security metrics (admin only)
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const getSecurityMetrics = async (req, res, next) => {
  try {
    const metrics = enhancedSecurityService.getSecurityMetrics();
    
    // Add user statistics
    const userStats = await User.aggregate([
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 },
          active: { $sum: { $cond: ['$isActive', 1, 0] } }
        }
      }
    ]);

    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ isActive: true });
    const recentLogins = await User.countDocuments({
      lastLoginAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });

    res.json({
      security: metrics,
      users: {
        total: totalUsers,
        active: activeUsers,
        recentLogins,
        byRole: userStats
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Get security metrics error:', error);
    next(await standardizedErrorHandler.handleError(error, {
      service: 'Authentication',
      operation: 'getSecurityMetrics'
    }));
  }
};

export default {
  register,
  login,
  refreshToken,
  logout,
  getProfile,
  updateProfile,
  getSecurityMetrics,
  User // Export User model for other controllers
};
