import React, { useEffect, useRef, useState } from 'react';
import Chart from 'chart.js/auto';
import { getPatternInfo } from '../../../utils/candlestickPatterns';
import 'chartjs-adapter-date-fns';
import { enUS } from 'date-fns/locale';
import ExportResults from './ExportResults';

/**
 * PatternBacktestResults Component
 *
 * Displays the results of a pattern-based backtest including performance metrics,
 * equity curve, and pattern-specific performance data
 */
const PatternBacktestResults = ({ results }) => {
  // State for chart options
  const [selectedTimeframe, setSelectedTimeframe] = useState('all');
  const [showPatterns, setShowPatterns] = useState(true);
  const [selectedPatternTypes, setSelectedPatternTypes] = useState(['bullish', 'bearish', 'neutral', 'reversal', 'continuation']);

  // Chart references
  const priceChartRef = useRef(null);
  const priceChartInstance = useRef(null);

  const equityChartRef = useRef(null);
  const equityChartInstance = useRef(null);

  const patternPerformanceChartRef = useRef(null);
  const patternPerformanceChartInstance = useRef(null);

  // Initialize price chart with pattern visualization
  useEffect(() => {
    if (!results || !priceChartRef.current || !results.historicalData) return;

    // Destroy existing chart if it exists
    if (priceChartInstance.current) {
      priceChartInstance.current.destroy();
    }

    // Create new chart
    const ctx = priceChartRef.current.getContext('2d');

    // Filter data based on selected timeframe
    let chartData = [...results.historicalData];
    if (selectedTimeframe !== 'all') {
      const now = new Date();
      let startDate;

      switch (selectedTimeframe) {
        case '1m':
          startDate = new Date(now.setMonth(now.getMonth() - 1));
          break;
        case '3m':
          startDate = new Date(now.setMonth(now.getMonth() - 3));
          break;
        case '6m':
          startDate = new Date(now.setMonth(now.getMonth() - 6));
          break;
        case '1y':
          startDate = new Date(now.setFullYear(now.getFullYear() - 1));
          break;
        default:
          startDate = new Date(0); // Beginning of time
      }

      chartData = chartData.filter(d => new Date(d.date) >= startDate);
    }

    // Prepare data for chart
    const ohlcData = chartData.map(d => ({
      x: new Date(d.date),
      o: d.open,
      h: d.high,
      l: d.low,
      c: d.close
    }));

    // Prepare pattern annotations
    const patternAnnotations = [];

    if (showPatterns && results.patterns) {
      // Filter patterns by selected types
      const filteredPatterns = results.patterns.filter(p =>
        selectedPatternTypes.includes(p.type)
      );

      // Create annotations for each pattern
      filteredPatterns.forEach(pattern => {
        const patternInfo = getPatternInfo(pattern.pattern);
        const patternName = patternInfo ? patternInfo.name : pattern.pattern;
        const patternColor = getPatternColor(pattern.type);

        // Add annotation based on pattern type
        if (pattern.points) {
          if (pattern.pattern.includes('head_and_shoulders')) {
            // Head and shoulders pattern
            const { leftShoulder, head, rightShoulder } = pattern.points;

            // Add line connecting shoulders
            patternAnnotations.push({
              type: 'line',
              xMin: chartData[leftShoulder].date,
              xMax: chartData[rightShoulder].date,
              yMin: chartData[leftShoulder].high,
              yMax: chartData[rightShoulder].high,
              borderColor: patternColor,
              borderWidth: 2,
              label: {
                content: patternName,
                enabled: true,
                position: 'top'
              }
            });

            // Add point for head
            patternAnnotations.push({
              type: 'point',
              xValue: chartData[head].date,
              yValue: chartData[head].high,
              backgroundColor: patternColor,
              radius: 4
            });
          } else if (pattern.pattern.includes('double_top')) {
            // Double top pattern
            const { firstPeak, secondPeak, trough } = pattern.points;

            // Add line connecting peaks
            patternAnnotations.push({
              type: 'line',
              xMin: chartData[firstPeak].date,
              xMax: chartData[secondPeak].date,
              yMin: chartData[firstPeak].high,
              yMax: chartData[secondPeak].high,
              borderColor: patternColor,
              borderWidth: 2,
              label: {
                content: patternName,
                enabled: true,
                position: 'top'
              }
            });

            // Add point for trough
            patternAnnotations.push({
              type: 'point',
              xValue: chartData[trough].date,
              yValue: chartData[trough].low,
              backgroundColor: patternColor,
              radius: 4
            });
          } else if (pattern.pattern.includes('double_bottom')) {
            // Double bottom pattern
            const { firstTrough, secondTrough, peak } = pattern.points;

            // Add line connecting troughs
            patternAnnotations.push({
              type: 'line',
              xMin: chartData[firstTrough].date,
              xMax: chartData[secondTrough].date,
              yMin: chartData[firstTrough].low,
              yMax: chartData[secondTrough].low,
              borderColor: patternColor,
              borderWidth: 2,
              label: {
                content: patternName,
                enabled: true,
                position: 'bottom'
              }
            });

            // Add point for peak
            patternAnnotations.push({
              type: 'point',
              xValue: chartData[peak].date,
              yValue: chartData[peak].high,
              backgroundColor: patternColor,
              radius: 4
            });
          } else if (pattern.pattern.includes('triangle')) {
            // Triangle patterns
            const { firstPeak, secondPeak, firstTrough, lastTrough } = pattern.points;

            // Add upper trend line
            if (pattern.upper) {
              const { slope, intercept } = pattern.upper;
              patternAnnotations.push({
                type: 'line',
                xMin: chartData[firstPeak].date,
                xMax: chartData[secondPeak].date,
                yMin: chartData[firstPeak].high,
                yMax: chartData[secondPeak].high,
                borderColor: patternColor,
                borderWidth: 2,
                borderDash: [5, 5]
              });
            }

            // Add lower trend line
            if (pattern.lower) {
              const { slope, intercept } = pattern.lower;
              patternAnnotations.push({
                type: 'line',
                xMin: chartData[firstTrough].date,
                xMax: chartData[lastTrough].date,
                yMin: chartData[firstTrough].low,
                yMax: chartData[lastTrough].low,
                borderColor: patternColor,
                borderWidth: 2,
                borderDash: [5, 5]
              });
            }

            // Add label
            patternAnnotations.push({
              type: 'label',
              xValue: chartData[Math.floor((firstPeak + secondPeak) / 2)].date,
              yValue: (chartData[firstPeak].high + chartData[firstTrough].low) / 2,
              content: patternName,
              backgroundColor: 'rgba(255, 255, 255, 0.7)',
              color: patternColor,
              font: {
                size: 12
              }
            });
          } else if (pattern.pattern.includes('channel')) {
            // Channel patterns
            const { firstPeak, secondPeak, firstTrough, secondTrough } = pattern.points;

            // Add upper trend line
            if (pattern.upper) {
              patternAnnotations.push({
                type: 'line',
                xMin: chartData[firstPeak].date,
                xMax: chartData[secondPeak].date,
                yMin: chartData[firstPeak].high,
                yMax: chartData[secondPeak].high,
                borderColor: patternColor,
                borderWidth: 2
              });
            } else if (pattern.resistance) {
              // Horizontal channel
              patternAnnotations.push({
                type: 'line',
                xMin: chartData[firstPeak].date,
                xMax: chartData[secondPeak].date,
                yMin: pattern.resistance,
                yMax: pattern.resistance,
                borderColor: patternColor,
                borderWidth: 2
              });
            }

            // Add lower trend line
            if (pattern.lower) {
              patternAnnotations.push({
                type: 'line',
                xMin: chartData[firstTrough].date,
                xMax: chartData[secondTrough].date,
                yMin: chartData[firstTrough].low,
                yMax: chartData[secondTrough].low,
                borderColor: patternColor,
                borderWidth: 2
              });
            } else if (pattern.support) {
              // Horizontal channel
              patternAnnotations.push({
                type: 'line',
                xMin: chartData[firstTrough].date,
                xMax: chartData[secondTrough].date,
                yMin: pattern.support,
                yMax: pattern.support,
                borderColor: patternColor,
                borderWidth: 2
              });
            }

            // Add label
            patternAnnotations.push({
              type: 'label',
              xValue: chartData[Math.floor((firstPeak + secondPeak) / 2)].date,
              yValue: (chartData[firstPeak].high + chartData[firstTrough].low) / 2,
              content: patternName,
              backgroundColor: 'rgba(255, 255, 255, 0.7)',
              color: patternColor,
              font: {
                size: 12
              }
            });
          } else {
            // Simple candlestick patterns
            const index = pattern.index;
            if (index >= 0 && index < chartData.length) {
              patternAnnotations.push({
                type: 'point',
                xValue: chartData[index].date,
                yValue: pattern.type === 'bullish' ? chartData[index].low * 0.99 : chartData[index].high * 1.01,
                backgroundColor: patternColor,
                radius: 4,
                label: {
                  content: patternName,
                  enabled: true,
                  position: pattern.type === 'bullish' ? 'bottom' : 'top'
                }
              });
            }
          }
        }
      });
    }

    // Create chart
    priceChartInstance.current = new Chart(ctx, {
      type: 'candlestick',
      data: {
        datasets: [{
          label: results.symbol,
          data: ohlcData,
          color: {
            up: 'rgba(75, 192, 192, 1)',
            down: 'rgba(255, 99, 132, 1)',
            unchanged: 'rgba(201, 203, 207, 1)'
          }
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: function(context) {
                const point = context.raw;
                return [
                  `Open: ${point.o.toFixed(4)}`,
                  `High: ${point.h.toFixed(4)}`,
                  `Low: ${point.l.toFixed(4)}`,
                  `Close: ${point.c.toFixed(4)}`
                ];
              }
            }
          },
          legend: {
            display: false
          },
          title: {
            display: true,
            text: `${results.symbol} - ${results.timeframe} Chart with Detected Patterns`,
            font: {
              size: 16
            }
          },
          annotation: {
            annotations: patternAnnotations
          }
        },
        scales: {
          x: {
            type: 'time',
            time: {
              unit: getTimeUnit(results.timeframe),
              tooltipFormat: 'MMM d, yyyy',
              displayFormats: {
                millisecond: 'HH:mm:ss.SSS',
                second: 'HH:mm:ss',
                minute: 'HH:mm',
                hour: 'MMM d, HH:mm',
                day: 'MMM d',
                week: 'MMM d',
                month: 'MMM yyyy',
                quarter: 'MMM yyyy',
                year: 'yyyy'
              }
            },
            adapters: {
              date: {
                locale: enUS
              }
            },
            title: {
              display: true,
              text: 'Date'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Price'
            }
          }
        }
      }
    });

    return () => {
      if (priceChartInstance.current) {
        priceChartInstance.current.destroy();
      }
    };
  }, [results, selectedTimeframe, showPatterns, selectedPatternTypes]);

  // Get appropriate time unit based on timeframe
  const getTimeUnit = (timeframe) => {
    switch (timeframe) {
      case 'M1':
      case 'M5':
      case 'M15':
      case 'M30':
        return 'minute';
      case 'H1':
      case 'H4':
        return 'hour';
      case 'D1':
        return 'day';
      case 'W1':
        return 'week';
      case 'MN':
        return 'month';
      default:
        return 'day';
    }
  };

  // Get color for pattern type
  const getPatternColor = (type) => {
    switch (type) {
      case 'bullish':
        return 'rgba(75, 192, 192, 1)';
      case 'bearish':
        return 'rgba(255, 99, 132, 1)';
      case 'neutral':
        return 'rgba(201, 203, 207, 1)';
      case 'reversal':
        return 'rgba(153, 102, 255, 1)';
      case 'continuation':
        return 'rgba(255, 159, 64, 1)';
      default:
        return 'rgba(201, 203, 207, 1)';
    }
  };

  // Initialize equity chart
  useEffect(() => {
    if (!results || !equityChartRef.current) return;

    // Destroy existing chart if it exists
    if (equityChartInstance.current) {
      equityChartInstance.current.destroy();
    }

    // Create new chart
    const ctx = equityChartRef.current.getContext('2d');

    // Extract data for chart
    const labels = results.equityCurve.map(point => point.date);
    const data = results.equityCurve.map(point => point.equity);

    // Create chart
    equityChartInstance.current = new Chart(ctx, {
      type: 'line',
      data: {
        labels,
        datasets: [{
          label: 'Account Equity',
          data,
          borderColor: '#3b82f6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: function(context) {
                return `Equity: $${context.raw.toFixed(2)}`;
              }
            }
          },
          legend: {
            display: false
          },
          title: {
            display: true,
            text: 'Equity Curve',
            font: {
              size: 16
            }
          }
        },
        scales: {
          x: {
            type: 'time',
            time: {
              unit: 'day',
              tooltipFormat: 'MMM d, yyyy',
              displayFormats: {
                day: 'MMM d'
              }
            },
            title: {
              display: true,
              text: 'Date'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Equity ($)'
            },
            min: Math.min(results.initialCapital * 0.9, Math.min(...data)),
            ticks: {
              callback: function(value) {
                return '$' + value.toFixed(0);
              }
            }
          }
        }
      }
    });

    return () => {
      if (equityChartInstance.current) {
        equityChartInstance.current.destroy();
      }
    };
  }, [results]);

  // Initialize pattern performance chart
  useEffect(() => {
    if (!results || !patternPerformanceChartRef.current || !results.patternPerformance) return;

    // Destroy existing chart if it exists
    if (patternPerformanceChartInstance.current) {
      patternPerformanceChartInstance.current.destroy();
    }

    // Create new chart
    const ctx = patternPerformanceChartRef.current.getContext('2d');

    // Extract data for chart
    const patternIds = Object.keys(results.patternPerformance);
    const patternNames = patternIds.map(id => {
      const info = getPatternInfo(id);
      return info ? info.name : id;
    });

    const winRates = patternIds.map(id => results.patternPerformance[id].winRate);
    const profitFactors = patternIds.map(id =>
      Math.min(results.patternPerformance[id].profitFactor, 5) // Cap at 5 for better visualization
    );
    const tradeCounts = patternIds.map(id => results.patternPerformance[id].totalTrades);

    // Create chart
    patternPerformanceChartInstance.current = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: patternNames,
        datasets: [
          {
            label: 'Win Rate (%)',
            data: winRates,
            backgroundColor: 'rgba(59, 130, 246, 0.7)',
            borderColor: 'rgba(59, 130, 246, 1)',
            borderWidth: 1,
            yAxisID: 'y'
          },
          {
            label: 'Profit Factor',
            data: profitFactors,
            backgroundColor: 'rgba(16, 185, 129, 0.7)',
            borderColor: 'rgba(16, 185, 129, 1)',
            borderWidth: 1,
            yAxisID: 'y1'
          },
          {
            label: 'Trade Count',
            data: tradeCounts,
            type: 'line',
            backgroundColor: 'rgba(245, 158, 11, 0.7)',
            borderColor: 'rgba(245, 158, 11, 1)',
            borderWidth: 2,
            yAxisID: 'y2',
            fill: false
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          tooltip: {
            mode: 'index',
            intersect: false
          },
          legend: {
            position: 'top'
          },
          title: {
            display: true,
            text: 'Pattern Performance Comparison',
            font: {
              size: 16
            }
          }
        },
        scales: {
          x: {
            title: {
              display: true,
              text: 'Pattern'
            }
          },
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            title: {
              display: true,
              text: 'Win Rate (%)'
            },
            min: 0,
            max: 100
          },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            title: {
              display: true,
              text: 'Profit Factor'
            },
            min: 0,
            max: 5,
            grid: {
              drawOnChartArea: false
            }
          },
          y2: {
            type: 'linear',
            display: true,
            position: 'right',
            title: {
              display: true,
              text: 'Trade Count'
            },
            min: 0,
            grid: {
              drawOnChartArea: false
            }
          }
        }
      }
    });

    return () => {
      if (patternPerformanceChartInstance.current) {
        patternPerformanceChartInstance.current.destroy();
      }
    };
  }, [results]);

  if (!results) return null;

  return (
    <div className="pattern-backtest-results">
      {/* Summary Section */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">Net Profit</p>
            <p className={`text-xl font-bold ${results.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              ${results.netProfit.toFixed(2)}
            </p>
          </div>

          <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">Win Rate</p>
            <p className="text-xl font-bold">{results.winRate.toFixed(2)}%</p>
          </div>

          <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">Profit Factor</p>
            <p className="text-xl font-bold">{results.profitFactor.toFixed(2)}</p>
          </div>

          <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">Max Drawdown</p>
            <p className="text-xl font-bold text-red-600">{results.maxDrawdown.toFixed(2)}%</p>
          </div>
        </div>
      </div>

      {/* Price Chart with Pattern Visualization */}
      {results.historicalData && (
        <div className="mb-6">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
            <div className="flex flex-wrap items-center justify-between mb-4">
              <h3 className="text-lg font-medium">Price Chart with Patterns</h3>

              <div className="flex flex-wrap gap-2 mt-2 sm:mt-0">
                {/* Timeframe Selector */}
                <select
                  className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
                  value={selectedTimeframe}
                  onChange={(e) => setSelectedTimeframe(e.target.value)}
                >
                  <option value="all">All Data</option>
                  <option value="1m">1 Month</option>
                  <option value="3m">3 Months</option>
                  <option value="6m">6 Months</option>
                  <option value="1y">1 Year</option>
                </select>

                {/* Pattern Visibility Toggle */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showPatterns"
                    checked={showPatterns}
                    onChange={(e) => setShowPatterns(e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="showPatterns" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                    Show Patterns
                  </label>
                </div>

                {/* Pattern Type Filter */}
                {showPatterns && (
                  <div className="relative">
                    <button
                      className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
                      onClick={() => document.getElementById('patternTypeDropdown').classList.toggle('hidden')}
                    >
                      Filter Patterns
                    </button>
                    <div
                      id="patternTypeDropdown"
                      className="absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg z-10 hidden"
                    >
                      <div className="p-2">
                        {['bullish', 'bearish', 'neutral', 'reversal', 'continuation'].map(type => (
                          <div key={type} className="flex items-center mb-1">
                            <input
                              type="checkbox"
                              id={`pattern-${type}`}
                              checked={selectedPatternTypes.includes(type)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedPatternTypes([...selectedPatternTypes, type]);
                                } else {
                                  setSelectedPatternTypes(selectedPatternTypes.filter(t => t !== type));
                                }
                              }}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label htmlFor={`pattern-${type}`} className="ml-2 text-sm text-gray-700 dark:text-gray-300 capitalize">
                              {type}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="h-96">
              <canvas ref={priceChartRef}></canvas>
            </div>

            {/* Pattern Legend */}
            {showPatterns && (
              <div className="mt-4 flex flex-wrap gap-4 justify-center">
                {selectedPatternTypes.map(type => (
                  <div key={type} className="flex items-center">
                    <span
                      className="inline-block w-3 h-3 rounded-full mr-1"
                      style={{ backgroundColor: getPatternColor(type) }}
                    ></span>
                    <span className="text-xs text-gray-700 dark:text-gray-300 capitalize">{type}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Equity Chart */}
      <div className="mb-6">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h3 className="text-lg font-medium mb-3">Equity Curve</h3>
          <div className="h-64">
            <canvas ref={equityChartRef}></canvas>
          </div>
        </div>
      </div>

      {/* Pattern Performance */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">Pattern Performance</h3>

        {/* Pattern Performance Chart */}
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow mb-4">
          <div className="h-64">
            <canvas ref={patternPerformanceChartRef}></canvas>
          </div>
        </div>

        {/* Pattern Performance Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Pattern</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Trades</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Win Rate</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Profit Factor</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Avg. Profit</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {Object.entries(results.patternPerformance).map(([patternId, performance]) => {
                  const patternInfo = getPatternInfo(patternId);
                  const patternName = patternInfo ? patternInfo.name : patternId;

                  return (
                    <tr key={patternId}>
                      <td className="px-4 py-2 whitespace-nowrap text-sm">
                        <div className="flex items-center">
                          <span className={`w-3 h-3 rounded-full mr-2 ${
                            performance.netProfit > 0 ? 'bg-green-500' : 'bg-red-500'
                          }`}></span>
                          {patternName}
                        </div>
                      </td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm">{performance.totalTrades}</td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm">{performance.winRate.toFixed(2)}%</td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm">{performance.profitFactor.toFixed(2)}</td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm">
                        <span className={performance.averageProfit >= 0 ? 'text-green-600' : 'text-red-600'}>
                          ${performance.averageProfit.toFixed(2)}
                        </span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Detailed Statistics */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">Detailed Statistics</h3>
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Symbol</td>
                    <td className="py-2 text-sm font-medium text-right">{results.symbol}</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Timeframe</td>
                    <td className="py-2 text-sm font-medium text-right">{results.timeframe}</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Period</td>
                    <td className="py-2 text-sm font-medium text-right">{results.startDate} to {results.endDate}</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Initial Capital</td>
                    <td className="py-2 text-sm font-medium text-right">${results.initialCapital.toFixed(2)}</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Final Capital</td>
                    <td className="py-2 text-sm font-medium text-right">${results.finalCapital.toFixed(2)}</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Total Trades</td>
                    <td className="py-2 text-sm font-medium text-right">{results.totalTrades}</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Winning Trades</td>
                    <td className="py-2 text-sm font-medium text-right">{results.winningTrades} ({(results.winningTrades / results.totalTrades * 100).toFixed(2)}%)</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Losing Trades</td>
                    <td className="py-2 text-sm font-medium text-right">{results.losingTrades} ({(results.losingTrades / results.totalTrades * 100).toFixed(2)}%)</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div>
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Sharpe Ratio</td>
                    <td className="py-2 text-sm font-medium text-right">{results.sharpeRatio.toFixed(2)}</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Sortino Ratio</td>
                    <td className="py-2 text-sm font-medium text-right">{results.sortinoRatio.toFixed(2)}</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Avg. Return per Trade</td>
                    <td className="py-2 text-sm font-medium text-right">${results.averageReturn.toFixed(2)}</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Avg. Winning Trade</td>
                    <td className="py-2 text-sm font-medium text-right">${results.averageWin.toFixed(2)}</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Avg. Losing Trade</td>
                    <td className="py-2 text-sm font-medium text-right">-${Math.abs(results.averageLoss).toFixed(2)}</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Expectancy</td>
                    <td className="py-2 text-sm font-medium text-right">${results.expectancy.toFixed(2)}</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Avg. Holding Time</td>
                    <td className="py-2 text-sm font-medium text-right">{results.averageHoldingTime}</td>
                  </tr>
                  <tr>
                    <td className="py-2 text-sm text-gray-500 dark:text-gray-400">Max Drawdown</td>
                    <td className="py-2 text-sm font-medium text-right text-red-600">{results.maxDrawdown.toFixed(2)}%</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Export Results */}
      <div className="mb-6">
        <ExportResults results={results} />
      </div>
    </div>
  );
};

export default PatternBacktestResults;
