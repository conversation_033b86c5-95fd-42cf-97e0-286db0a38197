/**
 * Settings Manager for Trading Signals App
 * 
 * This module provides a UI for managing user settings, including:
 * - Data source preferences
 * - API key management
 * - UI preferences
 * - Chart settings
 */

class SettingsManager {
    /**
     * Create a new SettingsManager instance
     */
    constructor() {
        // Default settings
        this.defaultSettings = {
            dataSources: {
                // Use default priorities from data source registry
                priorities: {}
            },
            apiKeys: {
                // Empty by default, will be populated from API key manager
            },
            ui: {
                theme: 'dark',
                language: 'en',
                notifications: true,
                animations: true
            },
            chart: {
                defaultTimeframe: 'M15',
                defaultIndicators: ['ema', 'volume'],
                showPriceLabels: true,
                showGrid: true,
                showCrosshair: true
            },
            advanced: {
                useCache: true,
                logLevel: 'info',
                debugMode: false
            }
        };
        
        // Current settings
        this.settings = this._loadSettings();
        
        // Initialize
        this._init();
    }
    
    /**
     * Initialize the settings manager
     * @private
     */
    _init() {
        // Create settings modal if it doesn't exist
        this._createSettingsModal();
        
        // Add event listeners
        this._addEventListeners();
        
        console.log('Settings manager initialized');
    }
    
    /**
     * Load settings from localStorage
     * @returns {Object} - User settings
     * @private
     */
    _loadSettings() {
        try {
            const savedSettings = localStorage.getItem('user_settings');
            const parsedSettings = savedSettings ? JSON.parse(savedSettings) : {};
            
            // Merge with default settings
            return this._mergeObjects(this.defaultSettings, parsedSettings);
        } catch (error) {
            console.warn('Error loading settings:', error);
            return { ...this.defaultSettings };
        }
    }
    
    /**
     * Save settings to localStorage
     * @private
     */
    _saveSettings() {
        try {
            localStorage.setItem('user_settings', JSON.stringify(this.settings));
        } catch (error) {
            console.warn('Error saving settings:', error);
            
            // Show error notification
            if (window.showNotification) {
                window.showNotification('Error saving settings. Your preferences may not persist when you close the app.', 'error');
            }
        }
    }
    
    /**
     * Merge objects deeply
     * @param {Object} target - Target object
     * @param {Object} source - Source object
     * @returns {Object} - Merged object
     * @private
     */
    _mergeObjects(target, source) {
        const output = { ...target };
        
        if (source && typeof source === 'object' && !Array.isArray(source)) {
            Object.keys(source).forEach(key => {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    if (target[key]) {
                        output[key] = this._mergeObjects(target[key], source[key]);
                    } else {
                        output[key] = { ...source[key] };
                    }
                } else {
                    output[key] = source[key];
                }
            });
        }
        
        return output;
    }
    
    /**
     * Create settings modal
     * @private
     */
    _createSettingsModal() {
        // Check if modal already exists
        if (document.getElementById('settingsModal')) {
            return;
        }
        
        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="settingsModalLabel">Settings</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="data-sources-tab" data-bs-toggle="tab" data-bs-target="#data-sources" type="button" role="tab" aria-controls="data-sources" aria-selected="true">Data Sources</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="api-keys-tab" data-bs-toggle="tab" data-bs-target="#api-keys" type="button" role="tab" aria-controls="api-keys" aria-selected="false">API Keys</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="ui-tab" data-bs-toggle="tab" data-bs-target="#ui" type="button" role="tab" aria-controls="ui" aria-selected="false">UI</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="chart-tab" data-bs-toggle="tab" data-bs-target="#chart" type="button" role="tab" aria-controls="chart" aria-selected="false">Chart</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="advanced-tab" data-bs-toggle="tab" data-bs-target="#advanced" type="button" role="tab" aria-controls="advanced" aria-selected="false">Advanced</button>
                                </li>
                            </ul>
                            <div class="tab-content p-3" id="settingsTabContent">
                                <!-- Data Sources Tab -->
                                <div class="tab-pane fade show active" id="data-sources" role="tabpanel" aria-labelledby="data-sources-tab">
                                    <h6>Data Source Priorities</h6>
                                    <p class="text-muted small">Drag to reorder data sources. The app will try sources in this order.</p>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i> Changes to data source priorities will take effect immediately.
                                    </div>
                                    <div id="dataSourcesList" class="list-group mb-3">
                                        <!-- Data sources will be populated here -->
                                    </div>
                                    <button id="resetDataSourcesBtn" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-undo"></i> Reset to Defaults
                                    </button>
                                </div>
                                
                                <!-- API Keys Tab -->
                                <div class="tab-pane fade" id="api-keys" role="tabpanel" aria-labelledby="api-keys-tab">
                                    <h6>API Keys</h6>
                                    <p class="text-muted small">Enter your API keys for various data providers.</p>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i> API keys are stored in your browser. Never share them with anyone.
                                    </div>
                                    <div id="apiKeysList">
                                        <!-- API keys will be populated here -->
                                    </div>
                                </div>
                                
                                <!-- UI Tab -->
                                <div class="tab-pane fade" id="ui" role="tabpanel" aria-labelledby="ui-tab">
                                    <h6>UI Settings</h6>
                                    <div class="mb-3">
                                        <label class="form-label">Theme</label>
                                        <select id="themeSelect" class="form-select">
                                            <option value="dark">Dark</option>
                                            <option value="light">Light</option>
                                            <option value="system">System Default</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Language</label>
                                        <select id="languageSelect" class="form-select">
                                            <option value="en">English</option>
                                            <option value="ar">Arabic</option>
                                        </select>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="notificationsSwitch">
                                        <label class="form-check-label" for="notificationsSwitch">Show Notifications</label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="animationsSwitch">
                                        <label class="form-check-label" for="animationsSwitch">Enable Animations</label>
                                    </div>
                                </div>
                                
                                <!-- Chart Tab -->
                                <div class="tab-pane fade" id="chart" role="tabpanel" aria-labelledby="chart-tab">
                                    <h6>Chart Settings</h6>
                                    <div class="mb-3">
                                        <label class="form-label">Default Timeframe</label>
                                        <select id="timeframeSelect" class="form-select">
                                            <option value="M1">1 Minute</option>
                                            <option value="M5">5 Minutes</option>
                                            <option value="M15">15 Minutes</option>
                                            <option value="M30">30 Minutes</option>
                                            <option value="H1">1 Hour</option>
                                            <option value="H4">4 Hours</option>
                                            <option value="D1">1 Day</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Default Indicators</label>
                                        <div id="indicatorsList" class="mb-2">
                                            <!-- Indicators will be populated here -->
                                        </div>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="priceLabelsSwitch">
                                        <label class="form-check-label" for="priceLabelsSwitch">Show Price Labels</label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="gridSwitch">
                                        <label class="form-check-label" for="gridSwitch">Show Grid</label>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="crosshairSwitch">
                                        <label class="form-check-label" for="crosshairSwitch">Show Crosshair</label>
                                    </div>
                                </div>
                                
                                <!-- Advanced Tab -->
                                <div class="tab-pane fade" id="advanced" role="tabpanel" aria-labelledby="advanced-tab">
                                    <h6>Advanced Settings</h6>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i> These settings are for advanced users only.
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="cacheSwitch">
                                        <label class="form-check-label" for="cacheSwitch">Use Cache</label>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Log Level</label>
                                        <select id="logLevelSelect" class="form-select">
                                            <option value="error">Error</option>
                                            <option value="warn">Warning</option>
                                            <option value="info">Info</option>
                                            <option value="debug">Debug</option>
                                        </select>
                                    </div>
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="debugModeSwitch">
                                        <label class="form-check-label" for="debugModeSwitch">Debug Mode</label>
                                    </div>
                                    <div class="mb-3">
                                        <button id="clearCacheBtn" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i> Clear Cache
                                        </button>
                                        <button id="exportSettingsBtn" class="btn btn-outline-secondary btn-sm ms-2">
                                            <i class="fas fa-download"></i> Export Settings
                                        </button>
                                        <button id="importSettingsBtn" class="btn btn-outline-secondary btn-sm ms-2">
                                            <i class="fas fa-upload"></i> Import Settings
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" id="saveSettingsBtn">Save Changes</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Create modal element
        const modalElement = document.createElement('div');
        modalElement.innerHTML = modalHtml;
        
        // Add to document
        document.body.appendChild(modalElement.firstElementChild);
    }
    
    /**
     * Add event listeners
     * @private
     */
    _addEventListeners() {
        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', () => {
            // Get modal element
            const modal = document.getElementById('settingsModal');
            if (!modal) return;
            
            // Add event listener for modal show
            modal.addEventListener('show.bs.modal', () => {
                this._populateSettingsForm();
            });
            
            // Add event listener for save button
            const saveButton = document.getElementById('saveSettingsBtn');
            if (saveButton) {
                saveButton.addEventListener('click', () => {
                    this._saveSettingsFromForm();
                    
                    // Close modal
                    if (window.bootstrap && window.bootstrap.Modal) {
                        const modalInstance = window.bootstrap.Modal.getInstance(modal);
                        if (modalInstance) {
                            modalInstance.hide();
                        }
                    }
                    
                    // Show success notification
                    if (window.showNotification) {
                        window.showNotification('Settings saved successfully.', 'success');
                    }
                });
            }
            
            // Add event listener for reset data sources button
            const resetButton = document.getElementById('resetDataSourcesBtn');
            if (resetButton) {
                resetButton.addEventListener('click', () => {
                    this._resetDataSourcePriorities();
                });
            }
            
            // Add event listener for clear cache button
            const clearCacheButton = document.getElementById('clearCacheBtn');
            if (clearCacheButton) {
                clearCacheButton.addEventListener('click', () => {
                    this._clearCache();
                });
            }
            
            // Add event listener for export settings button
            const exportButton = document.getElementById('exportSettingsBtn');
            if (exportButton) {
                exportButton.addEventListener('click', () => {
                    this._exportSettings();
                });
            }
            
            // Add event listener for import settings button
            const importButton = document.getElementById('importSettingsBtn');
            if (importButton) {
                importButton.addEventListener('click', () => {
                    this._importSettings();
                });
            }
        });
    }
    
    /**
     * Populate settings form with current settings
     * @private
     */
    _populateSettingsForm() {
        // Populate data sources list
        this._populateDataSourcesList();
        
        // Populate API keys list
        this._populateApiKeysList();
        
        // Populate UI settings
        this._populateUiSettings();
        
        // Populate chart settings
        this._populateChartSettings();
        
        // Populate advanced settings
        this._populateAdvancedSettings();
    }
    
    /**
     * Populate data sources list
     * @private
     */
    _populateDataSourcesList() {
        const dataSourcesList = document.getElementById('dataSourcesList');
        if (!dataSourcesList) return;
        
        // Clear list
        dataSourcesList.innerHTML = '';
        
        // Get data sources from registry
        if (!window.dataSourceRegistry) return;
        
        const sources = window.dataSourceRegistry.getAllSources();
        
        // Sort by priority
        sources.sort((a, b) => a.priority - b.priority);
        
        // Add each source to the list
        sources.forEach((source, index) => {
            const listItem = document.createElement('div');
            listItem.className = 'list-group-item d-flex justify-content-between align-items-center';
            listItem.dataset.sourceId = source.id;
            listItem.dataset.priority = source.priority;
            
            // Add drag handle
            listItem.innerHTML = `
                <div class="d-flex align-items-center">
                    <span class="drag-handle me-2"><i class="fas fa-grip-vertical text-muted"></i></span>
                    <div>
                        <strong>${source.name}</strong>
                        <div class="text-muted small">${source.description}</div>
                    </div>
                </div>
                <div class="badge bg-secondary">${index + 1}</div>
            `;
            
            dataSourcesList.appendChild(listItem);
        });
        
        // Initialize drag and drop
        this._initializeDragAndDrop();
    }
    
    /**
     * Initialize drag and drop for data sources list
     * @private
     */
    _initializeDragAndDrop() {
        // Check if Sortable is available
        if (!window.Sortable) {
            console.warn('Sortable.js is not available. Drag and drop will not work.');
            return;
        }
        
        const dataSourcesList = document.getElementById('dataSourcesList');
        if (!dataSourcesList) return;
        
        // Initialize Sortable
        const sortable = new Sortable(dataSourcesList, {
            handle: '.drag-handle',
            animation: 150,
            onEnd: (evt) => {
                // Update priorities
                this._updateDataSourcePriorities();
            }
        });
    }
    
    /**
     * Update data source priorities based on current order
     * @private
     */
    _updateDataSourcePriorities() {
        const dataSourcesList = document.getElementById('dataSourcesList');
        if (!dataSourcesList) return;
        
        // Get all items
        const items = Array.from(dataSourcesList.querySelectorAll('.list-group-item'));
        
        // Update priorities
        items.forEach((item, index) => {
            const sourceId = item.dataset.sourceId;
            const priority = (index + 1) * 10; // 10, 20, 30, etc.
            
            // Update badge
            const badge = item.querySelector('.badge');
            if (badge) {
                badge.textContent = index + 1;
            }
            
            // Update data source registry
            if (window.dataSourceRegistry) {
                window.dataSourceRegistry.setSourcePriority(sourceId, priority);
            }
            
            // Update settings
            this.settings.dataSources.priorities[sourceId] = priority;
        });
        
        // Save settings
        this._saveSettings();
    }
    
    /**
     * Reset data source priorities to defaults
     * @private
     */
    _resetDataSourcePriorities() {
        if (window.dataSourceRegistry) {
            window.dataSourceRegistry.resetSourcePriorities();
        }
        
        // Clear priorities in settings
        this.settings.dataSources.priorities = {};
        
        // Save settings
        this._saveSettings();
        
        // Repopulate list
        this._populateDataSourcesList();
        
        // Show success notification
        if (window.showNotification) {
            window.showNotification('Data source priorities reset to defaults.', 'success');
        }
    }
    
    // Other methods will be implemented later
    
    /**
     * Populate API keys list
     * @private
     */
    _populateApiKeysList() {
        // Implementation will be added later
    }
    
    /**
     * Populate UI settings
     * @private
     */
    _populateUiSettings() {
        // Implementation will be added later
    }
    
    /**
     * Populate chart settings
     * @private
     */
    _populateChartSettings() {
        // Implementation will be added later
    }
    
    /**
     * Populate advanced settings
     * @private
     */
    _populateAdvancedSettings() {
        // Implementation will be added later
    }
    
    /**
     * Save settings from form
     * @private
     */
    _saveSettingsFromForm() {
        // Implementation will be added later
    }
    
    /**
     * Clear cache
     * @private
     */
    _clearCache() {
        if (window.enhancedCache) {
            window.enhancedCache.clear();
        }
        
        // Show success notification
        if (window.showNotification) {
            window.showNotification('Cache cleared successfully.', 'success');
        }
    }
    
    /**
     * Export settings
     * @private
     */
    _exportSettings() {
        // Implementation will be added later
    }
    
    /**
     * Import settings
     * @private
     */
    _importSettings() {
        // Implementation will be added later
    }
    
    /**
     * Open settings modal
     * @param {string} tab - Tab to open (data-sources, api-keys, ui, chart, advanced)
     */
    openSettings(tab = 'data-sources') {
        // Check if modal exists
        const modal = document.getElementById('settingsModal');
        if (!modal) return;
        
        // Open modal
        if (window.bootstrap && window.bootstrap.Modal) {
            const modalInstance = new window.bootstrap.Modal(modal);
            modalInstance.show();
            
            // Switch to specified tab
            const tabElement = document.getElementById(`${tab}-tab`);
            if (tabElement) {
                tabElement.click();
            }
        }
    }
}

// Create global instance
window.settingsManager = new SettingsManager();

console.log('Settings manager initialized');
