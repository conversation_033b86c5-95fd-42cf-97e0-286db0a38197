/**
 * Simple Icon Generator for Trading Signals App
 * 
 * This script creates SVG icons and saves them as files.
 * It doesn't require the canvas package, just Node.js fs module.
 */

const fs = require('fs');
const path = require('path');

// Ensure the public directory exists
const publicDir = path.join(__dirname, '../../public');
if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
    console.log('Created public directory');
}

/**
 * Create an SVG icon with the specified size
 * @param {number} size - Icon size in pixels
 */
function createSvgIcon(size) {
    console.log(`Creating ${size}x${size} SVG icon...`);
    
    // Calculate proportions based on size
    const center = size / 2;
    const radius = size / 2;
    const innerRadius = size * 0.45;
    const lineWidth = size * 0.06;
    const dotRadius = size * 0.03;
    
    // Chart line points
    const points = [
        { x: size * 0.2, y: size * 0.6 },
        { x: size * 0.35, y: size * 0.4 },
        { x: size * 0.5, y: size * 0.7 },
        { x: size * 0.65, y: size * 0.3 },
        { x: size * 0.8, y: size * 0.5 }
    ];
    
    // Create SVG content
    const svgContent = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
    <!-- Background circle -->
    <circle cx="${center}" cy="${center}" r="${radius}" fill="#0d6efd" />
    
    <!-- Inner circle -->
    <circle cx="${center}" cy="${center}" r="${innerRadius}" fill="rgba(255, 255, 255, 0.1)" />
    
    <!-- Chart line -->
    <path d="M${points[0].x},${points[0].y} L${points[1].x},${points[1].y} L${points[2].x},${points[2].y} L${points[3].x},${points[3].y} L${points[4].x},${points[4].y}"
          stroke="white" stroke-width="${lineWidth}" stroke-linecap="round" stroke-linejoin="round" fill="none" />
    
    <!-- End dot -->
    <circle cx="${points[4].x}" cy="${points[4].y}" r="${dotRadius}" fill="white" />
    
    <!-- Add filter for shadow effect -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
        <feDropShadow dx="${size * 0.01}" dy="${size * 0.01}" stdDeviation="${size * 0.02}" flood-color="rgba(0,0,0,0.3)" />
    </filter>
</svg>`;
    
    // Save the SVG file
    const svgFilePath = path.join(publicDir, `icon-${size}.svg`);
    fs.writeFileSync(svgFilePath, svgContent);
    console.log(`SVG icon saved to: ${svgFilePath}`);
    
    return svgFilePath;
}

/**
 * Create a simple PNG file with a placeholder message
 * @param {number} size - Icon size in pixels
 */
function createPlaceholderPng(size) {
    const pngFilePath = path.join(publicDir, `icon-${size}.png`);
    
    // Create a text file with instructions
    const instructionsPath = path.join(publicDir, `icon-${size}-instructions.txt`);
    const instructions = `
This is a placeholder for icon-${size}.png

Since we couldn't generate the actual PNG file (requires canvas package),
please use one of these methods to create the icon:

1. Open the generated icon-${size}.svg file in a browser and save it as PNG
2. Use an online SVG to PNG converter with the icon-${size}.svg file
3. Open the generate-icons.html file in a browser to create and download the icons
4. Use an online icon generator like:
   - https://www.favicon-generator.org/
   - https://realfavicongenerator.net/
   - https://www.canva.com/

The icon should be ${size}x${size} pixels with:
- A blue (#0d6efd) circular background
- A white chart line or financial symbol
- Clean, simple design that's recognizable at small sizes
`;
    
    fs.writeFileSync(instructionsPath, instructions);
    console.log(`Instructions saved to: ${instructionsPath}`);
    
    // Create an empty file for the PNG
    fs.writeFileSync(pngFilePath, '');
    console.log(`Placeholder PNG created at: ${pngFilePath}`);
    
    return pngFilePath;
}

// Create icons of different sizes
try {
    // Create SVG versions
    createSvgIcon(192);
    createSvgIcon(512);
    
    // Create placeholder PNGs
    createPlaceholderPng(192);
    createPlaceholderPng(512);
    
    console.log('\nIcon generation complete!');
    console.log('\nIMPORTANT: The PNG files are just placeholders.');
    console.log('Please follow the instructions in the generated text files to create actual PNG icons.');
    console.log('Alternatively, open the public/generate-icons.html file in a browser to create and download the icons.');
} catch (error) {
    console.error('Error generating icons:', error);
}
