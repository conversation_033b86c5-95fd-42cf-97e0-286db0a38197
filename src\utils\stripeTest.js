/**
 * Stripe Test Utility
 * 
 * This file contains functions to test Stripe integration.
 * It should only be used in development to verify that <PERSON><PERSON> is working correctly.
 */

import stripeService from '../services/stripeService.js';
import logger from './logger.js';

/**
 * Create a test customer
 * @returns {Promise<Object>} - Stripe customer object
 */
export const createTestCustomer = async () => {
  try {
    const customer = await stripeService.createCustomer(
      '<EMAIL>',
      'Test Customer',
      { source: 'test', testMode: true }
    );
    
    logger.info('Test customer created:', customer.id);
    return customer;
  } catch (error) {
    logger.error('Error creating test customer:', error);
    throw error;
  }
};

/**
 * Create a test payment intent
 * @returns {Promise<Object>} - Stripe payment intent object
 */
export const createTestPaymentIntent = async () => {
  try {
    const paymentIntent = await stripeService.createPaymentIntent(
      1000, // $10.00
      'usd',
      { source: 'test', testMode: true }
    );
    
    logger.info('Test payment intent created:', paymentIntent.id);
    return paymentIntent;
  } catch (error) {
    logger.error('Error creating test payment intent:', error);
    throw error;
  }
};

/**
 * Create a test webhook event
 * @param {string} type - Event type
 * @param {Object} data - Event data
 * @returns {Object} - Simulated webhook event
 */
export const createTestWebhookEvent = (type, data) => {
  return {
    id: `evt_test_${Date.now()}`,
    object: 'event',
    api_version: '2020-08-27',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: data
    },
    livemode: false,
    pending_webhooks: 0,
    request: {
      id: `req_test_${Date.now()}`,
      idempotency_key: `idempotency_test_${Date.now()}`
    },
    type
  };
};

/**
 * Test webhook handling
 * @param {string} eventType - Event type to test
 * @returns {Promise<Object>} - Result of webhook handling
 */
export const testWebhookHandling = async (eventType = 'payment_intent.succeeded') => {
  try {
    // Create test data based on event type
    let testData;
    
    switch (eventType) {
      case 'payment_intent.succeeded':
        const paymentIntent = await createTestPaymentIntent();
        testData = paymentIntent;
        break;
        
      case 'customer.subscription.created':
        const customer = await createTestCustomer();
        testData = {
          id: `sub_test_${Date.now()}`,
          object: 'subscription',
          customer: customer.id,
          status: 'active',
          current_period_start: Math.floor(Date.now() / 1000),
          current_period_end: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60, // 30 days
          items: {
            object: 'list',
            data: [{
              id: `si_test_${Date.now()}`,
              object: 'subscription_item',
              price: {
                id: 'price_test',
                product: 'prod_test'
              }
            }]
          }
        };
        break;
        
      default:
        testData = { id: `test_${Date.now()}` };
    }
    
    // Create test webhook event
    const event = createTestWebhookEvent(eventType, testData);
    
    // Process the webhook event
    const result = await stripeService.handleWebhookEvent(event);
    
    logger.info(`Test webhook handling result:`, result);
    return result;
  } catch (error) {
    logger.error('Error testing webhook handling:', error);
    throw error;
  }
};

export default {
  createTestCustomer,
  createTestPaymentIntent,
  createTestWebhookEvent,
  testWebhookHandling
};
