import React, { useState, useEffect } from 'react';
import { alphaVantageAPI } from '../../../services/apiService.js';

/**
 * Dashboard Component
 * 
 * Main dashboard showing market overview, recent signals, and performance metrics
 */
const Dashboard = () => {
  const [marketData, setMarketData] = useState({
    majorPairs: [],
    commodities: [],
    indices: [],
    loading: true,
    error: null
  });
  
  const [recentSignals, setRecentSignals] = useState([]);
  const [performanceMetrics, setPerformanceMetrics] = useState({
    winRate: 0,
    avgProfit: 0,
    totalSignals: 0
  });

  // Fetch market data on component mount
  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        // Fetch major forex pairs
        const eurusd = await alphaVantageAPI.get('query', {
          function: 'CURRENCY_EXCHANGE_RATE',
          from_currency: 'EUR',
          to_currency: 'USD'
        });
        
        const gbpusd = await alphaVantageAPI.get('query', {
          function: 'CURRENCY_EXCHANGE_RATE',
          from_currency: 'GBP',
          to_currency: 'USD'
        });
        
        const usdjpy = await alphaVantageAPI.get('query', {
          function: 'CURRENCY_EXCHANGE_RATE',
          from_currency: 'USD',
          to_currency: 'JPY'
        });
        
        // Fetch commodities (using forex endpoint as proxy)
        const gold = await alphaVantageAPI.get('query', {
          function: 'CURRENCY_EXCHANGE_RATE',
          from_currency: 'XAU',
          to_currency: 'USD'
        });
        
        // Format the data
        setMarketData({
          majorPairs: [
            formatCurrencyData(eurusd, 'EUR/USD'),
            formatCurrencyData(gbpusd, 'GBP/USD'),
            formatCurrencyData(usdjpy, 'USD/JPY')
          ],
          commodities: [
            formatCurrencyData(gold, 'GOLD')
          ],
          indices: [],
          loading: false,
          error: null
        });
      } catch (error) {
        console.error('Error fetching market data:', error);
        setMarketData(prev => ({
          ...prev,
          loading: false,
          error: 'Failed to load market data. Please try again later.'
        }));
      }
    };
    
    // Fetch recent signals (mock data for now)
    const fetchRecentSignals = () => {
      // This would be replaced with an actual API call
      const mockSignals = [
        {
          id: 1,
          symbol: 'EUR/USD',
          type: 'BUY',
          entryPrice: 1.0880,
          currentPrice: 1.0910,
          profit: '+0.28%',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          status: 'active'
        },
        {
          id: 2,
          symbol: 'GBP/USD',
          type: 'SELL',
          entryPrice: 1.2650,
          currentPrice: 1.2590,
          profit: '+0.47%',
          timestamp: new Date(Date.now() - 7200000).toISOString(),
          status: 'active'
        },
        {
          id: 3,
          symbol: 'GOLD',
          type: 'BUY',
          entryPrice: 1920.50,
          currentPrice: 1945.20,
          profit: '+1.29%',
          timestamp: new Date(Date.now() - 86400000).toISOString(),
          status: 'active'
        }
      ];
      
      setRecentSignals(mockSignals);
    };
    
    // Fetch performance metrics (mock data for now)
    const fetchPerformanceMetrics = () => {
      // This would be replaced with an actual API call
      setPerformanceMetrics({
        winRate: 68,
        avgProfit: 0.82,
        totalSignals: 125
      });
    };
    
    fetchMarketData();
    fetchRecentSignals();
    fetchPerformanceMetrics();
  }, []);
  
  // Helper function to format currency data
  const formatCurrencyData = (data, symbol) => {
    if (!data || !data['Realtime Currency Exchange Rate']) {
      return {
        symbol,
        price: 'N/A',
        change: 'N/A',
        changePercent: 'N/A'
      };
    }
    
    const exchangeData = data['Realtime Currency Exchange Rate'];
    return {
      symbol,
      price: parseFloat(exchangeData['5. Exchange Rate']).toFixed(4),
      change: '+0.0012', // Mock data, would be calculated from historical
      changePercent: '+0.11%' // Mock data, would be calculated from historical
    };
  };

  return (
    <div className="dashboard p-4">
      <h1 className="text-2xl font-bold mb-6">Trading Dashboard</h1>
      
      {/* Market Overview Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Market Overview</h2>
        
        {marketData.loading ? (
          <div className="loading-spinner">Loading market data...</div>
        ) : marketData.error ? (
          <div className="error-message">{marketData.error}</div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Forex Pairs */}
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
              <h3 className="font-medium mb-2">Major Forex Pairs</h3>
              <div className="space-y-2">
                {marketData.majorPairs.map((pair, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="font-medium">{pair.symbol}</span>
                    <div>
                      <span className="mr-2">{pair.price}</span>
                      <span className={pair.change.startsWith('+') ? 'text-green-500' : 'text-red-500'}>
                        {pair.changePercent}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Commodities */}
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
              <h3 className="font-medium mb-2">Commodities</h3>
              <div className="space-y-2">
                {marketData.commodities.map((commodity, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="font-medium">{commodity.symbol}</span>
                    <div>
                      <span className="mr-2">{commodity.price}</span>
                      <span className={commodity.change.startsWith('+') ? 'text-green-500' : 'text-red-500'}>
                        {commodity.changePercent}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </section>
      
      {/* Recent Signals Section */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Recent Signals</h2>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Symbol</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Entry Price</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Current Price</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Profit/Loss</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Time</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {recentSignals.map((signal) => (
                <tr key={signal.id}>
                  <td className="px-6 py-4 whitespace-nowrap">{signal.symbol}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${signal.type === 'BUY' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'}`}>
                      {signal.type}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">{signal.entryPrice}</td>
                  <td className="px-6 py-4 whitespace-nowrap">{signal.currentPrice}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={signal.profit.startsWith('+') ? 'text-green-500' : 'text-red-500'}>
                      {signal.profit}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {new Date(signal.timestamp).toLocaleTimeString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </section>
      
      {/* Performance Metrics Section */}
      <section>
        <h2 className="text-xl font-semibold mb-4">Performance Metrics</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow text-center">
            <h3 className="text-gray-500 dark:text-gray-400 text-sm uppercase">Win Rate</h3>
            <p className="text-3xl font-bold text-green-500">{performanceMetrics.winRate}%</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow text-center">
            <h3 className="text-gray-500 dark:text-gray-400 text-sm uppercase">Avg. Profit</h3>
            <p className="text-3xl font-bold text-green-500">+{performanceMetrics.avgProfit}%</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow text-center">
            <h3 className="text-gray-500 dark:text-gray-400 text-sm uppercase">Total Signals</h3>
            <p className="text-3xl font-bold">{performanceMetrics.totalSignals}</p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Dashboard;
