/**
 * Trading Signals App - Chart Module
 *
 * This module provides functionality for rendering and updating charts
 * using Chart.js and the chartjs-chart-financial plugin.
 */

// Get the candlestick elements from the financial chart plugin
const { CandlestickController, CandlestickElement } = window.Chart;

// Register the candlestick element
if (CandlestickController && CandlestickElement) {
    Chart.register(CandlestickController, CandlestickElement);
} else {
    console.error('Candlestick chart components not found. Make sure chartjs-chart-financial is loaded.');
}

// Chart configuration
const chartConfig = {
    type: 'candlestick',
    data: {
        datasets: [{
            label: 'EURUSD',
            data: []
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            x: {
                type: 'time',
                time: {
                    unit: 'day',
                    displayFormats: {
                        day: 'MMM d'
                    }
                },
                ticks: {
                    source: 'auto'
                }
            },
            y: {
                type: 'linear',
                position: 'right'
            }
        },
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const point = context.raw;
                        return [
                            `Open: ${point.o}`,
                            `High: ${point.h}`,
                            `Low: ${point.l}`,
                            `Close: ${point.c}`
                        ];
                    }
                }
            }
        }
    }
};

// Chart instance
let chartInstance = null;

// Initialize chart when DOM is loaded
document.addEventListener('DOMContentLoaded', initChart);

/**
 * Initialize chart
 */
function initChart() {
    const chartCanvas = document.getElementById('priceChart');
    if (!chartCanvas) {
        console.warn('Chart canvas not found');
        return;
    }

    // Check if Chart.js is loaded
    if (typeof Chart === 'undefined') {
        console.error('Chart.js not loaded');
        return;
    }

    // Create chart instance
    chartInstance = new Chart(chartCanvas, chartConfig);

    console.log('Chart initialized');
}

/**
 * Update chart with market data
 * @param {Object} data - Market data
 */
function updateChart(data) {
    try {
        if (!chartInstance) {
            console.warn('Chart not initialized, initializing now...');
            initChart();

            if (!chartInstance) {
                console.error('Failed to initialize chart');
                return;
            }
        }

        if (!data || !data.dates || !data.opens || !data.highs || !data.lows || !data.closes) {
            console.warn('Invalid market data for chart update');
            return;
        }

        console.log('Updating chart with new data for', data.symbol);

        // Format data for candlestick chart
        const chartData = formatCandlestickData(data);

        // Update chart dataset
        chartInstance.data.datasets[0].label = data.symbol;
        chartInstance.data.datasets[0].data = chartData;

        // Update x-axis time unit based on timeframe
        updateTimeUnit(data.interval);

        // Update chart
        chartInstance.update();

        console.log('Chart updated successfully');
    } catch (error) {
        console.error('Error updating chart:', error);
    }
}

/**
 * Format market data for candlestick chart - simplified version
 * @param {Object} data - Market data
 * @returns {Array} - Formatted data for candlestick chart
 */
function formatCandlestickData(data) {
    if (!data || !data.dates) return [];

    // Use only the last 30 data points for better performance
    const length = Math.min(data.dates.length, 30);
    const startIdx = data.dates.length - length;

    const chartData = [];

    for (let i = startIdx; i < data.dates.length; i++) {
        chartData.push({
            x: new Date(data.dates[i]),
            o: data.opens[i],
            h: data.highs[i],
            l: data.lows[i],
            c: data.closes[i]
        });
    }

    return chartData;
}

/**
 * Update chart time unit based on timeframe
 * @param {string} interval - Time interval (e.g., M1, M5, M15, M30, H1, H4, D1)
 */
function updateTimeUnit(interval) {
    if (!chartInstance) return;

    let timeUnit = 'day';
    let displayFormat = 'MMM d';

    switch (interval) {
        case 'M1':
            timeUnit = 'minute';
            displayFormat = 'HH:mm';
            break;
        case 'M5':
        case 'M15':
        case 'M30':
            timeUnit = 'minute';
            displayFormat = 'HH:mm';
            break;
        case 'H1':
        case 'H4':
            timeUnit = 'hour';
            displayFormat = 'HH:mm';
            break;
        case 'D1':
            timeUnit = 'day';
            displayFormat = 'MMM d';
            break;
    }

    try {
        // Safely update chart options
        if (chartInstance.options &&
            chartInstance.options.scales &&
            chartInstance.options.scales.x &&
            chartInstance.options.scales.x.time) {

            chartInstance.options.scales.x.time.unit = timeUnit;

            // Make sure displayFormats exists
            if (!chartInstance.options.scales.x.time.displayFormats) {
                chartInstance.options.scales.x.time.displayFormats = {};
            }

            chartInstance.options.scales.x.time.displayFormats[timeUnit] = displayFormat;
        }
    } catch (error) {
        console.error('Error updating chart time unit:', error);
    }
}

/**
 * Add technical indicator to chart
 * @param {string} indicator - Indicator type (e.g., sma, ema, bollinger)
 * @param {Object} params - Indicator parameters
 */
function addIndicator(indicator, params = {}) {
    if (!chartInstance) return;

    // Default parameters
    const defaultParams = {
        period: 14,
        color: 'rgba(255, 99, 132, 1)'
    };

    // Merge default parameters with provided parameters
    const indicatorParams = { ...defaultParams, ...params };

    // Check if indicator dataset already exists
    const existingDatasetIndex = chartInstance.data.datasets.findIndex(
        dataset => dataset.type === indicator
    );

    if (existingDatasetIndex !== -1) {
        // Update existing indicator
        chartInstance.data.datasets[existingDatasetIndex].data = calculateIndicator(
            indicator,
            chartInstance.data.datasets[0].data,
            indicatorParams
        );
    } else {
        // Add new indicator
        chartInstance.data.datasets.push({
            type: 'line',
            label: getIndicatorLabel(indicator, indicatorParams),
            data: calculateIndicator(indicator, chartInstance.data.datasets[0].data, indicatorParams),
            borderColor: indicatorParams.color,
            borderWidth: 1,
            pointRadius: 0,
            fill: false
        });
    }

    // Update chart
    chartInstance.update();
}

/**
 * Calculate technical indicator values
 * @param {string} indicator - Indicator type
 * @param {Array} data - Candlestick data
 * @param {Object} params - Indicator parameters
 * @returns {Array} - Indicator values
 */
function calculateIndicator(indicator, data, params) {
    switch (indicator) {
        case 'sma':
            return calculateSMA(data, params.period);
        case 'ema':
            return calculateEMA(data, params.period);
        case 'bollinger':
            return calculateBollingerBands(data, params.period, params.stdDev);
        default:
            return [];
    }
}

/**
 * Calculate Simple Moving Average (SMA)
 * @param {Array} data - Candlestick data
 * @param {number} period - SMA period
 * @returns {Array} - SMA values
 */
function calculateSMA(data, period) {
    const smaData = [];

    for (let i = 0; i < data.length; i++) {
        if (i < period - 1) {
            // Not enough data points yet
            smaData.push({
                x: data[i].x,
                y: null
            });
        } else {
            // Calculate SMA
            let sum = 0;
            for (let j = 0; j < period; j++) {
                sum += data[i - j].c;
            }

            smaData.push({
                x: data[i].x,
                y: sum / period
            });
        }
    }

    return smaData;
}

/**
 * Calculate Exponential Moving Average (EMA)
 * @param {Array} data - Candlestick data
 * @param {number} period - EMA period
 * @returns {Array} - EMA values
 */
function calculateEMA(data, period) {
    const emaData = [];
    const multiplier = 2 / (period + 1);

    // Calculate SMA for first EMA value
    let sum = 0;
    for (let i = 0; i < period; i++) {
        sum += data[i].c;
    }

    let ema = sum / period;

    for (let i = 0; i < data.length; i++) {
        if (i < period - 1) {
            // Not enough data points yet
            emaData.push({
                x: data[i].x,
                y: null
            });
        } else if (i === period - 1) {
            // First EMA value is SMA
            emaData.push({
                x: data[i].x,
                y: ema
            });
        } else {
            // Calculate EMA
            ema = (data[i].c - ema) * multiplier + ema;

            emaData.push({
                x: data[i].x,
                y: ema
            });
        }
    }

    return emaData;
}

/**
 * Get indicator label
 * @param {string} indicator - Indicator type
 * @param {Object} params - Indicator parameters
 * @returns {string} - Indicator label
 */
function getIndicatorLabel(indicator, params) {
    switch (indicator) {
        case 'sma':
            return `SMA(${params.period})`;
        case 'ema':
            return `EMA(${params.period})`;
        case 'bollinger':
            return `Bollinger(${params.period}, ${params.stdDev})`;
        default:
            return indicator;
    }
}

// Export functions for external use
window.chartModule = {
    updateChart,
    addIndicator
};

// Export updateChart function for compatibility with app.js
window.updateChart = updateChart;
