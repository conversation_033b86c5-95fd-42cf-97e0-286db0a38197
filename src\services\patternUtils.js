// Pattern Recognition Utilities

/**
 * Check if two levels are similar within a given tolerance (percentage)
 * @param {number} a
 * @param {number} b
 * @param {number} tolerance - e.g. 0.03 for 3%
 * @returns {boolean}
 */
export function areLevelsSimilar(a, b, tolerance = 0.03) {
  return Math.abs(a - b) / ((a + b) / 2) < tolerance;
}

/**
 * Check if two indices are separated by at least minSeparation
 * @param {number} indexA
 * @param {number} indexB
 * @param {number} minSeparation
 * @returns {boolean}
 */
export function isSeparated(indexA, indexB, minSeparation = 5) {
  return Math.abs(indexB - indexA) >= minSeparation;
}

/**
 * Find peaks in data
 * @param {Array} data
 * @param {string} valueKey - 'high' or 'low'
 * @param {number} window
 * @returns {Array} - Indices of peaks
 */
export function findPeaks(data, valueKey = 'high', window = 4) {
  const peaks = [];
  for (let i = window; i < data.length - window; i++) {
    let isPeak = true;
    for (let j = i - window; j <= i + window; j++) {
      if (j !== i && data[j][valueKey] >= data[i][valueKey]) {
        isPeak = false;
        break;
      }
    }
    if (isPeak) peaks.push(i);
  }
  return peaks;
}

/**
 * Find troughs in data
 * @param {Array} data
 * @param {string} valueKey - 'low'
 * @param {number} window
 * @returns {Array} - Indices of troughs
 */
export function findTroughs(data, valueKey = 'low', window = 4) {
  const troughs = [];
  for (let i = window; i < data.length - window; i++) {
    let isTrough = true;
    for (let j = i - window; j <= i + window; j++) {
      if (j !== i && data[j][valueKey] <= data[i][valueKey]) {
        isTrough = false;
        break;
      }
    }
    if (isTrough) troughs.push(i);
  }
  return troughs;
}

/**
 * Calculate trend line slope and intercept
 * @param {Array} points - Indices
 * @param {Array} data
 * @param {string} valueKey - 'high' or 'low'
 * @returns {{slope: number, intercept: number}}
 */
export function calculateTrendLine(points, data, valueKey = 'high') {
  if (points.length < 2) return { slope: 0, intercept: 0 };
  const x1 = points[0], x2 = points[1];
  const y1 = data[x1][valueKey], y2 = data[x2][valueKey];
  const slope = (y2 - y1) / (x2 - x1);
  const intercept = y1 - slope * x1;
  return { slope, intercept };
} 