/**
 * Unified Signal Service
 *
 * Combines AI-generated signals with traditional technical analysis signals
 * Provides signal ranking, validation, and performance tracking
 */

const logger = require('../../logging');
const { generateSignals } = require('../../utils/signals');
const {
  SIGNAL_TYPES,
  SIGNAL_SOURCES,
  TIMEFRAMES,
  SIGNAL_STATUS,
  SignalTransformers
} = require('../schemas/unifiedSignalSchema');

class UnifiedSignalService {
  constructor(consolidatedOpenAIService, mongoService) {
    this.aiService = consolidatedOpenAIService;
    this.mongoService = mongoService;
    this.signalCache = new Map();
    this.performanceMetrics = new Map();
  }

  /**
   * Generate unified signals combining AI and traditional analysis
   * @param {Object} params - Signal generation parameters
   * @param {string} params.symbol - Trading symbol
   * @param {Array} params.marketData - Historical price data
   * @param {Object} params.indicators - Technical indicators
   * @param {Object} params.options - Generation options
   * @returns {Promise<Object>} Unified signals with confidence scores
   */
  async generateUnifiedSignals(params) {
    const { symbol, marketData, indicators, options = {} } = params;

    try {
      logger.info(`Generating unified signals for ${symbol}`);

      // Generate traditional signals
      const traditionalSignals = this.generateTraditionalSignals(marketData);

      // Generate AI signals
      const aiSignals = await this.generateAISignals(symbol, marketData, indicators, options);

      // Combine and rank signals
      const unifiedSignals = this.combineSignals(traditionalSignals, aiSignals, symbol);

      // Validate and filter signals
      const validatedSignals = this.validateSignals(unifiedSignals);

      // Add confidence scoring
      const scoredSignals = this.addConfidenceScoring(validatedSignals);

      // Cache signals for performance tracking
      this.cacheSignals(symbol, scoredSignals);

      return {
        success: true,
        signals: scoredSignals,
        metadata: {
          symbol,
          timestamp: new Date().toISOString(),
          traditionalCount: traditionalSignals.signals?.length || 0,
          aiCount: aiSignals.length || 0,
          totalCount: scoredSignals.length,
          averageConfidence: this.calculateAverageConfidence(scoredSignals)
        }
      };

    } catch (error) {
      logger.error(`Error generating unified signals for ${symbol}:`, error);
      return {
        success: false,
        error: error.message,
        signals: [],
        metadata: {
          symbol,
          timestamp: new Date().toISOString(),
          error: true
        }
      };
    }
  }

  /**
   * Generate traditional technical analysis signals
   * @param {Array} marketData - Historical price data
   * @returns {Object} Traditional signals
   */
  generateTraditionalSignals(marketData) {
    try {
      const result = generateSignals(marketData);

      // Transform traditional signals to unified format
      const transformedSignals = result.signals?.map(signal => {
        const unifiedSignal = {
          id: this.generateSignalId(),
          type: signal.type.toUpperCase(),
          source: SIGNAL_SOURCES.TRADITIONAL,
          subSource: signal.source,
          strength: signal.strength,
          message: signal.message,
          reasoning: signal.message,
          confidence: Math.round(signal.strength * 100),
          timestamp: new Date().toISOString(),
          timeframe: 'H1', // Default timeframe
          entryPrice: 0, // Will be set by caller
          status: SIGNAL_STATUS.ACTIVE,
          technicalIndicators: result.analysis
        };

        // Validate and transform using unified schema
        try {
          return SignalTransformers.validate(unifiedSignal);
        } catch (error) {
          logger.warn('Traditional signal validation failed:', error.message);
          return null;
        }
      }).filter(signal => signal !== null) || [];

      return {
        signals: transformedSignals,
        analysis: result.analysis
      };
    } catch (error) {
      logger.error('Error generating traditional signals:', error);
      return { signals: [], analysis: null };
    }
  }

  /**
   * Generate AI-powered signals
   * @param {string} symbol - Trading symbol
   * @param {Array} marketData - Historical price data
   * @param {Object} indicators - Technical indicators
   * @param {Object} options - AI generation options
   * @returns {Promise<Array>} AI-generated signals
   */
  async generateAISignals(symbol, marketData, indicators, options) {
    try {
      if (!this.aiService) {
        logger.warn('AI service not available, skipping AI signal generation');
        return [];
      }

      const currentPrice = marketData[0]?.close || 0;
      const volume = marketData[0]?.volume || 0;

      const aiResult = await this.aiService.generateTradingSignal(
        { symbol, currentPrice, volume },
        indicators,
        {
          enableFallback: true,
          temperature: 0.3,
          ...options
        }
      );

      if (!aiResult || !aiResult.data) {
        return [];
      }

      // Transform AI result to unified format
      const aiSignal = {
        id: this.generateSignalId(),
        symbol: symbol,
        type: aiResult.data.signal?.toUpperCase() || SIGNAL_TYPES.HOLD,
        source: SIGNAL_SOURCES.AI,
        subSource: 'openai',
        strength: aiResult.data.confidence || 0.5,
        message: aiResult.data.reasoning || 'AI-generated signal',
        reasoning: aiResult.data.analysis || aiResult.data.reasoning || 'AI analysis',
        confidence: Math.round((aiResult.data.confidence || 0.5) * 100),
        timestamp: new Date().toISOString(),
        timeframe: options.timeframe || TIMEFRAMES.H1,
        entryPrice: aiResult.data.entryPrice || currentPrice,
        stopLoss: aiResult.data.stopLoss,
        takeProfit: aiResult.data.takeProfit,
        status: SIGNAL_STATUS.ACTIVE,
        metadata: {
          model: aiResult._metadata?.model,
          processingTime: aiResult._metadata?.processingTime,
          tokens: aiResult._metadata?.tokens
        }
      };

      // Validate AI signal using unified schema
      try {
        const validatedSignal = SignalTransformers.validate(aiSignal);
        return [validatedSignal];
      } catch (error) {
        logger.warn('AI signal validation failed:', error.message);
        return [];
      }
    } catch (error) {
      logger.error('Error generating AI signals:', error);
      return [];
    }
  }

  /**
   * Combine traditional and AI signals with conflict resolution
   * @param {Object} traditionalResult - Traditional signals result
   * @param {Array} aiSignals - AI signals array
   * @param {string} symbol - Trading symbol
   * @returns {Array} Combined signals
   */
  combineSignals(traditionalResult, aiSignals, symbol) {
    const allSignals = [
      ...(traditionalResult.signals || []),
      ...(aiSignals || [])
    ];

    // Add symbol to all signals
    allSignals.forEach(signal => {
      signal.symbol = symbol;
    });

    // Sort by confidence (highest first)
    allSignals.sort((a, b) => b.confidence - a.confidence);

    // Resolve conflicts (if AI and traditional signals contradict)
    return this.resolveSignalConflicts(allSignals);
  }

  /**
   * Resolve conflicts between different signal sources
   * @param {Array} signals - All signals
   * @returns {Array} Resolved signals
   */
  resolveSignalConflicts(signals) {
    const buySignals = signals.filter(s => s.type === 'BUY');
    const sellSignals = signals.filter(s => s.type === 'SELL');
    const holdSignals = signals.filter(s => s.type === 'HOLD');

    // If we have conflicting signals, prioritize by confidence and source
    if (buySignals.length > 0 && sellSignals.length > 0) {
      const highestBuy = buySignals[0];
      const highestSell = sellSignals[0];

      // If confidence difference is small, mark as conflicted
      if (Math.abs(highestBuy.confidence - highestSell.confidence) < 10) {
        return [{
          id: this.generateSignalId(),
          type: 'HOLD',
          source: 'UNIFIED',
          subSource: 'conflict-resolution',
          strength: 0.5,
          message: 'Conflicting signals detected - recommend holding',
          reasoning: `Conflicting signals: ${highestBuy.source} suggests BUY (${highestBuy.confidence}%), ${highestSell.source} suggests SELL (${highestSell.confidence}%)`,
          confidence: 50,
          timestamp: new Date().toISOString(),
          conflictData: {
            buySignal: highestBuy,
            sellSignal: highestSell
          }
        }];
      }
    }

    // Return top 3 signals to avoid noise
    return signals.slice(0, 3);
  }

  /**
   * Validate signals for quality and consistency
   * @param {Array} signals - Signals to validate
   * @returns {Array} Validated signals
   */
  validateSignals(signals) {
    return signals.filter(signal => {
      // Basic validation
      if (!signal.type || !signal.confidence || !signal.source) {
        return false;
      }

      // Confidence threshold
      if (signal.confidence < 30) {
        return false;
      }

      // Type validation
      if (!['BUY', 'SELL', 'HOLD'].includes(signal.type)) {
        return false;
      }

      return true;
    });
  }

  /**
   * Add enhanced confidence scoring
   * @param {Array} signals - Signals to score
   * @returns {Array} Signals with enhanced confidence scores
   */
  addConfidenceScoring(signals) {
    return signals.map(signal => {
      let adjustedConfidence = signal.confidence;

      // Boost confidence for AI signals with high certainty
      if (signal.source === 'AI' && signal.confidence > 80) {
        adjustedConfidence = Math.min(95, adjustedConfidence + 5);
      }

      // Boost confidence when multiple sources agree
      const sameTypeSignals = signals.filter(s => s.type === signal.type);
      if (sameTypeSignals.length > 1) {
        adjustedConfidence = Math.min(95, adjustedConfidence + 10);
      }

      return {
        ...signal,
        originalConfidence: signal.confidence,
        confidence: Math.round(adjustedConfidence),
        qualityScore: this.calculateQualityScore(signal)
      };
    });
  }

  /**
   * Calculate quality score for a signal
   * @param {Object} signal - Signal to score
   * @returns {number} Quality score (0-100)
   */
  calculateQualityScore(signal) {
    let score = signal.confidence;

    // Bonus for AI signals with detailed reasoning
    if (signal.source === 'AI' && signal.reasoning && signal.reasoning.length > 50) {
      score += 5;
    }

    // Bonus for traditional signals with multiple indicators
    if (signal.source === 'TRADITIONAL' && signal.technicalIndicators) {
      score += 3;
    }

    // Penalty for very recent duplicate signals
    const recentSignals = this.getRecentSignals(signal.symbol, 300000); // 5 minutes
    const duplicates = recentSignals.filter(s => s.type === signal.type && s.source === signal.source);
    if (duplicates.length > 0) {
      score -= 10;
    }

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Cache signals for performance tracking
   * @param {string} symbol - Trading symbol
   * @param {Array} signals - Signals to cache
   */
  cacheSignals(symbol, signals) {
    const cacheKey = `${symbol}_${Date.now()}`;
    this.signalCache.set(cacheKey, {
      symbol,
      signals,
      timestamp: new Date(),
      ttl: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
    });

    // Clean old cache entries
    this.cleanCache();
  }

  /**
   * Get recent signals for a symbol
   * @param {string} symbol - Trading symbol
   * @param {number} timeWindow - Time window in milliseconds
   * @returns {Array} Recent signals
   */
  getRecentSignals(symbol, timeWindow = 3600000) { // 1 hour default
    const cutoff = Date.now() - timeWindow;
    const recentSignals = [];

    for (const [key, cache] of this.signalCache.entries()) {
      if (cache.symbol === symbol && cache.timestamp.getTime() > cutoff) {
        recentSignals.push(...cache.signals);
      }
    }

    return recentSignals;
  }

  /**
   * Calculate average confidence of signals
   * @param {Array} signals - Signals array
   * @returns {number} Average confidence
   */
  calculateAverageConfidence(signals) {
    if (!signals || signals.length === 0) return 0;
    const total = signals.reduce((sum, signal) => sum + signal.confidence, 0);
    return Math.round(total / signals.length);
  }

  /**
   * Generate unique signal ID
   * @returns {string} Unique signal ID
   */
  generateSignalId() {
    return `signal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Clean expired cache entries
   */
  cleanCache() {
    const now = Date.now();
    for (const [key, cache] of this.signalCache.entries()) {
      if (cache.ttl < now) {
        this.signalCache.delete(key);
      }
    }
  }
}

module.exports = { UnifiedSignalService };
