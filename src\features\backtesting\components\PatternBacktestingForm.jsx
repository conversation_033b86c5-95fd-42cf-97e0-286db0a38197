import React, { useState } from 'react';
import { getPatternsByType, getPatternTypes } from '../../../utils/candlestickPatterns';

/**
 * PatternBacktestingForm Component
 *
 * Form for configuring pattern-based backtesting parameters
 */
const PatternBacktestingForm = ({ onRunBacktest, isLoading }) => {
  // State for backtest configuration
  const [config, setConfig] = useState({
    symbol: 'EURUSD',
    timeframe: 'H1',
    startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 90 days ago
    endDate: new Date().toISOString().split('T')[0], // Today
    initialCapital: 10000,
    positionSize: 'fixed', // 'fixed', 'percentage', 'risk_based'
    fixedSize: 0.1, // For fixed position size (in lots)
    percentageSize: 2, // For percentage position size (% of capital)
    riskPercentage: 1, // For risk-based position size (% of capital to risk per trade)
    stopLoss: 50, // In pips
    takeProfit: 100, // In pips
    maxOpenTrades: 3,
    patternTypes: ['bullish', 'bearish'], // Selected pattern types
    selectedPatterns: [], // Selected specific patterns
    confirmationIndicators: [], // Additional indicators for confirmation
    entryDelay: 1, // Candles to wait before entry
    exitStrategy: 'take_profit_stop_loss', // 'take_profit_stop_loss', 'trailing_stop', 'time_based', 'opposite_pattern'
    trailingStop: 30, // In pips
    timeBasedExit: 10, // Exit after X candles
    minPatternSignificance: 5, // Minimum pattern significance (1-10)
    useSupport: true, // Use support/resistance levels
    useVolume: true, // Consider volume in pattern detection
    useTrend: true, // Consider overall trend

    // Machine Learning options
    useML: false, // Use machine learning for pattern detection
    mlModelType: 'cnn', // 'cnn', 'lstm'
    mlConfidenceThreshold: 0.7, // Minimum confidence threshold for ML patterns (0-1)
  });

  // Available timeframes
  const timeframes = [
    { value: 'M1', label: '1 Minute' },
    { value: 'M5', label: '5 Minutes' },
    { value: 'M15', label: '15 Minutes' },
    { value: 'M30', label: '30 Minutes' },
    { value: 'H1', label: '1 Hour' },
    { value: 'H4', label: '4 Hours' },
    { value: 'D1', label: 'Daily' },
    { value: 'W1', label: 'Weekly' },
    { value: 'MN', label: 'Monthly' }
  ];

  // Available symbols
  const symbols = [
    { value: 'EURUSD', label: 'EUR/USD' },
    { value: 'GBPUSD', label: 'GBP/USD' },
    { value: 'USDJPY', label: 'USD/JPY' },
    { value: 'AUDUSD', label: 'AUD/USD' },
    { value: 'USDCAD', label: 'USD/CAD' },
    { value: 'NZDUSD', label: 'NZD/USD' },
    { value: 'USDCHF', label: 'USD/CHF' },
    { value: 'EURGBP', label: 'EUR/GBP' },
    { value: 'BTCUSD', label: 'BTC/USD' },
    { value: 'ETHUSD', label: 'ETH/USD' }
  ];

  // Available pattern types
  const patternTypes = getPatternTypes().map(type => ({
    value: type,
    label: type.charAt(0).toUpperCase() + type.slice(1) // Capitalize first letter
  }));

  // Available patterns based on selected types
  const availablePatterns = config.patternTypes.flatMap(type =>
    getPatternsByType(type).map(pattern => ({
      value: pattern.id,
      label: pattern.name,
      type: pattern.type
    }))
  );

  // Available confirmation indicators
  const confirmationIndicators = [
    { value: 'macd', label: 'MACD' },
    { value: 'rsi', label: 'RSI' },
    { value: 'stochastic', label: 'Stochastic' },
    { value: 'bollinger', label: 'Bollinger Bands' },
    { value: 'volume', label: 'Volume' },
    { value: 'ema', label: 'EMA' },
    { value: 'support_resistance', label: 'Support/Resistance' }
  ];

  // Available exit strategies
  const exitStrategies = [
    { value: 'take_profit_stop_loss', label: 'Take Profit / Stop Loss' },
    { value: 'trailing_stop', label: 'Trailing Stop' },
    { value: 'time_based', label: 'Time-Based' },
    { value: 'opposite_pattern', label: 'Opposite Pattern' }
  ];

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      setConfig(prev => ({ ...prev, [name]: checked }));
    } else if (name === 'patternTypes') {
      // Handle multi-select for pattern types
      const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
      setConfig(prev => ({
        ...prev,
        patternTypes: selectedOptions,
        // Reset selected patterns when pattern types change
        selectedPatterns: []
      }));
    } else if (name === 'selectedPatterns') {
      // Handle multi-select for specific patterns
      const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
      setConfig(prev => ({ ...prev, selectedPatterns: selectedOptions }));
    } else if (name === 'confirmationIndicators') {
      // Handle multi-select for confirmation indicators
      const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
      setConfig(prev => ({ ...prev, confirmationIndicators: selectedOptions }));
    } else if (type === 'number') {
      setConfig(prev => ({ ...prev, [name]: parseFloat(value) }));
    } else {
      setConfig(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    onRunBacktest(config);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Market Data Section */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-4">Market Data</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Symbol</label>
            <select
              name="symbol"
              value={config.symbol}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              required
            >
              {symbols.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Timeframe</label>
            <select
              name="timeframe"
              value={config.timeframe}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              required
            >
              {timeframes.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Start Date</label>
            <input
              type="date"
              name="startDate"
              value={config.startDate}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">End Date</label>
            <input
              type="date"
              name="endDate"
              value={config.endDate}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              required
            />
          </div>
        </div>
      </div>

      {/* Pattern Selection Section */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-4">Pattern Selection</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Pattern Types</label>
            <select
              name="patternTypes"
              value={config.patternTypes}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              multiple
              size="4"
            >
              {patternTypes.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Hold Ctrl/Cmd to select multiple</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Specific Patterns</label>
            <select
              name="selectedPatterns"
              value={config.selectedPatterns}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              multiple
              size="4"
              disabled={config.patternTypes.length === 0}
            >
              {availablePatterns.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Leave empty to include all patterns of selected types</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Minimum Pattern Significance (1-10)</label>
            <input
              type="number"
              name="minPatternSignificance"
              value={config.minPatternSignificance}
              onChange={handleChange}
              min="1"
              max="10"
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Entry Delay (Candles)</label>
            <input
              type="number"
              name="entryDelay"
              value={config.entryDelay}
              onChange={handleChange}
              min="0"
              max="10"
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Candles to wait after pattern before entry</p>
          </div>
        </div>

        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              name="useSupport"
              checked={config.useSupport}
              onChange={handleChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Use Support/Resistance
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              name="useVolume"
              checked={config.useVolume}
              onChange={handleChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Consider Volume
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              name="useTrend"
              checked={config.useTrend}
              onChange={handleChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Consider Overall Trend
            </label>
          </div>
        </div>

        {/* Machine Learning Options */}
        <div className="mt-4">
          <h4 className="text-md font-medium mb-2">Machine Learning Options</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                name="useML"
                checked={config.useML}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Use Machine Learning
              </label>
            </div>

            {config.useML && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ML Model Type</label>
                  <select
                    name="mlModelType"
                    value={config.mlModelType}
                    onChange={handleChange}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  >
                    <option value="cnn">CNN (Convolutional Neural Network)</option>
                    <option value="lstm">LSTM (Long Short-Term Memory)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    ML Confidence Threshold ({(config.mlConfidenceThreshold * 100).toFixed(0)}%)
                  </label>
                  <input
                    type="range"
                    name="mlConfidenceThreshold"
                    value={config.mlConfidenceThreshold}
                    onChange={handleChange}
                    min="0.5"
                    max="0.95"
                    step="0.05"
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Trading Parameters Section */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-4">Trading Parameters</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Initial Capital</label>
            <input
              type="number"
              name="initialCapital"
              value={config.initialCapital}
              onChange={handleChange}
              min="100"
              step="100"
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Position Size Type</label>
            <select
              name="positionSize"
              value={config.positionSize}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="fixed">Fixed (Lots)</option>
              <option value="percentage">Percentage of Capital</option>
              <option value="risk_based">Risk-Based</option>
            </select>
          </div>

          {config.positionSize === 'fixed' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fixed Size (Lots)</label>
              <input
                type="number"
                name="fixedSize"
                value={config.fixedSize}
                onChange={handleChange}
                min="0.01"
                step="0.01"
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          )}

          {config.positionSize === 'percentage' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Percentage of Capital (%)</label>
              <input
                type="number"
                name="percentageSize"
                value={config.percentageSize}
                onChange={handleChange}
                min="0.1"
                max="100"
                step="0.1"
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          )}

          {config.positionSize === 'risk_based' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Risk Percentage (%)</label>
              <input
                type="number"
                name="riskPercentage"
                value={config.riskPercentage}
                onChange={handleChange}
                min="0.1"
                max="10"
                step="0.1"
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Max Open Trades</label>
            <input
              type="number"
              name="maxOpenTrades"
              value={config.maxOpenTrades}
              onChange={handleChange}
              min="1"
              max="10"
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>
        </div>
      </div>

      {/* Exit Strategy Section */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-4">Exit Strategy</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Exit Strategy</label>
            <select
              name="exitStrategy"
              value={config.exitStrategy}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              {exitStrategies.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>

          {(config.exitStrategy === 'take_profit_stop_loss' || config.exitStrategy === 'trailing_stop') && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Stop Loss (Pips)</label>
                <input
                  type="number"
                  name="stopLoss"
                  value={config.stopLoss}
                  onChange={handleChange}
                  min="5"
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Take Profit (Pips)</label>
                <input
                  type="number"
                  name="takeProfit"
                  value={config.takeProfit}
                  onChange={handleChange}
                  min="5"
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                />
              </div>
            </>
          )}

          {config.exitStrategy === 'trailing_stop' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Trailing Stop (Pips)</label>
              <input
                type="number"
                name="trailingStop"
                value={config.trailingStop}
                onChange={handleChange}
                min="5"
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          )}

          {config.exitStrategy === 'time_based' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Exit After (Candles)</label>
              <input
                type="number"
                name="timeBasedExit"
                value={config.timeBasedExit}
                onChange={handleChange}
                min="1"
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
          )}
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <button
          type="submit"
          className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-md shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={isLoading}
        >
          {isLoading ? 'Running Backtest...' : 'Run Backtest'}
        </button>
      </div>
    </form>
  );
};

export default PatternBacktestingForm;
