import { APIError } from '../middleware/errorHandler.js';
import User from '../models/User.js';
import logger from '../utils/logger.js';

class AuthService {
  // Register a new user
  async register(userData) {
    try {
      // Check if user already exists
      const existing = await User.findOne({ email: userData.email });
      if (existing) {
        throw new APIError(400, 'User already exists');
      }

      // Create new user
      const user = new User({
        email: userData.email,
        password: userData.password,
        name: userData.name,
      });
      await user.save();

      // Generate token
      const token = user.generateToken();
      logger.info('New user registered:', { email: user.email });
      return {
        user: user.toJSON(),
        token,
      };
    } catch (error) {
      logger.error('Error registering user:', error);
      throw error;
    }
  }

  // Login user
  async login(email, password) {
    try {
      // Get user
      const user = await User.findOne({ email });
      if (!user) {
        throw new APIError(401, 'Invalid credentials');
      }

      // Check password
      const isMatch = await user.matchPassword(password);
      if (!isMatch) {
        throw new APIError(401, 'Invalid credentials');
      }

      // Update last login
      user.lastLogin = new Date();
      await user.save();

      // Generate token
      const token = user.generateToken();
      logger.info('User logged in:', { email: user.email });
      return {
        user: user.toJSON(),
        token,
      };
    } catch (error) {
      logger.error('Error logging in user:', error);
      throw error;
    }
  }

  // Get user by ID
  async getUserById(id) {
    try {
      const user = await User.findById(id);
      if (!user) {
        throw new APIError(404, 'User not found');
      }
      return user.toJSON();
    } catch (error) {
      logger.error('Error getting user:', error);
      throw error;
    }
  }

  // Update user
  async updateUser(id, updateData) {
    try {
      const user = await User.findById(id);
      if (!user) {
        throw new APIError(404, 'User not found');
      }
      if (updateData.name) user.name = updateData.name;
      if (updateData.password) user.password = updateData.password;
      if (updateData.avatar !== undefined) user.avatar = updateData.avatar;
      if (updateData.preferences !== undefined) user.preferences = updateData.preferences;
      await user.save();
      logger.info('User updated:', { email: user.email });
      return user.toJSON();
    } catch (error) {
      logger.error('Error updating user:', error);
      throw error;
    }
  }

  // Delete user
  async deleteUser(id) {
    try {
      const user = await User.findById(id);
      if (!user) {
        throw new APIError(404, 'User not found');
      }
      await user.deleteOne();
      logger.info('User deleted:', { email: user.email });
      return { success: true };
    } catch (error) {
      logger.error('Error deleting user:', error);
      throw error;
    }
  }
}

export default new AuthService();