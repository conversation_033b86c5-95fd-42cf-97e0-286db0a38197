# Dependency directories
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build directories
/dist/
/build/

# Cache
.cache/
.npm/
.temp/

# Log files
logs/
*.log

# Operating System Files
.DS_Store
Thumbs.db
Desktop.ini

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*.sublime-workspace
*.sublime-project

# Temporary files
*.tmp
*.bak
*.backup

# Node modules and dependencies
node_modules/

# Build artifacts
dist/
build/
.next/
.nuxt/
out/

# Test files and folders
test/
tests/
__tests__/
*.spec.js
*.test.js
legacy/

# Editor and IDE configs
.vscode/
.idea/

# Environment files
.env
.env.local
.env.development

# Version control
.git/
.github/
.gitlab/

# Large media files
*.mp4
*.mov
*.zip
*.tar.gz
*.psd
*.ai

# Documentation (optional)
*.md
LICENSE
