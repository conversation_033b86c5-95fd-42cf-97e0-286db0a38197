/**
 * WebSocket Client Service
 * 
 * Standalone WebSocket client for use outside of React components.
 * Provides connection management, message handling, and subscription management.
 * 
 * Features:
 * - Automatic reconnection with exponential backoff
 * - Message queuing during disconnections
 * - Type-safe message handling
 * - Event-driven architecture
 * - Connection health monitoring
 * - Rate limiting protection
 * 
 * @version 1.0.0
 */

import { EventEmitter } from 'events';
import {
  WebSocketMessage,
  WebSocketMessageType,
  WebSocketConnectionState,
  WebSocketChannel,
  createWebSocketMessage,
  isWebSocketMessage
} from '../types/websocket';
import { formatTimestamp } from '../types/common';

// ============================================================================
// CLIENT CONFIGURATION INTERFACE
// ============================================================================

export interface WebSocketClientConfig {
  url: string;
  protocols?: string[];
  reconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  timeout?: number;
  maxQueueSize?: number;
  rateLimitWindow?: number;
  rateLimitMax?: number;
}

// ============================================================================
// CLIENT EVENTS INTERFACE
// ============================================================================

export interface WebSocketClientEvents {
  'connected': () => void;
  'disconnected': (reason: string) => void;
  'error': (error: Error) => void;
  'message': (message: WebSocketMessage) => void;
  'signal_update': (signal: any) => void;
  'market_update': (data: any) => void;
  'notification': (notification: any) => void;
  'connection_state_changed': (state: WebSocketConnectionState) => void;
}

// ============================================================================
// WEBSOCKET CLIENT CLASS
// ============================================================================

export class WebSocketClient extends EventEmitter {
  private config: Required<WebSocketClientConfig>;
  private ws: WebSocket | null = null;
  private connectionState: WebSocketConnectionState;
  private messageQueue: WebSocketMessage[] = [];
  private subscriptions: Set<string> = new Set();
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private lastHeartbeat = 0;
  private rateLimitCounter = 0;
  private rateLimitWindow = 0;

  // Metrics
  private metrics = {
    messagesSent: 0,
    messagesReceived: 0,
    reconnections: 0,
    errors: 0,
    bytesTransferred: 0,
    averageLatency: 0,
    connectionUptime: 0,
    lastConnected: null as string | null
  };

  constructor(config: WebSocketClientConfig) {
    super();
    
    this.config = {
      protocols: [],
      reconnect: true,
      reconnectInterval: 5000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      timeout: 60000,
      maxQueueSize: 100,
      rateLimitWindow: 60000, // 1 minute
      rateLimitMax: 100, // 100 messages per minute
      ...config
    };

    this.connectionState = {
      connected: false,
      connecting: false,
      error: undefined,
      lastConnected: undefined,
      reconnectAttempts: 0
    };

    this.setupRateLimitReset();
  }

  // ========================================================================
  // CONNECTION MANAGEMENT
  // ========================================================================

  public connect(): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      return;
    }

    this.updateConnectionState({ connecting: true, error: undefined });

    try {
      this.ws = new WebSocket(this.config.url, this.config.protocols);
      this.setupEventHandlers();
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  public disconnect(): void {
    this.clearTimeouts();
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }

    this.updateConnectionState({
      connected: false,
      connecting: false,
      error: undefined,
      reconnectAttempts: 0
    });

    this.reconnectAttempts = 0;
  }

  public reconnect(): void {
    this.disconnect();
    setTimeout(() => this.connect(), 100);
  }

  // ========================================================================
  // EVENT HANDLERS SETUP
  // ========================================================================

  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      this.updateConnectionState({
        connected: true,
        connecting: false,
        error: undefined,
        lastConnected: formatTimestamp(),
        reconnectAttempts: 0
      });

      this.reconnectAttempts = 0;
      this.metrics.lastConnected = formatTimestamp();
      
      if (this.reconnectAttempts > 0) {
        this.metrics.reconnections++;
      }

      this.startHeartbeat();
      this.processMessageQueue();
      this.emit('connected');
    };

    this.ws.onmessage = (event) => {
      this.handleMessage(event.data);
    };

    this.ws.onclose = (event) => {
      this.updateConnectionState({
        connected: false,
        connecting: false,
        error: event.reason || 'Connection closed'
      });

      this.stopHeartbeat();
      this.emit('disconnected', event.reason || 'Connection closed');

      if (this.config.reconnect && this.reconnectAttempts < this.config.maxReconnectAttempts) {
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      this.handleError(new Error('WebSocket error'));
    };
  }

  // ========================================================================
  // MESSAGE HANDLING
  // ========================================================================

  private handleMessage(data: string): void {
    try {
      const message = JSON.parse(data);
      
      if (!isWebSocketMessage(message)) {
        console.warn('Invalid WebSocket message format:', message);
        return;
      }

      this.metrics.messagesReceived++;
      this.metrics.bytesTransferred += data.length;

      // Handle system messages
      this.handleSystemMessage(message);

      // Emit specific events based on message type
      this.emitTypedEvent(message);

      // Emit general message event
      this.emit('message', message);

    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
      this.metrics.errors++;
    }
  }

  private handleSystemMessage(message: WebSocketMessage): void {
    switch (message.type) {
      case WebSocketMessageType.PONG:
        this.lastHeartbeat = Date.now();
        break;
      
      case WebSocketMessageType.SUBSCRIPTION_CONFIRMED:
        if (message.data.channels) {
          message.data.channels.forEach((channel: string) => {
            this.subscriptions.add(channel);
          });
        }
        break;
      
      case WebSocketMessageType.ERROR:
        console.error('WebSocket server error:', message.data);
        this.metrics.errors++;
        break;
    }
  }

  private emitTypedEvent(message: WebSocketMessage): void {
    switch (message.type) {
      case WebSocketMessageType.SIGNAL_UPDATE:
      case WebSocketMessageType.SIGNAL_CREATED:
        this.emit('signal_update', message.data);
        break;
      
      case WebSocketMessageType.MARKET_UPDATE:
      case WebSocketMessageType.QUOTE_UPDATE:
        this.emit('market_update', message.data);
        break;
      
      case WebSocketMessageType.NOTIFICATION:
        this.emit('notification', message.data);
        break;
    }
  }

  // ========================================================================
  // MESSAGE SENDING
  // ========================================================================

  public sendMessage(message: WebSocketMessage): boolean {
    if (!this.checkRateLimit()) {
      console.warn('Rate limit exceeded, message not sent');
      return false;
    }

    if (this.ws?.readyState === WebSocket.OPEN) {
      try {
        const serialized = JSON.stringify(message);
        this.ws.send(serialized);
        this.metrics.messagesSent++;
        this.metrics.bytesTransferred += serialized.length;
        return true;
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
        this.metrics.errors++;
        return false;
      }
    } else {
      // Queue message if not connected
      if (this.messageQueue.length < this.config.maxQueueSize) {
        this.messageQueue.push(message);
        return true;
      } else {
        console.warn('Message queue full, message dropped');
        return false;
      }
    }
  }

  public sendTypedMessage<T>(type: WebSocketMessageType, data: T): boolean {
    const message = createWebSocketMessage(type, data);
    return this.sendMessage(message);
  }

  // ========================================================================
  // SUBSCRIPTION MANAGEMENT
  // ========================================================================

  public subscribe(channels: string[], symbols?: string[]): void {
    const message = createWebSocketMessage(WebSocketMessageType.SUBSCRIBE, {
      channels,
      symbols
    });
    this.sendMessage(message);
  }

  public unsubscribe(channels: string[]): void {
    const message = createWebSocketMessage(WebSocketMessageType.UNSUBSCRIBE, {
      channels
    });
    this.sendMessage(message);
    
    // Remove from local subscriptions
    channels.forEach(channel => this.subscriptions.delete(channel));
  }

  public getSubscriptions(): string[] {
    return Array.from(this.subscriptions);
  }

  // ========================================================================
  // HEARTBEAT MANAGEMENT
  // ========================================================================

  private startHeartbeat(): void {
    this.stopHeartbeat();
    
    this.heartbeatInterval = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        const heartbeat = createWebSocketMessage(WebSocketMessageType.HEARTBEAT, {
          timestamp: formatTimestamp()
        });
        this.sendMessage(heartbeat);
      }
    }, this.config.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // ========================================================================
  // RECONNECTION LOGIC
  // ========================================================================

  private scheduleReconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    this.reconnectAttempts++;
    
    this.updateConnectionState({
      ...this.connectionState,
      reconnectAttempts: this.reconnectAttempts
    });

    // Exponential backoff with jitter
    const baseDelay = this.config.reconnectInterval;
    const exponentialDelay = Math.min(baseDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);
    const jitter = Math.random() * 1000;
    const delay = exponentialDelay + jitter;

    this.reconnectTimeout = setTimeout(() => {
      this.connect();
    }, delay);
  }

  // ========================================================================
  // UTILITY METHODS
  // ========================================================================

  private processMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.ws?.readyState === WebSocket.OPEN) {
      const message = this.messageQueue.shift();
      if (message) {
        this.sendMessage(message);
      }
    }
  }

  private checkRateLimit(): boolean {
    const now = Date.now();
    
    if (now - this.rateLimitWindow >= this.config.rateLimitWindow) {
      this.rateLimitWindow = now;
      this.rateLimitCounter = 0;
    }
    
    if (this.rateLimitCounter >= this.config.rateLimitMax) {
      return false;
    }
    
    this.rateLimitCounter++;
    return true;
  }

  private setupRateLimitReset(): void {
    setInterval(() => {
      this.rateLimitCounter = 0;
      this.rateLimitWindow = Date.now();
    }, this.config.rateLimitWindow);
  }

  private updateConnectionState(newState: Partial<WebSocketConnectionState>): void {
    this.connectionState = { ...this.connectionState, ...newState };
    this.emit('connection_state_changed', this.connectionState);
  }

  private clearTimeouts(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    this.stopHeartbeat();
  }

  private handleError(error: Error): void {
    this.updateConnectionState({
      connected: false,
      connecting: false,
      error: error.message
    });

    this.metrics.errors++;
    this.emit('error', error);
  }

  // ========================================================================
  // PUBLIC API
  // ========================================================================

  public getConnectionState(): WebSocketConnectionState {
    return { ...this.connectionState };
  }

  public getMetrics() {
    return {
      ...this.metrics,
      connectionUptime: this.connectionState.connected && this.metrics.lastConnected
        ? Date.now() - new Date(this.metrics.lastConnected).getTime()
        : 0
    };
  }

  public isConnected(): boolean {
    return this.connectionState.connected;
  }

  public isConnecting(): boolean {
    return this.connectionState.connecting;
  }

  public getQueueSize(): number {
    return this.messageQueue.length;
  }

  public clearQueue(): void {
    this.messageQueue = [];
  }

  public destroy(): void {
    this.disconnect();
    this.removeAllListeners();
  }
}

export default WebSocketClient;
