/**
 * Validation Service for MongoDB Data
 * 
 * This service provides validation functionality for MongoDB data.
 */

const Joi = require('joi');
const logger = require('../utils/logger');

// User validation schema
const userSchema = Joi.object({
  email: Joi.string().email().required().lowercase().trim()
    .message({
      'string.email': 'Email must be a valid email address',
      'string.empty': 'Email is required',
      'any.required': 'Email is required'
    }),
  username: Joi.string().alphanum().min(3).max(30).required().trim()
    .message({
      'string.alphanum': 'Username must only contain alphanumeric characters',
      'string.min': 'Username must be at least 3 characters long',
      'string.max': 'Username cannot be longer than 30 characters',
      'string.empty': 'Username is required',
      'any.required': 'Username is required'
    }),
  password: Joi.string().min(8).required()
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)'))
    .message({
      'string.min': 'Password must be at least 8 characters long',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
      'string.empty': 'Password is required',
      'any.required': 'Password is required'
    }),
  name: Joi.string().min(2).max(100).trim()
    .message({
      'string.min': 'Name must be at least 2 characters long',
      'string.max': 'Name cannot be longer than 100 characters'
    }),
  role: Joi.string().valid('user', 'admin').default('user')
    .message({
      'any.only': 'Role must be either "user" or "admin"'
    })
});

// User preferences validation schema
const userPreferencesSchema = Joi.object({
  theme: Joi.string().valid('light', 'dark', 'system').default('system')
    .message({
      'any.only': 'Theme must be one of: light, dark, system'
    }),
  language: Joi.string().valid('en', 'ar').default('en')
    .message({
      'any.only': 'Language must be one of: en, ar'
    }),
  notifications: Joi.object({
    signals: Joi.boolean().default(true),
    news: Joi.boolean().default(true),
    economic: Joi.boolean().default(true)
  }).default(),
  trading: Joi.object({
    defaultTimeframe: Joi.string().valid('M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN').default('H1')
      .message({
        'any.only': 'Default timeframe must be one of: M1, M5, M15, M30, H1, H4, D1, W1, MN'
      }),
    defaultSymbol: Joi.string().default('EURUSD'),
    defaultLotSize: Joi.number().min(0.01).max(100).default(0.01)
      .message({
        'number.min': 'Default lot size must be at least 0.01',
        'number.max': 'Default lot size cannot be greater than 100'
      }),
    riskPercentage: Joi.number().min(0.1).max(100).default(2)
      .message({
        'number.min': 'Risk percentage must be at least 0.1',
        'number.max': 'Risk percentage cannot be greater than 100'
      })
  }).default()
});

// Trading signal validation schema
const signalSchema = Joi.object({
  symbol: Joi.string().required().trim()
    .message({
      'string.empty': 'Symbol is required',
      'any.required': 'Symbol is required'
    }),
  type: Joi.string().valid('buy', 'sell').required()
    .message({
      'any.only': 'Type must be either "buy" or "sell"',
      'any.required': 'Type is required'
    }),
  strength: Joi.number().integer().min(0).max(100).required()
    .message({
      'number.base': 'Strength must be a number',
      'number.integer': 'Strength must be an integer',
      'number.min': 'Strength must be at least 0',
      'number.max': 'Strength cannot be greater than 100',
      'any.required': 'Strength is required'
    }),
  timeframe: Joi.string().valid('M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN').required()
    .message({
      'any.only': 'Timeframe must be one of: M1, M5, M15, M30, H1, H4, D1, W1, MN',
      'any.required': 'Timeframe is required'
    }),
  entryPrice: Joi.number().min(0).required()
    .message({
      'number.base': 'Entry price must be a number',
      'number.min': 'Entry price must be at least 0',
      'any.required': 'Entry price is required'
    }),
  stopLoss: Joi.number().min(0)
    .message({
      'number.base': 'Stop loss must be a number',
      'number.min': 'Stop loss must be at least 0'
    }),
  takeProfit: Joi.number().min(0)
    .message({
      'number.base': 'Take profit must be a number',
      'number.min': 'Take profit must be at least 0'
    }),
  notes: Joi.string().max(1000)
    .message({
      'string.max': 'Notes cannot be longer than 1000 characters'
    }),
  status: Joi.string().valid('active', 'executed', 'expired').default('active')
    .message({
      'any.only': 'Status must be one of: active, executed, expired'
    })
});

// Market data validation schema
const marketDataSchema = Joi.object({
  symbol: Joi.string().required().trim()
    .message({
      'string.empty': 'Symbol is required',
      'any.required': 'Symbol is required'
    }),
  timeframe: Joi.string().valid('M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN').required()
    .message({
      'any.only': 'Timeframe must be one of: M1, M5, M15, M30, H1, H4, D1, W1, MN',
      'any.required': 'Timeframe is required'
    }),
  timestamp: Joi.date().required()
    .message({
      'date.base': 'Timestamp must be a valid date',
      'any.required': 'Timestamp is required'
    }),
  open: Joi.number().required()
    .message({
      'number.base': 'Open price must be a number',
      'any.required': 'Open price is required'
    }),
  high: Joi.number().required()
    .message({
      'number.base': 'High price must be a number',
      'any.required': 'High price is required'
    }),
  low: Joi.number().required()
    .message({
      'number.base': 'Low price must be a number',
      'any.required': 'Low price is required'
    }),
  close: Joi.number().required()
    .message({
      'number.base': 'Close price must be a number',
      'any.required': 'Close price is required'
    }),
  volume: Joi.number().integer().min(0)
    .message({
      'number.base': 'Volume must be a number',
      'number.integer': 'Volume must be an integer',
      'number.min': 'Volume must be at least 0'
    })
});

/**
 * Validate data against schema
 * @param {Object} data - Data to validate
 * @param {Object} schema - Joi schema
 * @returns {Object} Validation result
 */
function validate(data, schema) {
  try {
    const result = schema.validate(data, { abortEarly: false });
    
    if (result.error) {
      const errors = result.error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));
      
      return {
        valid: false,
        errors,
        value: result.value
      };
    }
    
    return {
      valid: true,
      value: result.value
    };
  } catch (error) {
    logger.error('Validation error:', error);
    return {
      valid: false,
      errors: [{ message: 'Internal validation error' }]
    };
  }
}

module.exports = {
  schemas: {
    user: userSchema,
    userPreferences: userPreferencesSchema,
    signal: signalSchema,
    marketData: marketDataSchema
  },
  validate
};
