{"name": "trading-signals-app", "version": "1.2.0", "description": "An advanced collaborative web application for market analysis and trading signals", "main": "server.js", "type": "commonjs", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "debug": "node --inspect server.js", "test": "jest --coverage --verbose", "test:unit": "jest --testPathPattern=unit --coverage", "test:integration": "jest --testPathPattern=integration --coverage", "test:e2e": "playwright test", "test:performance": "jest --testPathPattern=performance --coverage", "test:watch": "jest --watch", "test:ci": "jest --coverage --ci --watchAll=false --passWithNoTests", "test:all": "node scripts/run-all-tests.js", "test:coverage": "jest --coverage --coverageReporters=text-lcov | coveralls", "lint": "eslint . --fix", "build": "webpack --mode production", "analyze": "webpack-bundle-analyzer stats.json", "prepare": "husky install", "deploy": "node scripts/deploy-production.js", "deploy:staging": "NODE_ENV=staging node scripts/deploy-production.js", "docs:generate": "jsdoc -c jsdoc.conf.json", "docs:serve": "http-server docs -p 8080", "db:optimize": "node -e \"require('./src/services/databaseOptimizationService.js').createOptimizedIndexes()\"", "cache:warm": "node -e \"require('./src/services/enhancedRedisCacheService.js').warmCache()\"", "health:check": "node -e \"require('./src/utils/healthCheck.js').runHealthChecks()\"", "install-deps": "npm install", "health-check": "curl http://localhost:3000/health", "start-minimal": "node minimal-server.js", "check-env": "node -e \"console.log('Environment check:', {hasEnv: require('fs').existsSync('.env'), mongoUri: !!process.env.MONGODB_URI})\"", "setup-phase1": "node scripts/setupPhase1.js", "create-indexes": "node scripts/createOptimizedIndexes.js", "performance-check": "curl http://localhost:3000/api/performance/health", "cache-clear": "curl -X POST http://localhost:3000/api/performance/cache/clear -H 'Content-Type: application/json' -d '{\"type\":\"ALL\"}'", "db-optimize": "npm run create-indexes && npm run performance-check", "cleanup-phase2": "node scripts/cleanupAndConsolidate.js", "security-check": "curl http://localhost:3000/api/auth/security-metrics", "test-auth": "echo 'Testing authentication endpoints...' && curl -X POST http://localhost:3000/api/auth/register -H 'Content-Type: application/json' -d '{\"email\":\"<EMAIL>\",\"password\":\"SecurePass123!\",\"firstName\":\"Test\",\"lastName\":\"User\"}'", "setup-complete": "npm run setup-phase1 && npm run cleanup-phase2 && echo 'Setup complete! Start with: npm start'"}, "keywords": ["trading", "financial-markets", "technical-analysis", "signals", "charts", "real-time", "collaboration", "analytics"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.7", "bcrypt": "^5.1.0", "bull": "^4.11.3", "chalk": "^5.3.0", "chart.js": "^4.3.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^7.2.0", "ioredis": "^5.6.1", "joi": "^17.11.0", "jsonwebtoken": "^9.0.0", "mongodb": "^6.16.0", "mongoose": "^8.15.0", "morgan": "^1.10.0", "node-cache": "^5.1.2", "socket.io": "^4.7.2", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "eslint": "^8.40.0", "husky": "^8.0.3", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "lint-staged": "^15.2.0", "mongodb-memory-server": "^9.1.1", "nodemon": "^2.0.22", "playwright": "^1.40.0", "prettier": "^3.1.1", "supertest": "^6.3.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "webpack": "^5.89.0", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^5.1.4"}, "engines": {"node": ">=16.0.0"}, "lint-staged": {"*.js": "eslint --fix", "*.{js,json,md}": "prettier --write"}}