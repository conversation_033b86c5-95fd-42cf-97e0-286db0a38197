import { alphaVantageAPI, finnhubAPI, twelveDataAPI, polygonAPI } from './apiService.js';
import logger from '../utils/logger.js';
import { APIError } from '../middleware/errorHandler.js';

class MarketDataService {
  constructor() {
    this.supportedTimeframes = ['1min', '5min', '15min', '30min', '60min', '1day', '1week', '1month'];
    this.supportedAssetTypes = ['forex', 'crypto', 'stock', 'commodity'];
  }

  // Validate and format symbol
  validateSymbol(symbol) {
    if (!symbol || typeof symbol !== 'string') {
      throw new APIError(400, 'Invalid symbol provided');
    }
    return symbol.toUpperCase();
  }

  // Validate timeframe
  validateTimeframe(timeframe) {
    if (!this.supportedTimeframes.includes(timeframe)) {
      throw new APIError(400, `Unsupported timeframe. Supported timeframes: ${this.supportedTimeframes.join(', ')}`);
    }
    return timeframe;
  }

  // Get asset type from symbol
  getAssetType(symbol) {
    // Simple logic to determine asset type from symbol
    if (symbol.includes('/') || symbol.length === 6) return 'forex';
    if (symbol.endsWith('USDT') || symbol.endsWith('BTC')) return 'crypto';
    if (symbol === 'XAUUSD' || symbol === 'XAGUSD' || symbol.includes('OIL')) return 'commodity';
    return 'stock';
  }

  // Format market data response
  formatMarketData(data, source) {
    try {
      return {
        timestamp: new Date().toISOString(),
        source,
        data,
      };
    } catch (error) {
      logger.error('Error formatting market data:', error);
      throw new APIError(500, 'Error formatting market data');
    }
  }

  // Get real-time price data
  async getRealTimePrice(symbol) {
    try {
      symbol = this.validateSymbol(symbol);
      const assetType = this.getAssetType(symbol);

      let data;
      switch (assetType) {
        case 'forex':
          data = await twelveDataAPI.get('/price', { symbol });
          break;
        case 'crypto':
          data = await finnhubAPI.get('/crypto/candle', { symbol });
          break;
        case 'stock':
          data = await polygonAPI.get('/v2/aggs/ticker/' + symbol + '/prev', {});
          break;
        default:
          data = await alphaVantageAPI.get('/query', {
            function: 'GLOBAL_QUOTE',
            symbol,
          });
      }

      return this.formatMarketData(data, assetType);
    } catch (error) {
      logger.error('Error fetching real-time price:', error);
      throw new APIError(500, 'Error fetching real-time price data');
    }
  }

  // Get historical price data
  async getHistoricalData(symbol, timeframe = '1day', limit = 100) {
    try {
      symbol = this.validateSymbol(symbol);
      timeframe = this.validateTimeframe(timeframe);
      const assetType = this.getAssetType(symbol);

      let data;
      switch (assetType) {
        case 'forex':
          data = await twelveDataAPI.get('/time_series', {
            symbol,
            interval: timeframe,
            outputsize: limit,
          });
          break;
        case 'crypto':
          data = await finnhubAPI.get('/crypto/candle', {
            symbol,
            resolution: timeframe,
            count: limit,
          });
          break;
        case 'stock':
          // Convert timeframe to Polygon format
          const multiplier = this.getPolygonTimeframeMultiplier(timeframe);
          const timespan = this.getPolygonTimeframeTimespan(timeframe);

          // Get current date and date from 30 days ago
          const endDate = new Date().toISOString().split('T')[0];
          const startDate = new Date();
          startDate.setDate(startDate.getDate() - 30);
          const formattedStartDate = startDate.toISOString().split('T')[0];

          data = await polygonAPI.get(`/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${formattedStartDate}/${endDate}`, {
            adjusted: true,
            sort: 'asc',
            limit: limit
          });
          break;
        default:
          data = await alphaVantageAPI.get('/query', {
            function: 'TIME_SERIES_INTRADAY',
            symbol,
            interval: timeframe,
            outputsize: 'compact',
          });
      }

      return this.formatMarketData(data, assetType);
    } catch (error) {
      logger.error('Error fetching historical data:', error);
      throw new APIError(500, 'Error fetching historical price data');
    }
  }

  // Helper method to convert timeframe to Polygon multiplier
  getPolygonTimeframeMultiplier(timeframe) {
    switch (timeframe) {
      case '1min': return 1;
      case '5min': return 5;
      case '15min': return 15;
      case '30min': return 30;
      case '60min': return 1;
      case '1day': return 1;
      case '1week': return 1;
      case '1month': return 1;
      default: return 1;
    }
  }

  // Helper method to convert timeframe to Polygon timespan
  getPolygonTimeframeTimespan(timeframe) {
    switch (timeframe) {
      case '1min':
      case '5min':
      case '15min':
      case '30min': return 'minute';
      case '60min': return 'hour';
      case '1day': return 'day';
      case '1week': return 'week';
      case '1month': return 'month';
      default: return 'day';
    }
  }

  // Get market overview
  async getMarketOverview() {
    try {
      const promises = [
        // Major forex pairs
        this.getRealTimePrice('EUR/USD'),
        this.getRealTimePrice('GBP/USD'),
        this.getRealTimePrice('USD/JPY'),
        // Major cryptocurrencies
        this.getRealTimePrice('BTC/USDT'),
        this.getRealTimePrice('ETH/USDT'),
        // Major commodities
        this.getRealTimePrice('XAUUSD'),
        this.getRealTimePrice('XAGUSD'),
      ];

      const results = await Promise.allSettled(promises);
      const overview = {
        timestamp: new Date().toISOString(),
        forex: {},
        crypto: {},
        commodities: {},
      };

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          const { data, source } = result.value;
          overview[source][data.symbol] = data;
        }
      });

      return overview;
    } catch (error) {
      logger.error('Error fetching market overview:', error);
      throw new APIError(500, 'Error fetching market overview');
    }
  }
}

export default new MarketDataService();