/**
 * وحدة لعرض الرسوم البيانية للأسواق المالية
 *
 * هذا الملف يحتوي على وظائف لعرض الرسوم البيانية للأسواق المالية
 * باستخدام مكتبة Chart.js مع إضافة chartjs-chart-financial للرسوم البيانية المالية.
 *
 * Features:
 * - Candlestick charts for financial data
 * - Multiple timeframe support
 * - Technical indicators
 * - Dark mode support
 */

// بيانات الرسم البياني (محاكاة)
const chartData = {
    forex: {
        EURUSD: {
            M5: {
                timestamps: generateTimestamps(30, 5),
                prices: [1.0850, 1.0855, 1.0858, 1.0862, 1.0865, 1.0870, 1.0875, 1.0878, 1.0880, 1.0882,
                         1.0885, 1.0888, 1.0890, 1.0892, 1.0895, 1.0898, 1.0900, 1.0902, 1.0905, 1.0908,
                         1.0910, 1.0908, 1.0905, 1.0902, 1.0900, 1.0898, 1.0895, 1.0892, 1.0890, 1.0888],
                // OHLC data for candlestick charts
                ohlc: generateOHLCData(30, 1.0850, 1.0910, 0.0010)
            },
            M15: {
                timestamps: generateTimestamps(30, 15),
                prices: [1.0840, 1.0845, 1.0850, 1.0855, 1.0860, 1.0865, 1.0870, 1.0875, 1.0880, 1.0885,
                         1.0890, 1.0895, 1.0900, 1.0905, 1.0910, 1.0915, 1.0920, 1.0925, 1.0930, 1.0925,
                         1.0920, 1.0915, 1.0910, 1.0905, 1.0900, 1.0895, 1.0890, 1.0885, 1.0880, 1.0875],
                ohlc: generateOHLCData(30, 1.0840, 1.0930, 0.0015)
            }
        },
        GBPUSD: {
            M5: {
                timestamps: generateTimestamps(30, 5),
                prices: [1.2620, 1.2625, 1.2630, 1.2635, 1.2640, 1.2645, 1.2650, 1.2655, 1.2660, 1.2665,
                         1.2670, 1.2675, 1.2680, 1.2675, 1.2670, 1.2665, 1.2660, 1.2655, 1.2650, 1.2645,
                         1.2640, 1.2635, 1.2630, 1.2625, 1.2620, 1.2615, 1.2610, 1.2605, 1.2600, 1.2595],
                ohlc: generateOHLCData(30, 1.2600, 1.2680, 0.0020)
            }
        }
    },
    commodities: {
        XAUUSD: {
            M5: {
                timestamps: generateTimestamps(30, 5),
                prices: [2320.50, 2322.75, 2325.00, 2327.25, 2330.50, 2332.75, 2335.00, 2337.25, 2340.50, 2342.75,
                         2345.00, 2347.25, 2350.50, 2352.75, 2355.00, 2357.25, 2360.50, 2362.75, 2365.00, 2362.75,
                         2360.50, 2357.25, 2355.00, 2352.75, 2350.50, 2347.25, 2345.00, 2342.75, 2340.50, 2337.25],
                ohlc: generateOHLCData(30, 2320.50, 2365.00, 5.0)
            }
        },
        USOIL: {
            M5: {
                timestamps: generateTimestamps(30, 5),
                prices: [80.50, 80.35, 80.20, 80.05, 79.90, 79.75, 79.60, 79.45, 79.30, 79.15,
                         79.00, 78.85, 78.70, 78.55, 78.40, 78.25, 78.10, 77.95, 77.80, 77.65,
                         77.50, 77.35, 77.20, 77.35, 77.50, 77.65, 77.80, 77.95, 78.10, 78.25],
                ohlc: generateOHLCData(30, 77.20, 80.50, 0.50)
            }
        }
    }
};

// وظيفة لإنشاء طوابع زمنية
function generateTimestamps(count, intervalMinutes) {
    const timestamps = [];
    const now = new Date();

    for (let i = count - 1; i >= 0; i--) {
        const timestamp = new Date(now.getTime() - i * intervalMinutes * 60 * 1000);
        timestamps.push(timestamp.toLocaleTimeString());
    }

    return timestamps;
}

// Function to generate OHLC data for candlestick charts
function generateOHLCData(count, startPrice, maxPrice, volatility) {
    const ohlcData = [];
    const dates = generateTimestamps(count, 5);
    let lastClose = startPrice;

    for (let i = 0; i < count; i++) {
        // Generate random price movements
        const range = volatility * Math.random();
        const change = (Math.random() > 0.5 ? 1 : -1) * range;

        // Calculate OHLC values
        let open = lastClose;
        let close = Math.max(open + change, 0); // Ensure price doesn't go negative
        let high = Math.max(open, close) + (volatility * 0.5 * Math.random());
        let low = Math.min(open, close) - (volatility * 0.5 * Math.random());

        // Ensure high is the highest and low is the lowest
        high = Math.max(high, open, close);
        low = Math.min(low, open, close);

        // Add some trend to make the data more realistic
        if (i > 0 && i < count / 2) {
            // Uptrend in first half
            close = Math.min(close + (volatility * 0.2), maxPrice);
        } else if (i >= count / 2) {
            // Downtrend in second half
            close = Math.max(close - (volatility * 0.1), startPrice * 0.9);
        }

        // Create OHLC object in format required by chartjs-chart-financial
        ohlcData.push({
            x: new Date(dates[i]),
            o: parseFloat(open.toFixed(5)),
            h: parseFloat(high.toFixed(5)),
            l: parseFloat(low.toFixed(5)),
            c: parseFloat(close.toFixed(5))
        });

        // Set last close for next candle
        lastClose = close;
    }

    return ohlcData;
}

// عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة عنصر الرسم البياني إلى الصفحة
    addChartElement();

    // إضافة مستمع لحدث تحليل السوق
    document.getElementById('analyzeBtn').addEventListener('click', handleAnalyzeClick);

    // Add event listener for theme changes
    document.addEventListener('themeChanged', function() {
        updateChartTheme();
    });
});

// Handle analyze button click
async function handleAnalyzeClick() {
    try {
        // Show loading state
        showChartLoading();

        // Get selected values
        const marketType = document.getElementById('marketType').value;
        const symbol = document.getElementById('symbol').value;
        const timeframe = document.getElementById('timeframe').value;

        // Check if Alpha Vantage API is available
        if (window.alphaVantageAPI) {
            // Fetch real market data from Alpha Vantage
            await fetchMarketData(marketType, symbol, timeframe);
        } else {
            // Use mock data if API is not available
            updateChart();
        }
    } catch (error) {
        console.error('Error analyzing market:', error);
        showChartError(error.message);
    } finally {
        // Hide loading state
        hideChartLoading();
    }
}

// Function to fetch market data from Alpha Vantage
async function fetchMarketData(marketType, symbol, timeframe) {
    try {
        let data;
        const interval = convertTimeframeToInterval(timeframe);

        if (marketType === 'forex') {
            // For forex pairs, split the symbol (e.g., EURUSD -> EUR, USD)
            const fromSymbol = symbol.substring(0, 3);
            const toSymbol = symbol.substring(3, 6);
            data = await window.alphaVantageAPI.getForexTimeSeries(fromSymbol, toSymbol, interval);
        } else if (marketType === 'crypto') {
            data = await window.alphaVantageAPI.getCryptoTimeSeries(symbol, 'USD', interval);
        } else {
            // Stocks and commodities
            data = await window.alphaVantageAPI.getStockTimeSeries(symbol, interval);
        }

        // Update chart with real data
        updateChartWithRealData(data, symbol, timeframe);
    } catch (error) {
        console.error('Error fetching market data:', error);
        // Fallback to mock data
        updateChart();
    }
}

// Function to update chart with real market data
function updateChartWithRealData(data, symbol, timeframe) {
    if (!data || !window.priceChart) return;

    // Check chart type
    const isCandlestick = window.priceChart.config.type === 'candlestick';

    // Update chart data
    if (isCandlestick && data.ohlc) {
        window.priceChart.data.datasets[0].data = data.ohlc;
    } else {
        window.priceChart.data.labels = data.timestamps;
        window.priceChart.data.datasets[0].data = data.closes;
    }

    // Update chart title
    window.priceChart.options.plugins.title.text = `${getSymbolName(symbol)} - ${getTimeframeName(timeframe)}`;

    // Update chart
    window.priceChart.update();

    // Add technical indicators if available
    if (window.technicalAnalysis && typeof window.technicalAnalysis.updateIndicators === 'function') {
        window.technicalAnalysis.updateIndicators(window.priceChart, data);
    }
}

// Function to convert timeframe to Alpha Vantage interval
function convertTimeframeToInterval(timeframe) {
    switch(timeframe) {
        case 'M1': return '1min';
        case 'M5': return '5min';
        case 'M15': return '15min';
        case 'M30': return '30min';
        case 'H1': return '60min';
        case 'H4': return 'daily'; // Alpha Vantage doesn't have 4h, use daily
        case 'D1': return 'daily';
        case 'W1': return 'weekly';
        case 'MN': return 'monthly';
        default: return '5min';
    }
}

// Show chart loading state
function showChartLoading() {
    const chartContainer = document.getElementById('priceChart').parentNode;

    // Create loading overlay if it doesn't exist
    let loadingOverlay = chartContainer.querySelector('.chart-loading-overlay');
    if (!loadingOverlay) {
        loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'chart-loading-overlay';
        loadingOverlay.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
        loadingOverlay.style.position = 'absolute';
        loadingOverlay.style.top = '0';
        loadingOverlay.style.left = '0';
        loadingOverlay.style.width = '100%';
        loadingOverlay.style.height = '100%';
        loadingOverlay.style.display = 'flex';
        loadingOverlay.style.alignItems = 'center';
        loadingOverlay.style.justifyContent = 'center';
        loadingOverlay.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
        loadingOverlay.style.zIndex = '10';

        // Add to chart container
        chartContainer.style.position = 'relative';
        chartContainer.appendChild(loadingOverlay);
    } else {
        loadingOverlay.style.display = 'flex';
    }
}

// Hide chart loading state
function hideChartLoading() {
    const chartContainer = document.getElementById('priceChart').parentNode;
    const loadingOverlay = chartContainer.querySelector('.chart-loading-overlay');

    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

// Show chart error
function showChartError(message) {
    const chartContainer = document.getElementById('priceChart').parentNode;

    // Create error message if it doesn't exist
    let errorMessage = chartContainer.querySelector('.chart-error-message');
    if (!errorMessage) {
        errorMessage = document.createElement('div');
        errorMessage.className = 'chart-error-message alert alert-danger';
        errorMessage.style.position = 'absolute';
        errorMessage.style.top = '10px';
        errorMessage.style.left = '10px';
        errorMessage.style.right = '10px';
        errorMessage.style.zIndex = '20';

        // Add to chart container
        chartContainer.style.position = 'relative';
        chartContainer.appendChild(errorMessage);
    }

    errorMessage.textContent = message;
    errorMessage.style.display = 'block';

    // Auto-hide after 5 seconds
    setTimeout(() => {
        errorMessage.style.display = 'none';
    }, 5000);
}

// وظيفة لإضافة عنصر الرسم البياني إلى الصفحة - تم إلغاؤها لتجنب التكرار
function addChartElement() {
    // تم إلغاء هذه الوظيفة لتجنب تكرار عنصر الرسم البياني
    console.log('Chart element creation skipped to avoid duplication');

    // استخدام الرسم البياني الموجود بالفعل
    const existingChart = document.getElementById('priceChart');
    if (existingChart) {
        createChart();
    }
}

// وظيفة لإنشاء الرسم البياني
function createChart() {
    const ctx = document.getElementById('priceChart').getContext('2d');

    // الحصول على البيانات الافتراضية
    const defaultData = chartData.forex.EURUSD.M5;

    // Check if chartjs-chart-financial is loaded
    const useFinancialChart = typeof Chart !== 'undefined' &&
                             typeof Chart.controllers !== 'undefined' &&
                             typeof Chart.controllers.candlestick !== 'undefined';

    // Set chart type based on available plugins
    const chartType = useFinancialChart ? 'candlestick' : 'line';

    // Set chart colors based on theme
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
    const chartColors = {
        grid: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
        text: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
        candleUp: isDarkMode ? 'rgba(75, 192, 192, 1)' : 'rgba(0, 128, 0, 1)',
        candleDown: isDarkMode ? 'rgba(255, 99, 132, 1)' : 'rgba(255, 0, 0, 1)',
        line: isDarkMode ? 'rgba(75, 192, 192, 1)' : 'rgb(75, 192, 192)'
    };

    // Create datasets based on chart type
    let datasets = [];

    if (chartType === 'candlestick') {
        // Candlestick dataset
        datasets.push({
            label: 'OHLC',
            data: defaultData.ohlc,
            color: {
                up: chartColors.candleUp,
                down: chartColors.candleDown,
                unchanged: 'rgba(90, 90, 90, 1)',
            }
        });
    } else {
        // Fallback to line chart if financial plugin is not available
        datasets.push({
            label: 'السعر',
            data: defaultData.prices,
            borderColor: chartColors.line,
            tension: 0.1,
            pointRadius: 2,
            pointHoverRadius: 5
        });
    }

    // إنشاء الرسم البياني
    window.priceChart = new Chart(ctx, {
        type: chartType,
        data: {
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'يورو/دولار (EUR/USD) - 5 دقائق',
                    font: {
                        size: 16
                    },
                    color: chartColors.text
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            if (chartType === 'candlestick') {
                                const point = context.raw;
                                return [
                                    `فتح: ${point.o}`,
                                    `أعلى: ${point.h}`,
                                    `أدنى: ${point.l}`,
                                    `إغلاق: ${point.c}`
                                ];
                            }
                            return context.dataset.label + ': ' + context.parsed.y;
                        }
                    }
                },
                legend: {
                    labels: {
                        color: chartColors.text
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'الوقت',
                        color: chartColors.text
                    },
                    grid: {
                        color: chartColors.grid
                    },
                    ticks: {
                        color: chartColors.text
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'السعر',
                        color: chartColors.text
                    },
                    grid: {
                        color: chartColors.grid
                    },
                    ticks: {
                        color: chartColors.text
                    }
                }
            }
        }
    });
}

// وظيفة لتحديث الرسم البياني
function updateChart() {
    // الحصول على القيم المحددة
    const marketType = document.getElementById('marketType').value;
    const symbol = document.getElementById('symbol').value;
    const timeframe = document.getElementById('timeframe').value;

    // التحقق من وجود بيانات للرسم البياني
    if (chartData[marketType] &&
        chartData[marketType][symbol] &&
        chartData[marketType][symbol][timeframe]) {

        const data = chartData[marketType][symbol][timeframe];

        // Check chart type
        const isCandlestick = window.priceChart.config.type === 'candlestick';

        // تحديث بيانات الرسم البياني based on chart type
        if (isCandlestick && data.ohlc) {
            // Update candlestick data
            window.priceChart.data.datasets[0].data = data.ohlc;
        } else {
            // Update line chart data
            window.priceChart.data.labels = data.timestamps;
            window.priceChart.data.datasets[0].data = data.prices;
        }

        // تحديث عنوان الرسم البياني
        window.priceChart.options.plugins.title.text = `${getSymbolName(symbol)} - ${getTimeframeName(timeframe)}`;

        // تحديث الرسم البياني
        window.priceChart.update();

        // Add technical indicators if available
        if (window.technicalAnalysis && typeof window.technicalAnalysis.updateIndicators === 'function') {
            window.technicalAnalysis.updateIndicators(window.priceChart, data);
        }
    } else {
        console.log('البيانات غير متوفرة للرسم البياني');
    }

    // Update chart theme if needed
    updateChartTheme();
}

// Function to update chart theme based on dark/light mode
function updateChartTheme() {
    if (!window.priceChart) return;

    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
    const chartColors = {
        grid: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
        text: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
        candleUp: isDarkMode ? 'rgba(75, 192, 192, 1)' : 'rgba(0, 128, 0, 1)',
        candleDown: isDarkMode ? 'rgba(255, 99, 132, 1)' : 'rgba(255, 0, 0, 1)'
    };

    // Update chart colors
    if (window.priceChart.config.type === 'candlestick') {
        window.priceChart.data.datasets[0].color = {
            up: chartColors.candleUp,
            down: chartColors.candleDown,
            unchanged: 'rgba(90, 90, 90, 1)'
        };
    }

    // Update scales colors
    window.priceChart.options.scales.x.grid.color = chartColors.grid;
    window.priceChart.options.scales.y.grid.color = chartColors.grid;
    window.priceChart.options.scales.x.ticks.color = chartColors.text;
    window.priceChart.options.scales.y.ticks.color = chartColors.text;
    window.priceChart.options.scales.x.title.color = chartColors.text;
    window.priceChart.options.scales.y.title.color = chartColors.text;

    // Update plugin colors
    window.priceChart.options.plugins.title.color = chartColors.text;
    window.priceChart.options.plugins.legend.labels.color = chartColors.text;

    // Update chart
    window.priceChart.update();
}

// وظيفة للحصول على اسم الرمز
function getSymbolName(symbol) {
    switch(symbol) {
        case 'EURUSD': return 'يورو/دولار (EUR/USD)';
        case 'GBPUSD': return 'جنيه/دولار (GBP/USD)';
        case 'USDJPY': return 'دولار/ين (USD/JPY)';
        case 'XAUUSD': return 'الذهب (XAU/USD)';
        case 'XAGUSD': return 'الفضة (XAG/USD)';
        case 'USOIL': return 'النفط الأمريكي';
        case 'US30': return 'داو جونز (US30)';
        case 'SPX500': return 'S&P 500';
        case 'NASDAQ': return 'ناسداك';
        case 'BTCUSD': return 'بيتكوين/دولار (BTC/USD)';
        case 'ETHUSD': return 'إيثريوم/دولار (ETH/USD)';
        default: return symbol;
    }
}

// وظيفة للحصول على اسم الإطار الزمني
function getTimeframeName(timeframe) {
    switch(timeframe) {
        case 'M5': return '5 دقائق';
        case 'M15': return '15 دقيقة';
        case 'M30': return '30 دقيقة';
        case 'H1': return 'ساعة';
        case 'H4': return '4 ساعات';
        case 'D1': return 'يومي';
        case 'W1': return 'أسبوعي';
        case 'MN': return 'شهري';
        default: return timeframe;
    }
}
