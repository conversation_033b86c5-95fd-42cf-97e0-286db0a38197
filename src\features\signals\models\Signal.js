import mongoose from 'mongoose';
import { MongoSignalSchema, SIGNAL_TYPES, SIGNAL_SOURCES, TIMEFRAMES, SIGNAL_STATUS } from '../../schemas/unifiedSignalSchema.js';

// Create the unified signal schema using the standardized definition
const signalSchema = new mongoose.Schema(MongoSignalSchema);

// Add indexes for performance
signalSchema.index({ symbol: 1, timestamp: -1 });
signalSchema.index({ userId: 1, status: 1 });
signalSchema.index({ type: 1, confidence: -1 });
signalSchema.index({ source: 1, createdAt: -1 });

// Add virtual for id field (for API consistency)
signalSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

// Ensure virtual fields are serialized
signalSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

// Pre-save middleware to ensure data consistency
signalSchema.pre('save', function(next) {
  // Ensure timestamp is set
  if (!this.timestamp) {
    this.timestamp = new Date();
  }

  // Update updatedAt
  this.updatedAt = new Date();

  // Ensure confidence is within valid range
  if (this.confidence < 0) this.confidence = 0;
  if (this.confidence > 100) this.confidence = 100;

  // Convert legacy strength to confidence if needed
  if (this.strength && !this.confidence) {
    this.confidence = Math.round(this.strength * 100);
  }

  // Ensure type is uppercase
  if (this.type) {
    this.type = this.type.toUpperCase();
  }

  // Set default message if not provided
  if (!this.message) {
    this.message = `${this.type} signal for ${this.symbol}`;
  }

  next();
});

// Static method to create signal from legacy format
signalSchema.statics.fromLegacy = function(legacyData) {
  const { SignalTransformers } = require('../../schemas/unifiedSignalSchema.js');
  const unifiedData = SignalTransformers.fromLegacy(legacyData);
  return new this(unifiedData);
};

// Instance method to convert to API response format
signalSchema.methods.toApiResponse = function() {
  const { SignalTransformers } = require('../../schemas/unifiedSignalSchema.js');
  return SignalTransformers.toApiResponse(this.toObject());
};

// Static method to validate signal data
signalSchema.statics.validateSignalData = function(data) {
  const { SignalTransformers } = require('../../schemas/unifiedSignalSchema.js');
  return SignalTransformers.validate(data);
};

const Signal = mongoose.model('Signal', signalSchema);
export default Signal;