<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Trading Signals App API Documentation</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      text-align: center;
      padding: 2rem;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      max-width: 600px;
    }
    h1 {
      color: #0d47a1;
      margin-bottom: 1rem;
    }
    p {
      margin-bottom: 2rem;
      line-height: 1.6;
    }
    .btn {
      display: inline-block;
      background-color: #0d47a1;
      color: white;
      padding: 0.8rem 1.5rem;
      text-decoration: none;
      border-radius: 4px;
      font-weight: bold;
      transition: background-color 0.3s;
    }
    .btn:hover {
      background-color: #1565c0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Trading Signals App API Documentation</h1>
    <p>
      Welcome to the Trading Signals App API documentation. This page provides comprehensive information about the available API endpoints, request/response formats, and authentication requirements.
    </p>
    <a href="/api-docs" class="btn">View API Documentation</a>
  </div>
  <script>
    // Automatically redirect to the API documentation after 2 seconds
    setTimeout(() => {
      window.location.href = '/api-docs';
    }, 2000);
  </script>
</body>
</html>
