/**
 * Market Data Routes for Trading Signals App
 */

const axios = require('axios');
const logger = require('../utils/logger');

module.exports = function(app, apiCache, API_CONFIG, getMockMarketData, mapIntervalToAlphaVantage) {
  // Simple API test endpoint for market data service
  app.get('/api/market-data/test', (req, res) => {
    res.json({
      status: 'success',
      message: 'Market data API is working correctly',
      timestamp: new Date().toISOString(),
      endpoints: {
        marketData: '/api/market-data/:symbol?interval=M5',
        supportedSymbols: ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD', 'BTCUSD', 'AAPL', 'MSFT']
      }
    });
  });

  // Get market data for a specific symbol
  app.get('/api/market-data/:symbol', async (req, res) => {
    try {
      const symbol = req.params.symbol;
      const interval = req.query.interval || 'M5';
      const assetType = req.query.assetType || getAssetTypeFromSymbol(symbol);

      logger.info(`Received request for market data: ${symbol} (${interval}, ${assetType})`);

      const cacheKey = `market_data_${symbol}_${interval}_${assetType}`;

      // Check if data is in cache
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        logger.info(`Returning cached market data for ${symbol}`);
        return res.json(cachedData);
      }

      logger.info(`No cached data found for ${symbol}, fetching from API...`);

      // Try to get real data from Alpha Vantage
      try {
        let response;
        const config = API_CONFIG.ALPHA_VANTAGE;
        let apiUrl = '';

        // Ensure API key is set and valid
        let apiKey = config.API_KEY;

        // Validate API key format
        if (!apiKey || apiKey === 'demo' || apiKey === 'your-api-key') {
          // Use the one from .env file
          apiKey = process.env.ALPHA_VANTAGE_API_KEY;
          logger.info('Using Alpha Vantage API key from environment variables');
        }

        if (assetType === 'forex') {
          // Parse forex symbol (e.g., 'EURUSD' -> ['EUR', 'USD'])
          const fromCurrency = symbol.substring(0, 3);
          const toCurrency = symbol.substring(3);

          apiUrl = `${config.BASE_URL}?function=${config.ENDPOINTS.FX_INTRADAY}&from_symbol=${fromCurrency}&to_symbol=${toCurrency}&interval=${mapIntervalToAlphaVantage(interval)}&outputsize=compact&apikey=${apiKey}`;
          logger.info(`Making Alpha Vantage forex API request for ${fromCurrency}/${toCurrency}`);
        } else if (assetType === 'crypto') {
          // Parse crypto symbol (e.g., 'BTCUSD' -> ['BTC', 'USD'])
          const cryptoSymbol = symbol.substring(0, 3);
          const marketSymbol = symbol.substring(3);

          apiUrl = `${config.BASE_URL}?function=${config.ENDPOINTS.CRYPTO_INTRADAY}&symbol=${cryptoSymbol}&market=${marketSymbol}&interval=${mapIntervalToAlphaVantage(interval)}&outputsize=compact&apikey=${apiKey}`;
          logger.info(`Making Alpha Vantage crypto API request for ${cryptoSymbol}/${marketSymbol}`);
        } else {
          // Stock or other asset type
          // Try using GLOBAL_QUOTE for faster response if we just need current price
          if (interval === 'current') {
            apiUrl = `${config.BASE_URL}?function=${config.ENDPOINTS.GLOBAL_QUOTE}&symbol=${symbol}&apikey=${apiKey}`;
            logger.info(`Making Alpha Vantage global quote API request for ${symbol}`);
          } else {
            // Use intraday or daily based on interval
            const functionName = interval === 'D1' ? config.ENDPOINTS.DAILY : config.ENDPOINTS.INTRADAY;
            apiUrl = `${config.BASE_URL}?function=${functionName}&symbol=${symbol}&interval=${mapIntervalToAlphaVantage(interval)}&outputsize=compact&apikey=${apiKey}`;
            logger.info(`Making Alpha Vantage ${functionName} API request for ${symbol}`);
          }
        }

        // Add timeout and retry logic
        const maxRetries = 3; // Increased from 2 to 3
        let retries = 0;

        while (retries <= maxRetries) {
          try {
            logger.info(`Making API request to: ${apiUrl.replace(/apikey=([^&]*)/, 'apikey=HIDDEN')}`);

            response = await axios.get(apiUrl, {
              timeout: 15000, // Increased from 10 to 15 second timeout
              headers: {
                'User-Agent': 'Trading Signals App/1.0',
                'Accept': 'application/json'
              }
            });

            // Log response status and headers for debugging
            logger.info(`Alpha Vantage API response status: ${response.status}`);
            logger.debug(`Response headers: ${JSON.stringify(response.headers)}`);

            // Check if the response contains an error message from Alpha Vantage
            if (response.data && (
                response.data.hasOwnProperty('Error Message') ||
                response.data.hasOwnProperty('Information') ||
                response.data.hasOwnProperty('Note')
            )) {
              if (response.data['Error Message']) {
                throw new Error(`Alpha Vantage API error: ${response.data['Error Message']}`);
              } else if (response.data['Note'] && response.data['Note'].includes('API call frequency')) {
                // This is a rate limit error
                logger.warn(`Alpha Vantage rate limit reached: ${response.data['Note']}`);

                if (retries < maxRetries) {
                  // Wait before retrying (exponential backoff)
                  const delay = Math.pow(2, retries) * 1500; // Increased backoff time
                  logger.info(`Retrying after ${delay}ms (attempt ${retries + 1}/${maxRetries})`);
                  await new Promise(resolve => setTimeout(resolve, delay));
                  retries++;
                  continue;
                } else {
                  throw new Error(`Alpha Vantage rate limit reached: ${response.data['Note']}`);
                }
              } else if (response.data['Information']) {
                logger.info(`Alpha Vantage information: ${response.data['Information']}`);
                // Check if this is an invalid API key message
                if (response.data['Information'].includes('Invalid API call')) {
                  throw new Error(`Alpha Vantage API error: ${response.data['Information']}`);
                }
              }
            }

            // If we got here, the request was successful
            break;
          } catch (retryError) {
            if (retries < maxRetries) {
              // Wait before retrying (exponential backoff)
              const delay = Math.pow(2, retries) * 1000;
              logger.warn(`Alpha Vantage request failed, retrying after ${delay}ms (attempt ${retries + 1}/${maxRetries}): ${retryError.message}`);
              await new Promise(resolve => setTimeout(resolve, delay));
              retries++;
            } else {
              // Max retries reached, rethrow the error
              throw retryError;
            }
          }
        }

        logger.info(`Alpha Vantage API response status: ${response.status}`);

        // Validate the response data
        if (!response || !response.data) {
          throw new Error('Empty response from Alpha Vantage API');
        }

        // Check if the response is valid JSON
        if (typeof response.data !== 'object') {
          throw new Error('Invalid response format from Alpha Vantage API');
        }

        // Transform the data to a standard format
        const transformedData = transformAlphaVantageData(response.data, interval, assetType);

        // Cache the response
        apiCache.set(cacheKey, transformedData);

        return res.json(transformedData);
      } catch (error) {
        logger.warn(`Failed to get real data for ${symbol}, using mock data:`, error.message);

        // Log detailed error information for debugging
        logger.error({
          message: `Alpha Vantage API error for ${symbol}`,
          error: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString(),
          requestId: `404a5da4984-e2ee-409e-8c8e-0c8d1c48d8ab-${Date.now()}`
        });

        // Fall back to mock data
        logger.info(`Generating mock data for ${symbol}...`);
        const mockData = getMockMarketData(symbol, interval, assetType);

        // Cache the mock data (shorter TTL)
        logger.info(`Caching mock data for ${symbol} (TTL: 60 seconds)`);
        apiCache.set(cacheKey, mockData, 60); // 1 minute TTL for mock data

        return res.json(mockData);
      }
    } catch (error) {
      logger.error('Market data API error:', error.message);
      res.status(500).json({ error: 'Failed to fetch market data' });
    }
  });

  // Helper function to transform Alpha Vantage data
  function transformAlphaVantageData(data, interval, assetType) {
    try {
      // Check if this is a Global Quote response
      if (data['Global Quote']) {
        // Handle Global Quote format (single price point)
        const quote = data['Global Quote'];

        // Extract the data
        const currentPrice = parseFloat(quote['05. price']);
        const previousClose = parseFloat(quote['08. previous close']);
        const dailyChange = parseFloat(quote['09. change']);
        const dailyChangePercent = parseFloat(quote['10. change percent'].replace('%', ''));

        // Calculate a simple sentiment score based on price movement
        const sentiment = Math.min(Math.max(Math.round(50 + dailyChangePercent * 2), 0), 100);

        // Create a single data point
        return {
          dates: [new Date().toLocaleString()],
          prices: [currentPrice],
          opens: [parseFloat(quote['02. open'])],
          highs: [parseFloat(quote['03. high'])],
          lows: [parseFloat(quote['04. low'])],
          closes: [currentPrice],
          volumes: [parseFloat(quote['06. volume'])],
          currentPrice: currentPrice,
          dailyChange: dailyChangePercent,
          sentiment: sentiment
        };
      }

      // Determine the time series key based on the asset type and interval
      let timeSeriesKey;

      if (assetType === 'forex') {
        timeSeriesKey = `Time Series FX (${mapIntervalToAlphaVantage(interval)})`;
      } else if (assetType === 'crypto') {
        timeSeriesKey = `Time Series Crypto (${mapIntervalToAlphaVantage(interval)})`;
      } else if (interval === 'D1') {
        timeSeriesKey = 'Time Series (Daily)';
      } else {
        timeSeriesKey = `Time Series (${mapIntervalToAlphaVantage(interval)})`;
      }

      // Extract the time series data
      const timeSeries = data[timeSeriesKey];

      if (!timeSeries) {
        // Log available keys for debugging
        logger.warn(`Expected time series key "${timeSeriesKey}" not found in Alpha Vantage response`);
        logger.warn(`Available keys: ${Object.keys(data).join(', ')}`);

        // Try to find any time series key as a fallback
        const possibleTimeSeriesKey = Object.keys(data).find(key => key.includes('Time Series'));

        if (possibleTimeSeriesKey) {
          logger.info(`Using alternative time series key: ${possibleTimeSeriesKey}`);
          timeSeriesKey = possibleTimeSeriesKey;
        } else {
          throw new Error('No time series data found in Alpha Vantage response');
        }
      }

      // Transform the data
      const dates = [];
      const prices = [];
      const opens = [];
      const highs = [];
      const lows = [];
      const closes = [];
      const volumes = [];

      // Sort timestamps in ascending order
      const timestamps = Object.keys(timeSeries).sort();

      timestamps.forEach(timestamp => {
        const entry = timeSeries[timestamp];

        dates.push(new Date(timestamp).toLocaleString());

        // Handle different property names based on asset type
        if (assetType === 'forex' || assetType === 'crypto') {
          opens.push(parseFloat(entry['1. open']));
          highs.push(parseFloat(entry['2. high']));
          lows.push(parseFloat(entry['3. low']));
          closes.push(parseFloat(entry['4. close']));
          volumes.push(parseFloat(entry['5. volume'] || 0));
          prices.push(parseFloat(entry['4. close']));
        } else {
          opens.push(parseFloat(entry['1. open']));
          highs.push(parseFloat(entry['2. high']));
          lows.push(parseFloat(entry['3. low']));
          closes.push(parseFloat(entry['4. close']));
          volumes.push(parseFloat(entry['5. volume'] || 0));
          prices.push(parseFloat(entry['4. close']));
        }
      });

      // Calculate daily change percentage
      const lastPrice = prices[prices.length - 1];
      const prevPrice = prices[prices.length - 2] || prices[prices.length - 1];
      const dailyChange = ((lastPrice - prevPrice) / prevPrice) * 100;

      // Calculate a simple sentiment score based on price movement
      const sentiment = Math.min(Math.max(Math.round(50 + dailyChange * 5), 0), 100);

      return {
        dates,
        prices,
        opens,
        highs,
        lows,
        closes,
        volumes,
        currentPrice: lastPrice,
        dailyChange,
        sentiment
      };
    } catch (error) {
      logger.error('Error transforming Alpha Vantage data:', error.message);
      logger.debug('Raw data:', JSON.stringify(data).substring(0, 500) + '...');

      // Return a minimal valid response structure with error information
      return {
        dates: [new Date().toLocaleString()],
        prices: [0],
        opens: [0],
        highs: [0],
        lows: [0],
        closes: [0],
        volumes: [0],
        currentPrice: 0,
        dailyChange: 0,
        sentiment: 50,
        error: error.message
      };
    }
  }

  // Helper function to determine asset type from symbol
  function getAssetTypeFromSymbol(symbol) {
    if (['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD'].includes(symbol)) {
      return 'forex';
    } else if (['XAUUSD', 'XAGUSD', 'USOIL'].includes(symbol)) {
      return 'commodities';
    } else if (['BTCUSD', 'ETHUSD'].includes(symbol)) {
      return 'crypto';
    } else {
      return 'stock';
    }
  }
};
