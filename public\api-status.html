<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Status Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        h1, h2 {
            color: #333;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .api-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            transition: transform 0.3s ease;
        }
        .api-card:hover {
            transform: translateY(-5px);
        }
        .api-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .api-name {
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0;
        }
        .status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-loading {
            background-color: #cce5ff;
            color: #004085;
        }
        .api-details {
            margin-top: 10px;
        }
        .api-details p {
            margin: 5px 0;
        }
        .error-message {
            color: #721c24;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 0.9rem;
        }
        .data-preview {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 0.9rem;
            max-height: 150px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .refresh-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            margin-top: 20px;
        }
        .refresh-button:hover {
            background-color: #0069d9;
        }
        .timestamp {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Trading Signals App - API Status Dashboard</h1>
    <p>This dashboard shows the current status of all external APIs used by the Trading Signals App.</p>
    
    <button id="refresh-button" class="refresh-button">Refresh API Status</button>
    
    <div id="dashboard" class="dashboard">
        <!-- API cards will be inserted here -->
        <div class="api-card">
            <div class="api-header">
                <h3 class="api-name">Alpha Vantage</h3>
                <span class="status status-loading">Loading...</span>
            </div>
            <div class="api-details">
                <p>Checking API status...</p>
            </div>
        </div>
        <div class="api-card">
            <div class="api-header">
                <h3 class="api-name">FRED</h3>
                <span class="status status-loading">Loading...</span>
            </div>
            <div class="api-details">
                <p>Checking API status...</p>
            </div>
        </div>
        <div class="api-card">
            <div class="api-header">
                <h3 class="api-name">Polygon</h3>
                <span class="status status-loading">Loading...</span>
            </div>
            <div class="api-details">
                <p>Checking API status...</p>
            </div>
        </div>
        <div class="api-card">
            <div class="api-header">
                <h3 class="api-name">Finnhub</h3>
                <span class="status status-loading">Loading...</span>
            </div>
            <div class="api-details">
                <p>Checking API status...</p>
            </div>
        </div>
        <div class="api-card">
            <div class="api-header">
                <h3 class="api-name">Twelve Data</h3>
                <span class="status status-loading">Loading...</span>
            </div>
            <div class="api-details">
                <p>Checking API status...</p>
            </div>
        </div>
    </div>
    
    <p id="timestamp" class="timestamp">Last updated: Loading...</p>
    
    <script>
        // API names mapping
        const apiNames = {
            alphaVantage: 'Alpha Vantage',
            fred: 'FRED',
            polygon: 'Polygon',
            finnhub: 'Finnhub',
            twelveData: 'Twelve Data'
        };
        
        // Function to fetch API status
        async function fetchApiStatus() {
            try {
                // Update UI to show loading state
                document.querySelectorAll('.api-card').forEach(card => {
                    const statusEl = card.querySelector('.status');
                    statusEl.className = 'status status-loading';
                    statusEl.textContent = 'Loading...';
                    
                    const detailsEl = card.querySelector('.api-details');
                    detailsEl.innerHTML = '<p>Checking API status...</p>';
                });
                
                // Fetch API status
                const response = await fetch('/api/test-all-apis');
                const data = await response.json();
                
                // Clear dashboard
                const dashboard = document.getElementById('dashboard');
                dashboard.innerHTML = '';
                
                // Update timestamp
                document.getElementById('timestamp').textContent = `Last updated: ${new Date().toLocaleString()}`;
                
                // Create API cards
                for (const [apiKey, apiData] of Object.entries(data.results)) {
                    const apiName = apiNames[apiKey] || apiKey;
                    
                    // Create card
                    const card = document.createElement('div');
                    card.className = 'api-card';
                    
                    // Create header
                    const header = document.createElement('div');
                    header.className = 'api-header';
                    
                    // Create API name
                    const nameEl = document.createElement('h3');
                    nameEl.className = 'api-name';
                    nameEl.textContent = apiName;
                    
                    // Create status
                    const statusEl = document.createElement('span');
                    statusEl.className = `status status-${apiData.success ? 'success' : 'error'}`;
                    statusEl.textContent = apiData.success ? 'Online' : 'Error';
                    
                    // Add name and status to header
                    header.appendChild(nameEl);
                    header.appendChild(statusEl);
                    
                    // Create details
                    const details = document.createElement('div');
                    details.className = 'api-details';
                    
                    if (apiData.success) {
                        // Add status code
                        const statusCodeEl = document.createElement('p');
                        statusCodeEl.textContent = `Status: ${apiData.status}`;
                        details.appendChild(statusCodeEl);
                        
                        // Add data preview
                        const dataPreviewEl = document.createElement('div');
                        dataPreviewEl.className = 'data-preview';
                        dataPreviewEl.textContent = JSON.stringify(apiData.data, null, 2).substring(0, 200) + '...';
                        details.appendChild(dataPreviewEl);
                    } else {
                        // Add error message
                        const errorEl = document.createElement('div');
                        errorEl.className = 'error-message';
                        errorEl.textContent = apiData.error || 'Unknown error';
                        details.appendChild(errorEl);
                        
                        if (apiData.status) {
                            const statusCodeEl = document.createElement('p');
                            statusCodeEl.textContent = `Status: ${apiData.status}`;
                            details.appendChild(statusCodeEl);
                        }
                    }
                    
                    // Add header and details to card
                    card.appendChild(header);
                    card.appendChild(details);
                    
                    // Add card to dashboard
                    dashboard.appendChild(card);
                }
            } catch (error) {
                console.error('Error fetching API status:', error);
                alert('Error fetching API status. Please try again.');
            }
        }
        
        // Fetch API status on page load
        document.addEventListener('DOMContentLoaded', fetchApiStatus);
        
        // Fetch API status when refresh button is clicked
        document.getElementById('refresh-button').addEventListener('click', fetchApiStatus);
    </script>
</body>
</html>
