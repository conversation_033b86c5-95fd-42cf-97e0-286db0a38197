import logger from '../utils/logger.js';
import {
  getHttpStatusForErrorCode,
  createStandardError,
  ERROR_CODE_TO_HTTP_STATUS
} from './responseFormatter.js';

// Enhanced API Error class aligned with TypeScript interfaces
export class APIError extends Error {
  constructor(statusCode, message, isOperational = true, requestId = null, code = null, details = null, suggestion = null) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.requestId = requestId || `req-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
    this.timestamp = new Date().toISOString();
    this.code = code || 'INTERNAL_SERVER_ERROR';
    this.details = details;
    this.suggestion = suggestion;
    Error.captureStackTrace(this, this.constructor);
  }

  // Create APIError from error code
  static fromCode(code, message, details = null, suggestion = null) {
    const statusCode = getHttpStatusForErrorCode(code);
    return new APIError(statusCode, message, true, null, code, details, suggestion);
  }
}

// Handle 404 errors
export const notFound = (req, res, next) => {
  const error = new APIError(404, `Not Found - ${req.originalUrl}`);
  next(error);
};

// Enhanced global error handler with standardized responses
export const errorHandler = (err, req, res, next) => {
  // Set default values if not present
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';
  err.requestId = err.requestId || req.requestId || `req-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
  err.timestamp = err.timestamp || new Date().toISOString();
  err.code = err.code || 'INTERNAL_SERVER_ERROR';

  // Log error with detailed information
  logger.error('API Error Handler', {
    message: err.message,
    stack: err.stack,
    statusCode: err.statusCode,
    errorCode: err.code,
    path: req.path,
    method: req.method,
    requestId: err.requestId,
    timestamp: err.timestamp,
    query: req.query,
    body: req.body,
    headers: {
      'user-agent': req.get('User-Agent'),
      'content-type': req.get('Content-Type'),
      'authorization': req.get('Authorization') ? '[REDACTED]' : undefined
    },
    ip: req.ip,
    isOperational: err.isOperational
  });

  // Handle API errors (404, 429, etc.)
  if (err.statusCode === 404) {
    return res.status(404).json({
      status: 'fail',
      requestId: err.requestId,
      timestamp: err.timestamp,
      message: err.message || 'The requested resource was not found',
      suggestion: 'Please check the URL and try again'
    });
  }

  // Handle rate limiting errors
  if (err.statusCode === 429) {
    return res.status(429).json({
      status: 'fail',
      requestId: err.requestId,
      timestamp: err.timestamp,
      message: err.message || 'Too many requests',
      suggestion: 'Please try again later',
      retryAfter: '60' // Suggest retry after 60 seconds
    });
  }

  // Use standardized response format
  if (res.error) {
    // Use the response formatter if available
    if (err.isOperational) {
      return res.error(
        err.code,
        err.message,
        err.statusCode,
        err.details,
        err.suggestion || getSuggestionForStatusCode(err.statusCode)
      );
    } else {
      // Programming or unknown error: don't leak details
      return res.error(
        'INTERNAL_SERVER_ERROR',
        'Something went wrong on our end',
        500,
        null,
        'Please try again later or contact support with this request ID'
      );
    }
  } else {
    // Fallback to manual response if formatter not available
    const response = {
      status: 'error',
      error: {
        code: err.code || 'INTERNAL_SERVER_ERROR',
        message: err.isOperational ? err.message : 'Something went wrong on our end',
        details: err.isOperational ? err.details : undefined,
        suggestion: err.isOperational ?
          (err.suggestion || getSuggestionForStatusCode(err.statusCode)) :
          'Please try again later or contact support with this request ID'
      },
      meta: {
        timestamp: err.timestamp,
        requestId: err.requestId,
        version: '1.0.0'
      }
    };

    return res.status(err.statusCode).json(response);
  }
};

// Helper function to get suggestions based on status code
function getSuggestionForStatusCode(statusCode) {
  switch (statusCode) {
    case 400:
      return 'Please check your request parameters and try again';
    case 401:
      return 'Please check your authentication credentials';
    case 403:
      return 'You do not have permission to access this resource';
    case 404:
      return 'The requested resource was not found';
    case 409:
      return 'This resource already exists or conflicts with existing data';
    case 422:
      return 'Please check your input data and try again';
    case 429:
      return 'You are making too many requests. Please wait and try again';
    case 500:
    case 502:
    case 503:
    case 504:
      return 'Please try again later or contact support if the problem persists';
    default:
      return 'Please try again or contact support if the problem persists';
  }
}

// Enhanced uncaught exception handler
process.on('uncaughtException', (error) => {
  logger.error('UNCAUGHT EXCEPTION - Application will exit', {
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString()
  });

  // Graceful shutdown
  process.exit(1);
});

// Enhanced unhandled promise rejection handler
process.on('unhandledRejection', (reason, promise) => {
  logger.error('UNHANDLED PROMISE REJECTION - Application will exit', {
    reason: reason instanceof Error ? reason.message : reason,
    stack: reason instanceof Error ? reason.stack : undefined,
    promise: promise.toString(),
    timestamp: new Date().toISOString()
  });

  // Graceful shutdown
  process.exit(1);
});

export default {
  APIError,
  notFound,
  errorHandler,
};