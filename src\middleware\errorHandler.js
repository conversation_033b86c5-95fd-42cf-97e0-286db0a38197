import logger from '../utils/logger.js';

// Custom error class for API errors
export class APIError extends Error {
  constructor(statusCode, message, isOperational = true, requestId = null) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.requestId = requestId || `req-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
    this.timestamp = new Date().toISOString();
    Error.captureStackTrace(this, this.constructor);
  }
}

// Handle 404 errors
export const notFound = (req, res, next) => {
  const error = new APIError(404, `Not Found - ${req.originalUrl}`);
  next(error);
};

// Global error handler with improved client responses
export const errorHandler = (err, req, res, next) => {
  // Set default values if not present
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';
  err.requestId = err.requestId || `req-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
  err.timestamp = err.timestamp || new Date().toISOString();

  // Log error with detailed information
  logger.error('Error:', {
    message: err.message,
    stack: err.stack,
    statusCode: err.statusCode,
    path: req.path,
    method: req.method,
    requestId: err.requestId,
    timestamp: err.timestamp,
    query: req.query,
    headers: req.headers,
    ip: req.ip
  });

  // Handle API errors (404, 429, etc.)
  if (err.statusCode === 404) {
    return res.status(404).json({
      status: 'fail',
      requestId: err.requestId,
      timestamp: err.timestamp,
      message: err.message || 'The requested resource was not found',
      suggestion: 'Please check the URL and try again'
    });
  }

  // Handle rate limiting errors
  if (err.statusCode === 429) {
    return res.status(429).json({
      status: 'fail',
      requestId: err.requestId,
      timestamp: err.timestamp,
      message: err.message || 'Too many requests',
      suggestion: 'Please try again later',
      retryAfter: '60' // Suggest retry after 60 seconds
    });
  }

  // Operational, trusted error: send message to client with helpful information
  if (err.isOperational) {
    const response = {
      status: err.status,
      requestId: err.requestId,
      timestamp: err.timestamp,
      message: err.message
    };

    // Add suggestions based on status code
    if (err.statusCode >= 500) {
      response.suggestion = 'Please try again later or contact support if the problem persists';
    } else if (err.statusCode === 401 || err.statusCode === 403) {
      response.suggestion = 'Please check your authentication credentials';
    } else if (err.statusCode === 400) {
      response.suggestion = 'Please check your request parameters and try again';
    }

    return res.status(err.statusCode).json(response);
  }

  // Programming or other unknown error: don't leak error details
  // Send generic message with request ID for tracking
  return res.status(500).json({
    status: 'error',
    requestId: err.requestId,
    timestamp: err.timestamp,
    message: 'Something went wrong on our end',
    suggestion: 'Please try again later or contact support with this request ID'
  });
};

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('UNCAUGHT EXCEPTION:', {
    message: error.message,
    stack: error.stack,
  });
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (error) => {
  logger.error('UNHANDLED REJECTION:', {
    message: error.message,
    stack: error.stack,
  });
  process.exit(1);
});

export default {
  APIError,
  notFound,
  errorHandler,
};