<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Icon Generator - Trading Signals App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        h1 {
            color: #0d6efd;
            text-align: center;
        }
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        .preview {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
        }
        .icon-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ddd;
            margin-bottom: 10px;
            border-radius: 8px;
            background-color: white;
        }
        button {
            background-color: #0d6efd;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.2s ease;
        }
        button:hover {
            background-color: #0b5ed7;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .instructions {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #0d6efd;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            display: none;
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            display: none;
        }
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
    </style>
</head>
<body>
    <h1>PWA Icon Generator for Trading Signals App</h1>

    <div class="container">
        <div class="instructions">
            <h2>Instructions</h2>
            <p>This tool generates the required PWA icons for your Trading Signals App. Click the "Generate Icons" button to create the icons, then download them using the "Download" buttons.</p>
            <p>After downloading, place the icons in your project's <code>public</code> directory.</p>
            <p><strong>Note:</strong> The icons will have a blue (#0d6efd) circular background with a white chart line, matching your app's design.</p>
        </div>

        <div id="successMessage" class="success-message">
            Icons generated successfully! You can now download them.
        </div>

        <div id="errorMessage" class="error-message">
            There was an error generating the icons. Please try again.
        </div>

        <div class="button-group">
            <button id="generateBtn">Generate Icons</button>
            <button id="generateAllBtn">Generate & Download All</button>
        </div>

        <div class="preview">
            <div class="icon-container">
                <canvas id="canvas192" width="192" height="192"></canvas>
                <p>icon-192.png (192×192)</p>
                <button id="download192" disabled>Download icon-192.png</button>
            </div>

            <div class="icon-container">
                <canvas id="canvas512" width="512" height="512"></canvas>
                <p>icon-512.png (512×512)</p>
                <button id="download512" disabled>Download icon-512.png</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const canvas192 = document.getElementById('canvas192');
            const canvas512 = document.getElementById('canvas512');
            const ctx192 = canvas192.getContext('2d');
            const ctx512 = canvas512.getContext('2d');
            const generateBtn = document.getElementById('generateBtn');
            const generateAllBtn = document.getElementById('generateAllBtn');
            const download192Btn = document.getElementById('download192');
            const download512Btn = document.getElementById('download512');
            const successMessage = document.getElementById('successMessage');
            const errorMessage = document.getElementById('errorMessage');

            /**
             * Create an icon with the Trading Signals App design
             * @param {CanvasRenderingContext2D} ctx - Canvas context
             * @param {number} size - Icon size in pixels
             */
            function createIcon(ctx, size) {
                try {
                    // Clear canvas
                    ctx.clearRect(0, 0, size, size);

                    // Background
                    ctx.fillStyle = '#0d6efd'; // Primary blue color
                    ctx.beginPath();
                    ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
                    ctx.fill();

                    // Inner circle
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
                    ctx.beginPath();
                    ctx.arc(size / 2, size / 2, size * 0.45, 0, Math.PI * 2);
                    ctx.fill();

                    // Chart line
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = size * 0.06;
                    ctx.lineCap = 'round';
                    ctx.lineJoin = 'round';

                    // Draw chart line
                    ctx.beginPath();
                    ctx.moveTo(size * 0.2, size * 0.6);
                    ctx.lineTo(size * 0.35, size * 0.4);
                    ctx.lineTo(size * 0.5, size * 0.7);
                    ctx.lineTo(size * 0.65, size * 0.3);
                    ctx.lineTo(size * 0.8, size * 0.5);
                    ctx.stroke();

                    // Add a subtle shadow
                    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
                    ctx.shadowBlur = size * 0.05;
                    ctx.shadowOffsetX = size * 0.01;
                    ctx.shadowOffsetY = size * 0.01;

                    // Draw a small circle at the end of the line
                    ctx.fillStyle = 'white';
                    ctx.beginPath();
                    ctx.arc(size * 0.8, size * 0.5, size * 0.03, 0, Math.PI * 2);
                    ctx.fill();

                    return true;
                } catch (error) {
                    console.error('Error creating icon:', error);
                    showError('Error creating icon: ' + error.message);
                    return false;
                }
            }

            /**
             * Generate all icons
             * @returns {boolean} Success status
             */
            function generateIcons() {
                try {
                    const success1 = createIcon(ctx192, 192);
                    const success2 = createIcon(ctx512, 512);

                    if (success1 && success2) {
                        download192Btn.disabled = false;
                        download512Btn.disabled = false;
                        showSuccess('Icons generated successfully!');
                        return true;
                    } else {
                        return false;
                    }
                } catch (error) {
                    console.error('Error generating icons:', error);
                    showError('Error generating icons: ' + error.message);
                    return false;
                }
            }

            /**
             * Download an icon
             * @param {HTMLCanvasElement} canvas - Canvas element
             * @param {string} filename - Filename for download
             */
            function downloadIcon(canvas, filename) {
                try {
                    const link = document.createElement('a');
                    link.download = filename;
                    link.href = canvas.toDataURL('image/png');
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    return true;
                } catch (error) {
                    console.error('Error downloading icon:', error);
                    showError('Error downloading icon: ' + error.message);
                    return false;
                }
            }

            /**
             * Show success message
             * @param {string} message - Success message
             */
            function showSuccess(message) {
                successMessage.textContent = message;
                successMessage.style.display = 'block';
                errorMessage.style.display = 'none';

                // Hide after 5 seconds
                setTimeout(() => {
                    successMessage.style.display = 'none';
                }, 5000);
            }

            /**
             * Show error message
             * @param {string} message - Error message
             */
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
                successMessage.style.display = 'none';

                // Hide after 5 seconds
                setTimeout(() => {
                    errorMessage.style.display = 'none';
                }, 5000);
            }

            // Generate icons button
            generateBtn.addEventListener('click', function() {
                generateIcons();
            });

            // Generate and download all icons button
            generateAllBtn.addEventListener('click', function() {
                if (generateIcons()) {
                    setTimeout(() => {
                        downloadIcon(canvas192, 'icon-192.png');
                        setTimeout(() => {
                            downloadIcon(canvas512, 'icon-512.png');
                            showSuccess('All icons generated and downloaded successfully!');
                        }, 500);
                    }, 500);
                }
            });

            // Download icon-192.png button
            download192Btn.addEventListener('click', function() {
                downloadIcon(canvas192, 'icon-192.png');
            });

            // Download icon-512.png button
            download512Btn.addEventListener('click', function() {
                downloadIcon(canvas512, 'icon-512.png');
            });
        });
    </script>
</body>
</html>
