import React, { useState, useEffect } from 'react';
import { alphaVantageAPI, finnhubAPI } from '../../../services/apiService.js';

/**
 * TradingSignals Component
 *
 * Displays trading signals with filtering options and detailed analysis
 */
const TradingSignals = () => {
  // State for filters
  const [filters, setFilters] = useState({
    market: 'forex',
    symbol: 'EURUSD',
    timeframe: 'H1',
    signalType: 'all'
  });

  // State for signals
  const [signals, setSignals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // State for selected signal details
  const [selectedSignal, setSelectedSignal] = useState(null);

  // Available options for filters
  const marketOptions = [
    { value: 'forex', label: 'Forex' },
    { value: 'commodities', label: 'Commodities' },
    { value: 'indices', label: 'Indices' },
    { value: 'crypto', label: 'Cryptocurrencies' }
  ];

  const symbolOptions = {
    forex: [
      { value: 'EURUSD', label: 'EUR/USD' },
      { value: 'GBPUSD', label: 'GBP/USD' },
      { value: 'USDJPY', label: 'USD/JPY' },
      { value: 'AUDUSD', label: 'AUD/USD' }
    ],
    commodities: [
      { value: 'XAUUSD', label: 'Gold (XAU/USD)' },
      { value: 'XAGUSD', label: 'Silver (XAG/USD)' },
      { value: 'WTIUSD', label: 'Crude Oil (WTI)' }
    ],
    indices: [
      { value: 'SPX', label: 'S&P 500' },
      { value: 'NDX', label: 'NASDAQ 100' },
      { value: 'DJI', label: 'Dow Jones' }
    ],
    crypto: [
      { value: 'BTCUSD', label: 'Bitcoin (BTC/USD)' },
      { value: 'ETHUSD', label: 'Ethereum (ETH/USD)' }
    ]
  };

  const timeframeOptions = [
    { value: 'M5', label: '5 Minutes' },
    { value: 'M15', label: '15 Minutes' },
    { value: 'M30', label: '30 Minutes' },
    { value: 'H1', label: '1 Hour' },
    { value: 'H4', label: '4 Hours' },
    { value: 'D1', label: 'Daily' }
  ];

  const signalTypeOptions = [
    { value: 'all', label: 'All Signals' },
    { value: 'buy', label: 'Buy Signals' },
    { value: 'sell', label: 'Sell Signals' }
  ];

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;

    // If market changes, reset symbol to first option of that market
    if (name === 'market') {
      setFilters({
        ...filters,
        market: value,
        symbol: symbolOptions[value][0].value
      });
    } else {
      setFilters({
        ...filters,
        [name]: value
      });
    }
  };

  // Apply filters and fetch signals
  const applyFilters = async () => {
    setLoading(true);
    setError(null);

    try {
      // In a real app, this would be an API call to your backend
      // For now, we'll generate mock signals based on the filters
      await fetchSignals(filters);
    } catch (err) {
      console.error('Error fetching signals:', err);
      setError('Failed to load trading signals. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch signals based on filters
  const fetchSignals = async (filterParams) => {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      if (filterParams.symbol && filterParams.symbol !== 'all') {
        queryParams.append('symbol', filterParams.symbol);
      }

      if (filterParams.signalType && filterParams.signalType !== 'all') {
        queryParams.append('type', filterParams.signalType.toUpperCase());
      }

      // Add pagination
      queryParams.append('page', '1');
      queryParams.append('limit', '20');

      // Add status filter for active signals
      queryParams.append('status', 'ACTIVE');

      // Make API call to fetch signals
      const response = await fetch(`/api/signals?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers if available
          ...(window.Auth?.getAuthHeaders() || {})
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch signals: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      if (result.status === 'success' && Array.isArray(result.data)) {
        // Transform API response to component format
        const transformedSignals = result.data.map(signal => ({
          id: signal.id || signal._id,
          symbol: signal.symbol,
          market: filterParams.market, // Keep filter context
          timeframe: signal.timeframe || filterParams.timeframe,
          type: signal.type,
          entryPrice: signal.entryPrice,
          stopLoss: signal.stopLoss,
          takeProfit: signal.takeProfit,
          riskReward: signal.riskReward || (signal.takeProfit && signal.stopLoss ?
            parseFloat((Math.abs(signal.takeProfit - signal.entryPrice) / Math.abs(signal.stopLoss - signal.entryPrice)).toFixed(2)) :
            'N/A'),
          confidence: signal.confidence,
          timestamp: signal.timestamp || signal.createdAt,
          indicators: signal.technicalIndicators ?
            Object.keys(signal.technicalIndicators).filter(key => signal.technicalIndicators[key] !== null) :
            [],
          analysis: signal.reasoning || signal.message || 'No analysis available',
          source: signal.source,
          status: signal.status
        }));

        setSignals(transformedSignals);
      } else {
        throw new Error('Invalid response format from signals API');
      }

    } catch (error) {
      console.error('Error fetching signals from API:', error);

      // Show user-friendly error message
      setError(`Failed to load signals: ${error.message}`);

      // Fallback: try to generate unified signals if no existing signals found
      if (error.message.includes('404') || error.message.includes('No signals found')) {
        await generateNewSignals(filterParams);
      }
    }
  };

  // Generate new signals using unified signal service
  const generateNewSignals = async (filterParams) => {
    try {
      // Get current market data (this would typically come from a market data service)
      const marketData = await getCurrentMarketData(filterParams.symbol);

      // Call unified signals generation endpoint
      const response = await fetch('/api/signals/unified', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(window.Auth?.getAuthHeaders() || {})
        },
        body: JSON.stringify({
          symbol: filterParams.symbol,
          marketData: [marketData],
          indicators: {
            rsi: 50, // Default values - would be calculated from real market data
            macd: { line: 0, signal: 0, histogram: 0 },
            movingAverages: { sma20: marketData.close, sma50: marketData.close }
          },
          options: {
            timeframe: filterParams.timeframe,
            enableAI: true,
            enableTraditional: true,
            minConfidence: 50
          }
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.status === 'success' && result.data.length > 0) {
          // Refresh signals after generation
          await fetchSignals(filterParams);
        } else {
          setError('No signals could be generated for the selected criteria.');
        }
      } else {
        throw new Error(`Signal generation failed: ${response.status}`);
      }

    } catch (error) {
      console.error('Error generating new signals:', error);
      setError('Failed to generate new signals. Please try again later.');
    }
  };

  // Get current market data for a symbol
  const getCurrentMarketData = async (symbol) => {
    // This would typically call a market data API
    // For now, return mock data structure that matches expected format
    const basePrice = symbol === 'BTCUSD' ? 35000 :
                      symbol === 'ETHUSD' ? 2000 :
                      symbol === 'XAUUSD' ? 1900 :
                      symbol.includes('JPY') ? 140 :
                      1.0850; // Default for forex pairs

    return {
      timestamp: new Date().toISOString(),
      open: basePrice * 0.999,
      high: basePrice * 1.001,
      low: basePrice * 0.998,
      close: basePrice,
      volume: 1000000
    };
  };

  // View signal details
  const viewSignalDetails = (signal) => {
    setSelectedSignal(signal);
  };

  // Close signal details modal
  const closeSignalDetails = () => {
    setSelectedSignal(null);
  };

  // Apply filters on component mount and when filters change
  useEffect(() => {
    applyFilters();
  }, []);

  return (
    <div className="trading-signals p-4">
      <h1 className="text-2xl font-bold mb-6">Trading Signals</h1>

      {/* Filters Section */}
      <section className="mb-8">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Filters</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Market Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Market
              </label>
              <select
                name="market"
                value={filters.market}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {marketOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>

            {/* Symbol Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Symbol
              </label>
              <select
                name="symbol"
                value={filters.symbol}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {symbolOptions[filters.market].map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>

            {/* Timeframe Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Timeframe
              </label>
              <select
                name="timeframe"
                value={filters.timeframe}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {timeframeOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>

            {/* Signal Type Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Signal Type
              </label>
              <select
                name="signalType"
                value={filters.signalType}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {signalTypeOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="mt-4">
            <button
              onClick={applyFilters}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </section>

      {/* Signals Section */}
      <section>
        <h2 className="text-lg font-semibold mb-4">Signals</h2>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        ) : signals.length === 0 ? (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">No signals found for the selected filters.</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {signals.map(signal => (
              <div
                key={signal.id}
                className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => viewSignalDetails(signal)}
              >
                <div className={`p-2 text-white text-center ${signal.type === 'BUY' ? 'bg-green-600' : 'bg-red-600'}`}>
                  {signal.type} {signal.symbol} ({signal.timeframe})
                </div>
                <div className="p-4">
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-500 dark:text-gray-400">Entry Price:</span>
                    <span className="font-medium">{signal.entryPrice}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-500 dark:text-gray-400">Stop Loss:</span>
                    <span className="font-medium text-red-500">{signal.stopLoss}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-500 dark:text-gray-400">Take Profit:</span>
                    <span className="font-medium text-green-500">{signal.takeProfit}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-500 dark:text-gray-400">Risk/Reward:</span>
                    <span className="font-medium">{signal.riskReward}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-500 dark:text-gray-400">Confidence:</span>
                    <span className="font-medium">{signal.confidence}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Time:</span>
                    <span className="font-medium">{new Date(signal.timestamp).toLocaleString()}</span>
                  </div>
                </div>
                <div className="p-2 bg-gray-100 dark:bg-gray-700 text-center">
                  <button className="text-blue-600 dark:text-blue-400 hover:underline">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </section>

      {/* Signal Details Modal */}
      {selectedSignal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className={`p-4 text-white ${selectedSignal.type === 'BUY' ? 'bg-green-600' : 'bg-red-600'}`}>
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-bold">
                  {selectedSignal.type} {selectedSignal.symbol} ({selectedSignal.timeframe})
                </h3>
                <button
                  onClick={closeSignalDetails}
                  className="text-white hover:text-gray-200"
                >
                  &times;
                </button>
              </div>
            </div>

            <div className="p-4">
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <h4 className="text-sm text-gray-500 dark:text-gray-400">Entry Price</h4>
                  <p className="text-lg font-medium">{selectedSignal.entryPrice}</p>
                </div>
                <div>
                  <h4 className="text-sm text-gray-500 dark:text-gray-400">Confidence</h4>
                  <p className="text-lg font-medium">{selectedSignal.confidence}%</p>
                </div>
                <div>
                  <h4 className="text-sm text-gray-500 dark:text-gray-400">Stop Loss</h4>
                  <p className="text-lg font-medium text-red-500">{selectedSignal.stopLoss}</p>
                </div>
                <div>
                  <h4 className="text-sm text-gray-500 dark:text-gray-400">Take Profit</h4>
                  <p className="text-lg font-medium text-green-500">{selectedSignal.takeProfit}</p>
                </div>
                <div>
                  <h4 className="text-sm text-gray-500 dark:text-gray-400">Risk/Reward Ratio</h4>
                  <p className="text-lg font-medium">{selectedSignal.riskReward}</p>
                </div>
                <div>
                  <h4 className="text-sm text-gray-500 dark:text-gray-400">Time</h4>
                  <p className="text-lg font-medium">{new Date(selectedSignal.timestamp).toLocaleString()}</p>
                </div>
              </div>

              <div className="mb-4">
                <h4 className="text-sm text-gray-500 dark:text-gray-400 mb-1">Technical Indicators</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedSignal.indicators.map((indicator, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded text-sm"
                    >
                      {indicator}
                    </span>
                  ))}
                </div>
              </div>

              <div className="mb-4">
                <h4 className="text-sm text-gray-500 dark:text-gray-400 mb-1">Analysis</h4>
                <p className="text-gray-700 dark:text-gray-300">{selectedSignal.analysis}</p>
              </div>

              <div className="flex justify-end space-x-2">
                <button
                  onClick={closeSignalDetails}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TradingSignals;
