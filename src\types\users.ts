/**
 * User Types and Interfaces
 * 
 * Comprehensive type definitions for user management, authentication,
 * preferences, and subscription handling.
 * 
 * @version 1.0.0
 */

import {
  UserRole,
  Timestamp,
  ObjectId,
  UserPreferences,
  UserMetadata,
  SubscriptionInfo,
  ValidationResult
} from './common';

// ============================================================================
// CORE USER INTERFACES
// ============================================================================

/**
 * Unified User Interface
 * 
 * Standard user object used across the entire application.
 * Ensures consistency between database, API, and frontend.
 */
export interface User {
  // Core identification
  id: string;
  _id?: ObjectId; // MongoDB ObjectId
  
  // Basic information
  email: string;
  firstName: string;
  lastName: string;
  displayName?: string;
  avatar?: string;
  
  // Authentication
  password?: string; // Only present in backend, never sent to frontend
  emailVerified: boolean;
  emailVerificationToken?: string;
  
  // Authorization
  role: UserRole;
  permissions: string[];
  
  // Account status
  active: boolean;
  suspended: boolean;
  suspensionReason?: string;
  
  // Security
  twoFactorEnabled: boolean;
  twoFactorSecret?: string;
  lastPasswordChange?: Timestamp;
  failedLoginAttempts: number;
  lockedUntil?: Timestamp;
  
  // Social features
  referralCode?: string;
  referredBy?: ObjectId;
  referralCount: number;
  
  // Preferences and settings
  preferences: UserPreferences;
  
  // Subscription and billing
  subscription: SubscriptionInfo;
  
  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
  lastLogin?: Timestamp;
  
  // Metadata
  metadata: UserMetadata;
}

// ============================================================================
// USER PROFILE TYPES
// ============================================================================

export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  displayName: string;
  avatar?: string;
  role: UserRole;
  emailVerified: boolean;
  active: boolean;
  createdAt: Timestamp;
  lastLogin?: Timestamp;
  preferences: UserPreferences;
  subscription: SubscriptionInfo;
  stats: UserStats;
}

export interface UserStats {
  totalSignals: number;
  activeSignals: number;
  successfulSignals: number;
  winRate: number;
  totalPnL: number;
  averageReturn: number;
  tradingDays: number;
  lastActivity: Timestamp;
}

// ============================================================================
// AUTHENTICATION TYPES
// ============================================================================

export interface AuthenticatedUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  permissions: string[];
  emailVerified: boolean;
  subscription: {
    plan: string;
    status: string;
    features: string[];
  };
  preferences: UserPreferences;
}

export interface UserSession {
  userId: string;
  sessionId: string;
  token: string;
  refreshToken: string;
  expiresAt: Timestamp;
  createdAt: Timestamp;
  lastActivity: Timestamp;
  ipAddress: string;
  userAgent: string;
  active: boolean;
}

export interface LoginAttempt {
  email: string;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  timestamp: Timestamp;
  failureReason?: string;
}

// ============================================================================
// USER CREATION AND UPDATES
// ============================================================================

export interface CreateUserRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: UserRole;
  referralCode?: string;
  acceptTerms: boolean;
  acceptPrivacy: boolean;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  displayName?: string;
  avatar?: string;
  preferences?: Partial<UserPreferences>;
}

export interface UpdateUserPasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface UpdateUserEmailRequest {
  newEmail: string;
  password: string;
}

// ============================================================================
// USER SEARCH AND FILTERING
// ============================================================================

export interface UserFilter {
  role?: UserRole[];
  active?: boolean;
  emailVerified?: boolean;
  suspended?: boolean;
  subscriptionPlan?: string[];
  createdFrom?: Timestamp;
  createdTo?: Timestamp;
  lastLoginFrom?: Timestamp;
  lastLoginTo?: Timestamp;
  search?: string; // Search in name, email
}

export interface UserSortOptions {
  field: 'createdAt' | 'lastLogin' | 'email' | 'firstName' | 'lastName';
  direction: 'asc' | 'desc';
}

export interface UserQueryOptions {
  filter?: UserFilter;
  sort?: UserSortOptions;
  limit?: number;
  offset?: number;
  includeStats?: boolean;
  includeSubscription?: boolean;
}

// ============================================================================
// USER PERMISSIONS
// ============================================================================

export interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  resource: string;
  action: string;
}

export interface RolePermissions {
  role: UserRole;
  permissions: Permission[];
  inherits?: UserRole[];
}

// ============================================================================
// USER ACTIVITY TRACKING
// ============================================================================

export interface UserActivity {
  id: string;
  userId: ObjectId;
  type: UserActivityType;
  description: string;
  metadata?: any;
  ipAddress: string;
  userAgent: string;
  timestamp: Timestamp;
}

export enum UserActivityType {
  LOGIN = 'login',
  LOGOUT = 'logout',
  PASSWORD_CHANGE = 'password_change',
  EMAIL_CHANGE = 'email_change',
  PROFILE_UPDATE = 'profile_update',
  SIGNAL_CREATE = 'signal_create',
  SIGNAL_UPDATE = 'signal_update',
  SIGNAL_DELETE = 'signal_delete',
  SUBSCRIPTION_CHANGE = 'subscription_change',
  SETTINGS_UPDATE = 'settings_update',
  TWO_FACTOR_ENABLE = 'two_factor_enable',
  TWO_FACTOR_DISABLE = 'two_factor_disable'
}

// ============================================================================
// USER NOTIFICATIONS
// ============================================================================

export interface UserNotification {
  id: string;
  userId: ObjectId;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  expiresAt?: Timestamp;
  createdAt: Timestamp;
  readAt?: Timestamp;
}

export enum NotificationType {
  SIGNAL_ALERT = 'signal_alert',
  MARKET_UPDATE = 'market_update',
  SYSTEM_NOTIFICATION = 'system_notification',
  SUBSCRIPTION_UPDATE = 'subscription_update',
  SECURITY_ALERT = 'security_alert',
  FEATURE_ANNOUNCEMENT = 'feature_announcement',
  MAINTENANCE_NOTICE = 'maintenance_notice'
}

// ============================================================================
// USER ANALYTICS
// ============================================================================

export interface UserAnalytics {
  userId: ObjectId;
  period: {
    from: Timestamp;
    to: Timestamp;
  };
  metrics: {
    signalsCreated: number;
    signalsSuccessful: number;
    winRate: number;
    totalPnL: number;
    averageReturn: number;
    bestTrade: number;
    worstTrade: number;
    tradingDays: number;
    averageHoldTime: number;
  };
  breakdown: {
    bySymbol: Record<string, UserStats>;
    byTimeframe: Record<string, UserStats>;
    bySignalType: Record<string, UserStats>;
  };
  trends: {
    daily: UserStats[];
    weekly: UserStats[];
    monthly: UserStats[];
  };
}

// ============================================================================
// TYPE GUARDS AND VALIDATORS
// ============================================================================

export function isUser(obj: any): obj is User {
  return (
    obj &&
    typeof obj.id === 'string' &&
    typeof obj.email === 'string' &&
    typeof obj.firstName === 'string' &&
    typeof obj.lastName === 'string' &&
    Object.values(UserRole).includes(obj.role) &&
    typeof obj.emailVerified === 'boolean' &&
    typeof obj.active === 'boolean' &&
    typeof obj.createdAt === 'string'
  );
}

export function isAuthenticatedUser(obj: any): obj is AuthenticatedUser {
  return (
    obj &&
    typeof obj.id === 'string' &&
    typeof obj.email === 'string' &&
    Object.values(UserRole).includes(obj.role) &&
    Array.isArray(obj.permissions) &&
    typeof obj.emailVerified === 'boolean'
  );
}

export function validateUserEmail(email: string): ValidationResult {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const errors = [];
  
  if (!email) {
    errors.push({ field: 'email', message: 'Email is required', code: 'REQUIRED' });
  } else if (!emailRegex.test(email)) {
    errors.push({ field: 'email', message: 'Invalid email format', code: 'INVALID_FORMAT' });
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

export function validateUserPassword(password: string): ValidationResult {
  const errors = [];
  
  if (!password) {
    errors.push({ field: 'password', message: 'Password is required', code: 'REQUIRED' });
  } else {
    if (password.length < 8) {
      errors.push({ field: 'password', message: 'Password must be at least 8 characters', code: 'TOO_SHORT' });
    }
    if (!/[A-Z]/.test(password)) {
      errors.push({ field: 'password', message: 'Password must contain uppercase letter', code: 'MISSING_UPPERCASE' });
    }
    if (!/[a-z]/.test(password)) {
      errors.push({ field: 'password', message: 'Password must contain lowercase letter', code: 'MISSING_LOWERCASE' });
    }
    if (!/\d/.test(password)) {
      errors.push({ field: 'password', message: 'Password must contain number', code: 'MISSING_NUMBER' });
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push({ field: 'password', message: 'Password must contain special character', code: 'MISSING_SPECIAL' });
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export function createUserDisplayName(firstName: string, lastName: string): string {
  return `${firstName} ${lastName}`.trim();
}

export function generateReferralCode(userId: string): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `${userId.substring(0, 4)}${timestamp}${random}`.toUpperCase();
}

export function getUserFullName(user: User | UserProfile): string {
  return createUserDisplayName(user.firstName, user.lastName);
}

export function isUserActive(user: User): boolean {
  return user.active && !user.suspended && (!user.lockedUntil || new Date(user.lockedUntil) < new Date());
}

export function hasPermission(user: AuthenticatedUser, permission: string): boolean {
  return user.permissions.includes(permission) || user.role === UserRole.ADMIN;
}

export function canAccessFeature(user: AuthenticatedUser, feature: string): boolean {
  if (!user.subscription || user.subscription.status !== 'active') {
    return false;
  }
  return user.subscription.features.includes(feature);
}
