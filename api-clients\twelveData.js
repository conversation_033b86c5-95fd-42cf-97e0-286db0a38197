const axios = require('axios');
const logger = console;

// Rate limiting and retry configuration
const RATE_LIMIT_DELAY = 1000; // 1 second between requests
const MAX_RETRIES = 3;

// Error handling wrapper with retries
const handleApiRequest = async (requestFn) => {
  let attempts = 0;
  while (attempts < MAX_RETRIES) {
    try {
      if (attempts > 0) {
        await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY));
      }
      return await requestFn();
    } catch (error) {
      attempts++;
      if (attempts === MAX_RETRIES) {
        if (error.response) {
          logger.error(`TwelveData API error: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
          throw new Error(`TwelveData API error: ${error.response.status}`);
        } else if (error.request) {
          logger.error('TwelveData API no response');
          throw new Error('TwelveData API no response');
        } else {
          logger.error(`TwelveData API request failed: ${error.message}`);
          throw new Error(`TwelveData API request failed: ${error.message}`);
        }
      }
      logger.warn(`Retrying request, attempt ${attempts} of ${MAX_RETRIES}`);
    }
  }
};

/**
 * TwelveData API client
 * @param {Object} query - Query parameters
 * @param {string} query.type - Market type (forex, stocks, commodities)
 * @param {string} query.symbol - Market symbol
 * @param {string} query.timeframe - Time frame (1min, 5min, 15min, 30min, 1h, 2h, 4h, 1day, 1week, 1month)
 * @returns {Promise<Object>} - Market data
 */
module.exports = async function twelveData(query) {
  // API key is already validated in main.js validateApiKeys()
  const apiKey = process.env.TWELVE_DATA_API_KEY;

  // Map timeframe to TwelveData interval
  const timeframeMap = {
    'M1': '1min',
    'M5': '5min',
    'M15': '15min',
    'M30': '30min',
    'H1': '1h',
    'H4': '4h',
    'D1': '1day',
    'W1': '1week',
    'MN': '1month'
  };
  
  const interval = timeframeMap[query.timeframe] || '1day';
  let endpoint;
  let params = {
    symbol: query.symbol,
    apikey: apiKey,
    format: 'json'
  };

  try {
    // Choose appropriate endpoint based on query requirements
    if (query.timeframe) {
      // Time series data (OHLC)
      endpoint = 'time_series';
      params.interval = interval;
      params.outputsize = 30; // Get 30 data points
    } else {
      // Just current price
      endpoint = 'price';
    }
    
    if (!['commodities', 'forex', 'stocks'].includes(query.type)) {
      throw new Error(`Unsupported market type for TwelveData: ${query.type}`);
    }
    
    // Adjust symbol formatting for different market types
    if (query.type === 'forex') {
      // Make sure forex symbols use "/" format, e.g., EUR/USD
      const base = query.symbol.slice(0, 3);
      const quote = query.symbol.slice(3, 6) || 'USD';
      params.symbol = `${base}/${quote}`;
    }
    
    console.log(`Calling TwelveData API: ${endpoint} for ${params.symbol}`);
    const url = `https://api.twelvedata.com/${endpoint}`;
    const response = await axios.get(url, { 
      params, 
      timeout: 10000,
      headers: {
        'User-Agent': 'TradingSignalsApp/1.0'
      }
    });
    
    // Handle API errors
    if (response.data && response.data.code === 401) {
      throw new Error(`TwelveData API authentication error: ${response.data.message}`);
    }
    
    if (response.data && response.data.status === 'error') {
      throw new Error(`TwelveData API error: ${response.data.message}`);
    }
    
    return formatResponse(response.data, query, endpoint);
  } catch (error) {
    console.error('TwelveData API error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    throw error;
  }
};

/**
 * Format the TwelveData response to a standard format
 * @param {Object} data - TwelveData response
 * @param {Object} query - Original query
 * @param {string} endpoint - API endpoint used
 * @returns {Object} - Formatted response
 */
function formatResponse(data, query, endpoint) {
  if (!data) {
    throw new Error('Empty response from TwelveData');
  }
  
  // Return standardized format for all market types
  const result = {
    symbol: query.symbol,
    timeframe: query.timeframe,
    type: query.type,
    source: 'twelve_data',
    timestamp: new Date().toISOString(),
    data: []
  };
  
  try {
    if (endpoint === 'time_series' && data.values) {
      // Process time series data
      result.data = data.values.map(item => ({
        timestamp: item.datetime,
        open: parseFloat(item.open),
        high: parseFloat(item.high),
        low: parseFloat(item.low),
        close: parseFloat(item.close),
        volume: parseInt(item.volume || '0', 10)
      }));
      
      // Add meta information
      if (data.meta) {
        result.meta = {
          symbol: data.meta.symbol,
          interval: data.meta.interval,
          currency_base: data.meta.currency_base,
          currency_quote: data.meta.currency_quote,
          type: data.meta.type
        };
      }
    } else if (endpoint === 'price') {
      // Process simple price data
      result.data = [{
        timestamp: new Date().toISOString(),
        close: parseFloat(data.price)
      }];
    }
    
    return result;
  } catch (error) {
    console.error('Error formatting TwelveData response:', error);
    throw new Error(`Failed to format TwelveData response: ${error.message}`);
  }
} 