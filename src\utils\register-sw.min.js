/**
 * Enhanced Service Worker Registration for Trading Signals App
 * 
 * Features:
 * - Automatic service worker registration
 * - Offline detection and handling
 * - Update notification
 * - Background sync for critical data
 * - Periodic update checks
 */

// Configuration
const SW_CONFIG = {
  path: '/src/service-worker.js',
  updateCheckInterval: 60 * 60 * 1000, // 1 hour
  updateNotification: true,
  offlineRedirect: true,
  backgroundSync: true
};

// Check online status and redirect to offline page if needed
function checkOnlineStatus() {
  if (!navigator.onLine && SW_CONFIG.offlineRedirect) {
    console.log('[SW] Browser is offline, redirecting to offline page');
    // Only redirect if not already on the offline page
    if (!window.location.pathname.includes('/offline')) {
      window.location.href = '/offline.html';
    }
  }
}

// Set default language preference if not already set
function setDefaultLanguage() {
  if (!localStorage.getItem('language_preference')) {
    localStorage.setItem('language_preference', 'en');
    console.log('[SW] Default language set to English');
  }
}

// Register the service worker
async function registerServiceWorker() {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register(SW_CONFIG.path);
      console.log('[SW] Service Worker registered with scope:', registration.scope);
      
      // Check for updates
      registration.update();
      
      // Set up periodic updates
      setInterval(() => {
        registration.update();
        console.log('[SW] Service Worker update check');
      }, SW_CONFIG.updateCheckInterval);
      
      return registration;
    } catch (error) {
      console.error('[SW] Service Worker registration failed:', error);
      return null;
    }
  } else {
    console.warn('[SW] Service Workers are not supported in this browser');
    return null;
  }
}

// Show update notification
function showUpdateNotification() {
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification('Trading Signals App Updated', {
      body: 'The app has been updated to the latest version.',
      icon: '/icon-192.png'
    });
  } else {
    // Fallback to in-app notification if available
    if (window.notifications && typeof window.notifications.showInAppNotification === 'function') {
      window.notifications.showInAppNotification({
        title: 'App Updated',
        message: 'The app has been updated to the latest version.',
        type: 'system'
      });
    }
  }
}

// Request background sync for economic calendar
async function requestEconomicCalendarSync() {
  if ('serviceWorker' in navigator && 'SyncManager' in window) {
    try {
      const registration = await navigator.serviceWorker.ready;
      await registration.sync.register('sync-economic-calendar');
      console.log('[SW] Economic calendar background sync registered');
      return true;
    } catch (error) {
      console.error('[SW] Background sync registration failed:', error);
      return false;
    }
  }
  return false;
}

// Request background sync for trading signals
async function requestTradingSignalsSync() {
  if ('serviceWorker' in navigator && 'SyncManager' in window) {
    try {
      const registration = await navigator.serviceWorker.ready;
      await registration.sync.register('sync-trading-signals');
      console.log('[SW] Trading signals background sync registered');
      return true;
    } catch (error) {
      console.error('[SW] Background sync registration failed:', error);
      return false;
    }
  }
  return false;
}

// Initialize service worker and related features
async function initServiceWorker() {
  // Check online status and set default language
  checkOnlineStatus();
  setDefaultLanguage();
  
  // Register service worker
  const registration = await registerServiceWorker();
  
  if (registration) {
    // Listen for service worker updates
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      console.log('[SW] Service Worker updated, reloading page for new version');
      if (SW_CONFIG.updateNotification) {
        showUpdateNotification();
      }
      window.location.reload();
    });
    
    // Listen for messages from service worker
    navigator.serviceWorker.addEventListener('message', event => {
      console.log('[SW] Message from Service Worker:', event.data);
      
      // Handle different message types
      if (event.data.type === 'economic-calendar-updated') {
        // Update economic calendar UI
        if (window.economicCalendar && typeof window.economicCalendar.refreshEconomicCalendar === 'function') {
          window.economicCalendar.refreshEconomicCalendar();
        }
      } else if (event.data.type === 'offline') {
        // Handle offline notification from service worker
        checkOnlineStatus();
      }
    });
  }
}

// Listen for online/offline events
window.addEventListener('online', () => {
  console.log('[SW] Browser is now online');
  // If on offline page, redirect to home
  if (window.location.pathname.includes('/offline')) {
    window.location.href = '/';
  }
});

window.addEventListener('offline', () => {
  console.log('[SW] Browser is now offline');
  checkOnlineStatus();
});

// Initialize when DOM is loaded
window.addEventListener('load', initServiceWorker);

// Export functions for use in other modules
window.serviceWorkerUtils = {
  requestEconomicCalendarSync,
  requestTradingSignalsSync,
  checkOnlineStatus,
  setDefaultLanguage,
  registerServiceWorker
};
