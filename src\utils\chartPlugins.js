/**
 * Chart.js Plugins for Trading Signals App
 * 
 * Custom plugins to enhance Chart.js functionality for trading charts
 */

import Chart from 'chart.js/auto';
import { getPatternInfo } from './candlestickPatterns';

/**
 * Pattern Annotation Plugin
 * 
 * Adds visual indicators for detected patterns on the chart
 */
export const patternAnnotationPlugin = {
  id: 'patternAnnotation',
  
  // Default options
  defaults: {
    patterns: [],
    supportResistance: { support: [], resistance: [] },
    enabled: true,
    showPatterns: true,
    showSupportResistance: true,
    patternLabelFont: '12px sans-serif',
    supportResistanceWidth: 2,
    supportColor: 'rgba(0, 255, 0, 0.3)',
    resistanceColor: 'rgba(255, 0, 0, 0.3)',
    patternIcons: {
      bullish: '▲',
      bearish: '▼',
      neutral: '◆',
      continuation: '►',
      reversal: '◄'
    }
  },
  
  // Before init hook
  beforeInit(chart, args, options) {
    // Initialize storage for pattern annotations
    chart.patternAnnotations = {
      patterns: [],
      supportResistance: { support: [], resistance: [] }
    };
  },
  
  // After update hook
  afterUpdate(chart, args, options) {
    // Update pattern annotations
    chart.patternAnnotations.patterns = options.patterns || [];
    chart.patternAnnotations.supportResistance = options.supportResistance || { support: [], resistance: [] };
  },
  
  // After draw hook
  afterDraw(chart, args, options) {
    if (!options.enabled) return;
    
    const ctx = chart.ctx;
    const chartArea = chart.chartArea;
    
    // Draw support and resistance levels
    if (options.showSupportResistance) {
      this.drawSupportResistanceLevels(chart, ctx, chartArea, options);
    }
    
    // Draw pattern annotations
    if (options.showPatterns) {
      this.drawPatternAnnotations(chart, ctx, chartArea, options);
    }
  },
  
  /**
   * Draw support and resistance levels on the chart
   * 
   * @param {Chart} chart - Chart.js instance
   * @param {CanvasRenderingContext2D} ctx - Canvas context
   * @param {Object} chartArea - Chart area dimensions
   * @param {Object} options - Plugin options
   */
  drawSupportResistanceLevels(chart, ctx, chartArea, options) {
    const { support, resistance } = chart.patternAnnotations.supportResistance;
    const { left, right, top, bottom } = chartArea;
    const yScale = chart.scales.y;
    
    // Draw support levels
    support.forEach(level => {
      const y = yScale.getPixelForValue(level.price);
      
      // Skip if outside chart area
      if (y < top || y > bottom) return;
      
      // Set line style based on strength
      const alpha = 0.2 + (level.strength / 10) * 0.6; // 0.2 to 0.8 based on strength
      const width = options.supportResistanceWidth * (0.5 + (level.strength / 10) * 1); // 0.5x to 1.5x width based on strength
      
      ctx.save();
      ctx.beginPath();
      ctx.moveTo(left, y);
      ctx.lineTo(right, y);
      ctx.lineWidth = width;
      ctx.strokeStyle = options.supportColor.replace(/[\d\.]+\)$/, `${alpha})`);
      ctx.setLineDash([5, 3]);
      ctx.stroke();
      
      // Add label
      ctx.fillStyle = options.supportColor.replace(/[\d\.]+\)$/, '1)');
      ctx.font = options.patternLabelFont;
      ctx.textAlign = 'left';
      ctx.textBaseline = 'bottom';
      ctx.fillText(`S (${level.strength})`, left + 5, y - 2);
      
      ctx.restore();
    });
    
    // Draw resistance levels
    resistance.forEach(level => {
      const y = yScale.getPixelForValue(level.price);
      
      // Skip if outside chart area
      if (y < top || y > bottom) return;
      
      // Set line style based on strength
      const alpha = 0.2 + (level.strength / 10) * 0.6; // 0.2 to 0.8 based on strength
      const width = options.supportResistanceWidth * (0.5 + (level.strength / 10) * 1); // 0.5x to 1.5x width based on strength
      
      ctx.save();
      ctx.beginPath();
      ctx.moveTo(left, y);
      ctx.lineTo(right, y);
      ctx.lineWidth = width;
      ctx.strokeStyle = options.resistanceColor.replace(/[\d\.]+\)$/, `${alpha})`);
      ctx.setLineDash([5, 3]);
      ctx.stroke();
      
      // Add label
      ctx.fillStyle = options.resistanceColor.replace(/[\d\.]+\)$/, '1)');
      ctx.font = options.patternLabelFont;
      ctx.textAlign = 'left';
      ctx.textBaseline = 'top';
      ctx.fillText(`R (${level.strength})`, left + 5, y + 2);
      
      ctx.restore();
    });
  },
  
  /**
   * Draw pattern annotations on the chart
   * 
   * @param {Chart} chart - Chart.js instance
   * @param {CanvasRenderingContext2D} ctx - Canvas context
   * @param {Object} chartArea - Chart area dimensions
   * @param {Object} options - Plugin options
   */
  drawPatternAnnotations(chart, ctx, chartArea, options) {
    const patterns = chart.patternAnnotations.patterns;
    const { left, right, top, bottom } = chartArea;
    const xScale = chart.scales.x;
    const yScale = chart.scales.y;
    
    // Draw candlestick patterns
    patterns.forEach(pattern => {
      // Skip if no candle data
      if (!pattern.candle) return;
      
      const x = xScale.getPixelForValue(pattern.candle.x);
      const y = yScale.getPixelForValue(pattern.candle.h); // Position at the high of the candle
      
      // Skip if outside chart area
      if (x < left || x > right || y < top || y > bottom) return;
      
      // Get pattern info
      const patternInfo = getPatternInfo(pattern.pattern) || {
        name: pattern.pattern.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        type: pattern.type || 'neutral'
      };
      
      // Get pattern icon
      const icon = options.patternIcons[patternInfo.type] || options.patternIcons.neutral;
      
      // Set color based on pattern type
      let color;
      switch (patternInfo.type) {
        case 'bullish':
          color = 'rgba(0, 192, 0, 1)';
          break;
        case 'bearish':
          color = 'rgba(192, 0, 0, 1)';
          break;
        case 'continuation':
          color = 'rgba(0, 0, 192, 1)';
          break;
        case 'reversal':
          color = 'rgba(192, 0, 192, 1)';
          break;
        default:
          color = 'rgba(128, 128, 128, 1)';
      }
      
      // Draw pattern icon
      ctx.save();
      ctx.font = 'bold 16px sans-serif';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'bottom';
      ctx.fillStyle = color;
      ctx.fillText(icon, x, y - 5);
      
      // Draw pattern name on hover (if chart has active elements)
      if (chart.active && chart.active.length > 0) {
        const activeElement = chart.active[0];
        const activeX = activeElement.element.x;
        
        // If mouse is near the pattern icon
        if (Math.abs(activeX - x) < 10) {
          ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
          ctx.strokeStyle = color;
          ctx.lineWidth = 1;
          
          // Measure text width
          const textWidth = ctx.measureText(patternInfo.name).width;
          const padding = 5;
          const boxWidth = textWidth + padding * 2;
          const boxHeight = 20;
          
          // Draw tooltip background
          ctx.beginPath();
          ctx.roundRect(x - boxWidth / 2, y - 30, boxWidth, boxHeight, 3);
          ctx.fill();
          ctx.stroke();
          
          // Draw pattern name
          ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
          ctx.font = '12px sans-serif';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(patternInfo.name, x, y - 20);
        }
      }
      
      ctx.restore();
    });
    
    // Draw chart patterns (if any)
    // This would be more complex and would involve drawing shapes over multiple candles
  }
};

/**
 * Price Label Plugin
 * 
 * Adds current price labels to the right side of the chart
 */
export const priceLabelPlugin = {
  id: 'priceLabel',
  
  // Default options
  defaults: {
    enabled: true,
    font: '12px sans-serif',
    padding: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderColor: 'rgba(0, 0, 0, 0.1)',
    textColor: 'rgba(0, 0, 0, 0.8)',
    width: 60
  },
  
  // After draw hook
  afterDraw(chart, args, options) {
    if (!options.enabled || !chart.data.datasets || chart.data.datasets.length === 0) return;
    
    const ctx = chart.ctx;
    const chartArea = chart.chartArea;
    const yScale = chart.scales.y;
    
    // Get the last data point
    const dataset = chart.data.datasets[0];
    const lastPoint = dataset.data[dataset.data.length - 1];
    
    if (!lastPoint) return;
    
    // For candlestick charts, use the close price
    const price = lastPoint.c || lastPoint.y || 0;
    const y = yScale.getPixelForValue(price);
    
    // Skip if outside chart area
    if (y < chartArea.top || y > chartArea.bottom) return;
    
    // Format price based on value
    const formattedPrice = price < 0.01 
      ? price.toFixed(6) 
      : price < 1 
        ? price.toFixed(4) 
        : price < 100 
          ? price.toFixed(2) 
          : price.toFixed(0);
    
    // Draw price label
    ctx.save();
    
    // Draw background
    ctx.fillStyle = options.backgroundColor;
    ctx.strokeStyle = options.borderColor;
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.roundRect(chartArea.right + 2, y - 10, options.width, 20, 3);
    ctx.fill();
    ctx.stroke();
    
    // Draw price text
    ctx.fillStyle = options.textColor;
    ctx.font = options.font;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(formattedPrice, chartArea.right + 2 + options.width / 2, y);
    
    // Draw connecting line
    ctx.beginPath();
    ctx.setLineDash([2, 2]);
    ctx.moveTo(chartArea.right, y);
    ctx.lineTo(chartArea.right + 2, y);
    ctx.stroke();
    
    ctx.restore();
  }
};

// Register plugins globally
Chart.register(patternAnnotationPlugin, priceLabelPlugin);

export default {
  patternAnnotationPlugin,
  priceLabelPlugin
};
