/**
 * Chart Integration Module for Trading Signals App
 * 
 * This module integrates the technical analysis indicators with the chart visualization.
 * It provides functions to update the chart with technical indicators and trading signals.
 */

// Chart configuration
const chartIntegration = {
    // Chart instance reference
    chart: null,
    
    // Current chart type
    chartType: 'candlestick',
    
    // Available chart types
    chartTypes: ['candlestick', 'line', 'bar', 'area'],
    
    // Current timeframe
    timeframe: 'H1',
    
    // Available timeframes
    timeframes: ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN'],
    
    // Current symbol
    symbol: 'EURUSD',
    
    // Indicator visibility
    indicators: {
        volume: { visible: true, location: 'below' },
        rsi: { visible: true, location: 'below' },
        macd: { visible: true, location: 'below' },
        bollinger: { visible: true, location: 'main' },
        ema50: { visible: true, location: 'main' },
        ema200: { visible: true, location: 'main' },
        stochastic: { visible: false, location: 'below' },
        adx: { visible: false, location: 'below' },
        supertrend: { visible: false, location: 'main' }
    },
    
    // Chart colors
    colors: {
        candleUp: 'rgba(0, 128, 0, 1)',
        candleDown: 'rgba(255, 0, 0, 1)',
        ema50: 'rgba(255, 165, 0, 1)',
        ema200: 'rgba(128, 0, 128, 1)',
        bollingerUpper: 'rgba(0, 128, 0, 0.5)',
        bollingerMiddle: 'rgba(128, 128, 128, 0.5)',
        bollingerLower: 'rgba(0, 128, 0, 0.5)',
        volume: 'rgba(0, 0, 255, 0.5)',
        volumeUp: 'rgba(0, 128, 0, 0.5)',
        volumeDown: 'rgba(255, 0, 0, 0.5)',
        rsi: 'rgba(0, 0, 255, 1)',
        rsiOverbought: 'rgba(255, 0, 0, 0.2)',
        rsiOversold: 'rgba(0, 128, 0, 0.2)',
        macdLine: 'rgba(0, 0, 255, 1)',
        macdSignal: 'rgba(255, 165, 0, 1)',
        macdHistogramUp: 'rgba(0, 128, 0, 0.5)',
        macdHistogramDown: 'rgba(255, 0, 0, 0.5)',
        stochasticK: 'rgba(0, 0, 255, 1)',
        stochasticD: 'rgba(255, 165, 0, 1)',
        adx: 'rgba(128, 0, 128, 1)',
        adxPlusDI: 'rgba(0, 128, 0, 1)',
        adxMinusDI: 'rgba(255, 0, 0, 1)',
        supertrend: 'rgba(255, 0, 255, 1)',
        supertrendUp: 'rgba(0, 128, 0, 1)',
        supertrendDown: 'rgba(255, 0, 0, 1)',
        signalBuy: 'rgba(0, 128, 0, 0.3)',
        signalSell: 'rgba(255, 0, 0, 0.3)',
        grid: 'rgba(0, 0, 0, 0.1)',
        text: 'rgba(0, 0, 0, 0.7)'
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initChartIntegration);

/**
 * Initialize chart integration
 */
function initChartIntegration() {
    console.log('Initializing chart integration...');
    
    // Load saved settings
    loadChartSettings();
    
    // Set up event listeners
    setupChartEventListeners();
    
    // Initialize chart if canvas exists
    const chartCanvas = document.getElementById('priceChart');
    if (chartCanvas) {
        initializeChart(chartCanvas);
    }
    
    console.log('Chart integration initialized');
}

/**
 * Load chart settings from localStorage
 */
function loadChartSettings() {
    const savedSettings = localStorage.getItem('chart_settings');
    
    if (savedSettings) {
        try {
            const settings = JSON.parse(savedSettings);
            
            // Update chart type
            if (settings.chartType && chartIntegration.chartTypes.includes(settings.chartType)) {
                chartIntegration.chartType = settings.chartType;
            }
            
            // Update timeframe
            if (settings.timeframe && chartIntegration.timeframes.includes(settings.timeframe)) {
                chartIntegration.timeframe = settings.timeframe;
            }
            
            // Update symbol
            if (settings.symbol) {
                chartIntegration.symbol = settings.symbol;
            }
            
            // Update indicator visibility
            if (settings.indicators) {
                Object.keys(settings.indicators).forEach(indicator => {
                    if (chartIntegration.indicators[indicator]) {
                        chartIntegration.indicators[indicator].visible = settings.indicators[indicator].visible;
                    }
                });
            }
            
            // Update colors if in dark mode
            if (settings.darkMode) {
                updateChartColors(true);
            }
            
            console.log('Loaded chart settings:', chartIntegration);
        } catch (error) {
            console.error('Error loading chart settings:', error);
        }
    }
}

/**
 * Save chart settings to localStorage
 */
function saveChartSettings() {
    try {
        const settings = {
            chartType: chartIntegration.chartType,
            timeframe: chartIntegration.timeframe,
            symbol: chartIntegration.symbol,
            indicators: {}
        };
        
        // Save indicator visibility
        Object.keys(chartIntegration.indicators).forEach(indicator => {
            settings.indicators[indicator] = {
                visible: chartIntegration.indicators[indicator].visible
            };
        });
        
        // Save dark mode state
        settings.darkMode = document.documentElement.getAttribute('data-theme') === 'dark';
        
        localStorage.setItem('chart_settings', JSON.stringify(settings));
        console.log('Saved chart settings:', settings);
    } catch (error) {
        console.error('Error saving chart settings:', error);
    }
}

/**
 * Set up chart event listeners
 */
function setupChartEventListeners() {
    // Chart type selector
    const chartTypeSelect = document.getElementById('chartType');
    if (chartTypeSelect) {
        // Set initial value
        chartTypeSelect.value = chartIntegration.chartType;
        
        // Add event listener
        chartTypeSelect.addEventListener('change', function() {
            chartIntegration.chartType = this.value;
            saveChartSettings();
            updateChart();
        });
    }
    
    // Timeframe selector
    const timeframeSelect = document.getElementById('timeframe');
    if (timeframeSelect) {
        // Set initial value
        timeframeSelect.value = chartIntegration.timeframe;
        
        // Add event listener
        timeframeSelect.addEventListener('change', function() {
            chartIntegration.timeframe = this.value;
            saveChartSettings();
            
            // Reload market data with new timeframe
            if (window.marketData) {
                window.marketData.currentTimeframe = chartIntegration.timeframe;
                if (typeof loadMarketData === 'function') {
                    loadMarketData();
                }
            }
        });
    }
    
    // Symbol selector
    const symbolSelect = document.getElementById('symbol');
    if (symbolSelect) {
        // Set initial value if it exists in options
        const symbolOption = Array.from(symbolSelect.options).find(option => option.value === chartIntegration.symbol);
        if (symbolOption) {
            symbolSelect.value = chartIntegration.symbol;
        }
        
        // Add event listener
        symbolSelect.addEventListener('change', function() {
            chartIntegration.symbol = this.value;
            saveChartSettings();
            
            // Reload market data with new symbol
            if (window.marketData) {
                window.marketData.currentSymbol = chartIntegration.symbol;
                if (typeof loadMarketData === 'function') {
                    loadMarketData();
                }
            }
        });
    }
    
    // Indicator toggles
    Object.keys(chartIntegration.indicators).forEach(indicator => {
        const toggle = document.getElementById(`${indicator}Toggle`);
        if (toggle) {
            // Set initial state
            toggle.checked = chartIntegration.indicators[indicator].visible;
            
            // Add event listener
            toggle.addEventListener('change', function() {
                chartIntegration.indicators[indicator].visible = this.checked;
                saveChartSettings();
                updateChart();
            });
        }
    });
    
    // Theme change listener
    window.addEventListener('themechange', function(event) {
        if (event.detail && event.detail.theme) {
            updateChartColors(event.detail.theme === 'dark');
            updateChart();
        }
    });
}

/**
 * Initialize chart
 * @param {HTMLCanvasElement} canvas - Chart canvas element
 */
function initializeChart(canvas) {
    // Check if Chart.js is loaded
    if (typeof Chart === 'undefined') {
        console.error('Chart.js is not loaded');
        return;
    }
    
    // Check if financial chart extension is loaded for candlestick charts
    if (chartIntegration.chartType === 'candlestick' && !Chart.controllers.candlestick) {
        console.warn('Chart.js financial extension is not loaded, using line chart instead');
        chartIntegration.chartType = 'line';
    }
    
    // Create chart configuration
    const config = {
        type: chartIntegration.chartType,
        data: {
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: getTimeUnit(chartIntegration.timeframe)
                    },
                    grid: {
                        color: chartIntegration.colors.grid
                    },
                    ticks: {
                        color: chartIntegration.colors.text,
                        maxRotation: 0,
                        autoSkip: true
                    }
                },
                y: {
                    position: 'right',
                    grid: {
                        color: chartIntegration.colors.grid
                    },
                    ticks: {
                        color: chartIntegration.colors.text
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                },
                zoom: {
                    pan: {
                        enabled: true,
                        mode: 'x'
                    },
                    zoom: {
                        wheel: {
                            enabled: true
                        },
                        pinch: {
                            enabled: true
                        },
                        mode: 'x'
                    }
                }
            },
            animation: {
                duration: 0 // Disable animation for better performance
            }
        }
    };
    
    // Create chart
    chartIntegration.chart = new Chart(canvas, config);
    
    // Update chart with initial data
    updateChart();
    
    console.log('Chart initialized');
}

/**
 * Update chart with market data and indicators
 */
function updateChart() {
    if (!chartIntegration.chart) {
        console.warn('Chart not initialized');
        return;
    }
    
    // Get market data
    const marketData = window.marketData;
    if (!marketData || !marketData.dates || !marketData.dates.length) {
        console.warn('No market data available');
        return;
    }
    
    // Clear existing datasets
    chartIntegration.chart.data.datasets = [];
    
    // Add price dataset based on chart type
    addPriceDataset();
    
    // Add technical indicators
    addTechnicalIndicators();
    
    // Add trading signals
    addTradingSignals();
    
    // Update chart
    chartIntegration.chart.update();
}

/**
 * Add price dataset to chart
 */
function addPriceDataset() {
    const marketData = window.marketData;
    if (!marketData) return;
    
    const chart = chartIntegration.chart;
    
    // Create dataset based on chart type
    if (chartIntegration.chartType === 'candlestick') {
        // Create candlestick data
        const ohlc = [];
        for (let i = 0; i < marketData.dates.length; i++) {
            if (marketData.opens[i] && marketData.highs[i] && marketData.lows[i] && marketData.closes[i]) {
                ohlc.push({
                    x: new Date(marketData.dates[i]),
                    o: marketData.opens[i],
                    h: marketData.highs[i],
                    l: marketData.lows[i],
                    c: marketData.closes[i]
                });
            }
        }
        
        chart.data.datasets.push({
            label: marketData.currentSymbol,
            data: ohlc,
            type: 'candlestick',
            color: {
                up: chartIntegration.colors.candleUp,
                down: chartIntegration.colors.candleDown,
                unchanged: 'rgba(0, 0, 0, 1)',
            }
        });
    } else if (chartIntegration.chartType === 'line') {
        // Line chart
        const data = [];
        for (let i = 0; i < marketData.dates.length; i++) {
            if (marketData.closes[i]) {
                data.push({
                    x: new Date(marketData.dates[i]),
                    y: marketData.closes[i]
                });
            }
        }
        
        chart.data.datasets.push({
            label: marketData.currentSymbol,
            data: data,
            borderColor: 'rgba(0, 123, 255, 1)',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            fill: chartIntegration.chartType === 'area',
            tension: 0.1
        });
    } else if (chartIntegration.chartType === 'bar') {
        // Bar chart (OHLC)
        const data = [];
        for (let i = 0; i < marketData.dates.length; i++) {
            if (marketData.opens[i] && marketData.highs[i] && marketData.lows[i] && marketData.closes[i]) {
                data.push({
                    x: new Date(marketData.dates[i]),
                    o: marketData.opens[i],
                    h: marketData.highs[i],
                    l: marketData.lows[i],
                    c: marketData.closes[i]
                });
            }
        }
        
        chart.data.datasets.push({
            label: marketData.currentSymbol,
            data: data,
            type: 'ohlc',
            color: {
                up: chartIntegration.colors.candleUp,
                down: chartIntegration.colors.candleDown,
                unchanged: 'rgba(0, 0, 0, 1)',
            }
        });
    }
}

/**
 * Add technical indicators to chart
 */
function addTechnicalIndicators() {
    // This function will be implemented to add technical indicators to the chart
    // based on the indicator visibility settings
    
    // For now, we'll just log a message
    console.log('Adding technical indicators to chart...');
    
    // If technical-analysis.js is loaded, use its functions to calculate indicators
    if (window.technicalAnalysis) {
        // Add indicators based on visibility settings
        addIndicatorsToChart();
    }
}

/**
 * Add trading signals to chart
 */
function addTradingSignals() {
    // This function will be implemented to add trading signals to the chart
    console.log('Adding trading signals to chart...');
}

/**
 * Update chart colors based on theme
 * @param {boolean} isDarkMode - Whether dark mode is enabled
 */
function updateChartColors(isDarkMode) {
    if (isDarkMode) {
        // Dark mode colors
        chartIntegration.colors.grid = 'rgba(255, 255, 255, 0.1)';
        chartIntegration.colors.text = 'rgba(255, 255, 255, 0.7)';
    } else {
        // Light mode colors
        chartIntegration.colors.grid = 'rgba(0, 0, 0, 0.1)';
        chartIntegration.colors.text = 'rgba(0, 0, 0, 0.7)';
    }
    
    // Update chart options
    if (chartIntegration.chart) {
        chartIntegration.chart.options.scales.x.grid.color = chartIntegration.colors.grid;
        chartIntegration.chart.options.scales.y.grid.color = chartIntegration.colors.grid;
        chartIntegration.chart.options.scales.x.ticks.color = chartIntegration.colors.text;
        chartIntegration.chart.options.scales.y.ticks.color = chartIntegration.colors.text;
    }
}

/**
 * Get time unit for chart based on timeframe
 * @param {string} timeframe - Timeframe (e.g., 'M1', 'H1', 'D1')
 * @returns {string} - Time unit for Chart.js
 */
function getTimeUnit(timeframe) {
    switch (timeframe) {
        case 'M1':
        case 'M5':
        case 'M15':
        case 'M30':
            return 'minute';
        case 'H1':
        case 'H4':
            return 'hour';
        case 'D1':
            return 'day';
        case 'W1':
            return 'week';
        case 'MN':
            return 'month';
        default:
            return 'day';
    }
}

// Export functions
window.chartIntegration = {
    updateChart,
    changeChartType: function(type) {
        if (chartIntegration.chartTypes.includes(type)) {
            chartIntegration.chartType = type;
            saveChartSettings();
            updateChart();
        }
    },
    toggleIndicator: function(indicator, visible) {
        if (chartIntegration.indicators[indicator]) {
            chartIntegration.indicators[indicator].visible = visible;
            saveChartSettings();
            updateChart();
        }
    },
    resetZoom: function() {
        if (chartIntegration.chart && chartIntegration.chart.resetZoom) {
            chartIntegration.chart.resetZoom();
        }
    }
};
