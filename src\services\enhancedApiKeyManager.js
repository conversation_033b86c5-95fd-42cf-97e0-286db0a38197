import logger from '../utils/logger.js';

/**
 * Enhanced API Key Manager with intelligent rotation, health monitoring, and rate limit handling
 */
export class EnhancedApiKeyManager {
  constructor(provider, keys, options = {}) {
    this.provider = provider;
    this.keys = keys.map(key => ({
      key,
      failures: 0,
      lastFailure: null,
      cooldownUntil: null,
      rateLimitReset: null,
      requestCount: 0,
      successCount: 0,
      averageResponseTime: 0,
      lastUsed: null,
      isHealthy: true,
      healthScore: 100,
      rateLimitRemaining: null,
      rateLimitLimit: null
    }));
    
    this.options = {
      cooldownPeriod: 60 * 60 * 1000, // 60 minutes
      maxFailures: 3,
      logLevel: 'warn',
      healthCheckInterval: 5 * 60 * 1000, // 5 minutes
      rateLimitBuffer: 1000, // 1 second buffer
      enableIntelligentRotation: true,
      enableHealthMonitoring: true,
      enableRateLimitTracking: true,
      performanceWeight: 0.4,
      healthWeight: 0.3,
      usageWeight: 0.3,
      ...options
    };
    
    this.currentIndex = 0;
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      keyRotations: 0,
      rateLimitHits: 0,
      averageResponseTime: 0,
      lastRotation: null
    };

    this.healthCheckTimer = null;
    this.rateLimitTracking = new Map();
    
    // Start health monitoring if enabled
    if (this.options.enableHealthMonitoring) {
      this.startHealthMonitoring();
    }

    logger.info(`Enhanced API Key Manager initialized for ${provider} with ${keys.length} keys`);
  }

  /**
   * Get the best available API key using intelligent selection
   */
  getKey() {
    if (this.keys.length === 0) {
      throw new Error(`No API keys available for ${this.provider}`);
    }

    this.stats.totalRequests++;

    if (this.options.enableIntelligentRotation) {
      return this.getIntelligentKey();
    } else {
      return this.getNextAvailableKey();
    }
  }

  /**
   * Get key using intelligent selection based on performance, health, and usage
   */
  getIntelligentKey() {
    const now = Date.now();
    const availableKeys = this.keys.filter(keyInfo => {
      // Skip keys in cooldown
      if (keyInfo.cooldownUntil && keyInfo.cooldownUntil > now) {
        return false;
      }
      
      // Skip keys with active rate limits
      if (keyInfo.rateLimitReset && keyInfo.rateLimitReset > now) {
        return false;
      }
      
      return true;
    });

    if (availableKeys.length === 0) {
      // All keys are unavailable, use the one with earliest recovery
      const keyWithEarliestRecovery = this.keys.reduce((earliest, current) => {
        const earliestRecovery = Math.max(earliest.cooldownUntil || 0, earliest.rateLimitReset || 0);
        const currentRecovery = Math.max(current.cooldownUntil || 0, current.rateLimitReset || 0);
        return currentRecovery < earliestRecovery ? current : earliest;
      });
      
      logger.warn(`All keys for ${this.provider} are unavailable, using ${this.maskKey(keyWithEarliestRecovery.key)}`);
      return keyWithEarliestRecovery.key;
    }

    // Score each available key
    const scoredKeys = availableKeys.map(keyInfo => ({
      keyInfo,
      score: this.calculateKeyScore(keyInfo)
    }));

    // Sort by score (highest first)
    scoredKeys.sort((a, b) => b.score - a.score);
    
    const selectedKey = scoredKeys[0].keyInfo;
    selectedKey.lastUsed = now;
    
    logger.debug(`Selected key ${this.maskKey(selectedKey.key)} with score ${scoredKeys[0].score.toFixed(2)}`);
    
    return selectedKey.key;
  }

  /**
   * Calculate score for a key based on performance, health, and usage
   */
  calculateKeyScore(keyInfo) {
    const now = Date.now();
    
    // Performance score (0-100)
    const successRate = keyInfo.requestCount > 0 ? 
      (keyInfo.successCount / keyInfo.requestCount) * 100 : 50;
    const responseTimeScore = Math.max(0, 100 - (keyInfo.averageResponseTime / 100));
    const performanceScore = (successRate + responseTimeScore) / 2;
    
    // Health score (0-100)
    const healthScore = keyInfo.healthScore;
    
    // Usage score (0-100) - prefer less recently used keys
    const timeSinceLastUse = keyInfo.lastUsed ? now - keyInfo.lastUsed : Infinity;
    const usageScore = Math.min(100, timeSinceLastUse / (60 * 1000)); // 1 minute = 100 points
    
    // Rate limit score
    const rateLimitScore = keyInfo.rateLimitRemaining !== null ? 
      Math.min(100, (keyInfo.rateLimitRemaining / keyInfo.rateLimitLimit) * 100) : 100;
    
    // Weighted final score
    const finalScore = 
      (performanceScore * this.options.performanceWeight) +
      (healthScore * this.options.healthWeight) +
      (usageScore * this.options.usageWeight) +
      (rateLimitScore * 0.1); // Small weight for rate limit
    
    return finalScore;
  }

  /**
   * Get next available key (simple rotation)
   */
  getNextAvailableKey() {
    const now = Date.now();
    let attempts = 0;
    
    while (attempts < this.keys.length) {
      const keyInfo = this.keys[this.currentIndex];
      
      // Check if key is available
      if (this.isKeyAvailable(keyInfo, now)) {
        keyInfo.lastUsed = now;
        return keyInfo.key;
      }
      
      // Move to next key
      this.currentIndex = (this.currentIndex + 1) % this.keys.length;
      attempts++;
    }
    
    // All keys are unavailable, use current one anyway
    const keyInfo = this.keys[this.currentIndex];
    logger.warn(`All keys for ${this.provider} are unavailable, using ${this.maskKey(keyInfo.key)}`);
    return keyInfo.key;
  }

  /**
   * Check if a key is available for use
   */
  isKeyAvailable(keyInfo, now = Date.now()) {
    // Check cooldown
    if (keyInfo.cooldownUntil && keyInfo.cooldownUntil > now) {
      return false;
    }
    
    // Check rate limit
    if (keyInfo.rateLimitReset && keyInfo.rateLimitReset > now) {
      return false;
    }
    
    return true;
  }

  /**
   * Record API call result with enhanced metrics
   */
  recordApiCall(key, success, responseTime, error = null, rateLimitInfo = null) {
    const keyInfo = this.keys.find(k => k.key === key);
    if (!keyInfo) {
      logger.warn(`Key ${this.maskKey(key)} not found in ${this.provider} keys`);
      return;
    }

    // Update request metrics
    keyInfo.requestCount++;
    this.stats.totalRequests++;
    
    if (success) {
      keyInfo.successCount++;
      this.stats.successfulRequests++;
      
      // Update response time
      if (keyInfo.averageResponseTime === 0) {
        keyInfo.averageResponseTime = responseTime;
      } else {
        keyInfo.averageResponseTime = (keyInfo.averageResponseTime + responseTime) / 2;
      }
      
      // Update global average
      this.stats.averageResponseTime = (this.stats.averageResponseTime + responseTime) / 2;
      
      // Reset failures on success
      if (keyInfo.failures > 0) {
        keyInfo.failures = 0;
        logger.debug(`Key ${this.maskKey(key)} failures reset after success`);
      }
      
      // Improve health score
      keyInfo.healthScore = Math.min(100, keyInfo.healthScore + 1);
      
    } else {
      this.stats.failedRequests++;
      keyInfo.failures++;
      keyInfo.lastFailure = Date.now();
      
      // Decrease health score
      keyInfo.healthScore = Math.max(0, keyInfo.healthScore - 5);
      
      // Handle rate limit errors
      if (error && (error.status === 429 || error.message?.includes('rate limit'))) {
        this.handleRateLimit(keyInfo, rateLimitInfo);
      } else if (keyInfo.failures >= this.options.maxFailures) {
        this.putKeyInCooldown(keyInfo);
      }
    }

    // Update rate limit info if provided
    if (rateLimitInfo) {
      keyInfo.rateLimitRemaining = rateLimitInfo.remaining;
      keyInfo.rateLimitLimit = rateLimitInfo.limit;
      keyInfo.rateLimitReset = rateLimitInfo.reset ? 
        new Date(rateLimitInfo.reset * 1000).getTime() : null;
    }
  }

  /**
   * Handle rate limit for a key
   */
  handleRateLimit(keyInfo, rateLimitInfo) {
    this.stats.rateLimitHits++;
    
    if (rateLimitInfo && rateLimitInfo.reset) {
      keyInfo.rateLimitReset = new Date(rateLimitInfo.reset * 1000).getTime() + this.options.rateLimitBuffer;
      logger.warn(`Key ${this.maskKey(keyInfo.key)} rate limited until ${new Date(keyInfo.rateLimitReset).toISOString()}`);
    } else {
      // Default rate limit cooldown
      keyInfo.rateLimitReset = Date.now() + (15 * 60 * 1000); // 15 minutes
      logger.warn(`Key ${this.maskKey(keyInfo.key)} rate limited for 15 minutes (default)`);
    }
    
    // Rotate to next key
    this.rotateToNextKey();
  }

  /**
   * Put key in cooldown
   */
  putKeyInCooldown(keyInfo) {
    keyInfo.cooldownUntil = Date.now() + this.options.cooldownPeriod;
    keyInfo.failures = 0;
    keyInfo.isHealthy = false;
    
    logger.warn(`Key ${this.maskKey(keyInfo.key)} put in cooldown until ${new Date(keyInfo.cooldownUntil).toISOString()}`);
    
    this.rotateToNextKey();
  }

  /**
   * Rotate to next available key
   */
  rotateToNextKey() {
    this.stats.keyRotations++;
    this.stats.lastRotation = Date.now();
    
    if (this.options.enableIntelligentRotation) {
      // Intelligent rotation will handle this in getKey()
      return;
    }
    
    // Simple rotation
    this.currentIndex = (this.currentIndex + 1) % this.keys.length;
  }

  /**
   * Start health monitoring
   */
  startHealthMonitoring() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }
    
    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.options.healthCheckInterval);
    
    logger.info(`Health monitoring started for ${this.provider} keys`);
  }

  /**
   * Perform health check on all keys
   */
  performHealthCheck() {
    const now = Date.now();
    
    for (const keyInfo of this.keys) {
      // Reset cooldown if expired
      if (keyInfo.cooldownUntil && keyInfo.cooldownUntil <= now) {
        keyInfo.cooldownUntil = null;
        keyInfo.isHealthy = true;
        keyInfo.healthScore = Math.min(100, keyInfo.healthScore + 10);
        logger.info(`Key ${this.maskKey(keyInfo.key)} recovered from cooldown`);
      }
      
      // Reset rate limit if expired
      if (keyInfo.rateLimitReset && keyInfo.rateLimitReset <= now) {
        keyInfo.rateLimitReset = null;
        logger.info(`Key ${this.maskKey(keyInfo.key)} recovered from rate limit`);
      }
      
      // Gradually improve health score for unused keys
      if (!keyInfo.lastUsed || (now - keyInfo.lastUsed) > (60 * 60 * 1000)) { // 1 hour
        keyInfo.healthScore = Math.min(100, keyInfo.healthScore + 0.5);
      }
    }
  }

  /**
   * Get comprehensive statistics
   */
  getStats() {
    const now = Date.now();
    
    return {
      provider: this.provider,
      totalKeys: this.keys.length,
      availableKeys: this.keys.filter(k => this.isKeyAvailable(k, now)).length,
      stats: { ...this.stats },
      keyDetails: this.keys.map(keyInfo => ({
        key: this.maskKey(keyInfo.key),
        requestCount: keyInfo.requestCount,
        successCount: keyInfo.successCount,
        successRate: keyInfo.requestCount > 0 ? 
          Math.round((keyInfo.successCount / keyInfo.requestCount) * 100) : 0,
        averageResponseTime: Math.round(keyInfo.averageResponseTime),
        healthScore: Math.round(keyInfo.healthScore),
        isHealthy: keyInfo.isHealthy,
        inCooldown: keyInfo.cooldownUntil && keyInfo.cooldownUntil > now,
        rateLimited: keyInfo.rateLimitReset && keyInfo.rateLimitReset > now,
        lastUsed: keyInfo.lastUsed,
        score: this.calculateKeyScore(keyInfo)
      }))
    };
  }

  /**
   * Mask API key for logging
   */
  maskKey(key) {
    if (!key) return 'undefined';
    if (key.length <= 8) return '****';
    return key.substring(0, 4) + '****' + key.substring(key.length - 4);
  }

  /**
   * Cleanup resources
   */
  destroy() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }
    
    logger.info(`Enhanced API Key Manager for ${this.provider} destroyed`);
  }
}

export default EnhancedApiKeyManager;
