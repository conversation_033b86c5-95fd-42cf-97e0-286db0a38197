import { consolidatedOpenAIService } from '../../../services/consolidatedOpenAIService.js';
import { enhancedSecurityService } from '../../../services/enhancedSecurityService.js';
import logger from '../../../utils/logger.js';

/**
 * AI Controller - Enhanced with consolidated OpenAI service
 *
 * Provides endpoints for AI-powered trading analysis including:
 * - Market analysis generation
 * - Trading signal generation
 * - Sentiment analysis
 * - Risk assessment
 * - Model performance metrics
 *
 * @version 2.0.0
 */

/**
 * Generate AI trading signal based on market data
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const generateSignal = async (req, res, next) => {
  try {
    const { marketData, indicators, options = {} } = req.body;

    // Validate required data
    if (!marketData || !marketData.symbol) {
      return res.status(400).json({
        error: 'Market data with symbol is required',
        required: ['marketData.symbol']
      });
    }

    const signal = await consolidatedOpenAIService.generateTradingSignal(
      marketData,
      indicators || {},
      options
    );

    res.json({
      status: 'success',
      signal,
      metadata: {
        timestamp: new Date().toISOString(),
        symbol: marketData.symbol,
        model: signal._metadata?.model || 'unknown'
      }
    });
  } catch (error) {
    logger.error('Error generating AI signal:', error);
    next(error);
  }
};

/**
 * Generate comprehensive market analysis
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const generateMarketAnalysis = async (req, res, next) => {
  try {
    // Validate input using enhanced security service
    const validation = enhancedSecurityService.validateInput(req.body, 'aiAnalysisRequest');
    if (!validation.isValid) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validation.errors
      });
    }

    const { symbol, timeframe, analysisType, model, options } = validation.data;

    // Check if user has permission for AI analysis
    if (!enhancedSecurityService.hasPermission(req.user?.role, 'ai_analysis')) {
      return res.status(403).json({
        error: 'AI analysis not available for your subscription level',
        code: 'FEATURE_RESTRICTED'
      });
    }

    const marketData = {
      symbol,
      timeframe,
      // Add any additional market data from request
      ...req.body.marketData
    };

    const analysis = await consolidatedOpenAIService.generateMarketAnalysis(
      marketData,
      { model, ...options }
    );

    res.json({
      status: 'success',
      analysis,
      metadata: {
        timestamp: new Date().toISOString(),
        symbol,
        model: analysis._metadata?.model,
        userRole: req.user?.role
      }
    });
  } catch (error) {
    logger.error('Error generating market analysis:', error);
    next(error);
  }
};

/**
 * Analyze sentiment from news headlines
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const analyzeSentiment = async (req, res, next) => {
  try {
    const { headlines, options = {} } = req.body;

    if (!headlines || !Array.isArray(headlines) || headlines.length === 0) {
      return res.status(400).json({
        error: 'Headlines array is required and must not be empty'
      });
    }

    const sentiment = await consolidatedOpenAIService.analyzeSentiment(
      headlines,
      options
    );

    res.json({
      status: 'success',
      sentiment,
      metadata: {
        timestamp: new Date().toISOString(),
        headlineCount: headlines.length
      }
    });
  } catch (error) {
    logger.error('Error analyzing sentiment:', error);
    next(error);
  }
};

/**
 * Assess risk for trading position
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const assessRisk = async (req, res, next) => {
  try {
    const { position, marketConditions, options = {} } = req.body;

    if (!position || !marketConditions) {
      return res.status(400).json({
        error: 'Position and market conditions are required'
      });
    }

    const riskAssessment = await consolidatedOpenAIService.assessRisk(
      position,
      marketConditions,
      options
    );

    res.json({
      status: 'success',
      riskAssessment,
      metadata: {
        timestamp: new Date().toISOString(),
        symbol: position.symbol
      }
    });
  } catch (error) {
    logger.error('Error assessing risk:', error);
    next(error);
  }
};

/**
 * Get AI service metrics and performance data
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const getMetrics = async (req, res, next) => {
  try {
    const { model, operation } = req.query;

    const metrics = consolidatedOpenAIService.getModelMetrics(model, operation);
    const circuitBreakerStatus = consolidatedOpenAIService.getCircuitBreakerStatus();
    const cacheStats = consolidatedOpenAIService.getCacheStats();

    res.json({
      status: 'success',
      data: {
        modelMetrics: metrics,
        circuitBreaker: circuitBreakerStatus,
        cache: cacheStats,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Error getting AI metrics:', error);
    next(error);
  }
};

/**
 * Compare different AI models for the same analysis
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const compareModels = async (req, res, next) => {
  try {
    const { marketData, models, operation = 'market_analysis' } = req.body;

    if (!marketData || !models || !Array.isArray(models)) {
      return res.status(400).json({
        error: 'Market data and models array are required'
      });
    }

    const comparison = await consolidatedOpenAIService.compareModels(
      marketData,
      models,
      operation
    );

    res.json({
      status: 'success',
      comparison
    });
  } catch (error) {
    logger.error('Error comparing models:', error);
    next(error);
  }
};

/**
 * Reset circuit breaker (admin endpoint)
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const resetCircuitBreaker = async (req, res, next) => {
  try {
    consolidatedOpenAIService.resetCircuitBreaker();

    res.json({
      status: 'success',
      message: 'Circuit breaker reset successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error resetting circuit breaker:', error);
    next(error);
  }
};

export default {
  generateSignal,
  generateMarketAnalysis,
  analyzeSentiment,
  assessRisk,
  getMetrics,
  compareModels,
  resetCircuitBreaker
};