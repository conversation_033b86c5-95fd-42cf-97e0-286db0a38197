/**
 * Data Source Registry for Trading Signals App
 * 
 * This module provides a registry for all data sources used in the app.
 * It allows for dynamic registration, prioritization, and management of data sources.
 */

class DataSourceRegistry {
    /**
     * Create a new DataSourceRegistry instance
     */
    constructor() {
        // Registry of data sources
        this.sources = new Map();
        
        // Default source priorities (lower number = higher priority)
        this.defaultPriorities = {
            'integrated-api': 10,
            'local-api': 20,
            'alpha-vantage': 30,
            'yahoo-finance': 40,
            'polygon': 50,
            'coingecko': 60,
            'fmp': 70,
            'mock-data': 100
        };
        
        // User preferences for source priorities
        this.userPriorities = this._loadUserPriorities();
        
        // Source capabilities
        this.capabilities = {
            'integrated-api': ['forex', 'stocks', 'crypto', 'commodities', 'indices'],
            'local-api': ['forex', 'stocks', 'crypto', 'commodities', 'indices'],
            'alpha-vantage': ['forex', 'stocks', 'crypto'],
            'yahoo-finance': ['stocks', 'forex', 'crypto', 'indices'],
            'polygon': ['stocks', 'forex', 'crypto'],
            'coingecko': ['crypto'],
            'fmp': ['stocks', 'forex'],
            'mock-data': ['forex', 'stocks', 'crypto', 'commodities', 'indices']
        };
        
        // Initialize with built-in sources
        this._initializeBuiltInSources();
    }
    
    /**
     * Initialize built-in data sources
     * @private
     */
    _initializeBuiltInSources() {
        // Register built-in sources
        this.registerSource('integrated-api', {
            name: 'Integrated API',
            description: 'Multi-API integration with automatic fallback',
            fetchFunction: async (symbol, interval, assetType) => {
                if (!window.FinancialDataClient) {
                    throw new Error('Integrated API client not available');
                }
                
                // Map interval format
                const mappedInterval = this._mapInterval(interval);
                
                // Get market data from the financial data client
                const response = await window.FinancialDataClient.getMarketData(symbol, mappedInterval);
                
                // Format the response
                return this._formatIntegratedApiData(response, symbol, interval, assetType);
            },
            isAvailable: () => !!window.FinancialDataClient,
            priority: this.defaultPriorities['integrated-api']
        });
        
        this.registerSource('local-api', {
            name: 'Local API',
            description: 'Local server API',
            fetchFunction: async (symbol, interval, assetType) => {
                if (!window.MarketDataService || !window.MarketDataService.fetchLocalAPI) {
                    throw new Error('Local API service not available');
                }
                
                return await window.MarketDataService.fetchLocalAPI(`/api/market-data/${symbol}`, {
                    interval,
                    assetType
                });
            },
            isAvailable: () => !!window.MarketDataService && !!window.MarketDataService.fetchLocalAPI,
            priority: this.defaultPriorities['local-api']
        });
        
        this.registerSource('alpha-vantage', {
            name: 'Alpha Vantage',
            description: 'Alpha Vantage financial data API',
            fetchFunction: async (symbol, interval, assetType) => {
                if (!window.MarketDataService || !window.MarketDataService.fetchAlphaVantage) {
                    throw new Error('Alpha Vantage service not available');
                }
                
                // Determine endpoint and parameters based on asset type
                let endpoint, params;
                
                if (assetType === 'forex') {
                    // Parse forex symbol
                    let fromCurrency, toCurrency;
                    
                    if (symbol.includes('/')) {
                        [fromCurrency, toCurrency] = symbol.split('/');
                    } else if (symbol.length === 6) {
                        fromCurrency = symbol.substring(0, 3);
                        toCurrency = symbol.substring(3, 6);
                    } else {
                        throw new Error(`Invalid forex symbol format: ${symbol}`);
                    }
                    
                    endpoint = 'FX_INTRADAY';
                    params = {
                        from_symbol: fromCurrency,
                        to_symbol: toCurrency,
                        interval: this._mapInterval(interval),
                        outputsize: 'compact'
                    };
                } else if (assetType === 'crypto') {
                    // For crypto, we need to handle different formats
                    let cryptoSymbol, market;
                    
                    if (symbol.includes('/')) {
                        [cryptoSymbol, market] = symbol.split('/');
                    } else if (symbol.length >= 6) {
                        // Assume format like BTCUSD
                        cryptoSymbol = symbol.substring(0, 3);
                        market = symbol.substring(3);
                    } else {
                        throw new Error(`Invalid crypto symbol format: ${symbol}`);
                    }
                    
                    endpoint = 'CRYPTO_INTRADAY';
                    params = {
                        symbol: cryptoSymbol,
                        market: market,
                        interval: this._mapInterval(interval),
                        outputsize: 'compact'
                    };
                } else {
                    endpoint = 'TIME_SERIES_INTRADAY';
                    params = {
                        symbol,
                        interval: this._mapInterval(interval),
                        outputsize: 'compact'
                    };
                }
                
                // Fetch data
                const data = await window.MarketDataService.fetchAlphaVantage(endpoint, params);
                
                // Format data
                return window.MarketDataService.formatAlphaVantageData(data, symbol, interval, assetType);
            },
            isAvailable: () => !!window.MarketDataService && !!window.MarketDataService.fetchAlphaVantage,
            priority: this.defaultPriorities['alpha-vantage']
        });
        
        this.registerSource('yahoo-finance', {
            name: 'Yahoo Finance',
            description: 'Yahoo Finance API',
            fetchFunction: async (symbol, interval, assetType) => {
                // Format symbol for Yahoo Finance
                const yahooSymbol = this._formatYahooSymbol(symbol, assetType);
                
                // Map interval to Yahoo Finance format
                const yahooInterval = this._mapYahooInterval(interval);
                
                // Fetch data
                const response = await fetch(`/api/financial/yahoo?symbol=${yahooSymbol}&interval=${yahooInterval}`);
                
                if (!response.ok) {
                    throw new Error(`Yahoo Finance API error: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Format data
                return this._formatYahooData(data, symbol, interval, assetType);
            },
            isAvailable: () => true, // Yahoo Finance API is always available through our proxy
            priority: this.defaultPriorities['yahoo-finance']
        });
        
        this.registerSource('coingecko', {
            name: 'CoinGecko',
            description: 'CoinGecko cryptocurrency API',
            fetchFunction: async (symbol, interval, assetType) => {
                // Only available for crypto assets
                if (assetType !== 'crypto') {
                    throw new Error('CoinGecko only supports cryptocurrency data');
                }
                
                // Format symbol for CoinGecko
                const coinId = this._formatCoinGeckoSymbol(symbol);
                
                // Map interval to days
                const days = this._mapCoinGeckoDays(interval);
                
                // Fetch data
                const response = await fetch(`/api/financial/coingecko?id=${coinId}&days=${days}&vs_currency=usd`);
                
                if (!response.ok) {
                    throw new Error(`CoinGecko API error: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Format data
                return this._formatCoinGeckoData(data, symbol, interval, assetType);
            },
            isAvailable: () => true, // CoinGecko API is always available through our proxy
            priority: this.defaultPriorities['coingecko'],
            supportedAssetTypes: ['crypto']
        });
        
        this.registerSource('mock-data', {
            name: 'Mock Data',
            description: 'Simulated market data for testing',
            fetchFunction: async (symbol, interval, assetType) => {
                if (!window.MarketDataService || !window.MarketDataService.generateMockMarketData) {
                    throw new Error('Mock data generator not available');
                }
                
                return window.MarketDataService.generateMockMarketData(symbol, interval, assetType);
            },
            isAvailable: () => !!window.MarketDataService && !!window.MarketDataService.generateMockMarketData,
            priority: this.defaultPriorities['mock-data']
        });
    }
    
    /**
     * Load user preferences for source priorities
     * @returns {Object} - User priorities
     * @private
     */
    _loadUserPriorities() {
        try {
            const savedPriorities = localStorage.getItem('data_source_priorities');
            return savedPriorities ? JSON.parse(savedPriorities) : {};
        } catch (error) {
            console.warn('Error loading user priorities:', error);
            return {};
        }
    }
    
    /**
     * Save user preferences for source priorities
     * @private
     */
    _saveUserPriorities() {
        try {
            localStorage.setItem('data_source_priorities', JSON.stringify(this.userPriorities));
        } catch (error) {
            console.warn('Error saving user priorities:', error);
        }
    }
    
    /**
     * Register a new data source
     * @param {string} id - Source ID
     * @param {Object} source - Source configuration
     * @param {string} source.name - Display name
     * @param {string} source.description - Description
     * @param {Function} source.fetchFunction - Function to fetch data
     * @param {Function} source.isAvailable - Function to check if source is available
     * @param {number} source.priority - Priority (lower = higher priority)
     * @param {Array<string>} source.supportedAssetTypes - Asset types supported by this source
     */
    registerSource(id, source) {
        // Apply user priority if available
        if (this.userPriorities[id] !== undefined) {
            source.priority = this.userPriorities[id];
        }
        
        this.sources.set(id, source);
        console.log(`Registered data source: ${id}`);
    }
    
    /**
     * Get all registered data sources
     * @returns {Array<Object>} - Array of data sources with their IDs
     */
    getAllSources() {
        return Array.from(this.sources.entries()).map(([id, source]) => ({
            id,
            ...source
        }));
    }
    
    /**
     * Get available data sources for a specific asset type
     * @param {string} assetType - Asset type
     * @returns {Array<Object>} - Array of available data sources
     */
    getAvailableSourcesForAssetType(assetType) {
        return this.getAllSources()
            .filter(source => {
                // Check if source is available
                if (typeof source.isAvailable === 'function' && !source.isAvailable()) {
                    return false;
                }
                
                // Check if source supports this asset type
                if (source.supportedAssetTypes && !source.supportedAssetTypes.includes(assetType)) {
                    return false;
                }
                
                // Check capabilities
                if (this.capabilities[source.id] && !this.capabilities[source.id].includes(assetType)) {
                    return false;
                }
                
                return true;
            })
            .sort((a, b) => a.priority - b.priority);
    }
    
    /**
     * Set user priority for a data source
     * @param {string} id - Source ID
     * @param {number} priority - Priority (lower = higher priority)
     */
    setSourcePriority(id, priority) {
        if (!this.sources.has(id)) {
            throw new Error(`Unknown data source: ${id}`);
        }
        
        // Update source priority
        const source = this.sources.get(id);
        source.priority = priority;
        this.sources.set(id, source);
        
        // Update user preferences
        this.userPriorities[id] = priority;
        this._saveUserPriorities();
        
        console.log(`Updated priority for ${id}: ${priority}`);
    }
    
    /**
     * Reset source priorities to defaults
     */
    resetSourcePriorities() {
        // Reset all sources to default priorities
        for (const [id, source] of this.sources.entries()) {
            source.priority = this.defaultPriorities[id] || 999;
            this.sources.set(id, source);
        }
        
        // Clear user preferences
        this.userPriorities = {};
        this._saveUserPriorities();
        
        console.log('Reset all source priorities to defaults');
    }
    
    /**
     * Fetch data from the highest priority available source
     * @param {string} symbol - Market symbol
     * @param {string} interval - Time interval
     * @param {string} assetType - Asset type
     * @returns {Promise<Object>} - Market data
     */
    async fetchData(symbol, interval, assetType) {
        // Get available sources for this asset type
        const availableSources = this.getAvailableSourcesForAssetType(assetType);
        
        if (availableSources.length === 0) {
            throw new Error(`No available data sources for ${assetType}`);
        }
        
        // Try each source in priority order
        let lastError = null;
        
        for (const source of availableSources) {
            try {
                console.log(`Trying data source: ${source.id}`);
                
                // Fetch data from this source
                const data = await source.fetchFunction(symbol, interval, assetType);
                
                // Add source information
                data.source = source.id;
                data.sourceName = source.name;
                
                console.log(`Successfully fetched data from ${source.id}`);
                return data;
            } catch (error) {
                console.warn(`Data source ${source.id} failed:`, error);
                lastError = error;
                // Continue to next source
            }
        }
        
        // If all sources failed, throw error
        throw new Error(`All data sources failed: ${lastError?.message || 'Unknown error'}`);
    }
    
    // Helper methods for formatting data from different sources
    
    /**
     * Format integrated API data
     * @private
     */
    _formatIntegratedApiData(response, symbol, interval, assetType) {
        // This should match the formatIntegratedApiData function in market-data-service.js
        if (!window.MarketDataService || !window.MarketDataService.formatIntegratedApiData) {
            throw new Error('Integrated API formatter not available');
        }
        
        return window.MarketDataService.formatIntegratedApiData(response, symbol, interval, assetType);
    }
    
    /**
     * Format Yahoo Finance data
     * @private
     */
    _formatYahooData(data, symbol, interval, assetType) {
        // Implementation will be added later
        throw new Error('Yahoo Finance formatter not implemented yet');
    }
    
    /**
     * Format CoinGecko data
     * @private
     */
    _formatCoinGeckoData(data, symbol, interval, assetType) {
        // Implementation will be added later
        throw new Error('CoinGecko formatter not implemented yet');
    }
    
    /**
     * Map interval to standard format
     * @private
     */
    _mapInterval(interval) {
        const map = {
            'M1': '1min',
            'M5': '5min',
            'M15': '15min',
            'M30': '30min',
            'H1': '60min',
            'H4': '60min', // Alpha Vantage doesn't have 4h, use 1h instead
            'D1': 'daily'
        };
        
        return map[interval] || interval;
    }
    
    /**
     * Format symbol for Yahoo Finance
     * @private
     */
    _formatYahooSymbol(symbol, assetType) {
        // Implementation will be added later
        return symbol;
    }
    
    /**
     * Map interval to Yahoo Finance format
     * @private
     */
    _mapYahooInterval(interval) {
        // Implementation will be added later
        return '1d';
    }
    
    /**
     * Format symbol for CoinGecko
     * @private
     */
    _formatCoinGeckoSymbol(symbol) {
        // Implementation will be added later
        return 'bitcoin';
    }
    
    /**
     * Map interval to CoinGecko days parameter
     * @private
     */
    _mapCoinGeckoDays(interval) {
        // Implementation will be added later
        return '7';
    }
}

// Create global instance
window.dataSourceRegistry = new DataSourceRegistry();

console.log('Data source registry initialized');
