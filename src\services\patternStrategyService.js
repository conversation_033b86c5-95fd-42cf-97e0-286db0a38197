/**
 * Pattern Strategy Service
 * 
 * Provides functions to backtest trading strategies based on candlestick patterns
 * and chart formations.
 */

import patternRecognitionService from './patternRecognitionService';
import logger from '../utils/logger';

/**
 * Backtest a pattern-based trading strategy
 * 
 * @param {Array} data - Historical OHLC data
 * @param {Object} config - Strategy configuration
 * @returns {Object} - Backtest results
 */
export const backtestPatternStrategy = (data, config) => {
  try {
    if (!data || !Array.isArray(data) || data.length < 20) {
      throw new Error('Insufficient data for backtesting');
    }
    
    // Initialize backtest state
    const initialCapital = config.initialCapital || 10000;
    let capital = initialCapital;
    let position = null;
    const trades = [];
    const equityCurve = [{ date: data[0].x, equity: capital }];
    
    // Detect patterns in the data
    const patterns = [];
    const supportResistance = { support: [], resistance: [] };
    
    // Process data in chunks to avoid memory issues with large datasets
    const chunkSize = 500;
    for (let i = 0; i < data.length; i += chunkSize) {
      const chunk = data.slice(i, i + chunkSize);
      
      // Detect patterns in this chunk
      const chunkPatterns = patternRecognitionService.detectCandlestickPatterns(chunk);
      
      // Adjust pattern indices to match the original data array
      chunkPatterns.forEach(pattern => {
        patterns.push({
          ...pattern,
          index: pattern.index + i
        });
      });
      
      // Detect support and resistance in this chunk
      if (i + chunkSize >= data.length) {
        // Only detect support/resistance in the last chunk
        const sr = patternRecognitionService.detectSupportResistance(data.slice(-chunkSize));
        
        // Adjust indices to match the original data array
        sr.support.forEach(level => {
          supportResistance.support.push({
            ...level,
            index: level.index + (data.length - chunkSize)
          });
        });
        
        sr.resistance.forEach(level => {
          supportResistance.resistance.push({
            ...level,
            index: level.index + (data.length - chunkSize)
          });
        });
      }
    }
    
    // Sort patterns by index
    patterns.sort((a, b) => a.index - b.index);
    
    // Process each candle for trading signals
    for (let i = 20; i < data.length; i++) {
      const candle = data[i];
      const prevCandle = data[i - 1];
      
      // Skip if no price data
      if (!candle.c || !prevCandle.c) continue;
      
      // Find patterns at this index
      const candlePatterns = patterns.filter(p => p.index === i);
      
      // Check for entry signals
      if (!position) {
        const entrySignal = getEntrySignal(candle, candlePatterns, data.slice(0, i + 1), config, supportResistance);
        
        if (entrySignal) {
          // Calculate position size
          const positionSize = calculatePositionSize(capital, candle, config);
          
          // Calculate stop loss and take profit levels
          const stopLoss = calculateStopLoss(entrySignal.type, candle, config);
          const takeProfit = calculateTakeProfit(entrySignal.type, candle, config);
          
          // Open position
          position = {
            type: entrySignal.type,
            entryPrice: candle.c,
            entryDate: candle.x,
            size: positionSize,
            stopLoss,
            takeProfit,
            trailingStop: config.trailingStop ? stopLoss : null,
            pattern: entrySignal.pattern
          };
          
          logger.debug('Opening position', position);
        }
      }
      // Check for exit signals
      else {
        // Check if stop loss or take profit hit
        const isStopLossHit = position.type === 'BUY' 
          ? candle.l <= position.stopLoss 
          : candle.h >= position.stopLoss;
        
        const isTakeProfitHit = position.type === 'BUY' 
          ? candle.h >= position.takeProfit 
          : candle.l <= position.takeProfit;
        
        // Check for exit patterns
        const exitPatterns = candlePatterns.filter(p => 
          (position.type === 'BUY' && p.type === 'bearish') || 
          (position.type === 'SELL' && p.type === 'bullish')
        );
        
        // Update trailing stop if enabled
        if (position.trailingStop !== null) {
          if (position.type === 'BUY' && candle.h > position.entryPrice) {
            const newTrailingStop = candle.h - (config.trailingStopDistance * 0.0001);
            if (newTrailingStop > position.trailingStop) {
              position.trailingStop = newTrailingStop;
            }
          } else if (position.type === 'SELL' && candle.l < position.entryPrice) {
            const newTrailingStop = candle.l + (config.trailingStopDistance * 0.0001);
            if (newTrailingStop < position.trailingStop) {
              position.trailingStop = newTrailingStop;
            }
          }
          
          // Check if trailing stop hit
          const isTrailingStopHit = position.type === 'BUY' 
            ? candle.l <= position.trailingStop 
            : candle.h >= position.trailingStop;
          
          if (isTrailingStopHit) {
            // Close position at trailing stop
            const exitPrice = position.trailingStop;
            const profit = calculateProfit(position, exitPrice);
            
            trades.push({
              type: position.type,
              entryDate: position.entryDate,
              exitDate: candle.x,
              entryPrice: position.entryPrice,
              exitPrice,
              profit,
              exitReason: 'TRAILING_STOP',
              pattern: position.pattern
            });
            
            // Update capital
            capital += profit;
            position = null;
            
            logger.debug('Position closed (trailing stop)', { exitPrice, profit });
          }
        }
        
        // Close position if stop loss or take profit hit
        if (position && (isStopLossHit || isTakeProfitHit || exitPatterns.length > 0)) {
          let exitPrice;
          let exitReason;
          
          if (isStopLossHit) {
            exitPrice = position.stopLoss;
            exitReason = 'STOP_LOSS';
          } else if (isTakeProfitHit) {
            exitPrice = position.takeProfit;
            exitReason = 'TAKE_PROFIT';
          } else {
            exitPrice = candle.c;
            exitReason = 'PATTERN';
          }
          
          const profit = calculateProfit(position, exitPrice);
          
          trades.push({
            type: position.type,
            entryDate: position.entryDate,
            exitDate: candle.x,
            entryPrice: position.entryPrice,
            exitPrice,
            profit,
            exitReason,
            pattern: position.pattern
          });
          
          // Update capital
          capital += profit;
          position = null;
          
          logger.debug('Position closed', { exitPrice, profit, exitReason });
        }
      }
      
      // Update equity curve
      equityCurve.push({
        date: candle.x,
        equity: capital + (position ? calculateProfit(position, candle.c) : 0)
      });
    }
    
    // Close any open position at the end of the test
    if (position) {
      const lastCandle = data[data.length - 1];
      const profit = calculateProfit(position, lastCandle.c);
      
      trades.push({
        type: position.type,
        entryDate: position.entryDate,
        exitDate: lastCandle.x,
        entryPrice: position.entryPrice,
        exitPrice: lastCandle.c,
        profit,
        exitReason: 'END_OF_TEST',
        pattern: position.pattern
      });
      
      // Update capital
      capital += profit;
      
      logger.debug('Position closed (end of test)', { exitPrice: lastCandle.c, profit });
    }
    
    // Calculate performance metrics
    const results = calculatePerformanceMetrics(trades, initialCapital, capital, equityCurve);
    
    return results;
  } catch (error) {
    logger.error('Error in backtestPatternStrategy:', error);
    throw error;
  }
};

/**
 * Get entry signal based on patterns and configuration
 * 
 * @param {Object} candle - Current candle
 * @param {Array} patterns - Detected patterns at this candle
 * @param {Array} data - Historical data up to this candle
 * @param {Object} config - Strategy configuration
 * @param {Object} supportResistance - Support and resistance levels
 * @returns {Object|null} - Entry signal or null if no signal
 */
const getEntrySignal = (candle, patterns, data, config, supportResistance) => {
  // Skip if no patterns detected
  if (!patterns || patterns.length === 0) return null;
  
  // Filter patterns by minimum significance
  const significantPatterns = patterns.filter(p => 
    p.significance >= (config.minPatternSignificance || 6)
  );
  
  if (significantPatterns.length === 0) return null;
  
  // Check if we're near support or resistance
  const isNearSupport = supportResistance.support.some(level => 
    Math.abs(candle.c - level.price) / level.price < 0.005 // Within 0.5%
  );
  
  const isNearResistance = supportResistance.resistance.some(level => 
    Math.abs(candle.c - level.price) / level.price < 0.005 // Within 0.5%
  );
  
  // Find the highest significance pattern
  const topPattern = significantPatterns.reduce((top, current) => 
    current.significance > top.significance ? current : top
  , significantPatterns[0]);
  
  // Determine signal type based on pattern type
  let signalType = null;
  
  if (topPattern.type === 'bullish' && !isNearResistance) {
    signalType = 'BUY';
  } else if (topPattern.type === 'bearish' && !isNearSupport) {
    signalType = 'SELL';
  }
  
  // Check if the pattern is in the allowed patterns list
  if (config.patterns && config.patterns.length > 0) {
    if (!config.patterns.includes(topPattern.pattern)) {
      return null;
    }
  }
  
  // Return signal if valid
  if (signalType) {
    return {
      type: signalType,
      pattern: topPattern
    };
  }
  
  return null;
};

/**
 * Calculate position size based on capital and risk management settings
 * 
 * @param {number} capital - Current capital
 * @param {Object} candle - Current candle
 * @param {Object} config - Strategy configuration
 * @returns {number} - Position size in lots
 */
const calculatePositionSize = (capital, candle, config) => {
  switch (config.positionSize) {
    case 'fixed':
      return config.fixedSize || 0.1;
    
    case 'percentage':
      const percentage = config.percentageSize || 2;
      return (capital * (percentage / 100)) / (candle.c * 10000);
    
    case 'risk_based':
      const riskAmount = capital * (config.riskPercentage / 100);
      const pipValue = 10; // $10 per pip for 1 lot
      const stopLossPips = config.stopLoss || 50;
      return riskAmount / (stopLossPips * pipValue);
    
    default:
      return 0.1; // Default to 0.1 lots
  }
};

/**
 * Calculate stop loss level
 * 
 * @param {string} positionType - 'BUY' or 'SELL'
 * @param {Object} candle - Current candle
 * @param {Object} config - Strategy configuration
 * @returns {number} - Stop loss price
 */
const calculateStopLoss = (positionType, candle, config) => {
  const stopLossPips = config.stopLoss || 50;
  const pipValue = 0.0001; // 1 pip = 0.0001 for most forex pairs
  
  if (positionType === 'BUY') {
    return candle.c - (stopLossPips * pipValue);
  } else {
    return candle.c + (stopLossPips * pipValue);
  }
};

/**
 * Calculate take profit level
 * 
 * @param {string} positionType - 'BUY' or 'SELL'
 * @param {Object} candle - Current candle
 * @param {Object} config - Strategy configuration
 * @returns {number} - Take profit price
 */
const calculateTakeProfit = (positionType, candle, config) => {
  const takeProfitPips = config.takeProfit || 100;
  const pipValue = 0.0001; // 1 pip = 0.0001 for most forex pairs
  
  if (positionType === 'BUY') {
    return candle.c + (takeProfitPips * pipValue);
  } else {
    return candle.c - (takeProfitPips * pipValue);
  }
};

/**
 * Calculate profit for a position
 * 
 * @param {Object} position - Position object
 * @param {number} exitPrice - Exit price
 * @returns {number} - Profit in account currency
 */
const calculateProfit = (position, exitPrice) => {
  const pipValue = 10 * position.size; // $10 per pip for 1 lot
  const pipDifference = position.type === 'BUY'
    ? (exitPrice - position.entryPrice) / 0.0001
    : (position.entryPrice - exitPrice) / 0.0001;
  
  return pipDifference * pipValue;
};

/**
 * Calculate performance metrics for the backtest
 * 
 * @param {Array} trades - Array of completed trades
 * @param {number} initialCapital - Initial capital
 * @param {number} finalCapital - Final capital
 * @param {Array} equityCurve - Equity curve data
 * @returns {Object} - Performance metrics
 */
const calculatePerformanceMetrics = (trades, initialCapital, finalCapital, equityCurve) => {
  // Basic metrics
  const netProfit = finalCapital - initialCapital;
  const totalTrades = trades.length;
  const winningTrades = trades.filter(trade => trade.profit > 0).length;
  const losingTrades = trades.filter(trade => trade.profit <= 0).length;
  const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
  
  // Profit metrics
  const grossProfit = trades.filter(trade => trade.profit > 0).reduce((sum, trade) => sum + trade.profit, 0);
  const grossLoss = Math.abs(trades.filter(trade => trade.profit <= 0).reduce((sum, trade) => sum + trade.profit, 0));
  const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0;
  
  // Average metrics
  const averageWin = winningTrades > 0 ? grossProfit / winningTrades : 0;
  const averageLoss = losingTrades > 0 ? grossLoss / losingTrades : 0;
  const averageReturn = totalTrades > 0 ? netProfit / totalTrades : 0;
  
  // Drawdown calculation
  let maxDrawdown = 0;
  let peak = initialCapital;
  
  for (const point of equityCurve) {
    if (point.equity > peak) {
      peak = point.equity;
    } else {
      const drawdown = ((peak - point.equity) / peak) * 100;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }
  }
  
  // Calculate monthly returns
  const monthlyReturns = {};
  let prevMonthEquity = initialCapital;
  let currentMonth = '';
  
  for (const point of equityCurve) {
    const date = new Date(point.date);
    const month = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    
    if (month !== currentMonth) {
      if (currentMonth) {
        // Calculate return for previous month
        const monthEndEquity = prevMonthEquity;
        const monthStartEquity = monthlyReturns[currentMonth]?.startEquity || initialCapital;
        const monthReturn = ((monthEndEquity - monthStartEquity) / monthStartEquity) * 100;
        
        monthlyReturns[currentMonth] = {
          startEquity: monthStartEquity,
          endEquity: monthEndEquity,
          return: monthReturn
        };
      }
      
      // Start new month
      currentMonth = month;
      if (!monthlyReturns[month]) {
        monthlyReturns[month] = {
          startEquity: prevMonthEquity,
          endEquity: prevMonthEquity,
          return: 0
        };
      }
    }
    
    prevMonthEquity = point.equity;
  }
  
  // Add the last month
  if (currentMonth) {
    const monthStartEquity = monthlyReturns[currentMonth]?.startEquity || initialCapital;
    const monthReturn = ((prevMonthEquity - monthStartEquity) / monthStartEquity) * 100;
    
    monthlyReturns[currentMonth] = {
      startEquity: monthStartEquity,
      endEquity: prevMonthEquity,
      return: monthReturn
    };
  }
  
  // Format monthly returns for chart
  const formattedMonthlyReturns = {};
  Object.keys(monthlyReturns).forEach(month => {
    formattedMonthlyReturns[month] = monthlyReturns[month].return;
  });
  
  // Calculate Sharpe and Sortino ratios
  const returns = trades.map(trade => trade.profit / initialCapital);
  const averageReturn2 = returns.reduce((sum, ret) => sum + ret, 0) / returns.length || 0;
  const stdDev = Math.sqrt(returns.reduce((sum, ret) => sum + Math.pow(ret - averageReturn2, 2), 0) / returns.length || 0);
  const negativeReturns = returns.filter(ret => ret < 0);
  const downDev = negativeReturns.length === 0 ? 1 : Math.sqrt(negativeReturns.reduce((sum, ret) => sum + Math.pow(ret, 2), 0) / negativeReturns.length);
  
  const sharpeRatio = stdDev === 0 ? 0 : averageReturn2 / stdDev;
  const sortinoRatio = downDev === 0 ? 0 : averageReturn2 / downDev;
  
  // Calculate expectancy
  const expectancy = (winRate / 100) * averageWin - (1 - winRate / 100) * averageLoss;
  
  // Calculate average holding time
  const holdingTimes = trades.map(trade => {
    const entryTime = new Date(trade.entryDate).getTime();
    const exitTime = new Date(trade.exitDate).getTime();
    return (exitTime - entryTime) / (1000 * 60 * 60 * 24); // in days
  });
  
  const averageHoldingTime = holdingTimes.reduce((sum, time) => sum + time, 0) / holdingTimes.length || 0;
  const averageHoldingTimeFormatted = `${Math.floor(averageHoldingTime)} days, ${Math.floor((averageHoldingTime % 1) * 24)} hours`;
  
  // Pattern performance
  const patternPerformance = {};
  
  trades.forEach(trade => {
    if (trade.pattern) {
      const patternName = trade.pattern.pattern;
      
      if (!patternPerformance[patternName]) {
        patternPerformance[patternName] = {
          totalTrades: 0,
          winningTrades: 0,
          losingTrades: 0,
          grossProfit: 0,
          grossLoss: 0,
          netProfit: 0,
          winRate: 0,
          averageProfit: 0,
          profitFactor: 0
        };
      }
      
      patternPerformance[patternName].totalTrades++;
      
      if (trade.profit > 0) {
        patternPerformance[patternName].winningTrades++;
        patternPerformance[patternName].grossProfit += trade.profit;
      } else {
        patternPerformance[patternName].losingTrades++;
        patternPerformance[patternName].grossLoss += Math.abs(trade.profit);
      }
      
      patternPerformance[patternName].netProfit += trade.profit;
    }
  });
  
  // Calculate pattern performance metrics
  Object.keys(patternPerformance).forEach(pattern => {
    const perf = patternPerformance[pattern];
    
    perf.winRate = (perf.winningTrades / perf.totalTrades) * 100;
    perf.averageProfit = perf.netProfit / perf.totalTrades;
    perf.profitFactor = perf.grossLoss > 0 ? perf.grossProfit / perf.grossLoss : perf.grossProfit > 0 ? Infinity : 0;
  });
  
  return {
    initialCapital,
    finalCapital,
    netProfit,
    totalTrades,
    winningTrades,
    losingTrades,
    winRate,
    profitFactor,
    maxDrawdown,
    averageWin,
    averageLoss,
    averageReturn,
    sharpeRatio,
    sortinoRatio,
    expectancy,
    averageHoldingTime: averageHoldingTimeFormatted,
    trades,
    equityCurve,
    monthlyReturns: formattedMonthlyReturns,
    patternPerformance
  };
};

export default {
  backtestPatternStrategy
};
