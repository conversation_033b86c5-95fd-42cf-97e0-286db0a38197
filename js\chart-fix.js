/**
 * Chart Fix Script for Trading Signals App
 * 
 * This script provides fixes and enhancements for the chart functionality,
 * ensuring compatibility with different data sources and chart libraries.
 */

// Chart configuration
const chartConfig = {
  // Default chart options
  defaultOptions: {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 0 // Disable animation for better performance
    },
    scales: {
      x: {
        type: 'time',
        time: {
          unit: 'hour',
          displayFormats: {
            minute: 'HH:mm',
            hour: 'MMM d, HH:mm',
            day: 'MMM d',
            week: 'MMM d',
            month: 'MMM yyyy'
          }
        },
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)'
        }
      },
      y: {
        position: 'right',
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)'
        }
      }
    },
    plugins: {
      tooltip: {
        mode: 'index',
        intersect: false,
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || '';
            const value = context.parsed.y;
            return `${label}: ${value.toFixed(2)}`;
          }
        }
      },
      legend: {
        display: true,
        position: 'top'
      }
    },
    interaction: {
      mode: 'nearest',
      axis: 'x',
      intersect: false
    }
  },
  
  // Chart colors
  colors: {
    primary: 'rgba(0, 123, 255, 1)',
    secondary: 'rgba(108, 117, 125, 1)',
    success: 'rgba(40, 167, 69, 1)',
    danger: 'rgba(220, 53, 69, 1)',
    warning: 'rgba(255, 193, 7, 1)',
    info: 'rgba(23, 162, 184, 1)',
    light: 'rgba(248, 249, 250, 1)',
    dark: 'rgba(52, 58, 64, 1)'
  },
  
  // Chart types
  types: {
    line: 'line',
    bar: 'bar',
    candlestick: 'candlestick',
    ohlc: 'ohlc'
  }
};

// Chart instances
const chartInstances = {};

/**
 * Initialize a chart
 * 
 * @param {string} containerId - ID of the container element
 * @param {Object} options - Chart options
 * @returns {Object} - Chart instance
 */
function initChart(containerId, options = {}) {
  // Get container element
  const container = document.getElementById(containerId);
  if (!container) {
    console.error(`Chart container '${containerId}' not found`);
    return null;
  }
  
  // Create canvas element if it doesn't exist
  let canvas = container.querySelector('canvas');
  if (!canvas) {
    canvas = document.createElement('canvas');
    container.appendChild(canvas);
  }
  
  // Get chart type
  const chartType = options.type || 'line';
  
  // Merge options with defaults
  const chartOptions = {
    ...chartConfig.defaultOptions,
    ...options
  };
  
  // Create chart instance
  let chart;
  
  try {
    // Check if Chart.js is available
    if (typeof Chart === 'undefined') {
      console.error('Chart.js is not loaded');
      return null;
    }
    
    // Create chart based on type
    if (chartType === 'candlestick' || chartType === 'ohlc') {
      // Check if financial chart plugin is available
      if (typeof Chart.FinancialChart === 'undefined') {
        console.error('Chart.js Financial plugin is not loaded');
        return null;
      }
      
      // Create financial chart
      chart = new Chart.FinancialChart(canvas, {
        type: chartType,
        data: {
          datasets: []
        },
        options: chartOptions
      });
    } else {
      // Create standard chart
      chart = new Chart(canvas, {
        type: chartType,
        data: {
          labels: [],
          datasets: []
        },
        options: chartOptions
      });
    }
    
    // Store chart instance
    chartInstances[containerId] = chart;
    
    return chart;
  } catch (error) {
    console.error('Error initializing chart:', error);
    return null;
  }
}

/**
 * Update a chart with market data
 * 
 * @param {Object} marketData - Market data
 * @param {string} containerId - ID of the container element
 * @returns {Object} - Updated chart instance
 */
function updateChart(marketData, containerId = 'chartContainer') {
  // Check if market data is valid
  if (!marketData || !marketData.dates || marketData.dates.length === 0) {
    console.warn('Invalid market data for chart update');
    return null;
  }
  
  // Get chart instance
  let chart = chartInstances[containerId];
  
  // Create chart if it doesn't exist
  if (!chart) {
    chart = initChart(containerId, {
      type: 'candlestick'
    });
    
    if (!chart) {
      return null;
    }
  }
  
  try {
    // Prepare data for chart
    const chartData = prepareChartData(marketData, chart.config.type);
    
    // Update chart data
    chart.data = chartData;
    
    // Update chart options based on timeframe
    updateChartOptions(chart, marketData.interval);
    
    // Update chart
    chart.update();
    
    return chart;
  } catch (error) {
    console.error('Error updating chart:', error);
    return null;
  }
}

/**
 * Prepare chart data from market data
 * 
 * @param {Object} marketData - Market data
 * @param {string} chartType - Chart type
 * @returns {Object} - Chart data
 */
function prepareChartData(marketData, chartType) {
  // Convert dates to Date objects
  const dates = marketData.dates.map(date => new Date(date));
  
  // Create chart data based on chart type
  if (chartType === 'candlestick' || chartType === 'ohlc') {
    // Create data for financial chart
    return {
      datasets: [
        {
          label: marketData.symbol,
          data: dates.map((date, i) => ({
            x: date,
            o: marketData.opens[i],
            h: marketData.highs[i],
            l: marketData.lows[i],
            c: marketData.closes[i]
          })),
          borderColor: chartConfig.colors.primary,
          color: {
            up: chartConfig.colors.success,
            down: chartConfig.colors.danger,
            unchanged: chartConfig.colors.secondary
          }
        }
      ]
    };
  } else {
    // Create data for standard chart
    return {
      labels: dates,
      datasets: [
        {
          label: `${marketData.symbol} Price`,
          data: marketData.closes,
          borderColor: chartConfig.colors.primary,
          backgroundColor: 'rgba(0, 123, 255, 0.1)',
          borderWidth: 2,
          pointRadius: 0,
          pointHoverRadius: 4,
          fill: true
        }
      ]
    };
  }
}

/**
 * Update chart options based on timeframe
 * 
 * @param {Object} chart - Chart instance
 * @param {string} timeframe - Timeframe
 */
function updateChartOptions(chart, timeframe) {
  // Set time unit based on timeframe
  let timeUnit = 'hour';
  
  switch (timeframe) {
    case '1m':
    case '5m':
    case '15m':
    case '30m':
      timeUnit = 'minute';
      break;
    case '1h':
    case '4h':
      timeUnit = 'hour';
      break;
    case '1d':
      timeUnit = 'day';
      break;
    case '1w':
      timeUnit = 'week';
      break;
    case '1M':
      timeUnit = 'month';
      break;
  }
  
  // Update time unit
  if (chart.options.scales.x.time) {
    chart.options.scales.x.time.unit = timeUnit;
  }
}

/**
 * Add a technical indicator to a chart
 * 
 * @param {string} indicatorType - Indicator type (e.g., 'sma', 'ema')
 * @param {Object} indicatorData - Indicator data
 * @param {string} containerId - ID of the container element
 * @returns {Object} - Updated chart instance
 */
function addIndicator(indicatorType, indicatorData, containerId = 'chartContainer') {
  // Get chart instance
  const chart = chartInstances[containerId];
  
  if (!chart) {
    console.error(`Chart '${containerId}' not found`);
    return null;
  }
  
  // Check if indicator data is valid
  if (!indicatorData || !indicatorData.values || indicatorData.values.length === 0) {
    console.warn('Invalid indicator data');
    return chart;
  }
  
  try {
    // Create dataset for indicator
    const dataset = {
      label: indicatorType.toUpperCase(),
      data: indicatorData.values,
      borderColor: getIndicatorColor(indicatorType),
      backgroundColor: 'transparent',
      borderWidth: 1.5,
      pointRadius: 0,
      pointHoverRadius: 3,
      fill: false,
      tension: 0.4
    };
    
    // Add dataset to chart
    chart.data.datasets.push(dataset);
    
    // Update chart
    chart.update();
    
    return chart;
  } catch (error) {
    console.error('Error adding indicator:', error);
    return chart;
  }
}

/**
 * Get color for an indicator
 * 
 * @param {string} indicatorType - Indicator type
 * @returns {string} - Color
 */
function getIndicatorColor(indicatorType) {
  switch (indicatorType.toLowerCase()) {
    case 'sma':
      return chartConfig.colors.primary;
    case 'ema':
      return chartConfig.colors.info;
    case 'rsi':
      return chartConfig.colors.warning;
    case 'macd':
      return chartConfig.colors.success;
    case 'bollinger':
      return chartConfig.colors.secondary;
    default:
      return chartConfig.colors.dark;
  }
}

/**
 * Destroy a chart
 * 
 * @param {string} containerId - ID of the container element
 */
function destroyChart(containerId) {
  const chart = chartInstances[containerId];
  
  if (chart) {
    chart.destroy();
    delete chartInstances[containerId];
  }
}

// Initialize charts when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Initialize main chart
  const chartContainer = document.getElementById('chartContainer');
  if (chartContainer) {
    initChart('chartContainer', {
      type: 'candlestick'
    });
  }
  
  // Initialize other charts
  const miniCharts = document.querySelectorAll('[data-chart]');
  miniCharts.forEach(container => {
    const chartId = container.id;
    const chartType = container.getAttribute('data-chart-type') || 'line';
    
    initChart(chartId, {
      type: chartType
    });
  });
});

// Handle theme changes
window.addEventListener('themechange', function(event) {
  const theme = event.detail.theme;
  
  // Update chart colors based on theme
  Object.values(chartInstances).forEach(chart => {
    if (theme === 'dark') {
      // Dark theme colors
      chart.options.scales.x.grid.color = 'rgba(255, 255, 255, 0.1)';
      chart.options.scales.y.grid.color = 'rgba(255, 255, 255, 0.1)';
      chart.options.scales.x.ticks.color = 'rgba(255, 255, 255, 0.7)';
      chart.options.scales.y.ticks.color = 'rgba(255, 255, 255, 0.7)';
    } else {
      // Light theme colors
      chart.options.scales.x.grid.color = 'rgba(0, 0, 0, 0.1)';
      chart.options.scales.y.grid.color = 'rgba(0, 0, 0, 0.1)';
      chart.options.scales.x.ticks.color = 'rgba(0, 0, 0, 0.7)';
      chart.options.scales.y.ticks.color = 'rgba(0, 0, 0, 0.7)';
    }
    
    chart.update();
  });
});

// Export functions for external use
window.chartFix = {
  initChart,
  updateChart,
  addIndicator,
  destroyChart
};

// For backward compatibility
window.updateChart = updateChart;
