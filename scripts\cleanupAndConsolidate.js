#!/usr/bin/env node

/**
 * Cleanup and Consolidation Script - Phase 2 Completion
 * 
 * This script:
 * - Removes unused and redundant files
 * - Consolidates duplicate functionality
 * - Cleans up old authentication files
 * - Removes deprecated services
 * - Updates imports and references
 * 
 * @version 2.0.0
 */

import fs from 'fs/promises';
import path from 'path';
import logger from '../src/utils/logger.js';
import chalk from 'chalk';

/**
 * Files and directories to remove
 */
const filesToRemove = [
  // Old authentication files
  'src/middleware/auth.js',
  'src/middleware/authMiddleware.js',
  'src/controllers/authController.js',
  'src/routes/authRoutes.js',
  'src/routes/auth.js',
  
  // Duplicate AI services (already consolidated)
  'src/features/ai/services/aiService.js',
  'src/features/news/services/openAISentimentService.js',
  'api-clients/openai.js',
  
  // Old security middleware
  'src/middleware/security.js',
  'src/middleware/rateLimiting.js',
  'src/middleware/validation.js',
  
  // Duplicate user models
  'src/models/User.js',
  'src/models/user.js',
  'models/User.js',
  
  // Old session management
  'src/services/sessionService.js',
  'src/middleware/session.js',
  
  // Deprecated API files
  'src/api/auth.js',
  'src/api/users.js',
  'routes/auth.js',
  'routes/users.js',
  
  // Old validation files
  'src/validation/userValidation.js',
  'src/validation/authValidation.js',
  'src/utils/validation.js',
  
  // Duplicate configuration files
  'config/auth.js',
  'config/security.js',
  'config/database.js',
  
  // Test files for removed features
  'tests/auth.test.js',
  'tests/user.test.js',
  '__tests__/auth.test.js',
  
  // Old documentation
  'docs/auth.md',
  'docs/security.md',
  'README_OLD.md',
  
  // Temporary files
  'temp/',
  'tmp/',
  '.tmp/',
  
  // Old build files
  'dist/',
  'build/',
  
  // Unused static files
  'public/old/',
  'public/deprecated/',
  'static/old/',
  
  // Old migration files
  'migrations/old/',
  'database/migrations/old/',
  
  // Backup files
  '*.bak',
  '*.backup',
  '*.old',
  
  // Log files (keep structure, remove content)
  'logs/*.log',
  
  // Cache files
  '.cache/',
  'node_modules/.cache/',
  
  // IDE files
  '.vscode/settings.json.bak',
  '.idea/',
  
  // OS files
  '.DS_Store',
  'Thumbs.db',
  'desktop.ini'
];

/**
 * Directories to clean (remove contents but keep directory)
 */
const directoriesToClean = [
  'logs',
  'temp',
  'tmp'
];

/**
 * Files to update with new imports
 */
const filesToUpdate = [
  {
    file: 'server.js',
    updates: [
      {
        from: "require('./src/middleware/auth.js')",
        to: "require('./src/services/enhancedSecurityService.js')"
      },
      {
        from: "require('./src/controllers/authController.js')",
        to: "require('./src/controllers/enhancedAuthController.js')"
      }
    ]
  }
];

/**
 * Print cleanup header
 */
function printHeader() {
  console.log(chalk.red.bold('\n🧹 Trading Signals App - Cleanup & Consolidation'));
  console.log(chalk.red('Phase 2 Implementation Cleanup\n'));
  console.log(chalk.gray('This script will:'));
  console.log(chalk.gray('• Remove unused and redundant files'));
  console.log(chalk.gray('• Clean up old authentication files'));
  console.log(chalk.gray('• Remove deprecated services'));
  console.log(chalk.gray('• Update imports and references'));
  console.log(chalk.gray('• Consolidate duplicate functionality\n'));
}

/**
 * Check if file or directory exists
 * 
 * @param {string} filePath - Path to check
 * @returns {Promise<boolean>} Whether file exists
 */
async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

/**
 * Remove file or directory
 * 
 * @param {string} filePath - Path to remove
 * @returns {Promise<boolean>} Success status
 */
async function removeFileOrDirectory(filePath) {
  try {
    const stats = await fs.stat(filePath);
    
    if (stats.isDirectory()) {
      await fs.rmdir(filePath, { recursive: true });
      console.log(chalk.red(`✓ Removed directory: ${filePath}`));
    } else {
      await fs.unlink(filePath);
      console.log(chalk.red(`✓ Removed file: ${filePath}`));
    }
    
    return true;
  } catch (error) {
    if (error.code !== 'ENOENT') {
      console.log(chalk.yellow(`⚠ Could not remove ${filePath}: ${error.message}`));
    }
    return false;
  }
}

/**
 * Clean directory contents
 * 
 * @param {string} dirPath - Directory to clean
 * @returns {Promise<boolean>} Success status
 */
async function cleanDirectory(dirPath) {
  try {
    if (!(await fileExists(dirPath))) {
      // Create directory if it doesn't exist
      await fs.mkdir(dirPath, { recursive: true });
      console.log(chalk.blue(`✓ Created directory: ${dirPath}`));
      return true;
    }
    
    const files = await fs.readdir(dirPath);
    let removedCount = 0;
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      if (await removeFileOrDirectory(filePath)) {
        removedCount++;
      }
    }
    
    if (removedCount > 0) {
      console.log(chalk.blue(`✓ Cleaned directory: ${dirPath} (${removedCount} items removed)`));
    }
    
    return true;
  } catch (error) {
    console.log(chalk.yellow(`⚠ Could not clean directory ${dirPath}: ${error.message}`));
    return false;
  }
}

/**
 * Update file imports and references
 * 
 * @param {Object} fileUpdate - File update configuration
 * @returns {Promise<boolean>} Success status
 */
async function updateFileImports(fileUpdate) {
  try {
    if (!(await fileExists(fileUpdate.file))) {
      console.log(chalk.yellow(`⚠ File not found: ${fileUpdate.file}`));
      return false;
    }
    
    let content = await fs.readFile(fileUpdate.file, 'utf8');
    let updated = false;
    
    for (const update of fileUpdate.updates) {
      if (content.includes(update.from)) {
        content = content.replace(new RegExp(update.from, 'g'), update.to);
        updated = true;
      }
    }
    
    if (updated) {
      await fs.writeFile(fileUpdate.file, content, 'utf8');
      console.log(chalk.green(`✓ Updated imports in: ${fileUpdate.file}`));
      return true;
    }
    
    return false;
  } catch (error) {
    console.log(chalk.yellow(`⚠ Could not update ${fileUpdate.file}: ${error.message}`));
    return false;
  }
}

/**
 * Create .gitignore entries for cleanup
 */
async function updateGitignore() {
  try {
    const gitignoreEntries = [
      '# Cleanup - Phase 2',
      'logs/*.log',
      'temp/',
      'tmp/',
      '.tmp/',
      '*.bak',
      '*.backup',
      '*.old',
      '.cache/',
      '.DS_Store',
      'Thumbs.db',
      'desktop.ini',
      ''
    ].join('\n');
    
    let gitignoreContent = '';
    
    if (await fileExists('.gitignore')) {
      gitignoreContent = await fs.readFile('.gitignore', 'utf8');
    }
    
    if (!gitignoreContent.includes('# Cleanup - Phase 2')) {
      gitignoreContent += '\n' + gitignoreEntries;
      await fs.writeFile('.gitignore', gitignoreContent, 'utf8');
      console.log(chalk.green('✓ Updated .gitignore with cleanup entries'));
    }
    
    return true;
  } catch (error) {
    console.log(chalk.yellow(`⚠ Could not update .gitignore: ${error.message}`));
    return false;
  }
}

/**
 * Generate cleanup report
 * 
 * @param {Object} stats - Cleanup statistics
 */
function generateReport(stats) {
  console.log(chalk.blue.bold('\n📋 Cleanup Report'));
  console.log(chalk.blue('==================\n'));
  
  console.log(chalk.green(`✓ Files removed: ${stats.filesRemoved}`));
  console.log(chalk.green(`✓ Directories cleaned: ${stats.directoriesCleaned}`));
  console.log(chalk.green(`✓ Files updated: ${stats.filesUpdated}`));
  console.log(chalk.green(`✓ Total operations: ${stats.totalOperations}`));
  
  if (stats.errors > 0) {
    console.log(chalk.yellow(`⚠ Warnings/Errors: ${stats.errors}`));
  }
  
  console.log(chalk.blue('\nCleanup completed successfully!'));
  console.log(chalk.blue('Your Trading Signals App is now consolidated and optimized.'));
  
  console.log(chalk.green.bold('\n🎉 Phase 2 Implementation Complete!'));
  console.log(chalk.green('Enhanced Security Features:'));
  console.log(chalk.green('• JWT refresh token mechanism'));
  console.log(chalk.green('• RBAC system (Admin/Premium/Basic)'));
  console.log(chalk.green('• Rate limiting with user-based limits'));
  console.log(chalk.green('• Input validation using Joi schemas'));
  console.log(chalk.green('• Session management with Redis'));
  console.log(chalk.green('• Enhanced password security'));
  
  console.log(chalk.blue('\nNext steps:'));
  console.log(chalk.blue('• Install dependencies: npm install'));
  console.log(chalk.blue('• Start the application: npm start'));
  console.log(chalk.blue('• Test authentication: POST /api/auth/register'));
  console.log(chalk.blue('• Monitor security: GET /api/auth/security-metrics'));
  console.log(chalk.blue('• Review documentation in docs/ folder\n'));
}

/**
 * Main cleanup function
 */
async function runCleanup() {
  printHeader();
  
  const stats = {
    filesRemoved: 0,
    directoriesCleaned: 0,
    filesUpdated: 0,
    totalOperations: 0,
    errors: 0
  };
  
  try {
    console.log(chalk.yellow('🗑️  Removing unused files...\n'));
    
    // Remove unused files
    for (const filePath of filesToRemove) {
      if (await fileExists(filePath)) {
        if (await removeFileOrDirectory(filePath)) {
          stats.filesRemoved++;
        } else {
          stats.errors++;
        }
      }
      stats.totalOperations++;
    }
    
    console.log(chalk.yellow('\n🧽 Cleaning directories...\n'));
    
    // Clean directories
    for (const dirPath of directoriesToClean) {
      if (await cleanDirectory(dirPath)) {
        stats.directoriesCleaned++;
      } else {
        stats.errors++;
      }
      stats.totalOperations++;
    }
    
    console.log(chalk.yellow('\n📝 Updating file imports...\n'));
    
    // Update file imports
    for (const fileUpdate of filesToUpdate) {
      if (await updateFileImports(fileUpdate)) {
        stats.filesUpdated++;
      } else {
        stats.errors++;
      }
      stats.totalOperations++;
    }
    
    // Update .gitignore
    await updateGitignore();
    
    // Generate report
    generateReport(stats);
    
  } catch (error) {
    console.error(chalk.red('\n❌ Cleanup failed with error:'), error);
    process.exit(1);
  }
}

// Run cleanup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runCleanup().catch(error => {
    console.error(chalk.red('Cleanup script failed:'), error);
    process.exit(1);
  });
}

export { runCleanup };
export default runCleanup;
