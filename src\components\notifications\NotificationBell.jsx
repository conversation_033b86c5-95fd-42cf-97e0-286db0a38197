import React, { useEffect, useState, useRef } from 'react';
import { BellIcon } from '@heroicons/react/outline';
import Modal from '../ui/Modal.jsx';
import Button from '../ui/Button.jsx';

const fetchNotifications = async () => {
  const res = await fetch('/api/v1/notifications', { credentials: 'include' });
  const data = await res.json();
  return data.data || [];
};

const markAsRead = async (id) => {
  await fetch(`/api/v1/notifications/${id}/read`, {
    method: 'PATCH',
    credentials: 'include',
  });
};

const markAllAsRead = async () => {
  await fetch('/api/v1/notifications/read-all', {
    method: 'PATCH',
    credentials: 'include',
  });
};

export default function NotificationBell() {
  const [notifications, setNotifications] = useState([]);
  const [open, setOpen] = useState(false);
  const [installPrompt, setInstallPrompt] = useState(null);
  const bellRef = useRef();

  useEffect(() => {
    if (open) {
      fetchNotifications().then(setNotifications);
    }
  }, [open]);

  // Listen for beforeinstallprompt event
  useEffect(() => {
    const handler = (e) => {
      e.preventDefault();
      setInstallPrompt(e);
    };
    window.addEventListener('beforeinstallprompt', handler);
    return () => window.removeEventListener('beforeinstallprompt', handler);
  }, []);

  const handleInstall = () => {
    if (installPrompt) {
      installPrompt.prompt();
      installPrompt.userChoice.then(() => setInstallPrompt(null));
    }
  };

  const unreadCount = notifications.filter((n) => !n.read).length;

  const handleMarkAsRead = async (id) => {
    await markAsRead(id);
    setNotifications((prev) => prev.map((n) => n._id === id ? { ...n, read: true } : n));
  };

  const handleMarkAllAsRead = async () => {
    await markAllAsRead();
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
  };

  // Close dropdown on outside click
  useEffect(() => {
    function handleClickOutside(event) {
      if (bellRef.current && !bellRef.current.contains(event.target)) {
        setOpen(false);
      }
    }
    if (open) document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [open]);

  return (
    <div className="relative" ref={bellRef}>
      <button
        className="relative p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none"
        onClick={() => setOpen((v) => !v)}
        aria-label="Notifications"
      >
        <BellIcon className="h-6 w-6 text-gray-700 dark:text-gray-200" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5">
            {unreadCount}
          </span>
        )}
      </button>
      {open && (
        <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 shadow-lg rounded-lg z-50 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between px-4 py-2 border-b border-gray-100 dark:border-gray-700">
            <span className="font-semibold text-gray-800 dark:text-gray-100">Notifications</span>
            <Button size="sm" onClick={handleMarkAllAsRead} disabled={unreadCount === 0}>
              Mark all as read
            </Button>
          </div>
          <ul className="max-h-80 overflow-y-auto divide-y divide-gray-100 dark:divide-gray-700">
            {notifications.length === 0 && (
              <li className="p-4 text-center text-gray-500 dark:text-gray-400">No notifications</li>
            )}
            {notifications.map((n) => (
              <li
                key={n._id}
                className={`px-4 py-3 flex items-start gap-2 cursor-pointer ${!n.read ? 'bg-blue-50 dark:bg-blue-900/30' : ''}`}
                onClick={() => {
                  if (!n.read) handleMarkAsRead(n._id);
                  if (n.link) window.location.href = n.link;
                }}
              >
                <span className={`inline-block w-2 h-2 rounded-full mt-2 ${n.read ? 'bg-gray-300' : 'bg-blue-500'}`}></span>
                <div className="flex-1">
                  <div className="text-sm text-gray-800 dark:text-gray-100">{n.message}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{new Date(n.createdAt).toLocaleString()}</div>
                </div>
                {!n.read && (
                  <Button size="xs" onClick={e => { e.stopPropagation(); handleMarkAsRead(n._id); }}>
                    Mark as read
                  </Button>
                )}
              </li>
            ))}
          </ul>
          <div className="px-4 py-2 border-t border-gray-100 dark:border-gray-700 text-center flex flex-col gap-2">
            <a href="/notifications" className="text-blue-600 dark:text-blue-400 text-sm hover:underline">View all</a>
            {installPrompt && (
              <Button type="button" size="sm" onClick={handleInstall}>
                Install App
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
} 