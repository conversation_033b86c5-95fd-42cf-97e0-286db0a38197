/**
 * Structured Logger for Trading Signals App
 * 
 * Provides comprehensive logging with correlation IDs, structured data,
 * and specialized logging for AI response parsing, API key rotation,
 * and trading signal generation events.
 */

const winston = require('winston');
const { v4: uuidv4 } = require('uuid');

/**
 * Custom log levels for trading application
 */
const customLevels = {
  levels: {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
    trace: 5
  },
  colors: {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    http: 'magenta',
    debug: 'blue',
    trace: 'cyan'
  }
};

/**
 * Custom format for structured logging
 */
const structuredFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, correlationId, component, ...meta }) => {
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      correlationId: correlationId || 'unknown',
      component: component || 'system',
      message,
      ...meta
    };

    return JSON.stringify(logEntry);
  })
);

/**
 * Create logger instance with multiple transports
 */
const logger = winston.createLogger({
  levels: customLevels.levels,
  level: process.env.LOG_LEVEL || 'info',
  format: structuredFormat,
  defaultMeta: {
    service: 'trading-signals-app',
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  },
  transports: [
    // Console transport for development
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize({ all: true }),
        winston.format.simple()
      )
    }),

    // File transport for all logs
    new winston.transports.File({
      filename: 'logs/app.log',
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      tailable: true
    }),

    // Separate file for errors
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      tailable: true
    }),

    // Separate file for AI-related logs
    new winston.transports.File({
      filename: 'logs/ai-operations.log',
      level: 'info',
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      tailable: true,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json(),
        winston.format.printf((info) => {
          if (info.component === 'ai-service' || info.component === 'openai-service') {
            return JSON.stringify(info);
          }
          return false; // Don't log non-AI entries to this file
        })
      )
    }),

    // Separate file for trading signals
    new winston.transports.File({
      filename: 'logs/trading-signals.log',
      level: 'info',
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      tailable: true,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json(),
        winston.format.printf((info) => {
          if (info.component === 'signal-generator' || info.component === 'trading-engine') {
            return JSON.stringify(info);
          }
          return false;
        })
      )
    })
  ],

  // Handle uncaught exceptions
  exceptionHandlers: [
    new winston.transports.File({ filename: 'logs/exceptions.log' })
  ],

  // Handle unhandled promise rejections
  rejectionHandlers: [
    new winston.transports.File({ filename: 'logs/rejections.log' })
  ]
});

/**
 * Structured Logger Class with specialized methods
 */
class StructuredLogger {
  constructor() {
    this.logger = logger;
    this.correlationIdStore = new Map();
  }

  /**
   * Generate or retrieve correlation ID for request tracking
   */
  getCorrelationId(req = null) {
    if (req && req.correlationId) {
      return req.correlationId;
    }

    const correlationId = uuidv4();
    if (req) {
      req.correlationId = correlationId;
    }

    return correlationId;
  }

  /**
   * Log AI response parsing failures with detailed context
   */
  logAIParsingFailure(correlationId, inputData, expectedFormat, actualResponse, error, context = {}) {
    this.logger.error('AI response parsing failed', {
      correlationId,
      component: 'ai-service',
      event: 'parsing_failure',
      inputData: {
        symbol: inputData.symbol,
        dataPoints: Object.keys(inputData).length,
        hasRequiredFields: this.validateRequiredFields(inputData)
      },
      expectedFormat,
      actualResponse: {
        type: typeof actualResponse,
        length: actualResponse ? actualResponse.length : 0,
        preview: actualResponse ? actualResponse.substring(0, 200) : null,
        isValidJSON: this.isValidJSON(actualResponse)
      },
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      context: {
        model: context.model || 'unknown',
        attempt: context.attempt || 1,
        timestamp: new Date().toISOString(),
        ...context
      },
      troubleshooting: {
        possibleCauses: this.identifyParsingIssues(actualResponse, error),
        suggestedActions: this.suggestParsingFixes(actualResponse, error)
      }
    });
  }

  /**
   * Log API key rotation events and fallback patterns
   */
  logAPIKeyRotation(correlationId, event, details = {}) {
    this.logger.info('API key rotation event', {
      correlationId,
      component: 'api-key-manager',
      event: `key_rotation_${event}`,
      details: {
        previousKeyId: details.previousKeyId ? this.maskAPIKey(details.previousKeyId) : null,
        newKeyId: details.newKeyId ? this.maskAPIKey(details.newKeyId) : null,
        reason: details.reason || 'unknown',
        provider: details.provider || 'openai',
        remainingKeys: details.remainingKeys || 0,
        rotationTime: new Date().toISOString(),
        ...details
      },
      metrics: {
        totalRotations: details.totalRotations || 0,
        successfulRotations: details.successfulRotations || 0,
        failedRotations: details.failedRotations || 0,
        averageRotationTime: details.averageRotationTime || 0
      }
    });
  }

  /**
   * Log comprehensive trading signal generation events
   */
  logTradingSignalGeneration(correlationId, signalData, context = {}) {
    this.logger.info('Trading signal generated', {
      correlationId,
      component: 'signal-generator',
      event: 'signal_generated',
      signal: {
        symbol: signalData.symbol,
        type: signalData.type,
        strength: signalData.strength,
        timeframe: signalData.timeframe,
        entryPrice: signalData.entryPrice,
        stopLoss: signalData.stopLoss,
        takeProfit: signalData.takeProfit,
        generatedBy: signalData.generatedBy || 'unknown',
        timestamp: new Date().toISOString()
      },
      aiModel: {
        model: context.model || 'unknown',
        confidence: context.confidence || 0,
        reasoning: context.reasoning || '',
        processingTime: context.processingTime || 0,
        tokensUsed: context.tokensUsed || 0
      },
      marketContext: {
        rsi: context.marketData?.rsi,
        macd: context.marketData?.macd,
        ema: context.marketData?.ema,
        volume: context.marketData?.volume,
        volatility: context.marketData?.volatility
      },
      performance: {
        generationTime: context.generationTime || 0,
        cacheHit: context.cacheHit || false,
        fallbackUsed: context.fallbackUsed || false,
        retryCount: context.retryCount || 0
      },
      validation: {
        isValid: this.validateSignal(signalData),
        riskLevel: this.calculateRiskLevel(signalData),
        qualityScore: this.calculateQualityScore(signalData, context)
      }
    });
  }

  /**
   * Log critical failures in real-time data processing
   */
  logRealTimeDataFailure(correlationId, dataType, error, context = {}) {
    this.logger.error('Real-time data processing failure', {
      correlationId,
      component: 'real-time-processor',
      event: 'data_processing_failure',
      dataType,
      error: {
        message: error.message,
        stack: error.stack,
        code: error.code || 'unknown'
      },
      context: {
        symbol: context.symbol,
        timeframe: context.timeframe,
        dataSource: context.dataSource,
        lastSuccessfulUpdate: context.lastSuccessfulUpdate,
        failureCount: context.failureCount || 1,
        ...context
      },
      impact: {
        affectedUsers: context.affectedUsers || 0,
        affectedSymbols: context.affectedSymbols || [],
        serviceStatus: context.serviceStatus || 'degraded',
        estimatedRecoveryTime: context.estimatedRecoveryTime
      },
      recovery: {
        fallbackActive: context.fallbackActive || false,
        cacheUsed: context.cacheUsed || false,
        manualInterventionRequired: context.manualInterventionRequired || false
      }
    });
  }

  /**
   * Log performance metrics and alerts
   */
  logPerformanceAlert(correlationId, metric, threshold, actual, context = {}) {
    const severity = this.calculateAlertSeverity(metric, threshold, actual);
    
    this.logger.warn('Performance threshold exceeded', {
      correlationId,
      component: 'performance-monitor',
      event: 'threshold_exceeded',
      alert: {
        metric,
        threshold,
        actual,
        severity,
        exceedancePercentage: ((actual - threshold) / threshold * 100).toFixed(2)
      },
      context: {
        component: context.component,
        operation: context.operation,
        duration: context.duration,
        timestamp: new Date().toISOString(),
        ...context
      },
      recommendations: this.getPerformanceRecommendations(metric, actual, threshold)
    });
  }

  /**
   * Utility methods for log analysis
   */
  validateRequiredFields(inputData) {
    const required = ['symbol', 'price'];
    return required.every(field => inputData.hasOwnProperty(field));
  }

  isValidJSON(str) {
    try {
      JSON.parse(str);
      return true;
    } catch {
      return false;
    }
  }

  identifyParsingIssues(response, error) {
    const issues = [];
    
    if (!response) {
      issues.push('Empty or null response');
    } else if (typeof response !== 'string') {
      issues.push('Response is not a string');
    } else if (!this.isValidJSON(response)) {
      issues.push('Invalid JSON format');
    } else if (response.includes('```')) {
      issues.push('Response contains markdown code blocks');
    } else if (error.message.includes('Unexpected token')) {
      issues.push('JSON syntax error');
    }

    return issues;
  }

  suggestParsingFixes(response, error) {
    const suggestions = [];
    
    if (response && response.includes('```')) {
      suggestions.push('Strip markdown code blocks from response');
    }
    if (error.message.includes('Unexpected token')) {
      suggestions.push('Validate JSON syntax before parsing');
    }
    if (!response) {
      suggestions.push('Implement response validation before parsing');
    }

    return suggestions;
  }

  maskAPIKey(key) {
    if (!key || key.length < 8) return '***';
    return key.substring(0, 4) + '***' + key.substring(key.length - 4);
  }

  validateSignal(signal) {
    const required = ['symbol', 'type', 'strength', 'entryPrice'];
    return required.every(field => signal.hasOwnProperty(field));
  }

  calculateRiskLevel(signal) {
    const stopLossDistance = Math.abs(signal.entryPrice - signal.stopLoss);
    const takeProfitDistance = Math.abs(signal.takeProfit - signal.entryPrice);
    const riskRewardRatio = takeProfitDistance / stopLossDistance;
    
    if (riskRewardRatio >= 2) return 'low';
    if (riskRewardRatio >= 1) return 'medium';
    return 'high';
  }

  calculateQualityScore(signal, context) {
    let score = 0;
    
    if (signal.strength >= 80) score += 30;
    else if (signal.strength >= 60) score += 20;
    else score += 10;
    
    if (context.confidence >= 80) score += 25;
    else if (context.confidence >= 60) score += 15;
    else score += 5;
    
    if (context.reasoning && context.reasoning.length > 50) score += 20;
    else score += 10;
    
    if (this.calculateRiskLevel(signal) === 'low') score += 25;
    else if (this.calculateRiskLevel(signal) === 'medium') score += 15;
    else score += 5;
    
    return Math.min(score, 100);
  }

  calculateAlertSeverity(metric, threshold, actual) {
    const exceedance = (actual - threshold) / threshold;
    
    if (exceedance >= 1) return 'critical';
    if (exceedance >= 0.5) return 'high';
    if (exceedance >= 0.2) return 'medium';
    return 'low';
  }

  getPerformanceRecommendations(metric, actual, threshold) {
    const recommendations = [];
    
    switch (metric) {
      case 'response_time':
        recommendations.push('Consider implementing caching');
        recommendations.push('Review database query optimization');
        break;
      case 'memory_usage':
        recommendations.push('Check for memory leaks');
        recommendations.push('Implement garbage collection tuning');
        break;
      case 'error_rate':
        recommendations.push('Review error handling logic');
        recommendations.push('Implement circuit breaker pattern');
        break;
      default:
        recommendations.push('Review system performance metrics');
    }
    
    return recommendations;
  }

  /**
   * Create child logger with specific context
   */
  child(context) {
    return {
      ...this,
      logger: this.logger.child(context)
    };
  }

  /**
   * Standard logging methods with correlation ID support
   */
  info(message, meta = {}, correlationId = null) {
    this.logger.info(message, { ...meta, correlationId: correlationId || this.getCorrelationId() });
  }

  error(message, meta = {}, correlationId = null) {
    this.logger.error(message, { ...meta, correlationId: correlationId || this.getCorrelationId() });
  }

  warn(message, meta = {}, correlationId = null) {
    this.logger.warn(message, { ...meta, correlationId: correlationId || this.getCorrelationId() });
  }

  debug(message, meta = {}, correlationId = null) {
    this.logger.debug(message, { ...meta, correlationId: correlationId || this.getCorrelationId() });
  }
}

// Create singleton instance
const structuredLogger = new StructuredLogger();

module.exports = { structuredLogger, StructuredLogger };
