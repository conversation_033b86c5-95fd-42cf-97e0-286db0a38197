/**
 * Signal Routes for Trading Signals App
 * 
 * This file contains routes for managing trading signals with proper validation.
 */

const express = require('express');
const { ObjectId } = require('mongodb');
const validator = require('../../middleware/validator');
const auth = require('../../middleware/auth');
const logger = require('../../utils/logger');

// Create router
const router = express.Router();

// Get signals collection helper
const getSignalsCollection = (req) => {
  return req.app.locals.db.collection('tradingSignals');
};

/**
 * @swagger
 * /api/signals:
 *   get:
 *     summary: Get trading signals
 *     description: Retrieves trading signals for the authenticated user with optional filtering
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: symbol
 *         schema:
 *           type: string
 *         description: Filter by symbol
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [buy, sell]
 *         description: Filter by signal type
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [M1, M5, M15, M30, H1, H4, D1, W1, MN]
 *         description: Filter by timeframe
 *     responses:
 *       200:
 *         description: List of trading signals
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', 
  auth.authenticateToken,
  validator.signalFilterValidationRules,
  validator.validateRequest,
  async (req, res) => {
    try {
      const { symbol, type, timeframe, status } = req.query;
      
      // Build query
      const query = { userId: new ObjectId(req.user.id) };
      if (symbol) query.symbol = symbol;
      if (type) query.type = type;
      if (timeframe) query.timeframe = timeframe;
      if (status) query.status = status;
      
      // Get signals
      const signals = await getSignalsCollection(req)
        .find(query)
        .sort({ createdAt: -1 })
        .toArray();
      
      res.json(signals);
    } catch (error) {
      logger.error('Error getting signals', error);
      res.status(500).json({ 
        status: 'error', 
        message: 'Failed to retrieve signals' 
      });
    }
  }
);

/**
 * @swagger
 * /api/signals/{id}:
 *   get:
 *     summary: Get a trading signal by ID
 *     description: Retrieves a specific trading signal by its ID
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Signal ID
 *     responses:
 *       200:
 *         description: Trading signal
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Signal not found
 *       500:
 *         description: Server error
 */
router.get('/:id', 
  auth.authenticateToken,
  validator.signalIdValidationRules,
  validator.validateRequest,
  async (req, res) => {
    try {
      const signal = await getSignalsCollection(req)
        .findOne({ 
          _id: new ObjectId(req.params.id),
          userId: new ObjectId(req.user.id)
        });
      
      if (!signal) {
        return res.status(404).json({ 
          status: 'error', 
          message: 'Signal not found' 
        });
      }
      
      res.json(signal);
    } catch (error) {
      logger.error('Error getting signal by ID', error);
      res.status(500).json({ 
        status: 'error', 
        message: 'Failed to retrieve signal' 
      });
    }
  }
);

/**
 * @swagger
 * /api/signals:
 *   post:
 *     summary: Create a new trading signal
 *     description: Creates a new trading signal for the authenticated user
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - symbol
 *               - type
 *               - entryPrice
 *               - stopLoss
 *               - takeProfit
 *               - timeframe
 *             properties:
 *               symbol:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [buy, sell]
 *               entryPrice:
 *                 type: number
 *               stopLoss:
 *                 type: number
 *               takeProfit:
 *                 type: number
 *               timeframe:
 *                 type: string
 *                 enum: [M1, M5, M15, M30, H1, H4, D1, W1, MN]
 *               analysis:
 *                 type: string
 *     responses:
 *       201:
 *         description: Signal created successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/', 
  auth.authenticateToken,
  validator.signalValidationRules,
  validator.validateRequest,
  async (req, res) => {
    try {
      const { symbol, type, entryPrice, stopLoss, takeProfit, timeframe, analysis } = req.body;
      
      // Create signal object
      const signal = {
        symbol,
        type,
        entryPrice: parseFloat(entryPrice),
        stopLoss: parseFloat(stopLoss),
        takeProfit: parseFloat(takeProfit),
        timeframe,
        analysis,
        status: 'active',
        userId: new ObjectId(req.user.id),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // Insert signal
      const result = await getSignalsCollection(req).insertOne(signal);
      
      // Add _id to response
      signal._id = result.insertedId;
      
      // Emit WebSocket event if available
      const io = req.app.get('io');
      if (io) {
        io.to(`user:${req.user.id}`).emit('signal_update', signal);
        io.to(`symbol:${symbol}`).emit('signal_update', signal);
      }
      
      res.status(201).json(signal);
    } catch (error) {
      logger.error('Error creating signal', error);
      res.status(500).json({ 
        status: 'error', 
        message: 'Failed to create signal' 
      });
    }
  }
);

/**
 * @swagger
 * /api/signals/{id}:
 *   put:
 *     summary: Update a trading signal
 *     description: Updates an existing trading signal
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Signal ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               entryPrice:
 *                 type: number
 *               stopLoss:
 *                 type: number
 *               takeProfit:
 *                 type: number
 *               status:
 *                 type: string
 *                 enum: [active, executed, expired]
 *               analysis:
 *                 type: string
 *     responses:
 *       200:
 *         description: Signal updated successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Signal not found
 *       500:
 *         description: Server error
 */
router.put('/:id', 
  auth.authenticateToken,
  validator.signalIdValidationRules,
  validator.signalValidationRules,
  validator.validateRequest,
  async (req, res) => {
    try {
      const signalId = new ObjectId(req.params.id);
      const userId = new ObjectId(req.user.id);
      
      // Check if signal exists and belongs to the user
      const existingSignal = await getSignalsCollection(req)
        .findOne({ _id: signalId, userId });
      
      if (!existingSignal) {
        return res.status(404).json({ 
          status: 'error', 
          message: 'Signal not found' 
        });
      }
      
      // Update signal
      const { symbol, type, entryPrice, stopLoss, takeProfit, timeframe, status, analysis } = req.body;
      
      const updatedSignal = {
        ...existingSignal,
        symbol,
        type,
        entryPrice: parseFloat(entryPrice),
        stopLoss: parseFloat(stopLoss),
        takeProfit: parseFloat(takeProfit),
        timeframe,
        status: status || existingSignal.status,
        analysis,
        updatedAt: new Date()
      };
      
      // Save to database
      await getSignalsCollection(req).updateOne(
        { _id: signalId, userId },
        { $set: updatedSignal }
      );
      
      // Emit WebSocket event if available
      const io = req.app.get('io');
      if (io) {
        io.to(`user:${req.user.id}`).emit('signal_update', updatedSignal);
        io.to(`symbol:${symbol}`).emit('signal_update', updatedSignal);
      }
      
      res.json(updatedSignal);
    } catch (error) {
      logger.error('Error updating signal', error);
      res.status(500).json({ 
        status: 'error', 
        message: 'Failed to update signal' 
      });
    }
  }
);

/**
 * @swagger
 * /api/signals/{id}:
 *   delete:
 *     summary: Delete a trading signal
 *     description: Deletes a trading signal by ID
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Signal ID
 *     responses:
 *       200:
 *         description: Signal deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Signal not found
 *       500:
 *         description: Server error
 */
router.delete('/:id', 
  auth.authenticateToken,
  validator.signalIdValidationRules,
  validator.validateRequest,
  async (req, res) => {
    try {
      const signalId = new ObjectId(req.params.id);
      const userId = new ObjectId(req.user.id);
      
      // Check if signal exists and belongs to the user
      const signal = await getSignalsCollection(req)
        .findOne({ _id: signalId, userId });
      
      if (!signal) {
        return res.status(404).json({ 
          status: 'error', 
          message: 'Signal not found' 
        });
      }
      
      // Delete signal
      await getSignalsCollection(req).deleteOne({ _id: signalId, userId });
      
      // Emit WebSocket event if available
      const io = req.app.get('io');
      if (io) {
        io.to(`user:${req.user.id}`).emit('signal_delete', { _id: req.params.id });
        io.to(`symbol:${signal.symbol}`).emit('signal_delete', { _id: req.params.id });
      }
      
      res.json({ 
        status: 'success', 
        message: 'Signal deleted successfully' 
      });
    } catch (error) {
      logger.error('Error deleting signal', error);
      res.status(500).json({ 
        status: 'error', 
        message: 'Failed to delete signal' 
      });
    }
  }
);

module.exports = router; 