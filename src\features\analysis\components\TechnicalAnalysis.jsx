import React, { useState, useEffect, useRef } from 'react';
import { alphaVantageAPI } from '../../../services/apiService.js';
import Chart from 'chart.js/auto';
import 'chartjs-adapter-date-fns';
import 'chartjs-chart-financial';
import PatternRecognition from './PatternRecognition.jsx';
import patternRecognitionService from '../../../services/patternRecognitionService.js';
import '../../../utils/chartPlugins.js'; // Import chart plugins

/**
 * TechnicalAnalysis Component
 *
 * Provides technical analysis tools and chart visualization
 */
const TechnicalAnalysis = () => {
  // Chart reference
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  // State for symbol and timeframe
  const [symbol, setSymbol] = useState('EURUSD');
  const [timeframe, setTimeframe] = useState('D1');

  // State for market data
  const [marketData, setMarketData] = useState({
    ohlc: [],
    volume: [],
    loading: true,
    error: null
  });

  // State for indicators
  const [indicators, setIndicators] = useState({
    sma: { enabled: false, period: 20, data: [] },
    ema: { enabled: false, period: 50, data: [] },
    rsi: { enabled: false, period: 14, data: [], overbought: 70, oversold: 30 },
    macd: { enabled: false, data: [], signal: [], histogram: [] },
    bbands: { enabled: false, data: { upper: [], middle: [], lower: [] } }
  });

  // State for patterns
  const [patterns, setPatterns] = useState({
    candlestick: [],
    chart: [],
    support: [],
    resistance: [],
    showPatterns: true,
    showSupportResistance: true
  });

  // Available symbols
  const symbols = [
    { value: 'EURUSD', label: 'EUR/USD' },
    { value: 'GBPUSD', label: 'GBP/USD' },
    { value: 'USDJPY', label: 'USD/JPY' },
    { value: 'XAUUSD', label: 'Gold (XAU/USD)' },
    { value: 'BTCUSD', label: 'Bitcoin (BTC/USD)' }
  ];

  // Available timeframes
  const timeframes = [
    { value: 'M5', label: '5 Minutes' },
    { value: 'M15', label: '15 Minutes' },
    { value: 'M30', label: '30 Minutes' },
    { value: 'H1', label: '1 Hour' },
    { value: 'H4', label: '4 Hours' },
    { value: 'D1', label: 'Daily' }
  ];

  // Fetch market data
  const fetchMarketData = async () => {
    setMarketData(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Map timeframe to Alpha Vantage interval
      const intervalMap = {
        'M5': '5min',
        'M15': '15min',
        'M30': '30min',
        'H1': '60min',
        'H4': '4hour',
        'D1': 'daily'
      };

      // Determine if we're dealing with forex, crypto, or stock
      let functionName = 'TIME_SERIES_INTRADAY';
      let dataKey = `Time Series (${intervalMap[timeframe]})`;

      if (timeframe === 'D1') {
        functionName = 'TIME_SERIES_DAILY';
        dataKey = 'Time Series (Daily)';
      }

      if (symbol.includes('USD') && symbol !== 'BTCUSD') {
        // Forex data
        const fromCurrency = symbol.substring(0, 3);
        const toCurrency = symbol.substring(3, 6);

        functionName = 'FX_' + (timeframe === 'D1' ? 'DAILY' : 'INTRADAY');
        dataKey = timeframe === 'D1' ? 'Time Series FX (Daily)' : `Time Series FX (${intervalMap[timeframe]})`;

        const response = await alphaVantageAPI.get('query', {
          function: functionName,
          from_symbol: fromCurrency,
          to_symbol: toCurrency,
          interval: intervalMap[timeframe],
          outputsize: 'compact'
        });

        processMarketData(response, dataKey);
      } else if (symbol === 'BTCUSD') {
        // Crypto data
        functionName = 'DIGITAL_CURRENCY_' + (timeframe === 'D1' ? 'DAILY' : 'INTRADAY');
        dataKey = timeframe === 'D1' ? 'Time Series (Digital Currency Daily)' : `Time Series (Digital Currency ${intervalMap[timeframe]})`;

        const response = await alphaVantageAPI.get('query', {
          function: functionName,
          symbol: 'BTC',
          market: 'USD',
          interval: intervalMap[timeframe],
          outputsize: 'compact'
        });

        processMarketData(response, dataKey);
      } else {
        // Stock data
        const response = await alphaVantageAPI.get('query', {
          function: functionName,
          symbol: symbol,
          interval: intervalMap[timeframe],
          outputsize: 'compact'
        });

        processMarketData(response, dataKey);
      }
    } catch (error) {
      console.error('Error fetching market data:', error);
      setMarketData(prev => ({
        ...prev,
        loading: false,
        error: 'Failed to load market data. Please try again later.'
      }));
    }
  };

  // Process market data from API response
  const processMarketData = (response, dataKey) => {
    if (!response || !response[dataKey]) {
      setMarketData(prev => ({
        ...prev,
        loading: false,
        error: 'Invalid data received from API.'
      }));
      return;
    }

    const timeSeriesData = response[dataKey];
    const dates = Object.keys(timeSeriesData).sort();

    const ohlc = [];
    const volume = [];

    dates.forEach(date => {
      const data = timeSeriesData[date];

      // Handle different API response formats
      const open = parseFloat(data['1. open'] || data['1a. open'] || data['1. open (USD)'] || 0);
      const high = parseFloat(data['2. high'] || data['2a. high'] || data['2. high (USD)'] || 0);
      const low = parseFloat(data['3. low'] || data['3a. low'] || data['3. low (USD)'] || 0);
      const close = parseFloat(data['4. close'] || data['4a. close'] || data['4. close (USD)'] || 0);
      const vol = parseFloat(data['5. volume'] || data['5. volume (USD)'] || 0);

      const timestamp = new Date(date).getTime();

      ohlc.push({
        x: timestamp,
        o: open,
        h: high,
        l: low,
        c: close
      });

      volume.push({
        x: timestamp,
        y: vol
      });
    });

    setMarketData({
      ohlc: ohlc.reverse(),
      volume: volume.reverse(),
      loading: false,
      error: null
    });
  };

  // Toggle indicator
  const toggleIndicator = (indicator) => {
    setIndicators(prev => ({
      ...prev,
      [indicator]: {
        ...prev[indicator],
        enabled: !prev[indicator].enabled
      }
    }));
  };

  // Update indicator period
  const updateIndicatorPeriod = (indicator, period) => {
    setIndicators(prev => ({
      ...prev,
      [indicator]: {
        ...prev[indicator],
        period: parseInt(period)
      }
    }));
  };

  // Calculate indicators
  const calculateIndicators = async () => {
    if (marketData.ohlc.length === 0) return;

    try {
      // Calculate SMA if enabled
      if (indicators.sma.enabled) {
        const response = await alphaVantageAPI.get('query', {
          function: 'SMA',
          symbol: symbol,
          interval: timeframe === 'D1' ? 'daily' : timeframe.toLowerCase(),
          time_period: indicators.sma.period,
          series_type: 'close'
        });

        // Process SMA data
        // This is a simplified version - in a real app, you'd need to handle the API response properly
        const smaData = []; // Placeholder for actual data processing

        setIndicators(prev => ({
          ...prev,
          sma: {
            ...prev.sma,
            data: smaData
          }
        }));
      }

      // Similar calculations would be done for other indicators
      // For brevity, we're not implementing all of them here

    } catch (error) {
      console.error('Error calculating indicators:', error);
    }
  };

  // Initialize chart
  const initializeChart = () => {
    if (!chartRef.current) return;

    // Destroy existing chart if it exists
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    // Create new chart
    const ctx = chartRef.current.getContext('2d');

    chartInstance.current = new Chart(ctx, {
      type: 'candlestick',
      data: {
        datasets: [
          {
            label: symbol,
            data: marketData.ohlc,
            color: {
              up: 'rgba(75, 192, 192, 1)',
              down: 'rgba(255, 99, 132, 1)',
              unchanged: 'rgba(90, 90, 90, 1)',
            }
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            type: 'time',
            time: {
              unit: timeframe === 'D1' ? 'day' :
                    timeframe === 'H4' || timeframe === 'H1' ? 'hour' : 'minute'
            }
          },
          y: {
            position: 'right'
          }
        },
        plugins: {
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: function(context) {
                const point = context.raw;
                return [
                  `Open: ${point.o}`,
                  `High: ${point.h}`,
                  `Low: ${point.l}`,
                  `Close: ${point.c}`
                ];
              }
            }
          },
          legend: {
            display: false
          },
          // Pattern annotation plugin configuration
          patternAnnotation: {
            enabled: true,
            patterns: patterns.candlestick,
            supportResistance: {
              support: patterns.support,
              resistance: patterns.resistance
            },
            showPatterns: patterns.showPatterns,
            showSupportResistance: patterns.showSupportResistance,
            supportColor: 'rgba(0, 192, 0, 0.3)',
            resistanceColor: 'rgba(192, 0, 0, 0.3)',
            supportResistanceWidth: 2
          },
          // Price label plugin configuration
          priceLabel: {
            enabled: true,
            font: '12px sans-serif',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            textColor: 'rgba(0, 0, 0, 0.8)',
            width: 70
          }
        }
      }
    });
  };

  // Update chart with new data
  const updateChart = () => {
    if (!chartInstance.current) return;

    // Update candlestick data
    chartInstance.current.data.datasets[0].data = marketData.ohlc;

    // Update pattern annotations
    chartInstance.current.options.plugins.patternAnnotation.patterns = patterns.candlestick;
    chartInstance.current.options.plugins.patternAnnotation.supportResistance = {
      support: patterns.support,
      resistance: patterns.resistance
    };
    chartInstance.current.options.plugins.patternAnnotation.showPatterns = patterns.showPatterns;
    chartInstance.current.options.plugins.patternAnnotation.showSupportResistance = patterns.showSupportResistance;

    // Update chart
    chartInstance.current.update();
  };

  // Fetch data when symbol or timeframe changes
  useEffect(() => {
    fetchMarketData();
  }, [symbol, timeframe]);

  // Initialize or update chart when market data changes
  useEffect(() => {
    if (!marketData.loading && !marketData.error && marketData.ohlc.length > 0) {
      // Detect patterns
      const candlestickPatterns = patternRecognitionService.detectCandlestickPatterns(marketData.ohlc);
      const chartPatterns = patternRecognitionService.detectChartPatterns(marketData.ohlc);
      const { support, resistance } = patternRecognitionService.detectSupportResistance(marketData.ohlc);

      // Update patterns state
      setPatterns(prev => ({
        ...prev,
        candlestick: candlestickPatterns,
        chart: chartPatterns,
        support,
        resistance
      }));

      if (!chartInstance.current) {
        initializeChart();
      } else {
        updateChart();
      }

      // Calculate indicators
      calculateIndicators();
    }
  }, [marketData]);

  // Clean up chart on component unmount
  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, []);

  return (
    <div className="technical-analysis p-4">
      <h1 className="text-2xl font-bold mb-6">Technical Analysis</h1>

      {/* Controls Section */}
      <section className="mb-6">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Symbol Selector */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Symbol
              </label>
              <select
                value={symbol}
                onChange={(e) => setSymbol(e.target.value)}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {symbols.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>

            {/* Timeframe Selector */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Timeframe
              </label>
              <select
                value={timeframe}
                onChange={(e) => setTimeframe(e.target.value)}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {timeframes.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>

            {/* Refresh Button */}
            <div className="flex items-end">
              <button
                onClick={fetchMarketData}
                className="w-full p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow"
                disabled={marketData.loading}
              >
                {marketData.loading ? 'Loading...' : 'Refresh Data'}
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Indicators Section */}
      <section className="mb-6">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Indicators</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* SMA */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="sma-toggle"
                checked={indicators.sma.enabled}
                onChange={() => toggleIndicator('sma')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="sma-toggle" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                SMA
              </label>
              <input
                type="number"
                min="1"
                max="200"
                value={indicators.sma.period}
                onChange={(e) => updateIndicatorPeriod('sma', e.target.value)}
                className="w-16 p-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>

            {/* EMA */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="ema-toggle"
                checked={indicators.ema.enabled}
                onChange={() => toggleIndicator('ema')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="ema-toggle" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                EMA
              </label>
              <input
                type="number"
                min="1"
                max="200"
                value={indicators.ema.period}
                onChange={(e) => updateIndicatorPeriod('ema', e.target.value)}
                className="w-16 p-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>

            {/* RSI */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="rsi-toggle"
                checked={indicators.rsi.enabled}
                onChange={() => toggleIndicator('rsi')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="rsi-toggle" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                RSI
              </label>
              <input
                type="number"
                min="1"
                max="100"
                value={indicators.rsi.period}
                onChange={(e) => updateIndicatorPeriod('rsi', e.target.value)}
                className="w-16 p-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>

            {/* MACD */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="macd-toggle"
                checked={indicators.macd.enabled}
                onChange={() => toggleIndicator('macd')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="macd-toggle" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                MACD
              </label>
            </div>

            {/* Bollinger Bands */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="bbands-toggle"
                checked={indicators.bbands.enabled}
                onChange={() => toggleIndicator('bbands')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="bbands-toggle" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Bollinger Bands
              </label>
            </div>
          </div>
        </div>
      </section>

      {/* Chart Section */}
      <section className="mb-6">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Chart</h2>

            {/* Pattern Visualization Controls */}
            {!marketData.loading && !marketData.error && marketData.ohlc.length > 0 && (
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="show-patterns"
                    checked={patterns.showPatterns}
                    onChange={() => {
                      setPatterns(prev => ({
                        ...prev,
                        showPatterns: !prev.showPatterns
                      }));
                    }}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="show-patterns" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                    Show Patterns
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="show-support-resistance"
                    checked={patterns.showSupportResistance}
                    onChange={() => {
                      setPatterns(prev => ({
                        ...prev,
                        showSupportResistance: !prev.showSupportResistance
                      }));
                    }}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="show-support-resistance" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                    Show Support/Resistance
                  </label>
                </div>
              </div>
            )}
          </div>

          {marketData.loading ? (
            <div className="flex justify-center items-center h-96">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : marketData.error ? (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
              <span className="block sm:inline">{marketData.error}</span>
            </div>
          ) : (
            <div className="h-96">
              <canvas ref={chartRef}></canvas>
            </div>
          )}

          {/* Pattern Legend */}
          {!marketData.loading && !marketData.error && marketData.ohlc.length > 0 && patterns.showPatterns && (
            <div className="mt-4 flex flex-wrap gap-2">
              <div className="text-sm text-gray-500 dark:text-gray-400">Pattern Types:</div>
              <div className="flex items-center">
                <span className="inline-block w-4 h-4 bg-green-500 rounded-full mr-1"></span>
                <span className="text-sm">Bullish</span>
              </div>
              <div className="flex items-center">
                <span className="inline-block w-4 h-4 bg-red-500 rounded-full mr-1"></span>
                <span className="text-sm">Bearish</span>
              </div>
              <div className="flex items-center">
                <span className="inline-block w-4 h-4 bg-blue-500 rounded-full mr-1"></span>
                <span className="text-sm">Continuation</span>
              </div>
              <div className="flex items-center">
                <span className="inline-block w-4 h-4 bg-purple-500 rounded-full mr-1"></span>
                <span className="text-sm">Reversal</span>
              </div>
              <div className="flex items-center">
                <span className="inline-block w-4 h-4 bg-gray-500 rounded-full mr-1"></span>
                <span className="text-sm">Neutral</span>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Pattern Recognition Section */}
      <section>
        {!marketData.loading && !marketData.error && marketData.ohlc.length > 0 && (
          <PatternRecognition
            marketData={marketData.ohlc}
            timeframe={timeframe}
            symbol={symbol}
          />
        )}
      </section>
    </div>
  );
};

export default TechnicalAnalysis;
