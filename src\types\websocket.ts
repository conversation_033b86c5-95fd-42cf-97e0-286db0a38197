/**
 * WebSocket Types and Interfaces
 * 
 * Type definitions for real-time communication, WebSocket events,
 * and client-server messaging protocols.
 * 
 * @version 1.0.0
 */

import {
  Timestamp,
  ObjectId,
  WebSocketMessage,
  WebSocketConnectionState
} from './common';
import { UnifiedSignal } from './signals';
import { Quote, MarketData } from './market';
import { UserNotification } from './users';

// ============================================================================
// WEBSOCKET CONNECTION TYPES
// ============================================================================

export interface WebSocketConfig {
  url: string;
  protocols?: string[];
  reconnect: boolean;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeatInterval: number;
  timeout: number;
  compression: boolean;
}

export interface WebSocketClient {
  id: string;
  userId?: ObjectId;
  sessionId: string;
  connected: boolean;
  connectedAt: Timestamp;
  lastActivity: Timestamp;
  subscriptions: Set<string>;
  metadata: {
    ipAddress: string;
    userAgent: string;
    version: string;
  };
}

export interface WebSocketServer {
  clients: Map<string, WebSocketClient>;
  rooms: Map<string, Set<string>>;
  stats: WebSocketStats;
}

export interface WebSocketStats {
  totalConnections: number;
  activeConnections: number;
  messagesSent: number;
  messagesReceived: number;
  errors: number;
  uptime: number;
  lastReset: Timestamp;
}

// ============================================================================
// MESSAGE TYPES
// ============================================================================

export enum WebSocketMessageType {
  // Connection management
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  HEARTBEAT = 'heartbeat',
  PONG = 'pong',
  
  // Subscription management
  SUBSCRIBE = 'subscribe',
  UNSUBSCRIBE = 'unsubscribe',
  SUBSCRIPTION_CONFIRMED = 'subscription_confirmed',
  SUBSCRIPTION_ERROR = 'subscription_error',
  
  // Signal updates
  SIGNAL_UPDATE = 'signal_update',
  SIGNAL_CREATED = 'signal_created',
  SIGNAL_DELETED = 'signal_deleted',
  SIGNAL_STATUS_CHANGED = 'signal_status_changed',
  
  // Market data updates
  MARKET_UPDATE = 'market_update',
  QUOTE_UPDATE = 'quote_update',
  PRICE_ALERT = 'price_alert',
  
  // User notifications
  NOTIFICATION = 'notification',
  SYSTEM_ANNOUNCEMENT = 'system_announcement',
  
  // Trading updates
  TRADE_EXECUTED = 'trade_executed',
  POSITION_UPDATE = 'position_update',
  
  // System events
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info',
  
  // Authentication
  AUTH_REQUIRED = 'auth_required',
  AUTH_SUCCESS = 'auth_success',
  AUTH_FAILED = 'auth_failed'
}

// ============================================================================
// SPECIFIC MESSAGE INTERFACES
// ============================================================================

export interface WebSocketConnectMessage extends WebSocketMessage {
  type: WebSocketMessageType.CONNECT;
  data: {
    clientId: string;
    userId?: ObjectId;
    token?: string;
    version: string;
    capabilities: string[];
  };
}

export interface WebSocketSubscribeMessage extends WebSocketMessage {
  type: WebSocketMessageType.SUBSCRIBE;
  data: {
    channels: string[];
    symbols?: string[];
    filters?: SubscriptionFilter;
  };
}

export interface WebSocketUnsubscribeMessage extends WebSocketMessage {
  type: WebSocketMessageType.UNSUBSCRIBE;
  data: {
    channels: string[];
    symbols?: string[];
  };
}

export interface WebSocketSignalUpdateMessage extends WebSocketMessage {
  type: WebSocketMessageType.SIGNAL_UPDATE;
  data: {
    signal: UnifiedSignal;
    action: 'created' | 'updated' | 'deleted';
    userId?: ObjectId;
  };
}

export interface WebSocketMarketUpdateMessage extends WebSocketMessage {
  type: WebSocketMessageType.MARKET_UPDATE;
  data: {
    symbol: string;
    quote?: Quote;
    marketData?: MarketData;
    change: {
      price: number;
      percentage: number;
    };
  };
}

export interface WebSocketNotificationMessage extends WebSocketMessage {
  type: WebSocketMessageType.NOTIFICATION;
  data: UserNotification;
}

export interface WebSocketErrorMessage extends WebSocketMessage {
  type: WebSocketMessageType.ERROR;
  data: {
    code: string;
    message: string;
    details?: any;
    recoverable: boolean;
  };
}

export interface WebSocketHeartbeatMessage extends WebSocketMessage {
  type: WebSocketMessageType.HEARTBEAT;
  data: {
    timestamp: Timestamp;
    serverTime: Timestamp;
  };
}

// ============================================================================
// SUBSCRIPTION TYPES
// ============================================================================

export interface SubscriptionFilter {
  symbols?: string[];
  timeframes?: string[];
  signalTypes?: string[];
  minConfidence?: number;
  sources?: string[];
  userId?: ObjectId;
}

export interface Subscription {
  id: string;
  clientId: string;
  channel: string;
  filter?: SubscriptionFilter;
  createdAt: Timestamp;
  lastActivity: Timestamp;
  messageCount: number;
}

export enum WebSocketChannel {
  // Signal channels
  SIGNALS = 'signals',
  SIGNALS_USER = 'signals:user',
  SIGNALS_SYMBOL = 'signals:symbol',
  
  // Market data channels
  MARKET_DATA = 'market_data',
  QUOTES = 'quotes',
  PRICE_ALERTS = 'price_alerts',
  
  // User channels
  USER_NOTIFICATIONS = 'user:notifications',
  USER_TRADES = 'user:trades',
  USER_POSITIONS = 'user:positions',
  
  // System channels
  SYSTEM_ANNOUNCEMENTS = 'system:announcements',
  SYSTEM_STATUS = 'system:status',
  
  // Trading channels
  TRADE_EXECUTION = 'trade:execution',
  ORDER_UPDATES = 'order:updates'
}

// ============================================================================
// EVENT HANDLERS
// ============================================================================

export interface WebSocketEventHandlers {
  onConnect?: (client: WebSocketClient) => void;
  onDisconnect?: (client: WebSocketClient, reason: string) => void;
  onMessage?: (client: WebSocketClient, message: WebSocketMessage) => void;
  onSubscribe?: (client: WebSocketClient, subscription: Subscription) => void;
  onUnsubscribe?: (client: WebSocketClient, channel: string) => void;
  onError?: (client: WebSocketClient, error: Error) => void;
  onHeartbeat?: (client: WebSocketClient) => void;
}

// ============================================================================
// ROOM MANAGEMENT
// ============================================================================

export interface Room {
  id: string;
  name: string;
  type: 'symbol' | 'user' | 'system' | 'custom';
  clients: Set<string>;
  metadata: {
    createdAt: Timestamp;
    lastActivity: Timestamp;
    messageCount: number;
    maxClients?: number;
    persistent: boolean;
  };
}

export interface RoomManager {
  rooms: Map<string, Room>;
  joinRoom(clientId: string, roomId: string): boolean;
  leaveRoom(clientId: string, roomId: string): boolean;
  broadcastToRoom(roomId: string, message: WebSocketMessage): void;
  createRoom(roomId: string, options?: Partial<Room>): Room;
  deleteRoom(roomId: string): boolean;
  getRoomClients(roomId: string): string[];
  getClientRooms(clientId: string): string[];
}

// ============================================================================
// MESSAGE QUEUE TYPES
// ============================================================================

export interface QueuedMessage {
  id: string;
  clientId: string;
  message: WebSocketMessage;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  attempts: number;
  maxAttempts: number;
  createdAt: Timestamp;
  scheduledAt?: Timestamp;
  expiresAt?: Timestamp;
}

export interface MessageQueue {
  pending: QueuedMessage[];
  processing: QueuedMessage[];
  failed: QueuedMessage[];
  completed: QueuedMessage[];
  stats: {
    totalQueued: number;
    totalProcessed: number;
    totalFailed: number;
    averageProcessingTime: number;
  };
}

// ============================================================================
// RATE LIMITING
// ============================================================================

export interface RateLimitConfig {
  windowMs: number;
  maxMessages: number;
  skipSuccessfulRequests: boolean;
  skipFailedRequests: boolean;
  keyGenerator: (client: WebSocketClient) => string;
}

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  resetTime: Timestamp;
  retryAfter?: number;
}

// ============================================================================
// AUTHENTICATION AND AUTHORIZATION
// ============================================================================

export interface WebSocketAuth {
  required: boolean;
  tokenValidation: (token: string) => Promise<{ valid: boolean; userId?: ObjectId; permissions?: string[] }>;
  sessionValidation: (sessionId: string) => Promise<{ valid: boolean; userId?: ObjectId }>;
  permissionCheck: (userId: ObjectId, channel: string, action: string) => Promise<boolean>;
}

// ============================================================================
// MONITORING AND METRICS
// ============================================================================

export interface WebSocketMetrics {
  connections: {
    total: number;
    active: number;
    peak: number;
    peakTime: Timestamp;
  };
  messages: {
    sent: number;
    received: number;
    queued: number;
    failed: number;
    averageSize: number;
  };
  subscriptions: {
    total: number;
    byChannel: Record<string, number>;
    averagePerClient: number;
  };
  performance: {
    averageLatency: number;
    messageProcessingTime: number;
    memoryUsage: number;
    cpuUsage: number;
  };
  errors: {
    total: number;
    byType: Record<string, number>;
    lastError?: {
      message: string;
      timestamp: Timestamp;
      clientId?: string;
    };
  };
}

// ============================================================================
// TYPE GUARDS
// ============================================================================

export function isWebSocketMessage(obj: any): obj is WebSocketMessage {
  return (
    obj &&
    typeof obj.type === 'string' &&
    obj.data !== undefined &&
    typeof obj.timestamp === 'string'
  );
}

export function isWebSocketClient(obj: any): obj is WebSocketClient {
  return (
    obj &&
    typeof obj.id === 'string' &&
    typeof obj.connected === 'boolean' &&
    typeof obj.connectedAt === 'string' &&
    obj.subscriptions instanceof Set
  );
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export function createWebSocketMessage<T>(
  type: WebSocketMessageType,
  data: T,
  id?: string
): WebSocketMessage<T> {
  return {
    type,
    data,
    timestamp: new Date().toISOString(),
    id: id || `msg-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`
  };
}

export function generateClientId(): string {
  return `client-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
}

export function generateRoomId(type: string, identifier: string): string {
  return `${type}:${identifier}`;
}

export function parseRoomId(roomId: string): { type: string; identifier: string } {
  const [type, identifier] = roomId.split(':', 2);
  return { type, identifier };
}

export function isChannelAllowed(channel: string, permissions: string[]): boolean {
  // System channels require admin permissions
  if (channel.startsWith('system:')) {
    return permissions.includes('admin') || permissions.includes('system_access');
  }
  
  // User-specific channels require matching user ID or admin
  if (channel.startsWith('user:')) {
    const userId = channel.split(':')[1];
    return permissions.includes('admin') || permissions.includes(`user:${userId}`);
  }
  
  // Public channels are generally allowed
  return true;
}
