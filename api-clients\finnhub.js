const axios = require('axios');
const logger = console;

// Rate limiting and retry configuration
const RATE_LIMIT_DELAY = 1000; // 1 second between requests
const MAX_RETRIES = 3;

// Error handling wrapper with retries
const handleApiRequest = async (requestFn) => {
  let attempts = 0;
  while (attempts < MAX_RETRIES) {
    try {
      if (attempts > 0) {
        await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY));
      }
      return await requestFn();
    } catch (error) {
      attempts++;
      if (attempts === MAX_RETRIES) {
        if (error.response) {
          logger.error(`Finnhub API error: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
          throw new Error(`Finnhub API error: ${error.response.status}`);
        } else if (error.request) {
          logger.error('Finnhub API no response');
          throw new Error('Finnhub API no response');
        } else {
          logger.error(`Finnhub API request failed: ${error.message}`);
          throw new Error(`Finnhub API request failed: ${error.message}`);
        }
      }
      logger.warn(`Retrying request, attempt ${attempts} of ${MAX_RETRIES}`);
    }
  }
};

module.exports = async function finnhub(query) {
  const apiKey = process.env.FINNHUB_API_KEY;
  let url = '';
  let params = {};

  try {
    if (query.type === 'commodities') {
      const symbolMap = { GOLD: 'XAU', OIL: 'OIL', NATGAS: 'NATGAS' };
      const symbol = symbolMap[query.symbol.toUpperCase()] || query.symbol;
      url = `https://finnhub.io/api/v1/quote`;
      params = { symbol };
    } else if (query.type === 'forex') {
      url = `https://finnhub.io/api/v1/forex/rates`;
      params = { base: query.symbol.slice(0, 3) };
    } else if (query.type === 'stocks') {
      url = `https://finnhub.io/api/v1/quote`;
      params = { symbol: query.symbol };
    } else if (query.type === 'crypto') {
      url = `https://finnhub.io/api/v1/crypto/candle`;
      params = { symbol: query.symbol };
    } else {
      throw new Error('Unsupported type for Finnhub');
    }

    const response = await handleApiRequest(() => 
      axios.get(url, {
        params,
        timeout: 10000,
        headers: { 
          'X-Finnhub-Token': apiKey,
          'User-Agent': 'TradingSignalsApp/1.0'
        }
      })
    );

    return formatResponse(response.data, query);

  } catch (error) {
    console.error('Finnhub API error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    throw error;
  }
};

function formatResponse(data, query) {
  if (!data) {
    throw new Error('Empty response from Finnhub');
  }

  return {
    symbol: query.symbol,
    timeframe: query.timeframe,
    type: query.type,
    source: 'finnhub',
    timestamp: new Date().toISOString(),
    data: [{
      timestamp: new Date().toISOString(),
      close: data.c || data.rate || null,
      open: data.o || null,
      high: data.h || null, 
      low: data.l || null,
      volume: data.v || 0
    }]
  };
}