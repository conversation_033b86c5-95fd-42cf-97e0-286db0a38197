/**
 * Simple script to check if the main page of the app is accessible
 */

const http = require('http');

// Configuration
const PORT = process.env.PORT || 3000;
const HOST = 'localhost';

// Make a GET request to the root path
const options = {
  hostname: HOST,
  port: PORT,
  path: '/',
  method: 'GET'
};

console.log(`Testing main page at http://${HOST}:${PORT}/...`);

const req = http.request(options, (res) => {
  console.log(`STATUS: ${res.statusCode}`);
  console.log(`HEADERS: ${JSON.stringify(res.headers)}`);
  
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    if (res.statusCode === 200) {
      console.log('\n✅ Main page is accessible!');
      console.log(`Response length: ${data.length} bytes`);
      
      // Check if it contains expected HTML elements
      if (data.includes('<html') && data.includes('Trading Signals')) {
        console.log('✅ Page content appears to be correct (contains HTML and Trading Signals text)');
      } else {
        console.log('❌ Page content may not be correct (missing expected HTML elements)');
      }
    } else {
      console.log('\n❌ Failed to access main page!');
    }
  });
});

req.on('error', (e) => {
  console.error(`\n❌ Request error: ${e.message}`);
  console.log('Make sure the server is running by executing: node unified-server.js');
});

req.end(); 