/**
 * Signal Repository for MongoDB
 * 
 * This repository handles all trading signal-related database operations.
 */

const BaseRepository = require('./BaseRepository');
const { ObjectId } = require('mongodb');
const logger = require('../utils/logger');

class SignalRepository extends BaseRepository {
  /**
   * Create a new SignalRepository instance
   * @param {Object} db - MongoDB database instance
   */
  constructor(db) {
    super(db, 'tradingSignals');
    logger.debug('SignalRepository initialized');
  }

  /**
   * Find signals by user ID
   * @param {string} userId - User ID
   * @param {Object} options - Query options (sort, limit, skip)
   * @returns {Promise<Array>} Array of signals
   */
  async findByUserId(userId, options = {}) {
    try {
      const userObjectId = this._toObjectId(userId);
      return await this.find({ userId: userObjectId }, options);
    } catch (error) {
      logger.error('Error finding signals by user ID:', error);
      throw error;
    }
  }

  /**
   * Find signals by symbol
   * @param {string} symbol - Trading symbol (e.g., 'EURUSD')
   * @param {Object} options - Query options (sort, limit, skip)
   * @returns {Promise<Array>} Array of signals
   */
  async findBySymbol(symbol, options = {}) {
    try {
      return await this.find({ symbol }, options);
    } catch (error) {
      logger.error('Error finding signals by symbol:', error);
      throw error;
    }
  }

  /**
   * Find signals by type
   * @param {string} type - Signal type ('buy' or 'sell')
   * @param {Object} options - Query options (sort, limit, skip)
   * @returns {Promise<Array>} Array of signals
   */
  async findByType(type, options = {}) {
    try {
      return await this.find({ type }, options);
    } catch (error) {
      logger.error('Error finding signals by type:', error);
      throw error;
    }
  }

  /**
   * Find signals by status
   * @param {string} status - Signal status ('active', 'executed', 'expired')
   * @param {Object} options - Query options (sort, limit, skip)
   * @returns {Promise<Array>} Array of signals
   */
  async findByStatus(status, options = {}) {
    try {
      return await this.find({ status }, options);
    } catch (error) {
      logger.error('Error finding signals by status:', error);
      throw error;
    }
  }

  /**
   * Find signals by date range
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @param {Object} options - Query options (sort, limit, skip)
   * @returns {Promise<Array>} Array of signals
   */
  async findByDateRange(startDate, endDate, options = {}) {
    try {
      return await this.find({
        createdAt: {
          $gte: startDate,
          $lte: endDate
        }
      }, options);
    } catch (error) {
      logger.error('Error finding signals by date range:', error);
      throw error;
    }
  }

  /**
   * Create a new signal
   * @param {Object} signalData - Signal data
   * @returns {Promise<Object>} Created signal
   */
  async createSignal(signalData) {
    try {
      // Validate required fields
      if (!signalData.symbol || !signalData.type || !signalData.userId) {
        throw new Error('Symbol, type, and userId are required');
      }

      // Convert userId to ObjectId if it's a string
      if (typeof signalData.userId === 'string') {
        signalData.userId = this._toObjectId(signalData.userId);
      }

      // Set default values
      const signalToInsert = {
        ...signalData,
        status: signalData.status || 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Insert signal
      return await this.insertOne(signalToInsert);
    } catch (error) {
      logger.error('Error creating signal:', error);
      throw error;
    }
  }

  /**
   * Update signal status
   * @param {string} signalId - Signal ID
   * @param {string} status - New status ('active', 'executed', 'expired')
   * @param {Object} additionalData - Additional data to update
   * @returns {Promise<Object>} Update result
   */
  async updateStatus(signalId, status, additionalData = {}) {
    try {
      return await this.updateById(signalId, {
        status,
        ...additionalData
      });
    } catch (error) {
      logger.error('Error updating signal status:', error);
      throw error;
    }
  }

  /**
   * Get signal statistics for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Signal statistics
   */
  async getStatistics(userId) {
    try {
      const userObjectId = this._toObjectId(userId);
      
      const pipeline = [
        { $match: { userId: userObjectId } },
        { $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ];
      
      const results = await this.collection.aggregate(pipeline).toArray();
      
      // Format results
      const stats = {
        total: 0,
        active: 0,
        executed: 0,
        expired: 0
      };
      
      results.forEach(result => {
        stats[result._id] = result.count;
        stats.total += result.count;
      });
      
      return stats;
    } catch (error) {
      logger.error('Error getting signal statistics:', error);
      throw error;
    }
  }
}

module.exports = SignalRepository;
