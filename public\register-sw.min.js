/**
 * Service Worker Registration for Trading Signals App (Minified)
 */
function checkOnlineStatus(){navigator.onLine||console.log("Browser is offline, redirecting to offline page"),window.location.pathname.includes("/offline")||window.location.href="/offline"}function setDefaultLanguage(){localStorage.getItem("language_preference")||(localStorage.setItem("language_preference","en"),console.log("Default language set to English"))}window.addEventListener("load",()=>{checkOnlineStatus(),setDefaultLanguage()}),window.addEventListener("online",()=>{console.log("Browser is now online"),window.location.pathname.includes("/offline")&&(window.location.href="/")}),window.addEventListener("offline",()=>{console.log("Browser is now offline"),checkOnlineStatus()}),"serviceWorker"in navigator?(window.addEventListener("load",()=>{navigator.serviceWorker.register("/service-worker.js").then(e=>{console.log("Service Worker registered with scope:",e.scope),e.update(),setInterval(()=>{e.update(),console.log("Service Worker update check")},36e5)}).catch(e=>{console.error("Service Worker registration failed:",e)})}),navigator.serviceWorker.addEventListener("controllerchange",()=>{console.log("Service Worker updated, reloading page for new version"),window.location.reload()}),navigator.serviceWorker.addEventListener("message",e=>{if(console.log("Message from Service Worker:",e.data),"economic-calendar-updated"===e.data.type)window.economicCalendar&&"function"==typeof window.economicCalendar.refreshEconomicCalendar&&window.economicCalendar.refreshEconomicCalendar();else if("offline"===e.data.type)checkOnlineStatus()}),window.serviceWorkerUtils={requestEconomicCalendarSync:function(){navigator.serviceWorker.ready.then(e=>e.sync.register("sync-economic-calendar")).then(()=>{console.log("Economic calendar background sync registered")}).catch(e=>{console.error("Background sync registration failed:",e)})},requestTradingSignalsSync:function(){navigator.serviceWorker.ready.then(e=>e.sync.register("sync-trading-signals")).then(()=>{console.log("Trading signals background sync registered")}).catch(e=>{console.error("Background sync registration failed:",e)})},checkOnlineStatus:checkOnlineStatus,setDefaultLanguage:setDefaultLanguage}):(console.warn("Service Workers are not supported in this browser"),checkOnlineStatus(),setDefaultLanguage());
