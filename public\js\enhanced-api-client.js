/**
 * Enhanced API Client for Trading Signals App
 * Provides unified interface for all API communications with error handling and retry logic
 */

class EnhancedAPIClient {
  constructor() {
    this.baseURL = '/api';
    this.timeout = 30000;
    this.retryAttempts = 3;
    this.retryDelay = 1000;
    
    this.requestInterceptors = [];
    this.responseInterceptors = [];
    this.errorInterceptors = [];
    
    this.cache = new Map();
    this.cacheTTL = 5 * 60 * 1000; // 5 minutes
    
    this.requestQueue = [];
    this.isOnline = navigator.onLine;
    this.offlineQueue = [];
    
    this.setupEventListeners();
    this.setupDefaultInterceptors();
  }

  /**
   * Setup event listeners for network status
   */
  setupEventListeners() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processOfflineQueue();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  /**
   * Setup default interceptors
   */
  setupDefaultInterceptors() {
    // Request interceptor for auth token
    this.addRequestInterceptor((config) => {
      const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
      if (token) {
        config.headers = config.headers || {};
        config.headers.Authorization = `Bearer ${token}`;
      }
      
      // Add request ID for tracking
      config.requestId = this.generateRequestId();
      config.timestamp = Date.now();
      
      return config;
    });

    // Response interceptor for token refresh
    this.addResponseInterceptor(
      (response) => response,
      async (error) => {
        if (error.status === 401 && !error.config._retry) {
          error.config._retry = true;
          
          try {
            await this.refreshToken();
            return this.request(error.config);
          } catch (refreshError) {
            this.handleAuthFailure();
            throw refreshError;
          }
        }
        
        return Promise.reject(error);
      }
    );

    // Error interceptor for notifications
    this.addErrorInterceptor((error) => {
      if (error.category === 'network' && !this.isOnline) {
        this.showNotification('You are offline. Request will be processed when connection is restored.', 'warning');
      } else if (error.category === 'rate_limit') {
        this.showNotification(`Rate limit exceeded. Please wait ${error.retryAfter || 60} seconds.`, 'warning');
      } else if (error.category === 'server_error') {
        this.showNotification('Server error occurred. Please try again later.', 'error');
      }
    });
  }

  /**
   * Add request interceptor
   */
  addRequestInterceptor(onFulfilled, onRejected) {
    this.requestInterceptors.push({ onFulfilled, onRejected });
  }

  /**
   * Add response interceptor
   */
  addResponseInterceptor(onFulfilled, onRejected) {
    this.responseInterceptors.push({ onFulfilled, onRejected });
  }

  /**
   * Add error interceptor
   */
  addErrorInterceptor(onError) {
    this.errorInterceptors.push(onError);
  }

  /**
   * Main request method
   */
  async request(config) {
    // Check if offline and queue request
    if (!this.isOnline && config.method !== 'GET') {
      return this.queueOfflineRequest(config);
    }

    // Apply request interceptors
    let processedConfig = { ...config };
    for (const interceptor of this.requestInterceptors) {
      if (interceptor.onFulfilled) {
        try {
          processedConfig = await interceptor.onFulfilled(processedConfig);
        } catch (error) {
          if (interceptor.onRejected) {
            processedConfig = await interceptor.onRejected(error);
          } else {
            throw error;
          }
        }
      }
    }

    // Check cache for GET requests
    if (processedConfig.method === 'GET' && processedConfig.useCache !== false) {
      const cachedResponse = this.getFromCache(processedConfig);
      if (cachedResponse) {
        return cachedResponse;
      }
    }

    // Execute request with retry logic
    return this.executeWithRetry(processedConfig);
  }

  /**
   * Execute request with retry logic
   */
  async executeWithRetry(config, attempt = 1) {
    try {
      const response = await this.executeRequest(config);
      
      // Apply response interceptors
      let processedResponse = response;
      for (const interceptor of this.responseInterceptors) {
        if (interceptor.onFulfilled) {
          processedResponse = await interceptor.onFulfilled(processedResponse);
        }
      }

      // Cache successful GET responses
      if (config.method === 'GET' && config.useCache !== false) {
        this.setCache(config, processedResponse);
      }

      return processedResponse;
      
    } catch (error) {
      // Standardize error
      const standardError = this.standardizeError(error, config, attempt);

      // Apply error interceptors
      for (const interceptor of this.errorInterceptors) {
        try {
          await interceptor(standardError);
        } catch (interceptorError) {
          console.warn('Error interceptor failed:', interceptorError);
        }
      }

      // Apply response error interceptors
      for (const interceptor of this.responseInterceptors) {
        if (interceptor.onRejected) {
          try {
            const result = await interceptor.onRejected(standardError);
            if (result) return result;
          } catch (interceptorError) {
            // Continue with original error
          }
        }
      }

      // Retry logic
      if (attempt < this.retryAttempts && this.isRetryableError(standardError)) {
        const delay = this.calculateRetryDelay(attempt);
        await this.sleep(delay);
        return this.executeWithRetry(config, attempt + 1);
      }

      throw standardError;
    }
  }

  /**
   * Execute the actual HTTP request
   */
  async executeRequest(config) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const url = this.buildURL(config.url, config.params);
      const options = {
        method: config.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...config.headers
        },
        signal: controller.signal
      };

      if (config.data && config.method !== 'GET') {
        options.body = JSON.stringify(config.data);
      }

      const response = await fetch(url, options);
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        config,
        request: { responseURL: response.url }
      };
      
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      
      throw error;
    }
  }

  /**
   * Build URL with query parameters
   */
  buildURL(url, params) {
    const fullURL = url.startsWith('http') ? url : `${this.baseURL}${url}`;
    
    if (!params) return fullURL;
    
    const urlObj = new URL(fullURL, window.location.origin);
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        urlObj.searchParams.append(key, value);
      }
    });
    
    return urlObj.toString();
  }

  /**
   * Standardize error format
   */
  standardizeError(error, config, attempt) {
    return {
      message: error.message || 'An unknown error occurred',
      category: this.categorizeError(error),
      status: error.status || 0,
      statusText: error.statusText || 'Unknown',
      requestId: config.requestId || 'unknown',
      timestamp: new Date().toISOString(),
      endpoint: config.url || 'unknown',
      method: config.method?.toUpperCase() || 'unknown',
      attempt,
      retryable: this.isRetryableError(error),
      config,
      originalError: error
    };
  }

  /**
   * Categorize error based on type and status
   */
  categorizeError(error) {
    if (!error.status) {
      if (error.message?.includes('timeout')) return 'timeout';
      if (error.message?.includes('network') || error.message?.includes('fetch')) return 'network';
      return 'network';
    }

    const status = error.status;
    
    if (status === 401) return 'authentication';
    if (status === 403) return 'authorization';
    if (status === 404) return 'not_found';
    if (status === 422 || status === 400) return 'validation';
    if (status === 429) return 'rate_limit';
    if (status >= 500) return 'server_error';
    
    return 'unknown';
  }

  /**
   * Check if error is retryable
   */
  isRetryableError(error) {
    const retryableCategories = ['network', 'timeout', 'server_error'];
    return retryableCategories.includes(error.category);
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  calculateRetryDelay(attempt) {
    const baseDelay = this.retryDelay;
    const exponentialDelay = Math.pow(2, attempt - 1) * baseDelay;
    const jitter = Math.random() * 1000;
    return Math.min(exponentialDelay + jitter, 30000);
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generate unique request ID
   */
  generateRequestId() {
    return `req-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
  }

  /**
   * Cache management
   */
  getFromCache(config) {
    const key = this.getCacheKey(config);
    const cached = this.cache.get(key);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
      return cached.data;
    }
    
    if (cached) {
      this.cache.delete(key);
    }
    
    return null;
  }

  setCache(config, response) {
    const key = this.getCacheKey(config);
    this.cache.set(key, {
      data: response,
      timestamp: Date.now()
    });
  }

  getCacheKey(config) {
    return `${config.method}:${config.url}:${JSON.stringify(config.params || {})}`;
  }

  clearCache() {
    this.cache.clear();
  }

  /**
   * Offline queue management
   */
  queueOfflineRequest(config) {
    return new Promise((resolve, reject) => {
      this.offlineQueue.push({ config, resolve, reject });
    });
  }

  async processOfflineQueue() {
    const queue = [...this.offlineQueue];
    this.offlineQueue = [];
    
    for (const { config, resolve, reject } of queue) {
      try {
        const response = await this.request(config);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    }
  }

  /**
   * Token management
   */
  async refreshToken() {
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await this.executeRequest({
      method: 'POST',
      url: '/auth/refresh',
      data: { refreshToken }
    });

    const { token, refreshToken: newRefreshToken } = response.data;
    localStorage.setItem('authToken', token);
    if (newRefreshToken) {
      localStorage.setItem('refreshToken', newRefreshToken);
    }

    return token;
  }

  handleAuthFailure() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
    sessionStorage.removeItem('authToken');
    
    // Redirect to login or emit event
    window.dispatchEvent(new CustomEvent('auth:logout'));
  }

  /**
   * Show notification helper
   */
  showNotification(message, type = 'info') {
    if (window.showNotification) {
      window.showNotification(message, type);
    } else {
      console.log(`[${type.toUpperCase()}] ${message}`);
    }
  }

  /**
   * Convenience methods
   */
  get(url, params, config = {}) {
    return this.request({ method: 'GET', url, params, ...config });
  }

  post(url, data, config = {}) {
    return this.request({ method: 'POST', url, data, ...config });
  }

  put(url, data, config = {}) {
    return this.request({ method: 'PUT', url, data, ...config });
  }

  patch(url, data, config = {}) {
    return this.request({ method: 'PATCH', url, data, ...config });
  }

  delete(url, config = {}) {
    return this.request({ method: 'DELETE', url, ...config });
  }
}

// Create global instance
window.enhancedAPIClient = new EnhancedAPIClient();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedAPIClient;
}
