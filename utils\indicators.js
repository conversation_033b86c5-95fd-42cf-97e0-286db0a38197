/**
 * Technical Indicators Calculation Module
 * Provides functions for calculating various technical analysis indicators
 */

const logger = require('../logging');

/**
 * Calculate Simple Moving Average (SMA)
 * @param {Array} data - Price data array
 * @param {number} period - Period for calculation
 * @param {string} field - Which field to use (close, open, high, low)
 * @returns {Array} - Array of SMA values
 */
function calculateSMA(data, period = 14, field = 'close') {
    const result = [];
    
    if (!data || data.length === 0) {
        return result;
    }
    
    // Ensure we have price data
    if (!data[0][field]) {
        logger.warn(`Field '${field}' not found in price data`);
        return result;
    }

    for (let i = 0; i < data.length; i++) {
        if (i < period - 1) {
            // Not enough data yet
            result.push(null);
            continue;
        }
        
        let sum = 0;
        for (let j = 0; j < period; j++) {
            sum += parseFloat(data[i - j][field]);
        }
        
        result.push(sum / period);
    }
    
    return result;
}

/**
 * Calculate Exponential Moving Average (EMA)
 * @param {Array} data - Price data
 * @param {number} period - Period for calculation
 * @param {string} field - Which field to use (close, open, high, low)
 * @returns {Array} - Array of EMA values
 */
function calculateEMA(data, period = 14, field = 'close') {
    const result = [];
    
    if (!data || data.length === 0) {
        return result;
    }
    
    // Ensure we have price data
    if (!data[0][field]) {
        logger.warn(`Field '${field}' not found in price data`);
        return result;
    }
    
    // Multiplier: (2 / (period + 1))
    const multiplier = 2 / (period + 1);
    
    // First EMA is calculated as SMA
    let ema = calculateSMA(data.slice(0, period), period, field)[period - 1];
    
    for (let i = 0; i < data.length; i++) {
        if (i < period - 1) {
            // Not enough data yet
            result.push(null);
            continue;
        } else if (i === period - 1) {
            // First EMA value
            result.push(ema);
            continue;
        }
        
        // EMA = Price(t) * multiplier + EMA(y) * (1 - multiplier)
        ema = (parseFloat(data[i][field]) * multiplier) + (ema * (1 - multiplier));
        result.push(ema);
    }
    
    return result;
}

/**
 * Calculate Relative Strength Index (RSI)
 * @param {Array} data - Price data
 * @param {number} period - Period for calculation
 * @param {string} field - Which field to use (close, open, high, low)
 * @returns {Array} - Array of RSI values
 */
function calculateRSI(data, period = 14, field = 'close') {
    const result = [];
    
    if (!data || data.length < period + 1) {
        return result;
    }
    
    // Ensure we have price data
    if (!data[0][field]) {
        logger.warn(`Field '${field}' not found in price data`);
        return result;
    }
    
    for (let i = 0; i < data.length; i++) {
        if (i < period) {
            // Not enough data yet
            result.push(null);
            continue;
        }
        
        let gains = 0;
        let losses = 0;
        
        // Calculate average gains and losses over the period
        for (let j = i - period + 1; j <= i; j++) {
            const change = parseFloat(data[j][field]) - parseFloat(data[j - 1][field]);
            
            if (change >= 0) {
                gains += change;
            } else {
                losses += Math.abs(change);
            }
        }
        
        const avgGain = gains / period;
        const avgLoss = losses / period;
        
        if (avgLoss === 0) {
            // No losses, RSI is 100
            result.push(100);
        } else {
            const rs = avgGain / avgLoss;
            const rsi = 100 - (100 / (1 + rs));
            result.push(rsi);
        }
    }
    
    return result;
}

/**
 * Calculate Moving Average Convergence Divergence (MACD)
 * @param {Array} data - Price data
 * @param {number} fastPeriod - Fast EMA period
 * @param {number} slowPeriod - Slow EMA period
 * @param {number} signalPeriod - Signal EMA period
 * @param {string} field - Which field to use (close, open, high, low)
 * @returns {Object} - Object containing MACD line, signal line, and histogram
 */
function calculateMACD(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9, field = 'close') {
    if (!data || data.length === 0) {
        return { macd: [], signal: [], histogram: [] };
    }
    
    // Calculate fast and slow EMAs
    const fastEMA = calculateEMA(data, fastPeriod, field);
    const slowEMA = calculateEMA(data, slowPeriod, field);
    
    // Calculate MACD line
    const macdLine = [];
    for (let i = 0; i < data.length; i++) {
        if (i < slowPeriod - 1) {
            macdLine.push(null);
        } else {
            macdLine.push(fastEMA[i] - slowEMA[i]);
        }
    }
    
    // Calculate signal line (EMA of MACD line)
    const signalLine = [];
    let ema = 0;
    const multiplier = 2 / (signalPeriod + 1);
    
    for (let i = 0; i < macdLine.length; i++) {
        if (i < slowPeriod + signalPeriod - 2) {
            signalLine.push(null);
        } else if (i === slowPeriod + signalPeriod - 2) {
            // First signal value is SMA of MACD
            let sum = 0;
            for (let j = i - signalPeriod + 1; j <= i; j++) {
                sum += macdLine[j];
            }
            ema = sum / signalPeriod;
            signalLine.push(ema);
        } else {
            // EMA formula
            ema = (macdLine[i] * multiplier) + (ema * (1 - multiplier));
            signalLine.push(ema);
        }
    }
    
    // Calculate histogram (MACD line - signal line)
    const histogram = [];
    for (let i = 0; i < data.length; i++) {
        if (i < slowPeriod + signalPeriod - 2) {
            histogram.push(null);
        } else {
            histogram.push(macdLine[i] - signalLine[i]);
        }
    }
    
    return { macd: macdLine, signal: signalLine, histogram };
}

/**
 * Calculate Bollinger Bands
 * @param {Array} data - Price data
 * @param {number} period - Period for calculation
 * @param {number} stdDev - Standard deviation multiplier
 * @param {string} field - Which field to use (close, open, high, low)
 * @returns {Object} - Object containing upper band, middle band, and lower band
 */
function calculateBollingerBands(data, period = 20, stdDev = 2, field = 'close') {
    const result = {
        upper: [],
        middle: [],
        lower: []
    };
    
    if (!data || data.length === 0) {
        return result;
    }
    
    // Middle band is SMA
    const middleBand = calculateSMA(data, period, field);
    
    for (let i = 0; i < data.length; i++) {
        if (i < period - 1) {
            result.upper.push(null);
            result.middle.push(null);
            result.lower.push(null);
            continue;
        }
        
        // Calculate standard deviation
        let sum = 0;
        for (let j = i - period + 1; j <= i; j++) {
            sum += Math.pow(parseFloat(data[j][field]) - middleBand[i], 2);
        }
        
        const standardDeviation = Math.sqrt(sum / period);
        
        result.middle.push(middleBand[i]);
        result.upper.push(middleBand[i] + (standardDeviation * stdDev));
        result.lower.push(middleBand[i] - (standardDeviation * stdDev));
    }
    
    return result;
}

/**
 * Calculate multiple indicators for price data
 * @param {Array} data - Price data
 * @param {string} indicators - Comma-separated list of indicators to calculate
 * @returns {Object} - Object containing calculated indicators
 */
function calculateIndicators(data, indicators = 'sma,ema,rsi,macd,bollinger') {
    if (!data || data.length === 0) {
        logger.warn('No data provided for indicator calculation');
        return {};
    }
    
    // Parse indicator list
    const indicatorList = indicators ? indicators.split(',').map(i => i.trim().toLowerCase()) : [];
    
    const result = {};
    
    try {
        // Calculate each requested indicator
        if (indicatorList.includes('sma') || indicatorList.length === 0) {
            result.sma = {
                sma14: calculateSMA(data, 14, 'close')
            };
        }
        
        if (indicatorList.includes('ema') || indicatorList.length === 0) {
            result.ema = {
                ema12: calculateEMA(data, 12, 'close'),
                ema26: calculateEMA(data, 26, 'close')
            };
        }
        
        if (indicatorList.includes('rsi') || indicatorList.length === 0) {
            result.rsi = {
                rsi14: calculateRSI(data, 14, 'close')
            };
        }
        
        if (indicatorList.includes('macd') || indicatorList.length === 0) {
            result.macd = calculateMACD(data, 12, 26, 9, 'close');
        }
        
        if (indicatorList.includes('bollinger') || indicatorList.length === 0) {
            result.bollinger = calculateBollingerBands(data, 20, 2, 'close');
        }
    } catch (error) {
        logger.error('Error calculating indicators:', error);
    }
    
    return result;
}

module.exports = {
    calculateIndicators,
    calculateSMA,
    calculateEMA,
    calculateRSI,
    calculateMACD,
    calculateBollingerBands
};
