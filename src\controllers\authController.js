import { z } from 'zod';
import authService from '../services/authService.js';
import { APIError } from '../middleware/errorHandler.js';
import logger from '../utils/logger.js';
import speakeasy from 'speakeasy';
import qrcode from 'qrcode';
import User from '../models/User.js';

// Validation schemas
const registerSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  name: z.string().min(2),
});

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string(),
});

const updateSchema = z.object({
  name: z.string().min(2).optional(),
  password: z.string().min(8).optional(),
  avatar: z.string().url().or(z.string().min(10)).optional(),
  preferences: z.record(z.any()).optional(),
}).refine(data => Object.keys(data).length > 0, {
  message: "At least one field must be provided for update"
});

// Register new user
export const register = async (req, res, next) => {
  try {
    // Validate request body
    const validatedData = registerSchema.parse(req.body);

    // Register user
    const result = await authService.register(validatedData);

    // Set JWT as cookie
    res.cookie('token', result.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    });

    res.status(201).json({
      status: 'success',
      data: result,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      next(new APIError(400, 'Invalid input data'));
    } else {
      next(error);
    }
  }
};

// Login user
export const login = async (req, res, next) => {
  try {
    // Validate request body
    const validatedData = loginSchema.parse(req.body);

    // Login user
    const result = await authService.login(validatedData.email, validatedData.password);

    // Set JWT as cookie
    res.cookie('token', result.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    });

    res.json({
      status: 'success',
      data: result,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      next(new APIError(400, 'Invalid input data'));
    } else {
      next(error);
    }
  }
};

// Get current user
export const getCurrentUser = async (req, res, next) => {
  try {
    const user = await authService.getUserById(req.user.id);
    res.json({
      status: 'success',
      data: { user },
    });
  } catch (error) {
    next(error);
  }
};

// Update user
export const updateUser = async (req, res, next) => {
  try {
    // Validate request body
    const validatedData = updateSchema.parse(req.body);

    // Update user
    const user = await authService.updateUser(req.user.id, validatedData);

    res.json({
      status: 'success',
      data: { user },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      next(new APIError(400, 'Invalid input data'));
    } else {
      next(error);
    }
  }
};

// Delete user
export const deleteUser = async (req, res, next) => {
  try {
    await authService.deleteUser(req.user.id);
    
    // Clear JWT cookie
    res.clearCookie('token');

    res.json({
      status: 'success',
      data: null,
    });
  } catch (error) {
    next(error);
  }
};

// Logout user
export const logout = (req, res) => {
  res.clearCookie('token');
  res.json({
    status: 'success',
    data: null,
  });
};

// 2FA: Setup (generate secret and QR code)
export const setup2FA = async (req, res, next) => {
  try {
    const secret = speakeasy.generateSecret({ name: 'Trading Signals App' });
    await User.findByIdAndUpdate(req.user.id, { twoFactorSecret: secret.base32 });
    const otpauthUrl = secret.otpauth_url;
    const qr = await qrcode.toDataURL(otpauthUrl);
    res.json({ status: 'success', data: { otpauthUrl, qr } });
  } catch (error) {
    next(error);
  }
};

// 2FA: Verify (enable 2FA)
export const verify2FA = async (req, res, next) => {
  try {
    const { token } = req.body;
    const user = await User.findById(req.user.id);
    if (!user || !user.twoFactorSecret) return next(new APIError(400, '2FA not set up'));
    const verified = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: 'base32',
      token,
      window: 1,
    });
    if (!verified) return next(new APIError(400, 'Invalid 2FA code'));
    user.twoFactorEnabled = true;
    await user.save();
    res.json({ status: 'success', message: '2FA enabled' });
  } catch (error) {
    next(error);
  }
};

// 2FA: Disable
export const disable2FA = async (req, res, next) => {
  try {
    await User.findByIdAndUpdate(req.user.id, { twoFactorEnabled: false, twoFactorSecret: null });
    res.json({ status: 'success', message: '2FA disabled' });
  } catch (error) {
    next(error);
  }
};

// Export user data as JSON
export const exportUserData = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id).lean();
    if (!user) return next(new APIError(404, 'User not found'));
    // Remove sensitive fields
    delete user.password;
    delete user.twoFactorSecret;
    res.setHeader('Content-Disposition', 'attachment; filename="user-data.json"');
    res.setHeader('Content-Type', 'application/json');
    res.send(JSON.stringify(user, null, 2));
  } catch (error) {
    next(error);
  }
};

export default {
  register,
  login,
  getCurrentUser,
  updateUser,
  deleteUser,
  logout,
  setup2FA,
  verify2FA,
  disable2FA,
  exportUserData,
}; 