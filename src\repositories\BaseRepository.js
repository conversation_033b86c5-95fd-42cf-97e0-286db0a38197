/**
 * Base Repository for MongoDB Collections
 *
 * This class provides a foundation for all repositories with common CRUD operations.
 */

const { ObjectId } = require('mongodb');
const logger = require('../utils/logger');
const cacheService = require('../services/cacheService');
const paginationUtils = require('../utils/paginationUtils');

class BaseRepository {
  /**
   * Create a new repository instance
   * @param {Object} db - MongoDB database instance
   * @param {string} collectionName - Name of the collection
   */
  constructor(db, collectionName) {
    this.db = db;
    this.collectionName = collectionName;
    this.collection = db.collection(collectionName);
    this.cacheEnabled = true;
    this.cacheTTL = cacheService.DEFAULT_TTL.MEDIUM;
    this.cacheKeyPrefix = `repo_${collectionName}`;
    logger.debug(`BaseRepository initialized for collection: ${collectionName}`);
  }

  /**
   * Enable or disable caching
   * @param {boolean} enabled - Whether caching is enabled
   */
  setCacheEnabled(enabled) {
    this.cacheEnabled = enabled;
  }

  /**
   * Set cache TTL
   * @param {number} ttl - Cache TTL in seconds
   */
  setCacheTTL(ttl) {
    this.cacheTTL = ttl;
  }

  /**
   * Find a document by ID
   * @param {string} id - Document ID
   * @returns {Promise<Object|null>} Document or null if not found
   */
  async findById(id) {
    try {
      const objectId = this._toObjectId(id);
      const cacheKey = `${this.cacheKeyPrefix}_id_${id}`;

      // Try to get from cache if enabled
      if (this.cacheEnabled) {
        const cachedData = cacheService.get(cacheKey);
        if (cachedData) {
          return cachedData;
        }
      }

      // Get from database
      const document = await this.collection.findOne({ _id: objectId });

      // Cache the result if enabled
      if (this.cacheEnabled && document) {
        cacheService.set(cacheKey, document, this.cacheTTL);
      }

      return document;
    } catch (error) {
      logger.error(`Error finding document by ID in ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Find documents by query
   * @param {Object} query - MongoDB query
   * @param {Object} options - Query options (sort, limit, skip, projection)
   * @returns {Promise<Array>} Array of documents
   */
  async find(query = {}, options = {}) {
    try {
      const { sort, limit, skip, projection } = options;
      let cursor = this.collection.find(query);

      if (projection) cursor = cursor.project(projection);
      if (sort) cursor = cursor.sort(sort);
      if (skip) cursor = cursor.skip(skip);
      if (limit) cursor = cursor.limit(limit);

      return await cursor.toArray();
    } catch (error) {
      logger.error(`Error finding documents in ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Find documents with pagination
   * @param {Object} query - MongoDB query
   * @param {Object} paginationOptions - Pagination options
   * @param {Object} queryOptions - Query options (sort, projection)
   * @returns {Promise<Object>} Paginated result
   */
  async findWithPagination(query = {}, paginationOptions = {}, queryOptions = {}) {
    try {
      const { page, limit, skip } = paginationUtils.parsePaginationParams(paginationOptions);
      const { sort, projection } = queryOptions;

      // Generate cache key if caching is enabled
      let cacheKey = null;
      if (this.cacheEnabled) {
        cacheKey = cacheService.generateKey(
          `${this.cacheKeyPrefix}_paginated`,
          { query, page, limit, sort }
        );

        // Try to get from cache
        const cachedData = cacheService.get(cacheKey);
        if (cachedData) {
          return cachedData;
        }
      }

      // Count total documents
      const total = await this.collection.countDocuments(query);

      // Get documents for current page
      let cursor = this.collection.find(query);
      if (projection) cursor = cursor.project(projection);
      if (sort) cursor = cursor.sort(sort);

      const data = await cursor.skip(skip).limit(limit).toArray();

      // Create pagination result
      const result = paginationUtils.createPaginationResult(data, total, { page, limit });

      // Cache the result if enabled
      if (this.cacheEnabled && cacheKey) {
        cacheService.set(cacheKey, result, this.cacheTTL);
      }

      return result;
    } catch (error) {
      logger.error(`Error finding documents with pagination in ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Find documents with cursor-based pagination
   * @param {Object} query - MongoDB query
   * @param {Object} cursorOptions - Cursor options
   * @param {Object} queryOptions - Query options
   * @returns {Promise<Object>} Cursor-based paginated result
   */
  async findWithCursor(query = {}, cursorOptions = {}, queryOptions = {}) {
    try {
      const { cursor, limit } = paginationUtils.parseCursorPaginationParams(cursorOptions);
      const {
        cursorField = '_id',
        sortField = cursorField,
        sortAscending = false,
        projection
      } = queryOptions;

      // Generate cache key if caching is enabled
      let cacheKey = null;
      if (this.cacheEnabled) {
        cacheKey = cacheService.generateKey(
          `${this.cacheKeyPrefix}_cursor`,
          { query, cursor, limit, sortField, sortAscending }
        );

        // Try to get from cache
        const cachedData = cacheService.get(cacheKey);
        if (cachedData) {
          return cachedData;
        }
      }

      // Build query with cursor condition
      const cursorQuery = paginationUtils.buildCursorQuery(
        query,
        cursor,
        cursorField,
        sortAscending
      );

      // Determine sort direction
      const sortDirection = sortAscending ? 1 : -1;
      const sortOptions = { [sortField]: sortDirection };

      // Get documents
      let dbCursor = this.collection.find(cursorQuery);
      if (projection) dbCursor = dbCursor.project(projection);

      const data = await dbCursor
        .sort(sortOptions)
        .limit(limit + 1)  // Get one extra to determine if there are more
        .toArray();

      // Create cursor-based pagination result
      const result = paginationUtils.createCursorPaginationResult(data, limit, cursorField);

      // Cache the result if enabled
      if (this.cacheEnabled && cacheKey) {
        cacheService.set(cacheKey, result, this.cacheTTL);
      }

      return result;
    } catch (error) {
      logger.error(`Error finding documents with cursor in ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Find one document by query
   * @param {Object} query - MongoDB query
   * @param {Object} options - Query options (projection)
   * @returns {Promise<Object|null>} Document or null if not found
   */
  async findOne(query = {}, options = {}) {
    try {
      const { projection } = options;
      return await this.collection.findOne(query, { projection });
    } catch (error) {
      logger.error(`Error finding one document in ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Insert a document
   * @param {Object} data - Document data
   * @param {Object} validationSchema - Joi validation schema
   * @returns {Promise<Object>} Inserted document with _id
   */
  async insertOne(data, validationSchema = null) {
    try {
      // Validate data if schema is provided
      if (validationSchema) {
        const validationService = require('../services/validationService');
        const validationResult = validationService.validate(data, validationSchema);

        if (!validationResult.valid) {
          const error = new Error('Validation failed');
          error.validationErrors = validationResult.errors;
          throw error;
        }

        // Use validated data
        data = validationResult.value;
      }

      const now = new Date();
      const dataWithTimestamps = {
        ...data,
        createdAt: data.createdAt || now,
        updatedAt: now
      };

      const result = await this.collection.insertOne(dataWithTimestamps);
      const insertedDocument = { ...dataWithTimestamps, _id: result.insertedId };

      // Invalidate collection cache
      if (this.cacheEnabled) {
        cacheService.invalidateByPattern(this.cacheKeyPrefix);
      }

      return insertedDocument;
    } catch (error) {
      logger.error(`Error inserting document into ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Insert multiple documents
   * @param {Array} documents - Array of documents
   * @returns {Promise<Object>} InsertMany result
   */
  async insertMany(documents) {
    try {
      const now = new Date();
      const docsWithTimestamps = documents.map(doc => ({
        ...doc,
        createdAt: doc.createdAt || now,
        updatedAt: now
      }));

      return await this.collection.insertMany(docsWithTimestamps);
    } catch (error) {
      logger.error(`Error inserting multiple documents into ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Update a document by ID
   * @param {string} id - Document ID
   * @param {Object} data - Update data
   * @param {Object} options - Update options
   * @param {Object} validationSchema - Joi validation schema
   * @returns {Promise<Object>} UpdateOne result and updated document
   */
  async updateById(id, data, options = {}, validationSchema = null) {
    try {
      const objectId = this._toObjectId(id);

      // Validate data if schema is provided
      if (validationSchema) {
        const validationService = require('../services/validationService');
        const validationResult = validationService.validate(data, validationSchema);

        if (!validationResult.valid) {
          const error = new Error('Validation failed');
          error.validationErrors = validationResult.errors;
          throw error;
        }

        // Use validated data
        data = validationResult.value;
      }

      const updateData = { ...data, updatedAt: new Date() };

      // Don't allow changing _id
      delete updateData._id;

      const result = await this.collection.updateOne(
        { _id: objectId },
        { $set: updateData },
        options
      );

      // Invalidate specific cache entries
      if (this.cacheEnabled) {
        cacheService.del(`${this.cacheKeyPrefix}_id_${id}`);
        cacheService.invalidateByPattern(`${this.cacheKeyPrefix}_paginated`);
        cacheService.invalidateByPattern(`${this.cacheKeyPrefix}_cursor`);
      }

      // Get updated document if needed
      let updatedDocument = null;
      if (result.modifiedCount > 0 || result.upsertedCount > 0) {
        updatedDocument = await this.findById(id);
      }

      return {
        result,
        document: updatedDocument
      };
    } catch (error) {
      logger.error(`Error updating document by ID in ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Update documents by query
   * @param {Object} query - MongoDB query
   * @param {Object} data - Update data
   * @param {Object} options - Update options
   * @returns {Promise<Object>} UpdateMany result
   */
  async updateMany(query, data, options = {}) {
    try {
      const updateData = { ...data, updatedAt: new Date() };

      // Don't allow changing _id
      delete updateData._id;

      return await this.collection.updateMany(
        query,
        { $set: updateData },
        options
      );
    } catch (error) {
      logger.error(`Error updating documents in ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Delete a document by ID
   * @param {string} id - Document ID
   * @returns {Promise<Object>} DeleteOne result
   */
  async deleteById(id) {
    try {
      // Get the document before deleting (for potential cache invalidation)
      const document = this.cacheEnabled ? await this.findById(id) : null;

      const objectId = this._toObjectId(id);
      const result = await this.collection.deleteOne({ _id: objectId });

      // Invalidate cache if document was found and deleted
      if (this.cacheEnabled && document && result.deletedCount > 0) {
        cacheService.del(`${this.cacheKeyPrefix}_id_${id}`);
        cacheService.invalidateByPattern(`${this.cacheKeyPrefix}_paginated`);
        cacheService.invalidateByPattern(`${this.cacheKeyPrefix}_cursor`);
      }

      return {
        result,
        deletedDocument: document
      };
    } catch (error) {
      logger.error(`Error deleting document by ID in ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Delete documents by query
   * @param {Object} query - MongoDB query
   * @returns {Promise<Object>} DeleteMany result
   */
  async deleteMany(query) {
    try {
      return await this.collection.deleteMany(query);
    } catch (error) {
      logger.error(`Error deleting documents in ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Count documents by query
   * @param {Object} query - MongoDB query
   * @returns {Promise<number>} Count of documents
   */
  async count(query = {}) {
    try {
      return await this.collection.countDocuments(query);
    } catch (error) {
      logger.error(`Error counting documents in ${this.collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Convert string ID to ObjectId
   * @param {string} id - String ID
   * @returns {ObjectId} MongoDB ObjectId
   * @private
   */
  _toObjectId(id) {
    try {
      return new ObjectId(id);
    } catch (error) {
      throw new Error(`Invalid ID format: ${id}`);
    }
  }
}

module.exports = BaseRepository;
