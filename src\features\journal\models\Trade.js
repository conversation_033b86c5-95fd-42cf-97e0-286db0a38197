import mongoose from 'mongoose';

const tradeSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  symbol: { type: String, required: true },
  direction: { type: String, enum: ['buy', 'sell'], required: true },
  entry: { type: Number, required: true },
  exit: { type: Number, required: true },
  stopLoss: { type: Number },
  takeProfit: { type: Number },
  size: { type: Number }, // lot size or units
  result: { type: Number }, // profit/loss in pips or $
  openedAt: { type: Date, required: true },
  closedAt: { type: Date, required: true },
  notes: { type: String },
  screenshot: { type: String }, // URL or base64
  createdAt: { type: Date, default: Date.now }
});

const Trade = mongoose.model('Trade', tradeSchema);
export default Trade; 