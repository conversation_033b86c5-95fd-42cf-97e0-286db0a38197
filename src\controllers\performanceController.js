import logger from '../utils/logger.js';
import { databaseOptimizationService } from '../services/databaseOptimizationService.js';
import { redisCacheService } from '../services/redisCacheService.js';
import { cacheIntegratedDataService } from '../services/cacheIntegratedDataService.js';
import { consolidatedOpenAIService } from '../services/consolidatedOpenAIService.js';

/**
 * Performance Monitoring Controller
 * 
 * Provides endpoints for monitoring database performance, cache metrics,
 * AI service performance, and overall system health.
 * 
 * @version 1.0.0
 */

/**
 * Get comprehensive performance dashboard data
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const getPerformanceDashboard = async (req, res, next) => {
  try {
    const dashboard = await databaseOptimizationService.getPerformanceDashboard();
    
    res.json({
      status: 'success',
      data: dashboard,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting performance dashboard:', error);
    next(error);
  }
};

/**
 * Get database performance metrics
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const getDatabaseMetrics = async (req, res, next) => {
  try {
    const { collection, operation } = req.query;
    
    const metrics = databaseOptimizationService.getPerformanceMetrics();
    
    // Filter by collection or operation if specified
    let filteredMetrics = metrics;
    if (collection || operation) {
      filteredMetrics = Object.fromEntries(
        Object.entries(metrics).filter(([key, value]) => {
          const [metricCollection, metricOperation] = key.split('_');
          return (!collection || metricCollection === collection) &&
                 (!operation || metricOperation === operation);
        })
      );
    }
    
    res.json({
      status: 'success',
      data: {
        metrics: filteredMetrics,
        summary: databaseOptimizationService._generatePerformanceSummary(),
        connectionPool: databaseOptimizationService.performanceStats.connectionPoolStats
      },
      filters: { collection, operation },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting database metrics:', error);
    next(error);
  }
};

/**
 * Get cache performance metrics
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const getCacheMetrics = async (req, res, next) => {
  try {
    const [cacheInfo, cacheMetrics] = await Promise.all([
      redisCacheService.getCacheInfo(),
      Promise.resolve(redisCacheService.getMetrics())
    ]);
    
    res.json({
      status: 'success',
      data: {
        info: cacheInfo,
        metrics: cacheMetrics,
        performance: {
          hitRate: cacheMetrics.hitRate,
          totalRequests: cacheMetrics.totalRequests,
          isHealthy: cacheInfo.connected && parseFloat(cacheMetrics.hitRate) > 50
        }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting cache metrics:', error);
    next(error);
  }
};

/**
 * Get AI service performance metrics
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const getAIMetrics = async (req, res, next) => {
  try {
    const { model, operation } = req.query;
    
    const [modelMetrics, circuitBreakerStatus, responseHistory] = await Promise.all([
      Promise.resolve(consolidatedOpenAIService.getModelMetrics(model, operation)),
      Promise.resolve(consolidatedOpenAIService.getCircuitBreakerStatus()),
      Promise.resolve(consolidatedOpenAIService.getResponseHistory(50, model, operation))
    ]);
    
    res.json({
      status: 'success',
      data: {
        modelMetrics,
        circuitBreaker: circuitBreakerStatus,
        responseHistory,
        systemHealth: {
          isHealthy: circuitBreakerStatus.isHealthy,
          overallPerformance: calculateAIPerformanceScore(modelMetrics)
        }
      },
      filters: { model, operation },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting AI metrics:', error);
    next(error);
  }
};

/**
 * Get system health overview
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const getSystemHealth = async (req, res, next) => {
  try {
    const [dbDashboard, cacheInfo, aiCircuitBreaker] = await Promise.all([
      databaseOptimizationService.getPerformanceDashboard(),
      redisCacheService.getCacheInfo(),
      Promise.resolve(consolidatedOpenAIService.getCircuitBreakerStatus())
    ]);
    
    const systemHealth = {
      overall: 'healthy',
      score: 100,
      components: {
        database: {
          status: dbDashboard.systemHealth.status,
          score: dbDashboard.systemHealth.score,
          issues: dbDashboard.systemHealth.issues
        },
        cache: {
          status: cacheInfo.connected ? 'healthy' : 'critical',
          score: cacheInfo.connected ? 100 : 0,
          issues: cacheInfo.connected ? [] : ['Redis disconnected']
        },
        aiServices: {
          status: aiCircuitBreaker.isHealthy ? 'healthy' : 'warning',
          score: aiCircuitBreaker.isHealthy ? 100 : 60,
          issues: aiCircuitBreaker.isHealthy ? [] : ['AI circuit breaker open']
        }
      },
      alerts: dbDashboard.alerts.filter(alert => !alert.acknowledged),
      recommendations: dbDashboard.recommendations
    };
    
    // Calculate overall score and status
    const componentScores = Object.values(systemHealth.components).map(c => c.score);
    systemHealth.score = Math.round(componentScores.reduce((a, b) => a + b, 0) / componentScores.length);
    
    if (systemHealth.score >= 80) {
      systemHealth.overall = 'healthy';
    } else if (systemHealth.score >= 60) {
      systemHealth.overall = 'warning';
    } else {
      systemHealth.overall = 'critical';
    }
    
    res.json({
      status: 'success',
      data: systemHealth,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting system health:', error);
    next(error);
  }
};

/**
 * Get performance alerts
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const getPerformanceAlerts = async (req, res, next) => {
  try {
    const { acknowledged = 'false', severity, limit = 50 } = req.query;
    
    let alerts = databaseOptimizationService.performanceAlerts;
    
    // Filter by acknowledgment status
    if (acknowledged !== 'all') {
      const isAcknowledged = acknowledged === 'true';
      alerts = alerts.filter(alert => alert.acknowledged === isAcknowledged);
    }
    
    // Filter by severity
    if (severity) {
      alerts = alerts.filter(alert => alert.data.severity === severity);
    }
    
    // Limit results
    alerts = alerts.slice(-parseInt(limit, 10));
    
    res.json({
      status: 'success',
      data: {
        alerts,
        summary: {
          total: alerts.length,
          unacknowledged: alerts.filter(a => !a.acknowledged).length,
          critical: alerts.filter(a => a.data.severity === 'critical').length,
          warning: alerts.filter(a => a.data.severity === 'warning').length
        }
      },
      filters: { acknowledged, severity, limit },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting performance alerts:', error);
    next(error);
  }
};

/**
 * Acknowledge a performance alert
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const acknowledgeAlert = async (req, res, next) => {
  try {
    const { alertId } = req.params;
    
    const success = databaseOptimizationService.acknowledgeAlert(alertId);
    
    if (success) {
      res.json({
        status: 'success',
        message: 'Alert acknowledged successfully',
        alertId,
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(404).json({
        status: 'error',
        message: 'Alert not found',
        alertId
      });
    }
  } catch (error) {
    logger.error('Error acknowledging alert:', error);
    next(error);
  }
};

/**
 * Clear cache for specific data types
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const clearCache = async (req, res, next) => {
  try {
    const { type = 'ALL', symbol } = req.body;
    
    const invalidated = await cacheIntegratedDataService.invalidateCache(type, { symbol });
    
    res.json({
      status: 'success',
      message: 'Cache cleared successfully',
      data: {
        type,
        symbol,
        entriesInvalidated: invalidated
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error clearing cache:', error);
    next(error);
  }
};

/**
 * Export performance data
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
export const exportPerformanceData = async (req, res, next) => {
  try {
    const { format = 'json' } = req.query;
    
    const data = databaseOptimizationService.exportPerformanceData(format);
    
    if (format === 'csv') {
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=performance-data.csv');
    } else {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename=performance-data.json');
    }
    
    res.send(data);
  } catch (error) {
    logger.error('Error exporting performance data:', error);
    next(error);
  }
};

/**
 * Calculate AI performance score based on metrics
 * 
 * @private
 * @param {Object} modelMetrics - AI model metrics
 * @returns {number} Performance score (0-100)
 */
function calculateAIPerformanceScore(modelMetrics) {
  if (!modelMetrics || Object.keys(modelMetrics).length === 0) {
    return 0;
  }
  
  let totalScore = 0;
  let metricCount = 0;
  
  for (const [key, metrics] of Object.entries(modelMetrics)) {
    const successRate = parseFloat(metrics.successRate) || 0;
    const avgResponseTime = metrics.averageResponseTime || 0;
    
    // Score based on success rate (70% weight)
    let score = successRate * 0.7;
    
    // Score based on response time (30% weight)
    // Good: <2000ms, Average: 2000-5000ms, Poor: >5000ms
    if (avgResponseTime < 2000) {
      score += 30;
    } else if (avgResponseTime < 5000) {
      score += 20;
    } else {
      score += 10;
    }
    
    totalScore += score;
    metricCount++;
  }
  
  return metricCount > 0 ? Math.round(totalScore / metricCount) : 0;
}

export default {
  getPerformanceDashboard,
  getDatabaseMetrics,
  getCacheMetrics,
  getAIMetrics,
  getSystemHealth,
  getPerformanceAlerts,
  acknowledgeAlert,
  clearCache,
  exportPerformanceData
};
