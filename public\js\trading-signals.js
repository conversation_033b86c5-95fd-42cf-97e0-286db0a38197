/**
 * Trading Signals Module for Trading Signals App
 *
 * This file handles creating, retrieving, updating, and deleting trading signals.
 */

// API base URL
const API_BASE_URL = '/api/mongodb';
let socket;

// Current signals data
let signalsData = [];

// Initialize the trading signals module
function initTradingSignals() {
  // Check if user is authenticated
  if (!window.Auth || !window.Auth.isAuthenticated()) {
    // Redirect to login page
    window.location.href = '/auth.html';
    return;
  }

  // Load signals data
  loadSignals();

  // Set up event listeners
  setupEventListeners();

  // Initialize WebSocket connection
  initWebSocket();
}

// Enhanced WebSocket client with reconnection and state management
class EnhancedWebSocketClient {
  constructor() {
    this.socket = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectDelay = 1000;
    this.connectionState = 'disconnected';
    this.messageQueue = [];
    this.subscribedSymbols = new Set();
    this.signalPreferences = {
      minConfidence: 60,
      enableNotifications: true,
      symbols: [],
      signalTypes: ['BUY', 'SELL']
    };
    this.lastPingTime = null;
    this.latency = 0;
  }

  connect() {
    if (typeof io === 'undefined') {
      console.error('Socket.io is not loaded. Real-time updates will not be available.');
      return;
    }

    const userId = window.Auth?.getUserId();
    if (!userId) {
      console.error('User ID not available. Real-time updates will not be available.');
      return;
    }

    this.connectionState = 'connecting';
    this.socket = io(window.location.origin, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: false
    });

    this.setupEventHandlers(userId);
  }

  setupEventHandlers(userId) {
    // Connection established
    this.socket.on('connect', () => {
      console.log('WebSocket connected successfully');
      this.connectionState = 'connected';
      this.reconnectAttempts = 0;

      // Authenticate with server
      this.socket.emit('authenticate', { userId });

      // Process queued messages
      this.processMessageQueue();

      // Update UI
      this.updateConnectionStatus('connected');
    });

    // Authentication confirmed
    this.socket.on('authenticated', (data) => {
      console.log('WebSocket authenticated:', data);

      // Join symbol-specific rooms
      this.subscribedSymbols.forEach(symbol => {
        this.socket.emit('join-market', symbol);
      });

      // Send signal preferences
      this.socket.emit('update-signal-preferences', this.signalPreferences);
    });

    // Handle disconnection
    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.connectionState = 'disconnected';
      this.updateConnectionStatus('disconnected');

      // Attempt reconnection
      this.scheduleReconnect();
    });

    // Handle connection errors
    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.connectionState = 'error';
      this.updateConnectionStatus('error');
      this.scheduleReconnect();
    });

    // Handle reconnection
    this.socket.on('reconnect', (attemptNumber) => {
      console.log('WebSocket reconnected after', attemptNumber, 'attempts');
      this.connectionState = 'connected';
      this.reconnectAttempts = 0;
      this.updateConnectionStatus('connected');
    });

    // Handle ping/pong for latency measurement
    this.socket.on('pong', (data) => {
      if (this.lastPingTime) {
        this.latency = Date.now() - this.lastPingTime;
        this.updateConnectionStatus('connected', this.latency);
      }
    });
  }

  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      this.updateConnectionStatus('failed');
      return;
    }

    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts), 30000);
    this.reconnectAttempts++;

    console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);

    setTimeout(() => {
      if (this.connectionState !== 'connected') {
        this.connect();
      }
    }, delay);
  }

  subscribeToSymbol(symbol) {
    this.subscribedSymbols.add(symbol);

    if (this.isConnected()) {
      this.socket.emit('join-market', symbol);
    } else {
      this.queueMessage('join-market', symbol);
    }
  }

  unsubscribeFromSymbol(symbol) {
    this.subscribedSymbols.delete(symbol);

    if (this.isConnected()) {
      this.socket.emit('leave-market', symbol);
    }
  }

  updateSignalPreferences(preferences) {
    this.signalPreferences = { ...this.signalPreferences, ...preferences };

    if (this.isConnected()) {
      this.socket.emit('update-signal-preferences', this.signalPreferences);
    } else {
      this.queueMessage('update-signal-preferences', this.signalPreferences);
    }
  }

  generateSignals(symbol, marketData, indicators, options) {
    const message = { symbol, marketData, indicators, options };

    if (this.isConnected()) {
      this.socket.emit('generate-signals', message);
    } else {
      this.queueMessage('generate-signals', message);
    }
  }

  ping() {
    if (this.isConnected()) {
      this.lastPingTime = Date.now();
      this.socket.emit('ping');
    }
  }

  isConnected() {
    return this.socket && this.socket.connected && this.connectionState === 'connected';
  }

  queueMessage(event, data) {
    this.messageQueue.push({ event, data, timestamp: Date.now() });

    // Limit queue size
    if (this.messageQueue.length > 100) {
      this.messageQueue = this.messageQueue.slice(-50);
    }
  }

  processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();

      // Skip old messages (older than 5 minutes)
      if (Date.now() - message.timestamp > 300000) {
        continue;
      }

      this.socket.emit(message.event, message.data);
    }
  }

  updateConnectionStatus(status, latency = null) {
    const statusElement = document.getElementById('websocket-status');
    if (statusElement) {
      statusElement.className = `websocket-status ${status}`;

      let statusText = status.charAt(0).toUpperCase() + status.slice(1);
      if (latency !== null) {
        statusText += ` (${latency}ms)`;
      }

      statusElement.textContent = statusText;
    }

    // Dispatch custom event for other components
    window.dispatchEvent(new CustomEvent('websocket-status-change', {
      detail: { status, latency }
    }));
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.connectionState = 'disconnected';
    this.subscribedSymbols.clear();
    this.messageQueue = [];
  }
}

// Initialize enhanced WebSocket client
let enhancedWebSocket;

// Initialize WebSocket connection for real-time updates
function initWebSocket() {
  enhancedWebSocket = new EnhancedWebSocketClient();
  enhancedWebSocket.connect();

  // Set up signal event handlers
  setupSignalEventHandlers();

  // Start periodic ping for latency monitoring
  setInterval(() => {
    if (enhancedWebSocket.isConnected()) {
      enhancedWebSocket.ping();
    }
  }, 30000); // Ping every 30 seconds
}

function setupSignalEventHandlers() {
  if (!enhancedWebSocket.socket) return;

  const socket = enhancedWebSocket.socket;

  // Handle signal updates
  socket.on('signal_update', (data) => {
    console.log('Received signal update:', data);

    // Find the signal in the current data
    const index = signalsData.findIndex(signal => signal.id === data.id);

    if (index !== -1) {
      // Update existing signal
      signalsData[index] = data;
    } else {
      // Add new signal
      signalsData.unshift(data); // Add to beginning for newest first
    }

    // Update UI
    displaySignals(signalsData);

    // Show notification for high-confidence signals
    if (data.confidence >= enhancedWebSocket.signalPreferences.minConfidence) {
      showSignalNotification(data);
    }

    // Update chart if available
    if (window.updateChartWithSignal) {
      window.updateChartWithSignal(data);
    }
  });

  // Handle signal deletion
  socket.on('signal_delete', (data) => {
    console.log('Received signal deletion:', data);

    // Remove the signal from the current data
    signalsData = signalsData.filter(signal => signal.id !== data.id);

    // Update UI
    displaySignals(signalsData);
  });

  // Handle trading signals from market rooms
  socket.on('trading-signal', (data) => {
    console.log('Received trading signal:', data);

    // Add to signals data
    signalsData.unshift(data.signal);

    // Update UI
    displaySignals(signalsData);

    // Show notification
    if (data.signal.confidence >= enhancedWebSocket.signalPreferences.minConfidence) {
      showSignalNotification(data.signal);
    }

    // Update chart
    if (window.updateChartWithSignal) {
      window.updateChartWithSignal(data.signal);
    }
  });

  // Handle recent signals when joining a market
  socket.on('recent-signals', (data) => {
    console.log('Received recent signals:', data);

    // Merge with existing signals, avoiding duplicates
    data.signals.forEach(signal => {
      const exists = signalsData.find(s => s.id === signal.id);
      if (!exists) {
        signalsData.push(signal);
      }
    });

    // Sort by timestamp (newest first)
    signalsData.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Update UI
    displaySignals(signalsData);
  });

  // Handle signals generated via WebSocket
  socket.on('signals-generated', (data) => {
    console.log('Signals generated:', data);

    // Add new signals
    data.signals.forEach(signal => {
      signalsData.unshift(signal);
    });

    // Update UI
    displaySignals(signalsData);

    // Show success notification
    showNotification(`Generated ${data.signals.length} signals for ${data.symbol}`, 'success');
  });

  // Handle signal generation errors
  socket.on('signal-error', (data) => {
    console.error('Signal generation error:', data);
    showNotification(`Signal generation failed: ${data.error}`, 'error');
  });

  // Handle market data updates
  socket.on('market-update', (data) => {
    console.log('Market update received:', data);

    // Update chart with new market data
    if (window.updateChartWithMarketData) {
      window.updateChartWithMarketData(data);
    }

    // Trigger signal generation if auto-generation is enabled
    if (window.autoGenerateSignals && data.symbol) {
      const currentSymbol = getCurrentSymbol();
      if (currentSymbol === data.symbol) {
        // Generate signals with new market data
        enhancedWebSocket.generateSignals(
          data.symbol,
          data.data,
          getCurrentIndicators(),
          { autoGenerated: true }
        );
      }
    }
  });
}

// Show signal notification
function showSignalNotification(signal) {
  if (!enhancedWebSocket.signalPreferences.enableNotifications) {
    return;
  }

  const title = `${signal.type} Signal: ${signal.symbol}`;
  const message = `Confidence: ${signal.confidence}% - ${signal.message}`;

  // Use browser notification if available
  if (window.Notification && Notification.permission === 'granted') {
    const notification = new Notification(title, {
      body: message,
      icon: signal.type === 'BUY' ? '/icons/buy-signal.png' : '/icons/sell-signal.png',
      tag: `signal-${signal.id}`,
      requireInteraction: signal.confidence >= 80
    });

    notification.onclick = () => {
      window.focus();
      notification.close();

      // Scroll to signal
      const signalElement = document.querySelector(`[data-signal-id="${signal.id}"]`);
      if (signalElement) {
        signalElement.scrollIntoView({ behavior: 'smooth' });
        signalElement.classList.add('highlight');
        setTimeout(() => signalElement.classList.remove('highlight'), 3000);
      }
    };

    // Auto-close after 10 seconds for low-confidence signals
    if (signal.confidence < 80) {
      setTimeout(() => notification.close(), 10000);
    }
  }

  // Also show in-app notification
  showNotification(message, signal.type === 'BUY' ? 'success' : 'warning');
}

// Get current symbol from UI
function getCurrentSymbol() {
  const symbolSelect = document.getElementById('symbol-filter');
  return symbolSelect ? symbolSelect.value : 'EURUSD';
}

// Get current indicators from UI or default
function getCurrentIndicators() {
  // This would typically come from your technical analysis module
  return {
    rsi: 50,
    macd: 0,
    sma20: 0,
    sma50: 0,
    volume: 0
  };
}

// Show notification helper
function showNotification(message, type = 'info') {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.innerHTML = `
    <div class="notification-content">
      <span class="notification-message">${message}</span>
      <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
    </div>
  `;

  // Add to notification container
  let container = document.getElementById('notification-container');
  if (!container) {
    container = document.createElement('div');
    container.id = 'notification-container';
    container.className = 'notification-container';
    document.body.appendChild(container);
  }

  container.appendChild(notification);

  // Auto-remove after 5 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      notification.remove();
    }
  }, 5000);
}

// Set up event listeners
function setupEventListeners() {
  // Add signal form submission
  const signalForm = document.getElementById('signal-form');
  if (signalForm) {
    signalForm.addEventListener('submit', handleSignalSubmit);
  }

  // Filter form submission
  const filterForm = document.getElementById('filter-form');
  if (filterForm) {
    filterForm.addEventListener('submit', handleFilterSubmit);
  }
}

// Load trading signals from the server
async function loadSignals(filters = {}) {
  try {
    // Build query string from filters
    const queryParams = new URLSearchParams();
    if (filters.symbol) queryParams.append('symbol', filters.symbol);
    if (filters.type) queryParams.append('type', filters.type);
    if (filters.timeframe) queryParams.append('timeframe', filters.timeframe);

    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';

    // Fetch signals from the server
    const response = await fetch(`${API_BASE_URL}/signals${queryString}`, {
      method: 'GET',
      headers: window.Auth.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to load signals');
    }

    // Parse response
    const data = await response.json();

    // Store signals data
    signalsData = data;

    // Display signals
    displaySignals(signalsData);

    return data;
  } catch (error) {
    console.error('Error loading signals:', error);
    showMessage('error', 'Failed to load signals: ' + error.message);
  }
}

// Display signals in the UI
function displaySignals(signals) {
  const signalsContainer = document.getElementById('signals-container');
  if (!signalsContainer) return;

  if (signals.length === 0) {
    signalsContainer.innerHTML = '<p>No trading signals found. Create your first signal!</p>';
    return;
  }

  // Create HTML for signals
  const signalsHTML = signals.map(signal => `
    <div class="signal-card ${signal.type.toLowerCase()}" data-id="${signal._id}">
      <div class="signal-header">
        <h3>${signal.symbol} - ${signal.type}</h3>
        <span class="signal-timeframe">${signal.timeframe}</span>
      </div>
      <div class="signal-body">
        <div class="signal-prices">
          <p><strong>Entry:</strong> ${signal.entryPrice}</p>
          <p><strong>Stop Loss:</strong> ${signal.stopLoss}</p>
          <p><strong>Take Profit:</strong> ${signal.takeProfit}</p>
        </div>
        <div class="signal-meta">
          <p><strong>Status:</strong> ${signal.status}</p>
          <p><strong>Created:</strong> ${new Date(signal.createdAt).toLocaleString()}</p>
        </div>
        ${signal.analysis ? `<div class="signal-analysis"><p>${signal.analysis}</p></div>` : ''}
      </div>
      <div class="signal-actions">
        <button class="btn btn-sm btn-edit" data-id="${signal._id}">Edit</button>
        <button class="btn btn-sm btn-delete" data-id="${signal._id}">Delete</button>
      </div>
    </div>
  `).join('');

  signalsContainer.innerHTML = signalsHTML;

  // Add event listeners to buttons
  document.querySelectorAll('.btn-edit').forEach(button => {
    button.addEventListener('click', () => editSignal(button.dataset.id));
  });

  document.querySelectorAll('.btn-delete').forEach(button => {
    button.addEventListener('click', () => deleteSignal(button.dataset.id));
  });
}

// Create a new trading signal
async function createSignal(signalData) {
  try {
    const response = await fetch(`${API_BASE_URL}/signals`, {
      method: 'POST',
      headers: window.Auth.getAuthHeaders(),
      body: JSON.stringify(signalData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create signal');
    }

    const data = await response.json();

    // Show success message
    showMessage('success', 'Trading signal created successfully!');

    // Reload signals
    loadSignals();

    // Reset form
    resetSignalForm();

    return data;
  } catch (error) {
    console.error('Error creating signal:', error);
    showMessage('error', 'Failed to create signal: ' + error.message);
    throw error;
  }
}

// Update an existing trading signal
async function updateSignal(signalId, signalData) {
  try {
    const response = await fetch(`${API_BASE_URL}/signals/${signalId}`, {
      method: 'PUT',
      headers: window.Auth.getAuthHeaders(),
      body: JSON.stringify(signalData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update signal');
    }

    const data = await response.json();

    // Show success message
    showMessage('success', 'Trading signal updated successfully!');

    // Reload signals
    loadSignals();

    // Reset form
    resetSignalForm();

    return data;
  } catch (error) {
    console.error('Error updating signal:', error);
    showMessage('error', 'Failed to update signal: ' + error.message);
    throw error;
  }
}

// Delete a trading signal
async function deleteSignal(signalId) {
  // Confirm deletion
  if (!confirm('Are you sure you want to delete this signal?')) {
    return;
  }

  try {
    const response = await fetch(`${API_BASE_URL}/signals/${signalId}`, {
      method: 'DELETE',
      headers: window.Auth.getAuthHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to delete signal');
    }

    // Show success message
    showMessage('success', 'Trading signal deleted successfully!');

    // Reload signals
    loadSignals();

    return true;
  } catch (error) {
    console.error('Error deleting signal:', error);
    showMessage('error', 'Failed to delete signal: ' + error.message);
    throw error;
  }
}

// Edit a signal (populate form with signal data)
function editSignal(signalId) {
  // Find the signal in the data
  const signal = signalsData.find(s => s._id === signalId);
  if (!signal) return;

  // Get the form
  const form = document.getElementById('signal-form');
  if (!form) return;

  // Set form to edit mode
  form.dataset.mode = 'edit';
  form.dataset.signalId = signalId;

  // Populate form fields
  document.getElementById('signal-symbol').value = signal.symbol;
  document.getElementById('signal-type').value = signal.type;
  document.getElementById('signal-entry-price').value = signal.entryPrice;
  document.getElementById('signal-stop-loss').value = signal.stopLoss;
  document.getElementById('signal-take-profit').value = signal.takeProfit;
  document.getElementById('signal-timeframe').value = signal.timeframe;

  if (document.getElementById('signal-analysis')) {
    document.getElementById('signal-analysis').value = signal.analysis || '';
  }

  // Update submit button text
  const submitButton = form.querySelector('button[type="submit"]');
  if (submitButton) {
    submitButton.textContent = 'Update Signal';
  }

  // Scroll to form
  form.scrollIntoView({ behavior: 'smooth' });
}

// Reset the signal form
function resetSignalForm() {
  const form = document.getElementById('signal-form');
  if (!form) return;

  // Reset form mode
  form.dataset.mode = 'create';
  delete form.dataset.signalId;

  // Clear form fields
  form.reset();

  // Reset submit button text
  const submitButton = form.querySelector('button[type="submit"]');
  if (submitButton) {
    submitButton.textContent = 'Create Signal';
  }
}

// Handle signal form submission
async function handleSignalSubmit(e) {
  e.preventDefault();

  // Get form data
  const form = e.target;
  const formData = new FormData(form);

  // Create signal data object
  const signalData = {
    symbol: formData.get('symbol').toUpperCase(),
    type: formData.get('type'),
    entryPrice: parseFloat(formData.get('entryPrice')),
    stopLoss: parseFloat(formData.get('stopLoss')),
    takeProfit: parseFloat(formData.get('takeProfit')),
    timeframe: formData.get('timeframe')
  };

  // Add optional fields if they exist
  if (formData.has('analysis')) {
    signalData.analysis = formData.get('analysis');
  }

  try {
    if (form.dataset.mode === 'edit') {
      // Update existing signal
      await updateSignal(form.dataset.signalId, signalData);
    } else {
      // Create new signal
      await createSignal(signalData);
    }
  } catch (error) {
    console.error('Error submitting signal:', error);
  }
}

// Handle filter form submission
function handleFilterSubmit(e) {
  e.preventDefault();

  // Get form data
  const form = e.target;
  const formData = new FormData(form);

  // Create filters object
  const filters = {};

  // Add filters if they have values
  if (formData.get('symbol')) {
    filters.symbol = formData.get('symbol').toUpperCase();
  }

  if (formData.get('type')) {
    filters.type = formData.get('type');
  }

  if (formData.get('timeframe')) {
    filters.timeframe = formData.get('timeframe');
  }

  // Load signals with filters
  loadSignals(filters);
}

// Show message in the UI
function showMessage(type, message) {
  const messageElement = document.getElementById('message-container');
  if (!messageElement) return;

  messageElement.className = `message ${type}`;
  messageElement.textContent = message;

  // Clear message after 5 seconds
  setTimeout(() => {
    messageElement.textContent = '';
    messageElement.className = 'message';
  }, 5000);
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  initTradingSignals();
});

// Export functions for use in other modules
window.TradingSignals = {
  loadSignals,
  createSignal,
  updateSignal,
  deleteSignal,
  resetSignalForm
};
