import { z } from 'zod';
import technicalAnalysisService from '../services/technicalAnalysisService.js';
import { APIError } from '../middleware/errorHandler.js';
import logger from '../utils/logger.js';

// Validation schemas
const indicatorSchema = z.object({
  symbol: z.string().min(1),
  period: z.number().optional(),
  interval: z.string().optional(),
});

const economicIndicatorSchema = z.object({
  seriesId: z.string().min(1),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

// Get technical indicator
export const getTechnicalIndicator = async (req, res, next) => {
  try {
    const { indicator } = req.params;
    const validatedData = indicatorSchema.parse(req.query);

    const data = await technicalAnalysisService.getTechnicalIndicator(
      validatedData.symbol,
      indicator.toUpperCase(),
      validatedData
    );

    res.json({
      status: 'success',
      data,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      next(new APIError(400, 'Invalid input data'));
    } else {
      next(error);
    }
  }
};

// Get economic indicator from FRED
export const getEconomicIndicator = async (req, res, next) => {
  try {
    const validatedData = economicIndicatorSchema.parse(req.query);

    const data = await technicalAnalysisService.getEconomicIndicator(
      validatedData.seriesId,
      {
        observation_start: validatedData.startDate,
        observation_end: validatedData.endDate,
      }
    );

    res.json({
      status: 'success',
      data,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      next(new APIError(400, 'Invalid input data'));
    } else {
      next(error);
    }
  }
};

// Get full technical analysis
export const getFullAnalysis = async (req, res, next) => {
  try {
    const { symbol } = req.params;
    if (!symbol) {
      throw new APIError(400, 'Symbol is required');
    }

    const data = await technicalAnalysisService.getFullAnalysis(symbol);

    res.json({
      status: 'success',
      data,
    });
  } catch (error) {
    next(error);
  }
};

// Get trading signals
export const getTradingSignals = async (req, res, next) => {
  try {
    const { symbol } = req.params;
    if (!symbol) {
      throw new APIError(400, 'Symbol is required');
    }

    const data = await technicalAnalysisService.getTradingSignals(symbol);

    res.json({
      status: 'success',
      data,
    });
  } catch (error) {
    next(error);
  }
};

// Get SMC/ICT structures
export const getSMCStructures = async (req, res, next) => {
  try {
    const { symbol } = req.params;
    const { timeframe } = req.query;
    if (!symbol) {
      throw new APIError(400, 'Symbol is required');
    }
    const data = await technicalAnalysisService.detectSMCStructures(symbol, timeframe);
    res.json({ status: 'success', data });
  } catch (error) {
    next(error);
  }
};

export default {
  getTechnicalIndicator,
  getEconomicIndicator,
  getFullAnalysis,
  getTradingSignals,
  getSMCStructures,
}; 