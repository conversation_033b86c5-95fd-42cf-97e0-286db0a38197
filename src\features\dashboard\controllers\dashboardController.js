// Dashboard Controller
export const getDashboardOverview = async (req, res, next) => {
  try {
    // Placeholder: Fetch real market data, watchlist, and signals in the future
    const marketSnapshot = {
      forex: [
        { symbol: 'EURUSD', price: 1.0875, change: 0.25 },
        { symbol: 'GBPUSD', price: 1.2732, change: -0.12 },
      ],
      crypto: [
        { symbol: 'BTCUSD', price: 42000, change: 1.5 },
        { symbol: 'ETHUSD', price: 2300, change: 0.8 },
      ],
      commodities: [
        { symbol: 'XAUUSD', price: 1950, change: 0.1 },
      ],
    };
    const watchlist = []; // To be populated per user
    const latestSignals = []; // To be populated with real signals

    res.json({
      status: 'success',
      data: {
        marketSnapshot,
        watchlist,
        latestSignals,
      },
    });
  } catch (error) {
    next(error);
  }
};

export default {
  getDashboardOverview,
}; 