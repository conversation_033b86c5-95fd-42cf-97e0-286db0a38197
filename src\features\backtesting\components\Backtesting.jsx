import React, { useState } from 'react';
import BacktestingForm from './BacktestingForm';
import BacktestResults from './BacktestResults';
import PatternBacktesting from './PatternBacktesting';
import { alphaVantageAPI } from '../../../services/apiService';

/**
 * Backtesting Component
 *
 * Main component for backtesting trading strategies
 */
const Backtesting = () => {
  // State for backtest results
  const [results, setResults] = useState(null);

  // State for loading status
  const [loading, setLoading] = useState(false);

  // State for error message
  const [error, setError] = useState(null);

  // State for active tab
  const [activeTab, setActiveTab] = useState('strategy'); // 'strategy' or 'pattern'

  // Run backtest with the given configuration
  const runBacktest = async (config) => {
    setLoading(true);
    setError(null);

    try {
      // In a real app, this would be an API call to your backend
      // For demo purposes, we'll simulate a backtest with a timeout

      // Fetch historical data for the selected symbol and timeframe
      const historicalData = await fetchHistoricalData(config.symbol, config.timeframe, config.startDate, config.endDate);

      // Run the backtest with the fetched data
      const backtestResults = simulateBacktest(historicalData, config);

      // Set the results
      setResults(backtestResults);
    } catch (error) {
      console.error('Error running backtest:', error);
      setError('Failed to run backtest. Please try again later.');

      // For demo purposes, generate mock results if API fails
      generateMockResults(config);
    } finally {
      setLoading(false);
    }
  };

  // Fetch historical data from Alpha Vantage API
  const fetchHistoricalData = async (symbol, timeframe, startDate, endDate) => {
    try {
      // Map timeframe to Alpha Vantage interval
      const intervalMap = {
        'M5': '5min',
        'M15': '15min',
        'M30': '30min',
        'H1': '60min',
        'H4': '4hour',
        'D1': 'daily'
      };

      // Determine if we're dealing with forex, crypto, or stock
      let functionName = 'TIME_SERIES_INTRADAY';
      let dataKey = `Time Series (${intervalMap[timeframe]})`;

      if (timeframe === 'D1') {
        functionName = 'TIME_SERIES_DAILY';
        dataKey = 'Time Series (Daily)';
      }

      if (symbol.includes('USD') && symbol !== 'BTCUSD') {
        // Forex data
        const fromCurrency = symbol.substring(0, 3);
        const toCurrency = symbol.substring(3, 6);

        functionName = 'FX_' + (timeframe === 'D1' ? 'DAILY' : 'INTRADAY');
        dataKey = timeframe === 'D1' ? 'Time Series FX (Daily)' : `Time Series FX (${intervalMap[timeframe]})`;

        const response = await alphaVantageAPI.get('query', {
          function: functionName,
          from_symbol: fromCurrency,
          to_symbol: toCurrency,
          interval: intervalMap[timeframe],
          outputsize: 'full'
        });

        return processHistoricalData(response, dataKey, startDate, endDate);
      } else if (symbol === 'BTCUSD') {
        // Crypto data
        functionName = 'DIGITAL_CURRENCY_' + (timeframe === 'D1' ? 'DAILY' : 'INTRADAY');
        dataKey = timeframe === 'D1' ? 'Time Series (Digital Currency Daily)' : `Time Series (Digital Currency ${intervalMap[timeframe]})`;

        const response = await alphaVantageAPI.get('query', {
          function: functionName,
          symbol: 'BTC',
          market: 'USD',
          interval: intervalMap[timeframe],
          outputsize: 'full'
        });

        return processHistoricalData(response, dataKey, startDate, endDate);
      } else {
        // Stock data
        const response = await alphaVantageAPI.get('query', {
          function: functionName,
          symbol: symbol,
          interval: intervalMap[timeframe],
          outputsize: 'full'
        });

        return processHistoricalData(response, dataKey, startDate, endDate);
      }
    } catch (error) {
      console.error('Error fetching historical data:', error);
      throw error;
    }
  };

  // Process historical data from API response
  const processHistoricalData = (response, dataKey, startDate, endDate) => {
    if (!response || !response[dataKey]) {
      throw new Error('Invalid data received from API.');
    }

    const timeSeriesData = response[dataKey];
    const dates = Object.keys(timeSeriesData).sort();

    const startTimestamp = new Date(startDate).getTime();
    const endTimestamp = new Date(endDate).getTime();

    const filteredData = [];

    dates.forEach(date => {
      const timestamp = new Date(date).getTime();

      if (timestamp >= startTimestamp && timestamp <= endTimestamp) {
        const data = timeSeriesData[date];

        // Handle different API response formats
        const open = parseFloat(data['1. open'] || data['1a. open'] || data['1. open (USD)'] || 0);
        const high = parseFloat(data['2. high'] || data['2a. high'] || data['2. high (USD)'] || 0);
        const low = parseFloat(data['3. low'] || data['3a. low'] || data['3. low (USD)'] || 0);
        const close = parseFloat(data['4. close'] || data['4a. close'] || data['4. close (USD)'] || 0);
        const volume = parseFloat(data['5. volume'] || data['5. volume (USD)'] || 0);

        filteredData.push({
          date,
          timestamp,
          open,
          high,
          low,
          close,
          volume
        });
      }
    });

    return filteredData.reverse(); // Oldest to newest
  };

  // Simulate backtest with the given data and configuration
  const simulateBacktest = (historicalData, config) => {
    // This is a simplified backtest simulation
    // In a real app, this would be a more complex algorithm

    // For demo purposes, we'll generate mock results
    return generateMockResults(config);
  };

  // Generate mock backtest results for demo purposes
  const generateMockResults = (config) => {
    // Initial capital
    const initialCapital = config.initialCapital;

    // Generate random trades
    const trades = [];
    const tradeCount = Math.floor(Math.random() * 50) + 50; // 50-100 trades

    let currentCapital = initialCapital;
    let highestCapital = initialCapital;
    let lowestDrawdown = 0;

    const startDate = new Date(config.startDate);
    const endDate = new Date(config.endDate);
    const dateRange = endDate.getTime() - startDate.getTime();

    // Generate equity curve points
    const equityCurve = [];
    const monthlyReturns = {};

    // Add initial point
    equityCurve.push({
      date: startDate.toISOString(),
      equity: initialCapital
    });

    // Generate trades
    for (let i = 0; i < tradeCount; i++) {
      // Random trade type (buy or sell)
      const type = Math.random() > 0.5 ? 'BUY' : 'SELL';

      // Random entry date within date range
      const entryTimestamp = startDate.getTime() + Math.random() * (dateRange * 0.9);
      const entryDate = new Date(entryTimestamp).toISOString();

      // Random exit date after entry date
      const maxExitTimestamp = startDate.getTime() + dateRange;
      const minExitTimestamp = entryTimestamp + (1000 * 60 * 60 * 24); // At least 1 day later
      const exitTimestamp = Math.min(minExitTimestamp + Math.random() * (1000 * 60 * 60 * 24 * 14), maxExitTimestamp); // Up to 14 days later
      const exitDate = new Date(exitTimestamp).toISOString();

      // Random prices based on symbol
      let entryPrice, exitPrice;

      if (config.symbol === 'EURUSD' || config.symbol === 'GBPUSD' || config.symbol === 'AUDUSD') {
        // Forex major pairs
        entryPrice = 1 + Math.random() * 0.5; // 1.0000 - 1.5000
      } else if (config.symbol === 'USDJPY') {
        // USD/JPY
        entryPrice = 100 + Math.random() * 50; // 100.00 - 150.00
      } else if (config.symbol === 'XAUUSD') {
        // Gold
        entryPrice = 1800 + Math.random() * 400; // 1800 - 2200
      } else {
        // Default
        entryPrice = 100 + Math.random() * 50; // 100 - 150
      }

      // Calculate exit price based on trade type and win/loss
      const isWin = Math.random() < 0.6; // 60% win rate

      if (type === 'BUY') {
        exitPrice = isWin ? entryPrice * (1 + Math.random() * 0.05) : entryPrice * (1 - Math.random() * 0.03);
      } else {
        exitPrice = isWin ? entryPrice * (1 - Math.random() * 0.05) : entryPrice * (1 + Math.random() * 0.03);
      }

      // Calculate profit/loss
      let profit;

      if (config.symbol.includes('USD') && !config.symbol.includes('JPY')) {
        // For forex pairs with USD as quote currency
        const pipValue = 0.0001;
        const lotSize = config.positionSize === 'fixed' ? config.fixedSize : 0.1;
        const pips = type === 'BUY' ? (exitPrice - entryPrice) / pipValue : (entryPrice - exitPrice) / pipValue;
        profit = pips * 10 * lotSize; // $10 per pip for 1 lot
      } else if (config.symbol === 'XAUUSD') {
        // For gold
        const lotSize = config.positionSize === 'fixed' ? config.fixedSize : 0.1;
        profit = type === 'BUY' ? (exitPrice - entryPrice) * 100 * lotSize : (entryPrice - exitPrice) * 100 * lotSize; // $100 per $1 move for 1 lot
      } else {
        // Default
        profit = type === 'BUY' ? (exitPrice - entryPrice) * 100 : (entryPrice - exitPrice) * 100;
      }

      // Add trade to list
      trades.push({
        type,
        entryDate,
        exitDate,
        entryPrice,
        exitPrice,
        profit
      });

      // Update capital
      currentCapital += profit;

      // Update highest capital and drawdown
      if (currentCapital > highestCapital) {
        highestCapital = currentCapital;
      } else {
        const drawdown = (highestCapital - currentCapital) / highestCapital * 100;
        if (drawdown > lowestDrawdown) {
          lowestDrawdown = drawdown;
        }
      }

      // Add equity point
      equityCurve.push({
        date: exitDate,
        equity: currentCapital
      });

      // Update monthly returns
      const month = exitDate.substring(0, 7); // YYYY-MM
      const prevEquity = monthlyReturns[month]?.prevEquity || initialCapital;

      if (!monthlyReturns[month]) {
        monthlyReturns[month] = {
          prevEquity,
          currentEquity: currentCapital,
          return: ((currentCapital - prevEquity) / prevEquity) * 100
        };
      } else {
        monthlyReturns[month].currentEquity = currentCapital;
        monthlyReturns[month].return = ((currentCapital - prevEquity) / prevEquity) * 100;
      }
    }

    // Sort trades by entry date
    trades.sort((a, b) => new Date(a.entryDate) - new Date(b.entryDate));

    // Calculate performance metrics
    const netProfit = currentCapital - initialCapital;
    const winningTrades = trades.filter(trade => trade.profit > 0).length;
    const losingTrades = trades.filter(trade => trade.profit <= 0).length;
    const winRate = (winningTrades / tradeCount) * 100;

    const grossProfit = trades.filter(trade => trade.profit > 0).reduce((sum, trade) => sum + trade.profit, 0);
    const grossLoss = Math.abs(trades.filter(trade => trade.profit <= 0).reduce((sum, trade) => sum + trade.profit, 0));
    const profitFactor = grossLoss === 0 ? grossProfit : grossProfit / grossLoss;

    const averageWin = winningTrades === 0 ? 0 : grossProfit / winningTrades;
    const averageLoss = losingTrades === 0 ? 0 : grossLoss / losingTrades;
    const averageReturn = netProfit / tradeCount;

    // Calculate Sharpe and Sortino ratios
    const returns = trades.map(trade => trade.profit / initialCapital);
    const averageReturn2 = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const stdDev = Math.sqrt(returns.reduce((sum, ret) => sum + Math.pow(ret - averageReturn2, 2), 0) / returns.length);
    const negativeReturns = returns.filter(ret => ret < 0);
    const downDev = negativeReturns.length === 0 ? 1 : Math.sqrt(negativeReturns.reduce((sum, ret) => sum + Math.pow(ret, 2), 0) / negativeReturns.length);

    const sharpeRatio = stdDev === 0 ? 0 : averageReturn2 / stdDev;
    const sortinoRatio = downDev === 0 ? 0 : averageReturn2 / downDev;

    // Calculate expectancy
    const expectancy = (winRate / 100) * averageWin - (1 - winRate / 100) * averageLoss;

    // Calculate average holding time
    const holdingTimes = trades.map(trade => {
      const entryTime = new Date(trade.entryDate).getTime();
      const exitTime = new Date(trade.exitDate).getTime();
      return (exitTime - entryTime) / (1000 * 60 * 60 * 24); // in days
    });

    const averageHoldingTime = holdingTimes.reduce((sum, time) => sum + time, 0) / holdingTimes.length;
    const averageHoldingTimeFormatted = `${Math.floor(averageHoldingTime)} days, ${Math.floor((averageHoldingTime % 1) * 24)} hours`;

    // Format monthly returns for chart
    const formattedMonthlyReturns = {};
    Object.keys(monthlyReturns).forEach(month => {
      formattedMonthlyReturns[month] = monthlyReturns[month].return;
    });

    // Return backtest results
    return {
      symbol: config.symbol,
      timeframe: config.timeframe,
      startDate: config.startDate,
      endDate: config.endDate,
      initialCapital,
      finalCapital: currentCapital,
      netProfit,
      totalTrades: tradeCount,
      winningTrades,
      losingTrades,
      winRate,
      profitFactor,
      maxDrawdown: lowestDrawdown,
      averageWin,
      averageLoss,
      averageReturn,
      sharpeRatio,
      sortinoRatio,
      expectancy,
      averageHoldingTime: averageHoldingTimeFormatted,
      trades,
      equityCurve,
      monthlyReturns: formattedMonthlyReturns
    };
  };

  return (
    <div className="backtesting p-4">
      <h1 className="text-2xl font-bold mb-6">Strategy Backtesting</h1>

      {/* Tabs */}
      <div className="mb-6 border-b border-gray-200 dark:border-gray-700">
        <ul className="flex flex-wrap -mb-px">
          <li className="mr-2">
            <button
              className={`inline-block py-2 px-4 text-sm font-medium ${
                activeTab === 'strategy'
                  ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('strategy')}
            >
              Strategy Backtesting
            </button>
          </li>
          <li className="mr-2">
            <button
              className={`inline-block py-2 px-4 text-sm font-medium ${
                activeTab === 'pattern'
                  ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('pattern')}
            >
              Pattern Backtesting
            </button>
          </li>
        </ul>
      </div>

      {/* Strategy Backtesting Tab */}
      {activeTab === 'strategy' && (
        <div className="grid grid-cols-1 gap-8">
          {/* Backtesting Form */}
          {!results && <BacktestingForm onRunBacktest={runBacktest} isLoading={loading} />}

          {/* Loading Indicator */}
          {loading && (
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow text-center">
              <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
              <p className="text-gray-700 dark:text-gray-300">Running backtest... This may take a few moments.</p>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
              <span className="block sm:inline">{error}</span>
            </div>
          )}

          {/* Backtest Results */}
          {!loading && !error && results && (
            <div>
              <BacktestResults results={results} />
              <button
                className="mt-4 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded"
                onClick={() => setResults(null)}
              >
                Run Another Backtest
              </button>
            </div>
          )}
        </div>
      )}

      {/* Pattern Backtesting Tab */}
      {activeTab === 'pattern' && (
        <PatternBacktesting />
      )}
    </div>
  );
};

export default Backtesting;
