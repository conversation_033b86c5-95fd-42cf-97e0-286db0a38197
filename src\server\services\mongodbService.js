/**
 * MongoDB Service for Trading Signals App
 * 
 * This service provides a high-level interface for MongoDB operations.
 */

const { MongoClient, ServerApiVersion } = require('mongodb');
const logger = require('../utils/logger');

// MongoDB client and database
let client = null;
let db = null;

// Database name
const dbName = "tradingSignalsApp";

// Collections
const collections = {
  USERS: "users",
  SIGNALS: "tradingSignals",
  MARKET_DATA: "marketData",
  USER_PREFERENCES: "userPreferences"
};

/**
 * Initialize MongoDB connection
 * @param {string} uri - MongoDB connection URI
 * @returns {Promise<Object>} MongoDB client and database
 */
async function initialize(uri) {
  try {
    logger.info('Initializing MongoDB connection');
    
    // Create MongoDB client
    client = new MongoClient(uri, {
      serverApi: {
        version: ServerApiVersion.v1,
        strict: true,
        deprecationErrors: true,
      }
    });
    
    // Connect to MongoDB
    await client.connect();
    logger.info('Connected to MongoDB successfully');
    
    // Ping the database to confirm connection
    await client.db('admin').command({ ping: 1 });
    logger.info('MongoDB ping successful - connection is fully working');
    
    // Get database
    db = client.db(dbName);
    
    return { client, db };
  } catch (error) {
    logger.error('Error initializing MongoDB connection', error);
    throw error;
  }
}

/**
 * Get MongoDB database instance
 * @returns {Object} MongoDB database
 */
function getDatabase() {
  if (!db) {
    throw new Error('MongoDB not initialized');
  }
  return db;
}

/**
 * Get MongoDB client instance
 * @returns {Object} MongoDB client
 */
function getClient() {
  if (!client) {
    throw new Error('MongoDB not initialized');
  }
  return client;
}

/**
 * Close MongoDB connection
 * @returns {Promise<void>}
 */
async function close() {
  if (client) {
    await client.close();
    logger.info('MongoDB connection closed');
    client = null;
    db = null;
  }
}

/**
 * Create indexes for better performance
 * @returns {Promise<void>}
 */
async function createIndexes() {
  try {
    if (!db) {
      throw new Error('MongoDB not initialized');
    }
    
    logger.info('Creating MongoDB indexes');
    
    // Users collection indexes
    await db.collection(collections.USERS).createIndex({ email: 1 }, { unique: true });
    await db.collection(collections.USERS).createIndex({ username: 1 }, { unique: true, sparse: true });
    
    // Trading signals collection indexes
    await db.collection(collections.SIGNALS).createIndex({ symbol: 1 });
    await db.collection(collections.SIGNALS).createIndex({ createdAt: 1 });
    await db.collection(collections.SIGNALS).createIndex({ userId: 1 });
    
    // Market data collection indexes
    await db.collection(collections.MARKET_DATA).createIndex({ symbol: 1, timestamp: 1 }, { unique: true });
    
    // User preferences collection indexes
    await db.collection(collections.USER_PREFERENCES).createIndex({ userId: 1 }, { unique: true });
    
    logger.info('MongoDB indexes created successfully');
  } catch (error) {
    logger.error('Error creating MongoDB indexes', error);
    throw error;
  }
}

/**
 * Check MongoDB connection status
 * @returns {Promise<Object>} Connection status
 */
async function checkStatus() {
  try {
    if (!client || !db) {
      return { status: 'disconnected', message: 'MongoDB not initialized' };
    }
    
    await client.db('admin').command({ ping: 1 });
    return { status: 'connected', message: 'MongoDB is connected and working properly' };
  } catch (error) {
    return { 
      status: 'error', 
      message: 'MongoDB connection error', 
      error: error.message 
    };
  }
}

/**
 * Setup MongoDB and add routes to Express app
 * @param {Express} app - Express app instance
 * @param {string} mongoUri - MongoDB connection URI
 * @returns {Promise<Object>} MongoDB client and database
 */
async function setupMongoDB(app, mongoUri) {
  try {
    logger.info('Setting up MongoDB integration');

    // Initialize MongoDB connection
    const { client: mongoClient, db: database } = await initialize(mongoUri);

    // Create indexes for better performance
    await createIndexes();

    // Add MongoDB status route
    app.get('/api/mongodb-status', async (req, res) => {
      const status = await checkStatus();
      res.json(status);
    });

    return { client: mongoClient, db: database };
  } catch (error) {
    logger.error('Error setting up MongoDB', error);
    throw error;
  }
}

module.exports = {
  initialize,
  getDatabase,
  getClient,
  close,
  createIndexes,
  checkStatus,
  setupMongoDB,
  collections
};
