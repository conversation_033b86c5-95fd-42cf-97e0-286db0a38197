<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Trading Signals App</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#0d6efd">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/basic.css">
    <link rel="stylesheet" href="src/styles/styles.css">

    <style>
        body {
            background-color: var(--body-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 30px;
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-logo img {
            max-width: 150px;
        }

        .login-form .form-control {
            padding: 12px;
            border-radius: 4px;
        }

        .login-form .btn {
            padding: 12px;
            border-radius: 4px;
        }

        .login-footer {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
        }

        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
        }
    </style>
</head>
<body>
    <div class="theme-toggle">
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="darkModeToggle">
            <label class="form-check-label" for="darkModeToggle">
                <i class="fas fa-moon"></i>
            </label>
        </div>
    </div>

    <div class="login-container">
        <div class="login-logo">
            <h2>Trading Signals App</h2>
            <p class="text-muted">Sign in to access your account</p>
        </div>

        <form class="login-form" id="loginForm">
            <div class="mb-3">
                <label for="email" class="form-label">Email address</label>
                <input type="email" class="form-control" id="email" placeholder="Enter your email" required>
            </div>

            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <div class="input-group">
                    <input type="password" class="form-control" id="password" placeholder="Enter your password" required>
                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="rememberMe">
                <label class="form-check-label" for="rememberMe">Remember me</label>
            </div>

            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">Sign In</button>
                <button type="button" class="btn btn-outline-secondary" id="guestLogin">Continue as Guest</button>
            </div>

            <div class="alert alert-danger mt-3" id="loginError" style="display: none;"></div>
        </form>

        <div class="login-footer">
            <p>Don't have an account? <a href="#" id="registerLink">Register</a></p>
            <p><a href="#" id="forgotPasswordLink">Forgot password?</a></p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Theme Switcher -->
    <script src="./src/utils/theme-switcher.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle password visibility
            const togglePassword = document.getElementById('togglePassword');
            const password = document.getElementById('password');

            togglePassword.addEventListener('click', function() {
                const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
                password.setAttribute('type', type);
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });

            // Handle login form submission
            const loginForm = document.getElementById('loginForm');
            const loginError = document.getElementById('loginError');

            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;

                // Simple validation
                if (!email || !password) {
                    loginError.textContent = 'Please enter both email and password';
                    loginError.style.display = 'block';
                    return;
                }

                // Simulate login (replace with actual API call)
                loginError.style.display = 'none';

                // Set user data in localStorage
                localStorage.setItem('token', 'user-token-' + Date.now());
                localStorage.setItem('user', JSON.stringify({
                    id: 'user-' + Date.now(),
                    name: email.split('@')[0],
                    email: email,
                    role: 'user'
                }));

                // Redirect to main page after successful login
                window.location.href = 'index_en.html';
            });

            // Guest login
            const guestLogin = document.getElementById('guestLogin');
            guestLogin.addEventListener('click', function() {
                // Set guest user data in localStorage
                localStorage.setItem('token', 'guest-token');
                localStorage.setItem('user', JSON.stringify({
                    id: 'guest',
                    name: 'Guest User',
                    email: '<EMAIL>',
                    role: 'guest'
                }));

                // Redirect to main page
                window.location.href = 'index_en.html';
            });

            // Register link
            const registerLink = document.getElementById('registerLink');
            registerLink.addEventListener('click', function(e) {
                e.preventDefault();
                alert('Registration functionality will be implemented soon!');
            });

            // Forgot password link
            const forgotPasswordLink = document.getElementById('forgotPasswordLink');
            forgotPasswordLink.addEventListener('click', function(e) {
                e.preventDefault();
                alert('Password recovery functionality will be implemented soon!');
            });
        });
    </script>
</body>
</html>
