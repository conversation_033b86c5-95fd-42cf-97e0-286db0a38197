@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles that extend Tailwind */
@layer components {
  .btn-primary {
    @apply bg-navy-600 hover:bg-navy-700 text-white font-semibold py-2 px-4 rounded shadow-trading hover:shadow-trading-hover transition-all duration-300 hover:-translate-y-1;
  }

  .btn-secondary {
    @apply bg-teal-500 hover:bg-teal-600 text-white font-semibold py-2 px-4 rounded shadow-trading hover:shadow-trading-hover transition-all duration-300 hover:-translate-y-1;
  }

  .btn-gold {
    @apply bg-gold-500 hover:bg-gold-600 text-navy-900 font-semibold py-2 px-4 rounded shadow-trading hover:shadow-trading-hover transition-all duration-300 hover:-translate-y-1;
  }

  .card {
    @apply bg-white dark:bg-navy-800 rounded-lg shadow-trading hover:shadow-trading-hover transition-all duration-300 overflow-hidden;
  }

  .card-header {
    @apply bg-navy-600 dark:bg-navy-700 text-white font-bold p-4 rounded-t-lg;
  }
}
