import React, { useState, useEffect } from 'react';
import { fredAPI } from '../../../services/apiService.js';

/**
 * EconomicCalendar Component
 * 
 * Displays economic events and releases calendar
 */
const EconomicCalendar = () => {
  // State for calendar data
  const [calendarData, setCalendarData] = useState({
    events: [],
    loading: true,
    error: null
  });
  
  // State for filters
  const [filters, setFilters] = useState({
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days ago
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ahead
    importance: 'all', // 'all', 'high', 'medium', 'low'
    country: 'all'
  });
  
  // Available countries
  const countries = [
    { value: 'all', label: 'All Countries' },
    { value: 'US', label: 'United States' },
    { value: 'EU', label: 'European Union' },
    { value: 'UK', label: 'United Kingdom' },
    { value: 'JP', label: 'Japan' },
    { value: 'CA', label: 'Canada' },
    { value: 'AU', label: 'Australia' },
    { value: 'NZ', label: 'New Zealand' },
    { value: 'CH', label: 'Switzerland' }
  ];
  
  // Importance levels
  const importanceLevels = [
    { value: 'all', label: 'All Levels' },
    { value: 'high', label: 'High Impact' },
    { value: 'medium', label: 'Medium Impact' },
    { value: 'low', label: 'Low Impact' }
  ];
  
  // Fetch calendar data
  const fetchCalendarData = async () => {
    setCalendarData(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      // In a real app, this would be an API call to your backend
      // which would then fetch data from FRED API or another economic calendar provider
      
      // For this example, we'll use the FRED API to get economic releases
      const response = await fredAPI.get('releases/dates', {
        realtime_start: filters.startDate,
        realtime_end: filters.endDate,
        limit: 100
      });
      
      // Process the response
      if (response && response.release_dates) {
        const events = processCalendarEvents(response.release_dates);
        setCalendarData({
          events,
          loading: false,
          error: null
        });
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching calendar data:', error);
      setCalendarData(prev => ({
        ...prev,
        loading: false,
        error: 'Failed to load economic calendar. Please try again later.'
      }));
      
      // For demo purposes, load mock data if API fails
      loadMockCalendarData();
    }
  };
  
  // Process calendar events from FRED API
  const processCalendarEvents = (releaseDates) => {
    return releaseDates.map(release => {
      // Assign random importance for demo purposes
      // In a real app, you would have actual importance data
      const importanceValues = ['high', 'medium', 'low'];
      const randomImportance = importanceValues[Math.floor(Math.random() * importanceValues.length)];
      
      // Assign country based on release name (simplified for demo)
      let country = 'US'; // Default
      if (release.name.includes('EU') || release.name.includes('Euro')) {
        country = 'EU';
      } else if (release.name.includes('UK') || release.name.includes('Britain')) {
        country = 'UK';
      } else if (release.name.includes('Japan')) {
        country = 'JP';
      }
      
      return {
        id: release.release_id,
        title: release.name,
        date: release.date,
        time: '08:30', // Mock time, FRED doesn't provide exact time
        country,
        importance: randomImportance,
        forecast: null, // FRED doesn't provide forecast
        previous: null, // FRED doesn't provide previous value
        actual: null // FRED doesn't provide actual value
      };
    });
  };
  
  // Load mock calendar data (for demo purposes)
  const loadMockCalendarData = () => {
    const mockEvents = [
      {
        id: 1,
        title: 'US Non-Farm Payrolls',
        date: '2023-06-02',
        time: '08:30',
        country: 'US',
        importance: 'high',
        forecast: '+180K',
        previous: '+236K',
        actual: '+339K'
      },
      {
        id: 2,
        title: 'ECB Interest Rate Decision',
        date: '2023-06-15',
        time: '07:45',
        country: 'EU',
        importance: 'high',
        forecast: '3.75%',
        previous: '3.50%',
        actual: null
      },
      {
        id: 3,
        title: 'US CPI m/m',
        date: '2023-06-13',
        time: '08:30',
        country: 'US',
        importance: 'high',
        forecast: '0.2%',
        previous: '0.4%',
        actual: null
      },
      {
        id: 4,
        title: 'UK GDP m/m',
        date: '2023-06-14',
        time: '02:00',
        country: 'UK',
        importance: 'medium',
        forecast: '0.2%',
        previous: '-0.3%',
        actual: null
      },
      {
        id: 5,
        title: 'Japan BOJ Outlook Report',
        date: '2023-06-16',
        time: '03:00',
        country: 'JP',
        importance: 'medium',
        forecast: null,
        previous: null,
        actual: null
      },
      {
        id: 6,
        title: 'Australia Employment Change',
        date: '2023-06-15',
        time: '01:30',
        country: 'AU',
        importance: 'medium',
        forecast: '+20.0K',
        previous: '-4.3K',
        actual: null
      },
      {
        id: 7,
        title: 'Canada Retail Sales m/m',
        date: '2023-06-21',
        time: '08:30',
        country: 'CA',
        importance: 'low',
        forecast: '0.2%',
        previous: '1.4%',
        actual: null
      },
      {
        id: 8,
        title: 'US Building Permits',
        date: '2023-06-20',
        time: '08:30',
        country: 'US',
        importance: 'low',
        forecast: '1.42M',
        previous: '1.40M',
        actual: null
      }
    ];
    
    setCalendarData({
      events: mockEvents,
      loading: false,
      error: null
    });
  };
  
  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Apply filters
  const applyFilters = () => {
    fetchCalendarData();
  };
  
  // Filter events based on current filters
  const filteredEvents = calendarData.events.filter(event => {
    // Filter by importance
    if (filters.importance !== 'all' && event.importance !== filters.importance) {
      return false;
    }
    
    // Filter by country
    if (filters.country !== 'all' && event.country !== filters.country) {
      return false;
    }
    
    return true;
  });
  
  // Group events by date
  const groupedEvents = filteredEvents.reduce((groups, event) => {
    const date = event.date;
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(event);
    return groups;
  }, {});
  
  // Sort dates
  const sortedDates = Object.keys(groupedEvents).sort();
  
  // Fetch calendar data on component mount
  useEffect(() => {
    fetchCalendarData();
  }, []);

  return (
    <div className="economic-calendar p-4">
      <h1 className="text-2xl font-bold mb-6">Economic Calendar</h1>
      
      {/* Filters Section */}
      <section className="mb-8">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Filters</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Start Date
              </label>
              <input
                type="date"
                name="startDate"
                value={filters.startDate}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                End Date
              </label>
              <input
                type="date"
                name="endDate"
                value={filters.endDate}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
            
            {/* Importance Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Importance
              </label>
              <select
                name="importance"
                value={filters.importance}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {importanceLevels.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
            
            {/* Country Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Country
              </label>
              <select
                name="country"
                value={filters.country}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {countries.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="mt-4">
            <button
              onClick={applyFilters}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </section>
      
      {/* Calendar Section */}
      <section>
        {calendarData.loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : calendarData.error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{calendarData.error}</span>
          </div>
        ) : filteredEvents.length === 0 ? (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">No economic events found for the selected filters.</span>
          </div>
        ) : (
          <div className="space-y-6">
            {sortedDates.map(date => (
              <div key={date} className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
                <div className="bg-gray-100 dark:bg-gray-700 p-3">
                  <h3 className="text-lg font-medium">
                    {new Date(date).toLocaleDateString(undefined, { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
                  </h3>
                </div>
                
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Time</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Country</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Event</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Importance</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actual</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Forecast</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Previous</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {groupedEvents[date].map(event => (
                        <tr key={event.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{event.time}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            <span className="font-medium">{event.country}</span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{event.title}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <span className={`px-2 py-1 rounded text-xs font-medium ${
                              event.importance === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                              event.importance === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                              'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            }`}>
                              {event.importance.charAt(0).toUpperCase() + event.importance.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {event.actual || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {event.forecast || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                            {event.previous || '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ))}
          </div>
        )}
      </section>
    </div>
  );
};

export default EconomicCalendar;
