/**
 * Icon Generator for Trading Signals App
 *
 * This script generates app icons for the Trading Signals App.
 * Run this script with Node.js to create the icon-192.png and icon-512.png files.
 *
 * Requirements:
 * - Node.js
 * - Canvas package (npm install canvas)
 *
 * Usage:
 * 1. Install the canvas package: npm install canvas
 * 2. Run this script: node generate-icons.js
 * 3. The icons will be created in the current directory
 */

const fs = require('fs');
const { createCanvas } = require('canvas');

/**
 * Create an icon with the specified size
 * @param {number} size - Icon size in pixels
 */
function createIcon(size) {
  console.log(`Creating ${size}x${size} icon...`);
  
  // Create canvas
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');
  
  // Background
  ctx.fillStyle = '#0d6efd'; // Primary blue color
  ctx.beginPath();
  ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
  ctx.fill();
  
  // Inner circle
  ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
  ctx.beginPath();
  ctx.arc(size / 2, size / 2, size * 0.45, 0, Math.PI * 2);
  ctx.fill();
  
  // Chart line
  ctx.strokeStyle = 'white';
  ctx.lineWidth = size * 0.06;
  ctx.lineCap = 'round';
  ctx.lineJoin = 'round';
  
  // Draw chart line
  ctx.beginPath();
  ctx.moveTo(size * 0.2, size * 0.6);
  ctx.lineTo(size * 0.35, size * 0.4);
  ctx.lineTo(size * 0.5, size * 0.7);
  ctx.lineTo(size * 0.65, size * 0.3);
  ctx.lineTo(size * 0.8, size * 0.5);
  ctx.stroke();
  
  // Add a subtle shadow
  ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
  ctx.shadowBlur = size * 0.05;
  ctx.shadowOffsetX = size * 0.01;
  ctx.shadowOffsetY = size * 0.01;
  
  // Draw a small circle at the end of the line
  ctx.fillStyle = 'white';
  ctx.beginPath();
  ctx.arc(size * 0.8, size * 0.5, size * 0.03, 0, Math.PI * 2);
  ctx.fill();
  
  // Save the icon
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(`icon-${size}.png`, buffer);
  
  console.log(`Icon icon-${size}.png created successfully!`);
}

// Create icons of different sizes
try {
  createIcon(192);
  createIcon(512);
  console.log('Icon generation complete!');
} catch (error) {
  console.error('Error generating icons:', error);
  console.log('\nAlternative method:');
  console.log('1. Create the icons using an online tool like:');
  console.log('   - https://www.favicon-generator.org/');
  console.log('   - https://realfavicongenerator.net/');
  console.log('   - https://www.canva.com/');
  console.log('\n2. Make sure to create:');
  console.log('   - icon-192.png (192x192 pixels)');
  console.log('   - icon-512.png (512x512 pixels)');
  console.log('\n3. The icons should have:');
  console.log('   - A blue (#0d6efd) circular background');
  console.log('   - A white chart line or financial symbol');
  console.log('   - Clean, simple design that\'s recognizable at small sizes');
}
