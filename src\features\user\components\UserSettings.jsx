import React, { useState, useEffect } from 'react';

/**
 * UserSettings Component
 * 
 * Allows users to customize their experience and manage account settings
 */
const UserSettings = () => {
  // State for user settings
  const [settings, setSettings] = useState({
    profile: {
      name: '<PERSON>',
      email: '<EMAIL>',
      avatar: null
    },
    preferences: {
      theme: 'system', // 'light', 'dark', 'system'
      defaultTimeframe: 'H1',
      defaultMarket: 'forex',
      showNotifications: true,
      emailAlerts: false,
      pushNotifications: true
    },
    trading: {
      defaultLotSize: 0.01,
      riskPercentage: 2,
      defaultStopLoss: 50,
      defaultTakeProfit: 100,
      tradingPairs: ['EURUSD', 'GBPUSD', 'XAUUSD']
    },
    notifications: {
      priceAlerts: true,
      signalAlerts: true,
      newsAlerts: false,
      economicCalendarAlerts: true
    },
    api: {
      alphaVantageKey: '',
      finnhubKey: '',
      twelveDataKey: ''
    }
  });
  
  // State for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitResult, setSubmitResult] = useState({ success: false, message: '' });
  
  // State for active tab
  const [activeTab, setActiveTab] = useState('profile');
  
  // Available timeframes
  const timeframes = [
    { value: 'M5', label: '5 Minutes' },
    { value: 'M15', label: '15 Minutes' },
    { value: 'M30', label: '30 Minutes' },
    { value: 'H1', label: '1 Hour' },
    { value: 'H4', label: '4 Hours' },
    { value: 'D1', label: 'Daily' }
  ];
  
  // Available markets
  const markets = [
    { value: 'forex', label: 'Forex' },
    { value: 'commodities', label: 'Commodities' },
    { value: 'indices', label: 'Indices' },
    { value: 'crypto', label: 'Cryptocurrencies' }
  ];
  
  // Available trading pairs
  const tradingPairs = [
    { value: 'EURUSD', label: 'EUR/USD' },
    { value: 'GBPUSD', label: 'GBP/USD' },
    { value: 'USDJPY', label: 'USD/JPY' },
    { value: 'AUDUSD', label: 'AUD/USD' },
    { value: 'USDCAD', label: 'USD/CAD' },
    { value: 'XAUUSD', label: 'Gold (XAU/USD)' },
    { value: 'XAGUSD', label: 'Silver (XAG/USD)' },
    { value: 'BTCUSD', label: 'Bitcoin (BTC/USD)' },
    { value: 'ETHUSD', label: 'Ethereum (ETH/USD)' }
  ];
  
  // Handle input changes
  const handleInputChange = (section, field, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };
  
  // Handle checkbox changes
  const handleCheckboxChange = (section, field) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: !prev[section][field]
      }
    }));
  };
  
  // Handle trading pair selection
  const handleTradingPairChange = (pair) => {
    setSettings(prev => {
      const currentPairs = prev.trading.tradingPairs;
      const newPairs = currentPairs.includes(pair)
        ? currentPairs.filter(p => p !== pair)
        : [...currentPairs, pair];
      
      return {
        ...prev,
        trading: {
          ...prev.trading,
          tradingPairs: newPairs
        }
      };
    });
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitResult({ success: false, message: '' });
    
    try {
      // In a real app, this would be an API call to save user settings
      // For demo purposes, we'll simulate an API call with a timeout
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Save settings to localStorage for demo purposes
      localStorage.setItem('userSettings', JSON.stringify(settings));
      
      setSubmitResult({
        success: true,
        message: 'Settings saved successfully!'
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      setSubmitResult({
        success: false,
        message: 'Failed to save settings. Please try again.'
      });
    } finally {
      setIsSubmitting(false);
      
      // Clear success message after 3 seconds
      if (submitResult.success) {
        setTimeout(() => {
          setSubmitResult({ success: false, message: '' });
        }, 3000);
      }
    }
  };
  
  // Load settings from localStorage on component mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('userSettings');
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Error parsing saved settings:', error);
      }
    }
  }, []);

  return (
    <div className="user-settings p-4">
      <h1 className="text-2xl font-bold mb-6">User Settings</h1>
      
      {/* Tabs */}
      <div className="mb-6 border-b border-gray-200 dark:border-gray-700">
        <ul className="flex flex-wrap -mb-px">
          <li className="mr-2">
            <button
              className={`inline-block p-4 border-b-2 rounded-t-lg ${
                activeTab === 'profile'
                  ? 'border-blue-600 text-blue-600 dark:border-blue-500 dark:text-blue-500'
                  : 'border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('profile')}
            >
              Profile
            </button>
          </li>
          <li className="mr-2">
            <button
              className={`inline-block p-4 border-b-2 rounded-t-lg ${
                activeTab === 'preferences'
                  ? 'border-blue-600 text-blue-600 dark:border-blue-500 dark:text-blue-500'
                  : 'border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('preferences')}
            >
              Preferences
            </button>
          </li>
          <li className="mr-2">
            <button
              className={`inline-block p-4 border-b-2 rounded-t-lg ${
                activeTab === 'trading'
                  ? 'border-blue-600 text-blue-600 dark:border-blue-500 dark:text-blue-500'
                  : 'border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('trading')}
            >
              Trading
            </button>
          </li>
          <li className="mr-2">
            <button
              className={`inline-block p-4 border-b-2 rounded-t-lg ${
                activeTab === 'notifications'
                  ? 'border-blue-600 text-blue-600 dark:border-blue-500 dark:text-blue-500'
                  : 'border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('notifications')}
            >
              Notifications
            </button>
          </li>
          <li>
            <button
              className={`inline-block p-4 border-b-2 rounded-t-lg ${
                activeTab === 'api'
                  ? 'border-blue-600 text-blue-600 dark:border-blue-500 dark:text-blue-500'
                  : 'border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('api')}
            >
              API Keys
            </button>
          </li>
        </ul>
      </div>
      
      {/* Settings Form */}
      <form onSubmit={handleSubmit}>
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          {/* Profile Settings */}
          {activeTab === 'profile' && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Profile Settings</h2>
              
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    value={settings.profile.name}
                    onChange={(e) => handleInputChange('profile', 'name', e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    value={settings.profile.email}
                    onChange={(e) => handleInputChange('profile', 'email', e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Avatar
                  </label>
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center overflow-hidden">
                      {settings.profile.avatar ? (
                        <img src={settings.profile.avatar} alt="Avatar" className="w-full h-full object-cover" />
                      ) : (
                        <span className="text-2xl text-gray-500 dark:text-gray-400">
                          {settings.profile.name.charAt(0).toUpperCase()}
                        </span>
                      )}
                    </div>
                    <button
                      type="button"
                      className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-600"
                    >
                      Upload
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Preferences Settings */}
          {activeTab === 'preferences' && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Preferences</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Theme
                  </label>
                  <select
                    value={settings.preferences.theme}
                    onChange={(e) => handleInputChange('preferences', 'theme', e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  >
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                    <option value="system">System Default</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Default Timeframe
                  </label>
                  <select
                    value={settings.preferences.defaultTimeframe}
                    onChange={(e) => handleInputChange('preferences', 'defaultTimeframe', e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  >
                    {timeframes.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Default Market
                  </label>
                  <select
                    value={settings.preferences.defaultMarket}
                    onChange={(e) => handleInputChange('preferences', 'defaultMarket', e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  >
                    {markets.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="show-notifications"
                    checked={settings.preferences.showNotifications}
                    onChange={() => handleCheckboxChange('preferences', 'showNotifications')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="show-notifications" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Show In-App Notifications
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="email-alerts"
                    checked={settings.preferences.emailAlerts}
                    onChange={() => handleCheckboxChange('preferences', 'emailAlerts')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="email-alerts" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Email Alerts
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="push-notifications"
                    checked={settings.preferences.pushNotifications}
                    onChange={() => handleCheckboxChange('preferences', 'pushNotifications')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="push-notifications" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Push Notifications
                  </label>
                </div>
              </div>
            </div>
          )}
          
          {/* Trading Settings */}
          {activeTab === 'trading' && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Trading Settings</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Default Lot Size
                  </label>
                  <input
                    type="number"
                    min="0.01"
                    step="0.01"
                    value={settings.trading.defaultLotSize}
                    onChange={(e) => handleInputChange('trading', 'defaultLotSize', parseFloat(e.target.value))}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Risk Percentage (%)
                  </label>
                  <input
                    type="number"
                    min="0.1"
                    max="100"
                    step="0.1"
                    value={settings.trading.riskPercentage}
                    onChange={(e) => handleInputChange('trading', 'riskPercentage', parseFloat(e.target.value))}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Default Stop Loss (pips)
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={settings.trading.defaultStopLoss}
                    onChange={(e) => handleInputChange('trading', 'defaultStopLoss', parseInt(e.target.value))}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Default Take Profit (pips)
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={settings.trading.defaultTakeProfit}
                    onChange={(e) => handleInputChange('trading', 'defaultTakeProfit', parseInt(e.target.value))}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                </div>
                
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Favorite Trading Pairs
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {tradingPairs.map(pair => (
                      <div key={pair.value} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`pair-${pair.value}`}
                          checked={settings.trading.tradingPairs.includes(pair.value)}
                          onChange={() => handleTradingPairChange(pair.value)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`pair-${pair.value}`} className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                          {pair.label}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Notifications Settings */}
          {activeTab === 'notifications' && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Notification Settings</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="price-alerts"
                    checked={settings.notifications.priceAlerts}
                    onChange={() => handleCheckboxChange('notifications', 'priceAlerts')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="price-alerts" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Price Alerts
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="signal-alerts"
                    checked={settings.notifications.signalAlerts}
                    onChange={() => handleCheckboxChange('notifications', 'signalAlerts')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="signal-alerts" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Trading Signal Alerts
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="news-alerts"
                    checked={settings.notifications.newsAlerts}
                    onChange={() => handleCheckboxChange('notifications', 'newsAlerts')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="news-alerts" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Market News Alerts
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="calendar-alerts"
                    checked={settings.notifications.economicCalendarAlerts}
                    onChange={() => handleCheckboxChange('notifications', 'economicCalendarAlerts')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="calendar-alerts" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    Economic Calendar Alerts
                  </label>
                </div>
              </div>
            </div>
          )}
          
          {/* API Keys Settings */}
          {activeTab === 'api' && (
            <div>
              <h2 className="text-xl font-semibold mb-4">API Keys</h2>
              
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Alpha Vantage API Key
                  </label>
                  <input
                    type="text"
                    value={settings.api.alphaVantageKey}
                    onChange={(e) => handleInputChange('api', 'alphaVantageKey', e.target.value)}
                    placeholder="Enter your Alpha Vantage API key"
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Get a free API key at <a href="https://www.alphavantage.co/support/#api-key" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">alphavantage.co</a>
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Finnhub API Key
                  </label>
                  <input
                    type="text"
                    value={settings.api.finnhubKey}
                    onChange={(e) => handleInputChange('api', 'finnhubKey', e.target.value)}
                    placeholder="Enter your Finnhub API key"
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Get a free API key at <a href="https://finnhub.io/register" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">finnhub.io</a>
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Twelve Data API Key
                  </label>
                  <input
                    type="text"
                    value={settings.api.twelveDataKey}
                    onChange={(e) => handleInputChange('api', 'twelveDataKey', e.target.value)}
                    placeholder="Enter your Twelve Data API key"
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  />
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Get a free API key at <a href="https://twelvedata.com/pricing" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">twelvedata.com</a>
                  </p>
                </div>
              </div>
            </div>
          )}
          
          {/* Submit Button */}
          <div className="mt-8 flex items-center justify-between">
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save Settings'}
            </button>
            
            {submitResult.message && (
              <div className={`px-4 py-2 rounded ${submitResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {submitResult.message}
              </div>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default UserSettings;
