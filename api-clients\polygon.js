const axios = require('axios');
const logger = console;

/**
 * Polygon.io API client
 * @param {Object} query - Query parameters
 * @param {string} query.type - Market type (stocks, forex, crypto)
 * @param {string} query.symbol - Market symbol
 * @param {string} query.timeframe - Time frame (M1, M5, M15, M30, H1, H4, D1, W1, MN)
 * @returns {Promise<Object>} - Market data
 */
module.exports = async function polygon(query) {
  // Check if API key is available
  const apiKey = process.env.POLYGON_API_KEY;
  if (!apiKey) {
    throw new Error('Polygon API key is not set');
  }

  // Map timeframe to Polygon timespan
  const timeframeMap = {
    'M1': 'minute',
    'M5': 'minute',
    'M15': 'minute',
    'M30': 'minute',
    'H1': 'hour',
    'H4': 'hour',
    'D1': 'day',
    'W1': 'week',
    'MN': 'month'
  };

  // Get the multiplier for the timeframe
  const getMultiplier = (timeframe) => {
    if (timeframe === 'M1') return 1;
    if (timeframe === 'M5') return 5;
    if (timeframe === 'M15') return 15;
    if (timeframe === 'M30') return 30;
    if (timeframe === 'H1') return 1;
    if (timeframe === 'H4') return 4;
    if (timeframe === 'D1') return 1;
    if (timeframe === 'W1') return 1;
    if (timeframe === 'MN') return 1;
    return 1;
  };

  const timespan = timeframeMap[query.timeframe] || 'day';
  const multiplier = getMultiplier(query.timeframe);

  let url = '';
  
  try {
    // Format the URL based on market type
    if (query.type === 'stocks') {
      url = `https://api.polygon.io/v2/aggs/ticker/${query.symbol}/range/${multiplier}/${timespan}/2020-01-01/${new Date().toISOString().slice(0, 10)}?apiKey=${apiKey}`;
    } else if (query.type === 'forex') {
      // For forex, Polygon uses C: prefix
      url = `https://api.polygon.io/v2/aggs/ticker/C:${query.symbol}/range/${multiplier}/${timespan}/2020-01-01/${new Date().toISOString().slice(0, 10)}?apiKey=${apiKey}`;
    } else if (query.type === 'crypto') {
      // For crypto, Polygon uses X: prefix
      url = `https://api.polygon.io/v2/aggs/ticker/X:${query.symbol}/range/${multiplier}/${timespan}/2020-01-01/${new Date().toISOString().slice(0, 10)}?apiKey=${apiKey}`;
    } else {
      throw new Error(`Unsupported market type for Polygon: ${query.type}`);
    }

    logger.info(`Calling Polygon API for ${query.symbol}`);
    
    const response = await axios.get(url, {
      timeout: 10000,
      headers: {
        'User-Agent': 'TradingSignalsApp/1.0'
      }
    });

    if (response.data.status === 'ERROR') {
      throw new Error(`Polygon API error: ${response.data.error}`);
    }

    return formatResponse(response.data, query);
  } catch (error) {
    logger.error('Polygon API error:', error.message);
    if (error.response) {
      logger.error('Status:', error.response.status);
      logger.error('Data:', JSON.stringify(error.response.data));
    }
    throw error;
  }
};

/**
 * Format the Polygon response to a standard format
 * @param {Object} data - Polygon response
 * @param {Object} query - Original query
 * @returns {Object} - Formatted response
 */
function formatResponse(data, query) {
  if (!data || !data.results || !Array.isArray(data.results)) {
    throw new Error('Invalid or empty response from Polygon API');
  }

  return {
    symbol: query.symbol,
    timeframe: query.timeframe,
    type: query.type,
    source: 'polygon',
    timestamp: new Date().toISOString(),
    data: data.results.map(bar => ({
      timestamp: new Date(bar.t).toISOString(),
      open: bar.o,
      high: bar.h,
      low: bar.l,
      close: bar.c,
      volume: bar.v
    })).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
  };
} 